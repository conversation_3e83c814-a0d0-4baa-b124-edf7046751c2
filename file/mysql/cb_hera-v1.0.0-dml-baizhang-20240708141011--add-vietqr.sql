--liquibase formatted sql
--changeset baizhang:************** labels:v1.0.0 context:v1.0.0
--comment: v1.0.0

CREATE TABLE cb_hera.payment_vietqr_mapping
(
    id                  BIGINT UNSIGNED AUTO_INCREMENT COMMENT '主键',
    trade_no            VARCHAR(64) NOT NULL COMMENT '交易订单号',
    transaction_id            VARCHAR(64) NOT NULL COMMENT 'vietqr transaction id',
    reference_number            VARCHAR(64) NOT NULL COMMENT 'transaction reference number for the refund',
    bank_account            VARCHAR(32) NOT NULL COMMENT 'customers bank account',
    deleted              TINYINT(1) DEFAULT '0' NOT NULL COMMENT '逻辑删除：0 未删除，1 已删除',
    gmt_create           BIGINT NOT NULL COMMENT '创建时间',
    gmt_update           BIGINT NOT NULL COMMENT '更新时间',

    PRIMARY KEY (id),
    KEY idx_trade_noe (trade_no),
    KEY idx_transaction_id (transaction_id),
    KEY idx_reference_number (reference_number),
    KEY idx_gmt_create (gmt_create),
    KEY idx_gmt_update (gmt_update)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '支付凭证-vietqr关联表';