<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.chargebolt</groupId>
  <artifactId>hera-client</artifactId>
  <version>1.0.20-RELEASE</version>
  <packaging>jar</packaging>
  <dependencies>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.11.4</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.chargebolt</groupId>
      <artifactId>commons-eden</artifactId>
      <version>2.5.5-RELEASE</version>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-validator</artifactId>
      <version>5.3.5.Final</version>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-web</artifactId>
      <version>5.2.15.RELEASE</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.swagger</groupId>
      <artifactId>swagger-annotations</artifactId>
      <version>1.5.10</version>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.8.5</version>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>22.0</version>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>1.2.83</version>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <!-- 要将源码上传到maven仓库 -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>3.0.1</version>
        <configuration>
          <attach>true</attach>
        </configuration>
        <executions>
          <execution>
            <phase>compile</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <distributionManagement>
    <snapshotRepository>
      <id>snapshots</id>
      <name>User Porject Snapshot</name>
      <url>http://nexus.xiaodiankeji.net/content/repositories/snapshots/</url>
      <uniqueVersion>true</uniqueVersion>
    </snapshotRepository>
    <repository>
      <id>releases</id>
      <name>User Porject Release</name>
      <url>http://nexus.xiaodiankeji.net/content/repositories/releases/</url>
    </repository>
  </distributionManagement>
</project>