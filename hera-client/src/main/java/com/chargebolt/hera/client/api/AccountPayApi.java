package com.chargebolt.hera.client.api;

import com.chargebolt.hera.client.dto.pay.account.req.AccountPayReq;
import com.chargebolt.hera.client.dto.pay.account.req.AccountRefundReq;
import com.chargebolt.hera.client.dto.pay.account.rsp.AccountPayRsp;
import com.chargebolt.hera.client.dto.pay.account.rsp.AccountRefundRsp;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.validation.Valid;

public interface AccountPayApi {

    /**
     * 账户支付
     */
    @RequestMapping(value = "/account/pay", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    AccountPayRsp pay(@RequestBody AccountPayReq withholdReq);

    /**
     * 账户退款
     * @param accountRefundReq
     * @return
     */
    @RequestMapping(value = "/account/refund", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    AccountRefundRsp refund(@RequestBody @Valid AccountRefundReq accountRefundReq);
}
