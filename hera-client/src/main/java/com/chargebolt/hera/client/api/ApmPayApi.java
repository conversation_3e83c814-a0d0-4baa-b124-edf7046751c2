package com.chargebolt.hera.client.api;

import com.chargebolt.hera.client.dto.pay.checkout.req.CancelCheckRequest;
import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.checkout.req.CancelRequest;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.CancelCheckResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.CancelResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PrepayCreateResultDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface ApmPayApi {


    @PostMapping(value = "/apm/prepay")
    PrepayCreateResultDTO prepay(@RequestBody PrepayCreateRequest prepayCreateRequest);

    /**
     * 钱包支付校验是否可以取消
     * @param cancelCheckRequest
     * @return
     */
    @PostMapping(value = "/apm/cancel/check")
    CancelCheckResultDTO cancelCheck(@RequestBody CancelCheckRequest cancelCheckRequest);

    /**
     * 钱包押金订单取消
     * @param cancelRequest
     * @return
     */
    @PostMapping(value = "/apm/cancel")
    CancelResultDTO cancel(@RequestBody CancelRequest cancelRequest);

}
