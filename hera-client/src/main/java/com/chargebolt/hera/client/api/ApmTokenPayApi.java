package com.chargebolt.hera.client.api;

import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.apmtoken.req.ApmTokenCheckRequest;
import com.chargebolt.hera.client.dto.pay.apmtoken.req.ApmTokenPayRequest;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenCheckResultDTO;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenApplyResultDTO;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenPayResultDTO;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenPrepareResultDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 授权支付
 */
public interface ApmTokenPayApi {

    @PostMapping(value = "/authorization/prepare")
    ApmTokenPrepareResultDTO prepare(@RequestBody PrepayCreateRequest request);

    @PostMapping(value = "/authorization/apply")
    ApmTokenApplyResultDTO apply(@RequestBody PrepayCreateRequest request);

    @PostMapping(value = "/authorization/pay")
    ApmTokenPayResultDTO pay(@RequestBody ApmTokenPayRequest request);

    @PostMapping(value = "/authorization/check")
    ApmTokenCheckResultDTO check(@RequestBody ApmTokenCheckRequest request);


}
