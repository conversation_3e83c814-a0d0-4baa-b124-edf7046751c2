package com.chargebolt.hera.client.api;

import com.chargebolt.hera.client.dto.pay.checkout.req.PreAuthCancelRequest;
import com.chargebolt.hera.client.dto.pay.checkout.req.PreAuthCaptureRequest;
import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PreAuthCancelResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PreAuthCaptureResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PrepayCreateResultDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import javax.validation.Valid;

/**
 * 渠道支付
 * */
public interface BankcardAuthorizationApi {

    /**
     * 预授权
     *
     * @param prepayCreateRequest
     * @return
     */
    @PostMapping(value = "/bankcard/preAuth")
    PrepayCreateResultDTO bankcardCreatedPreAuth(@RequestBody PrepayCreateRequest prepayCreateRequest);

    /**
     * 撤销
     *
     * @param preAuthCancelRequest
     * @return
     */
    @PostMapping(value = "/bankcard/cancel")
    PreAuthCancelResultDTO bankcardCancelPreAuth(@RequestBody PreAuthCancelRequest preAuthCancelRequest);

    /**
     * 请款
     *
     * @param preAuthCaptureRequest
     * @return
     */
    @PostMapping(value = "/bankcard/capture")
    PreAuthCaptureResultDTO bankcardCapture(@RequestBody @Valid PreAuthCaptureRequest preAuthCaptureRequest);


}
