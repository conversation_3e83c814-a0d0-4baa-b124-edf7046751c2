package com.chargebolt.hera.client.api;

import com.chargebolt.hera.client.enums.PayMethodEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.commons.eden.entity.BizResult;

import java.util.List;

public interface PaySettingApi {

    @GetMapping("/paySetting/wooshpay/supports/cards")
    BizResult<List<PayMethodEnum>> getWooshpaySupportsCards();

    @GetMapping("/paySetting/pingpong/supports/cards")
    BizResult<List<PayMethodEnum>> getPingpongSupportsCards();

    @GetMapping("/paySetting/pingpong/supports/apms")
    BizResult<List<PayMethodEnum>> getPingpongSupportsApms();

    @GetMapping("/paySetting/payMethods")
    BizResult<List<PayMethodEnum>> getPayMethods(@RequestParam(value = "payway") Integer payway);
}
