package com.chargebolt.hera.client.api;

import com.chargebolt.hera.client.dto.pay.QueryPageDTO;
import com.chargebolt.hera.client.dto.pay.QueryPageResultDTO;
import com.chargebolt.hera.client.dto.pay.basic.req.PaymentPayNoReq;
import com.chargebolt.hera.client.dto.pay.basic.req.PaymentQueryReq;
import com.chargebolt.hera.client.dto.pay.payment.PaymentDTO;
import com.chargebolt.hera.client.dto.pay.payment.PaymentExtInfoResponse;
import com.chargebolt.hera.client.dto.pay.payment.PaymentQueryDTO;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.commons.eden.entity.BizResult;

import java.util.List;

/**
 * PaymentApi
 *
 * <AUTHOR>
 * @desc 原支付凭证
 * @date 2018/11/20
 */
public interface PaymentApi {

    /**
     * 查询原支付凭证DTO
     * @param tradeNo 原商户单号
     */
    @RequestMapping(value = "/payment/get", method = RequestMethod.GET)
    BizResult<PaymentDTO> get(@RequestParam("tradeNo") String tradeNo);

    /**
     * 查询原支付凭证DTO
     * @param queryReq 查询条件
     */
    @RequestMapping(value = "/payment/getByCondition", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    BizResult<PaymentDTO> getByCondition(@RequestBody PaymentQueryReq queryReq);

    /**
     * 查询原支付凭证DTO
     * @param queryDTO 分页查询条件
     */
    @RequestMapping(value = "/payment/query", method = {RequestMethod.POST})
    BizResult<List<PaymentDTO>> query(@RequestBody QueryPageDTO<PaymentQueryDTO> queryDTO);

    /**
     * 查询原支付凭证DTO
     * @param queryDTO 分页查询条件
     */
    @RequestMapping(value = "/payment/queryByPage", method = {RequestMethod.POST})
    BizResult<QueryPageResultDTO<PaymentDTO>> queryByPage(@RequestBody QueryPageDTO<PaymentQueryDTO> queryDTO);

    /**
     * 查询原支付凭证DTO总数
     * @param queryDTO 分页查询条件
     */
    @RequestMapping(value = "/payment/total", method = {RequestMethod.POST})
    BizResult<Integer> total(@RequestBody PaymentQueryDTO queryDTO);

    /**
     * 更新支付单三方订单号
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "/payment/updatePayNo", method = {RequestMethod.POST})
    BizResult<Integer> updatePayNo(@RequestBody PaymentPayNoReq request);


    /**
     * 支付订单号，获取扫码的设备编号
     *
     * 推荐使用 {@link #getPaymentExtInfo(String)}
     * @param tradeNo
     * @return
     */
    @RequestMapping(value = "/payment/getBindingDeviceNo", method = {RequestMethod.GET})
    BizResult<String> getBindingDeviceNo(@RequestParam("tradeNo") String tradeNo);

    @RequestMapping(value = "/payment/getPaymentExtInfo", method = {RequestMethod.GET})
    BizResult<PaymentExtInfoResponse> getPaymentExtInfo(@RequestParam("tradeNo") String tradeNo);

}
