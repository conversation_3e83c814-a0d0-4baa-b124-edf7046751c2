package com.chargebolt.hera.client.api;

import com.chargebolt.hera.client.dto.pay.refund.req.RefundFeeRequest;
import com.chargebolt.hera.client.dto.pay.refund.req.RefundRequest;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundDTO;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundFeeResponse;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundResultDTO;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;

import java.util.List;

/**
 * RefundApi
 * @desc 退款
 */
public interface RefundApi {

    @PostMapping(value = "/refund/refund")
    BizResult<RefundResultDTO> refund(@RequestBody RefundRequest req);

    /**
     * 获取最近一次退款记录
     * @param orderNo
     * @return
     */
    @GetMapping(value = "/refund/getLast")
    BizResult<RefundDTO> getLast(@RequestParam("tradeNo") String tradeNo, @RequestParam("orderNo") String orderNo);

    /**
     * 获取支付订单的所有退款记录
     * @param orderNo
     * @return
     */
    @GetMapping(value = "/refund/record")
    BizResult<List<RefundDTO>> record(@RequestParam("tradeNo") String tradeNo, @RequestParam("orderNo") String orderNo);

    /**
     * 延迟发起退款
     * 刚支付完成，立刻发起退款场景下使用
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/refund/delayRefund")
    BizResult<Boolean> delayRefund(@RequestBody RefundRequest req);

    /**
     * 服务费保存
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/refund/saveRefundFee")
    BizResult<String> saveRefundFee(@RequestBody RefundFeeRequest request);

    @GetMapping(value = "/refund/getRefundChannelFee")
    BizResult<List<RefundFeeResponse>> getRefundChannelFeeRecord(@RequestParam("tradeNo") String tradeNo);

}
