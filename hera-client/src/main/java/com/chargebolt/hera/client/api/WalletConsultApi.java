package com.chargebolt.hera.client.api;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.chargebolt.hera.client.dto.pay.apmconsult.ConsultFreezeApmMoneyRequest;
import com.chargebolt.hera.client.dto.pay.apmconsult.ConsultFreezeResponse;

import so.dian.commons.eden.entity.BizResult;

public interface WalletConsultApi {

    /**
     * 钱包预授权请求令牌并冻结余额
     * 
     * @param request
     * @return
     */
    @RequestMapping(value = { "/wallet/consult/freeze" })
    BizResult<ConsultFreezeResponse> consultFreeze(@RequestBody ConsultFreezeApmMoneyRequest request);
}
