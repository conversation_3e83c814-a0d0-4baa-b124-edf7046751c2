package com.chargebolt.hera.client.api;

import com.chargebolt.hera.client.dto.pay.req.*;
import com.chargebolt.hera.client.dto.pay.rsp.*;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.commons.eden.entity.BizResult;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * 直连的电子钱包支付
 */
public interface WechatPayApi {

    /**
     * 支付接口
     *
     * @param req req
     * @return OrderPayRsp
     */
    @PostMapping(value = "/order/pay", consumes = MediaType.APPLICATION_JSON_VALUE)
    BizResult<OrderPayRsp> doPayOrder(@Valid @RequestBody OrderPayReq req);

    /**
     * 关单接口
     *
     * @param req req
     * @return OrderCloseRsp
     */
    @PostMapping(value = "/pay/close", consumes = MediaType.APPLICATION_JSON_VALUE)
    BizResult<OrderCloseRsp> doCloseOrder(@Valid @RequestBody OrderCloseReq req);

    /**
     * 支付查询
     *
     * @param req req
     * @return OrderPayQueryRsp
     */
    @PostMapping(value = "/pay/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    BizResult<OrderPayQueryRsp> doQueryOrder(@Valid @RequestBody OrderPayQueryReq req);

    /**
     * 退款申请,支付部分退款，多次退款总金额不能大于实际支付金额
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/order/refund", consumes = MediaType.APPLICATION_JSON_VALUE)
    BizResult<OrderRefundRsp> doRefundOrder(@Valid @RequestBody OrderRefundReq req);

    /**
     * 退款查询
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/refund/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    BizResult<OrderRefundQueryRsp> doQueryRefund(@Valid @RequestBody OrderRefundQueryReq req);

}
