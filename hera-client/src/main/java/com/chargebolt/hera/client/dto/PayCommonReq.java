package com.chargebolt.hera.client.dto;

import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

/**
 *
 * <AUTHOR>
 * @date 2017/11/4
 */
@Data
public class PayCommonReq {
    /**
     * 渠道ID
     * @see com.chargebolt.hera.client.enums.ChannelEnum
     */
    @NotNull(message = "channelId不能为空")
    private Integer channelId;

    /**
     * 应用ID
     * @see com.chargebolt.hera.client.enums.ChannelEnum
     */
    @ApiModelProperty(value = "应用ID")
    @NotEmpty(message = "应用ID不能为空")
    private String applicationId;

    /**
     * 渠道类型
     */
    @ApiModelProperty(value = "渠道类型")
    private Integer payWay;

    /**
     * @see PaymentBizTypeEnum
     */
    @ApiModelProperty(value = "业务类型")
    private Integer bizType;

    @ApiModelProperty(value = "请求系统名")
    private String reqSystem;

    /**
     * 支付订单号，内部系统使用
     */
    private String tradeNo;

    /**
     * 退款订单号，内部系统使用
     */
    private String refundNo;

    /**
     * 货币类型，内部系统使用
     */
    private String currency;

}
