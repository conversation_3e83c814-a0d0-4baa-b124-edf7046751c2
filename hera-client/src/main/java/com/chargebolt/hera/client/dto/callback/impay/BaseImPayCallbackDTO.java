package com.chargebolt.hera.client.dto.callback.impay;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 即时支付回调DTO
 *
 * <AUTHOR>
 */
@Data
public class BaseImPayCallbackDTO implements Serializable {

    private static final long serialVersionUID = 8230582461232681544L;

    /**
     * 状态，true：支付成功，false：支付失败
     */
    private Boolean payStatus = false;

    /**
     * 三方支付订单号，比如微信transaction_id
     */
    private String paymentTradeNo;

    /**
     * 商户系统内部订单号，比如小电交易订单号
     */
    private String outTradeNo;

    /**
     * 业务支付订单号
     */
    private String orderNo;

    /**
     * 订单总金额，单位为分
     */
    private Long totalFee;

    private String currency;

    /**
     * 支付完成时间
     */
    private Date payTime;

    private String errorCode;

    private String errorMsg;

    /**
     * 错误信息详情
     * */
    private Map errorDetail;

    /**
     * 用户id
     * */
    private Long userId;

    /**
     * 业务类型
     * */
    private Integer bizType;
}
