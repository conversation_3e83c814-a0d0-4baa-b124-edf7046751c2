package com.chargebolt.hera.client.dto.callback.refund;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 即时退款回调DTO，包含即时退款以及信用退款
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BaseRefundCallbackDTO implements Serializable {

    private static final long serialVersionUID = 6219140701690762678L;

    /**
     * 是否是保障机制发送的消息，目前hermes存在退款保障机制，会主动发起退款查询，然后发送保障消息
     */
    private Boolean guaranteeMessage = false;

    /**
     * 状态，true：退款成功，false：退款失败
     */
    private Boolean refundStatus = false;

    /**
     * 失败信息
     */
    private String responseMsg;

    /**
     * 商家退款订单号
     */
    private String refundOrderNo;

    /**
     * 微信退款订单号
     */
    private String refundId;

    /**
     * 业务退款订单号
     */
    private String orderNo;

    /**
     * 退款金额，单位分
     */
    private Integer refundAmount;

    /**
     * 退款时间
     */
    private Date refundTime;
}
