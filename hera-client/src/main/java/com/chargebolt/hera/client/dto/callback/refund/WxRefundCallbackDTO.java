package com.chargebolt.hera.client.dto.callback.refund;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class WxRefundCallbackDTO extends BaseRefundCallbackDTO {

    private static final long serialVersionUID = 5100629777907033527L;

    /**
     * 订单金额，单位分
     */
    private Long totalFee;

    /**
     * 应结订单金额，单位分，当该订单有使用非充值券时，返回此字段。
     */
    private Long settlementTotalFee;

    /**
     * 申请退款金额，单位分
     */
    private Long refundFee;

}
