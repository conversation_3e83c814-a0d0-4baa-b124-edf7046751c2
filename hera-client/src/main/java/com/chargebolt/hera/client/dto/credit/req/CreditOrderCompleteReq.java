package com.chargebolt.hera.client.dto.credit.req;

import com.chargebolt.hera.client.dto.PayCommonReq;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class CreditOrderCompleteReq extends PayCommonReq implements Serializable {
    private static final long serialVersionUID = 8043793783827899049L;

    private String outOrderNo;

    private List postPayments;
    private List postDiscounts;
    private Integer totalAmount;
    private Map timeRange;
    private String goodsTag;
}
