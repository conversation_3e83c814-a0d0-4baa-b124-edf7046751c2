package com.chargebolt.hera.client.dto.credit.req;

import com.chargebolt.hera.client.dto.PayCommonReq;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class CreditOrderCreateReq extends PayCommonReq implements Serializable {
    private static final long serialVersionUID = 8747446300111944808L;
    /**
     * 微信支付分参数
     */
    private String outOrderNo;
    private String serviceIntroduction;
    private Map<String, String> timeRange;
    private Map riskFund;
    private String openid;
    private Boolean needUserConfirm;

}
