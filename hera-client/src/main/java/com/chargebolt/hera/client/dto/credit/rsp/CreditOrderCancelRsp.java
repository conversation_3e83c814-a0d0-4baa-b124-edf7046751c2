package com.chargebolt.hera.client.dto.credit.rsp;

import com.chargebolt.hera.client.dto.wechat.v3.response.WechatCreditCancelRspDto;
import lombok.Data;

import java.io.Serializable;

@Data
public class CreditOrderCancelRsp implements Serializable {
    private static final long serialVersionUID = 7794839920364838984L;

    private Boolean success = false;

    private WechatCreditCancelRspDto result;

    private String outOrderNo;
    private String orderId;

    public CreditOrderCancelRsp() {

    }

    public CreditOrderCancelRsp(boolean success) {
        this.success = success;
    }
}
