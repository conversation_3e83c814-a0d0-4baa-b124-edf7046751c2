package com.chargebolt.hera.client.dto.credit.rsp;

import com.chargebolt.hera.client.dto.credit.CreditCommonRsp;
import com.chargebolt.hera.client.dto.wechat.v3.response.WechatCreditCompleteRspDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class CreditOrderCompleteRsp extends CreditCommonRsp implements Serializable {
    private static final long serialVersionUID = -7173891259223345375L;

    private String alipayFundOrderNo;
    private String orderNo;
    private String userId;


    private WechatCreditCompleteRspDto result;
    private String serviceIntroduction;
    private String state;
    private String stateDescription;
    private Integer totalAmount;
    private List postPayments;
    private List postDiscounts;
    private Map riskFund;
    private Map timeRange;
    private Map location;

    private String orderId;
    private String outOrderNo;

    private Boolean needCollection;

    public CreditOrderCompleteRsp() {
    }

    public CreditOrderCompleteRsp(Integer code, String msg) {
        setCode(code);
        setMsg(msg);
        setSuccess(false);
    }
}
