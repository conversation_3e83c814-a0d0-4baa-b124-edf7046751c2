package com.chargebolt.hera.client.dto.credit.rsp;

import com.chargebolt.hera.client.dto.wechat.v3.response.WechatCreditCreateRspDto;
import com.chargebolt.hera.client.dto.wechat.v3.response.WechatCreditJumpParamRspDto;
import lombok.Data;

import java.io.Serializable;

@Data
public class CreditOrderCreateRsp implements Serializable {
    private static final long serialVersionUID = -8323661440692607938L;
    /**
     * 芝麻信用借还出参
     */
    private String url;

    /**
     * 微信支付分
     */
    private String state;
    private String stateDescription;
    private String orderId;
    private String outOrderNo;

    private WechatCreditCreateRspDto result;

    public CreditOrderCreateRsp(WechatCreditCreateRspDto result) {
        this.result = result;
    }

    public CreditOrderCreateRsp() {

    }

    public CreditOrderCreateRsp(String url) {
        this.url = url;
    }

    private WechatCreditJumpParamRspDto jumpParam;
}
