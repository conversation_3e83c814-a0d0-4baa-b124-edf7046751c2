package com.chargebolt.hera.client.dto.credit.rsp;

import com.chargebolt.hera.client.dto.wechat.v3.response.WechatCreditQueryRspDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class CreditOrderQueryRsp implements Serializable {
    private static final long serialVersionUID = 3035849758592387630L;

    //芝麻信用借还
    private Map<String, String> map;

    //微信支付分
    private WechatCreditQueryRspDto result;
    private String outOrderNo;
    private String serviceIntroduction;
    private String state;
    private String stateDescription;
    private Integer totalAmount;
    private List postPayments;
    private List postDiscounts;
    private Map riskFund;
    private Map timeRange;
    private Map location;
    private String orderId;
    private Boolean needCollection;
    private Map collection;
    private String openid;

}
