package com.chargebolt.hera.client.dto.notify;

import lombok.Data;

import java.util.Date;

@Data
public class NotifyDTO {

    private Long userId;
    // 支付渠道
    private Integer payway;
    // 业务类型
    private Integer bizType;
    // 业务流水号
    private String paymentOrderNo;
    // 支付流水号
    private String tradeNo;
    //币种
    private String currency;
    //金额
    private Integer amount;
    private Integer refundAmount;
//    private Integer paymentRefundAmount;
    //支付时间
    private Date payTime;
    //退款时间
    private Date refundTime;
    /**
     * @see com.chargebolt.hera.client.enums.status.PayStatus
     *
     * */
    private Integer paymentStatus;
    /**
     * @see com.chargebolt.hera.client.enums.status.RefundStatus
     *
     * */
    private Integer refundStatus;

    /**
     * 设备编号
     */
    private String deviceNo;

    /**
     * 支付方式ID
     */
    private Integer payMethod;

    /**
     * 请款模式 CaptureModeEnum
     */
    private Integer captureMode;
}
