package com.chargebolt.hera.client.dto.pay;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * Created by ailun on 2017/11/4.
 */
@Data
@ToString(callSuper = true)
public class CommonReq extends NotifyReq {
    @ApiModelProperty(value = "客户号")
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @ApiModelProperty(value = "代理商id",required = true)
    @NotNull(message = "代理商id不能为空")
    private Long cooperatorId;

}
