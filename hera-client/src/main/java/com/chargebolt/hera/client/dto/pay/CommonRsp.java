package com.chargebolt.hera.client.dto.pay;

import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;
import lombok.Data;

import java.util.Date;

/**
 * Created by ailun on 2017/11/4.
 */
@Data
public class CommonRsp extends FeignErrorDto {

    /**
     * @see TransStatusEnum
     *
     * */
    private String status;
    private String rspCode;
    private String rspMsg;
    private Date rspTime;
    private Integer channel;

    public CommonRsp(String status,String rspCode,String rspMsg){
        this.status = status;
        this.setRspCode(rspCode);
        this.setRspMsg(rspMsg);
    }

    public CommonRsp(String rspCode,String rspMsg){
        this.status = PayStatus.FAIL.getKey();
        this.setRspCode(rspCode);
        this.setRspMsg(rspMsg);
    }
    public CommonRsp(){}
}
