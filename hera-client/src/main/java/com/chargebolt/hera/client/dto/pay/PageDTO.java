package com.chargebolt.hera.client.dto.pay;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019-10-09 15:59
 */
@ApiModel(value = "分页")
@Getter
@ToString
public class PageDTO implements Serializable {

    private int page = 1;

    private int pageSize = 10;

    public void setPage(int page) {
        if (page < 1) {
            throw new IllegalArgumentException("page must be positive");
        }
        this.page = page;
    }

    public void setPageSize(int pageSize) {
        if (pageSize < 10) {
            return;
        }
        this.pageSize = pageSize;
    }
}
