package com.chargebolt.hera.client.dto.pay;

import com.chargebolt.hera.client.dto.common.CurrencyExchangeInfo;
import com.chargebolt.hera.client.enums.PaywayEnum;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class PrepayCreateRequest extends CommonReq {

    @Data
    public static class PrepayCreateExtInfo {
        private String regionCode;
        private Integer app;
        private Integer clientType;
        private String payResult;
        private String deviceNo;
        private Integer userSignType;
        private String language;
        private String goodsJSON;
        private String redirectUrl;
        private Integer payMethod;
        private String authCode;
        private String authState;
        // 交易所属的一级代理ID，上层获取由设备归属查询到
        private Long topAgentId;
        private String mobile;
        /**
         * 请款类型
         */
        private Integer captureMode;
        /**
         * 系统类型,
         * @see OsTypeEnum
         */
        private String osType;
    }

    @NotEmpty(message = "bizNo不能为空")
    private String bizNo;
    @NotNull(message = "业务类型不能为空")
    private Integer bizType;
    @Min(value = 0, message = "支付金额不能小于0")
    private Integer payAmount;
    @NotNull(message = "渠道不能为空")
    private PaywayEnum payway;
    @NotEmpty(message = "货币类型不能为空")
    private String currency;
    private Long delayTime;
    private Boolean isAutoCancel;
    private String ip;
    private PrepayCreateExtInfo extInfo;
    /**
     * 币种汇率信息，目前支付宝小程序需要
     */
    private CurrencyExchangeInfo currencyExchangeInfo;
}
