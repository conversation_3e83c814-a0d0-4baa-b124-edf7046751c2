package com.chargebolt.hera.client.dto.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.ToString;

import java.io.Serializable;


@ApiModel(value = "分页查询")
@Getter
@ToString
public class QueryPageDTO<E> implements Serializable {

    @ApiModelProperty(value = "条件")
    private E condition;

    @ApiModelProperty(value = "分页条件")
    private PageDTO pageInfo;

    private QueryPageDTO() {
    }

    public QueryPageDTO(E condition) {
        this.condition = condition;
    }

    public QueryPageDTO(E condition, PageDTO pageInfo) {
        this.condition = condition;
        this.pageInfo = pageInfo;
    }
}
