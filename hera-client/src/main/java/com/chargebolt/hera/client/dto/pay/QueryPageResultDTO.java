package com.chargebolt.hera.client.dto.pay;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-10-14 16:41
 */
@ApiModel(value = "分页查询结果")
@Getter
public class QueryPageResultDTO<E> implements Serializable {

    @ApiModelProperty(value = "数据")
    private List<E> data;

    @ApiModelProperty(value = "总数")
    private int total;

    private QueryPageResultDTO() {
    }

    public QueryPageResultDTO(List<E> data, int total) {
        this.data = data;
        this.total = total;
    }

    public static <T> QueryPageResultDTO<T> empty() {
        return new QueryPageResultDTO<>(Collections.emptyList(), 0);
    }
}
