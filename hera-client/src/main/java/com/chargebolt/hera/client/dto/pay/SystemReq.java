package com.chargebolt.hera.client.dto.pay;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created by ailun on 2017/11/4.
 */
@Data
public class SystemReq {

    @ApiModelProperty(value = "系统名",required = true)
//    @NotEmpty(message = "系统名不能为空")
    private String system;

    @ApiModelProperty(value = "请求时间")
    private Date reqDate;

    @ApiModelProperty(value = "备注")
    private String remark;

}
