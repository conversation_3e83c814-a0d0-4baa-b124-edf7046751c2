package com.chargebolt.hera.client.dto.pay.account.req;

import com.chargebolt.hera.client.dto.pay.CommonReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

/**
 * Created by ailun on 2017/11/4.
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("account-refund-req")
public class AccountRefundReq extends CommonReq {

    @ApiModelProperty(value = "原订单号(和支付单号不可同时为空)")
    private String oriOrderNo;

    @ApiModelProperty(value = "原支付单号(和原订单不可同时为空)")
    private String oriTradeNo;

    @ApiModelProperty(value = "退款流水号")
    private String refundNo;

    @ApiModelProperty(value = "退款原因")
    private String refundReason;

    @ApiModelProperty(value = "渠道",required = true)
    @NotNull(message = "渠道不能为空")
    private Integer channel;

    @ApiModelProperty(value = "货币类型", required = true)
    @NotEmpty(message = "货币类型不能为空")
    private String currency;

    @ApiModelProperty(value = "金额", required = true)
    private Integer amount;
}
