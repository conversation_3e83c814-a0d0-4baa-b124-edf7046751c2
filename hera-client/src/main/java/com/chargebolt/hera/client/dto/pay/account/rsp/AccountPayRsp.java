package com.chargebolt.hera.client.dto.pay.account.rsp;

import com.chargebolt.hera.client.dto.pay.CommonRsp;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * Created by ailun on 2017/11/7.
 */
@Data
@ApiModel("account-pay-rsp")
@NoArgsConstructor
@ToString(callSuper = true)
public class AccountPayRsp extends CommonRsp {

    private String tradeNo;

    public AccountPayRsp(String errorCode,String errorMsg){
        super(errorCode,errorMsg);
    }

    public AccountPayRsp(String tradeNo, TransStatusEnum payStatus){
        super(payStatus.getKey(),null,null);
        this.tradeNo = tradeNo;
    }
}
