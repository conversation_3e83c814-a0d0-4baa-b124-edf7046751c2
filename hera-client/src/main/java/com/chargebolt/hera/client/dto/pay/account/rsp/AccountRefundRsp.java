package com.chargebolt.hera.client.dto.pay.account.rsp;

import com.chargebolt.hera.client.dto.pay.CommonRsp;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * Created by ailun on 2017/11/7.
 */
@Data
@ApiModel("account-refund-rsp")
@NoArgsConstructor
@ToString(callSuper = true)
public class AccountRefundRsp extends CommonRsp {

    private String tradeNo;

    public AccountRefundRsp(String errorCode, String errorMsg){
        super(errorCode,errorMsg);
    }

    public AccountRefundRsp(String tradeNo, TransStatusEnum payStatus){
        super(payStatus.getKey(),null,null);
        this.tradeNo = tradeNo;
    }
}
