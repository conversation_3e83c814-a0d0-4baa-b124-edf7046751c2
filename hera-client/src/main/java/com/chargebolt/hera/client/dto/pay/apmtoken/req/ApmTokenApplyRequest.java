package com.chargebolt.hera.client.dto.pay.apmtoken.req;

import com.chargebolt.hera.client.dto.pay.CommonRsp;
import com.chargebolt.hera.client.enums.PaywayEnum;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

@Data
public class ApmTokenApplyRequest extends CommonRsp {

    @NotEmpty(message = "bizNo不能为空")
    private String bizNo;
    @NotNull(message = "渠道不能为空")
    private PaywayEnum payway;
}
