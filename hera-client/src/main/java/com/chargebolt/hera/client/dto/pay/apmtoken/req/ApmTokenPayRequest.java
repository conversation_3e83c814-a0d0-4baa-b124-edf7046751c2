package com.chargebolt.hera.client.dto.pay.apmtoken.req;

import com.chargebolt.hera.client.dto.pay.CommonReq;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * Created by ailun on 2017/11/4.
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ApmTokenPayRequest extends CommonReq {

    @NotNull
    private String bizNo;
    @NotNull
    private Integer orderAmount;
    @NotNull
    private String currency;
    @NotNull
    private PaywayEnum payway;
    private String ip;
    @NotNull
    private Integer bizType;
    private Long delayTime;
}
