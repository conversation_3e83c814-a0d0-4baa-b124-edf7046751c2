package com.chargebolt.hera.client.dto.pay.basic.req;

import com.chargebolt.hera.client.dto.pay.CommonReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * Created by ailun on 2017/11/4.
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("sdk-pay")
public class SdkPayReq extends CommonReq {

    @ApiModelProperty(value = "业务流水号",required = true)
    @NotEmpty(message = "bizSeqNo不能为空")
    private String bizSeqNo;

    @ApiModelProperty(value = "业务类型",required = true)
    @NotNull(message = "业务类型不能为空")
    private Integer bizType;

    @ApiModelProperty(value = "支付平台渠道",required = true)
    private Integer payBizType;

    @ApiModelProperty(value = "待支付金额",required = true)
    @Min(value = 0,message = "待支付金额不能小于0")
    private Integer payAmount;

    @ApiModelProperty(value = "货币类型",required = true)
    @NotEmpty(message = "货币类型不能为空")
    private String currency;

    @ApiModelProperty(value = "渠道",required = true)
    @NotNull(message = "渠道不能为空")
    private Integer channel;

    @ApiModelProperty(value = "延迟时长（单位s）")
    private Long delayTime;

    private Object channelData;
}
