package com.chargebolt.hera.client.dto.pay.basic.rsp;

import com.chargebolt.hera.client.dto.pay.CommonRsp;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Map;

/**
 * Created by ailun on 2017/11/7.
 */
@Data
@ApiModel("sdk响应实体")
@NoArgsConstructor
@AllArgsConstructor
@ToString(exclude = "paySign",callSuper = true)
public class SdkPayRsp extends CommonRsp {
    private Map<String,Object> paySign;

    private String tradeNo;

    public SdkPayRsp(String rspCode,String rspMsg){
        super(rspCode,rspMsg);
    }

    public SdkPayRsp(Map<String,Object> paySign){
        super(TransStatusEnum.SUCCESS.getKey(),null,null);
        this.paySign = paySign;
    }
}
