package com.chargebolt.hera.client.dto.pay.checkout.req;

import com.chargebolt.hera.client.dto.pay.NotifyReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by ailun on 2017/11/4.
 */
@Data
@ApiModel("agency-cancel-req")
@AllArgsConstructor
@NoArgsConstructor
public class PreAuthCancelRequest extends NotifyReq {

    @ApiModelProperty(value = "支付流水号")
    private String tradeNo;

}
