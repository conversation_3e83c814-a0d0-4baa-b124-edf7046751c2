package com.chargebolt.hera.client.dto.pay.checkout.req;

import com.chargebolt.hera.client.dto.pay.CommonReq;
import com.chargebolt.hera.client.enums.PaywayEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * Created by ailun on 2017/11/4.
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("agency-capture-req")
public class PreAuthCaptureRequest extends CommonReq {

    @ApiModelProperty(value = "用于发起capture的、hera的tradeNo单号")
    private String preAuthTradeNo;

//    @ApiModelProperty(value = "发起第三方扣款的第三方凭据")
//    private String captureId;

    @ApiModelProperty(value = "订单号")
    private String bizNo;

    @ApiModelProperty(value = "订单金额")
    private Integer orderAmount;

    @ApiModelProperty(value = "货币类型")
    private String currency;

    @ApiModelProperty(value = "渠道",required = true)
    @NotNull(message = "渠道不能为空")
    private PaywayEnum payway;

    private Integer bizType;
}
