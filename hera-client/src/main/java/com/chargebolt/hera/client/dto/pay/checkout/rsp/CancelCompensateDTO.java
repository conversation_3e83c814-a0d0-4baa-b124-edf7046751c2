package com.chargebolt.hera.client.dto.pay.checkout.rsp;

import com.chargebolt.hera.client.dto.pay.CommonRsp;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * Created by ailun on 2017/11/7.
 */
@Data
@ApiModel("agency-cancel-compensate-rsp")
@NoArgsConstructor
@ToString(callSuper = true)
public class CancelCompensateDTO extends CommonRsp {

    public CancelCompensateDTO(String errorCode, String errorMsg){
        super(errorCode,errorMsg);
    }

    public CancelCompensateDTO(TransStatusEnum payStatus){
        super(payStatus.getKey(),null,null);
    }
}
