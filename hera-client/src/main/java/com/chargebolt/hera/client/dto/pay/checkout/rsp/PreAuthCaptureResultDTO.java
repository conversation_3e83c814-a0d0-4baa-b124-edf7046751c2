package com.chargebolt.hera.client.dto.pay.checkout.rsp;

import com.chargebolt.hera.client.dto.pay.CommonRsp;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * Created by ailun on 2017/11/7.
 */
@Data
@NoArgsConstructor
public class PreAuthCaptureResultDTO extends CommonRsp {

//    private Boolean captureCallSuccess;

    /**
     * 请款模式
     */
    private Integer captureMode;
}
