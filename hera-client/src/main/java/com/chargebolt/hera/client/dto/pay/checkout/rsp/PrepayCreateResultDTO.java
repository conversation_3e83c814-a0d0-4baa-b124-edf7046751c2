package com.chargebolt.hera.client.dto.pay.checkout.rsp;

import com.chargebolt.hera.client.dto.pay.CommonRsp;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Created by ailun on 2017/11/7.
 */
@Data
@NoArgsConstructor
public class PrepayCreateResultDTO extends CommonRsp {
    private String payNo;

    private String tradeNo;

    private Map prepayResult = Maps.newHashMap();
}
