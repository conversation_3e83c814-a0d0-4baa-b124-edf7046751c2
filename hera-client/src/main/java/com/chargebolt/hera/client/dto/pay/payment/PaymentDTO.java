package com.chargebolt.hera.client.dto.pay.payment;

import com.chargebolt.hera.client.enums.PayMethodEnum;
import com.chargebolt.hera.client.dto.common.CurrencyExchangeInfo;
import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * PaymentDTO
 *
 * <AUTHOR>
 * @desc 订单支付DTO
 * @date 18/3/1
 */
@ApiModel(value = "OSS端支付凭证DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PaymentDTO implements Serializable {

    @ApiModelProperty(value = "支付凭证ID：paymentID")
    private Long id;

    @ApiModelProperty(value = "订单ID")
    private Long orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "商户单号")
    private String tradeNo;

    @ApiModelProperty(value = "支付号")
    private String payNo;

    @ApiModelProperty(value = "设备编号")
    private String deviceNo;

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "支付业务类型")
    private PaymentBizTypeEnum bizType;

    @ApiModelProperty(value = "支付方式类型")
    private PaywayEnum payway;

    @ApiModelProperty(value = "支付状态")
    private PayStatus status;

    @ApiModelProperty(value = "实付金额")
    private Integer payAmount;

    @ApiModelProperty(value = "实退金额")
    private Integer refundAmount;

    @ApiModelProperty(value = "支付时间")
    private Date payTime;

    @ApiModelProperty(value = "退款时间")
    private Date refundTime;

    @ApiModelProperty(value = "备注")
    private String note;

    @ApiModelProperty(value = "业务ID")
    private Long bizId;

    @ApiModelProperty(value = "支付凭证创建时间")
    private Date createTime;

    @ApiModelProperty(value = "支付凭证修改时间")
    private Date updateTime;

    @ApiModelProperty(value = "货币单位")
    private String currency;

    @ApiModelProperty(value = "支付方法")
    private PayMethodEnum payMethod;

    private CurrencyExchangeInfo currencyExchangeInfo;
}
