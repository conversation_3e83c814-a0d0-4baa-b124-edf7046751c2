package com.chargebolt.hera.client.dto.pay.payment;

import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * PaymentDTO
 *
 * <AUTHOR>
 * @desc 订单支付DTO
 * @date 18/3/1
 */
@ApiModel(value = "OSS端支付凭证DTO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PaymentQueryDTO implements Serializable {

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "商户单号")
    private String tradeNo;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "支付类型")
    private PaymentBizTypeEnum bizType;

    @ApiModelProperty(value = "支付状态")
    private Set<PayStatus> status;

}
