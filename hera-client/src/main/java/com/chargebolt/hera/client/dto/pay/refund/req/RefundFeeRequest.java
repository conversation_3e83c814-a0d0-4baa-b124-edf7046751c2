/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.hera.client.dto.pay.refund.req;

import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: RefundFeeRequest.java, v 1.0 2024-12-05 10:45 Exp $
 */
@Data
public class RefundFeeRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202412340104551L;

    private Long amount;
    private Integer sourceType;
    private String note;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 代理商ID
     */
    private Long agentId;
    // =========== 只保存服务费的场景下使用 =============
    private String tradeNo;
}