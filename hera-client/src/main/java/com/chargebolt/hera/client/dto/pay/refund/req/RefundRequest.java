package com.chargebolt.hera.client.dto.pay.refund.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Created by ailun on 2017/11/6.
 */
@Data
@ApiModel
public class RefundRequest {

    @ApiModelProperty("订单支付的tradeNo")
    private String tradeNo;
    @ApiModelProperty("退款原因")
    private String refundReason;
    @ApiModelProperty("退款金额：分")
    private Integer amount;
    @ApiModelProperty("退款发起的应用和系统")
    private String system;
    /**
     * 退款服务费
     */
    private List<RefundFeeRequest> refundFeeRequestList;
}
