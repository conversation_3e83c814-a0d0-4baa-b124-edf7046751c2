package com.chargebolt.hera.client.dto.pay.refund.rsp;

import com.chargebolt.hera.client.dto.pay.FeignErrorDto;
import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.RefundStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@ApiModel(value = "退款凭证DTO")
@Data
public class RefundDTO extends FeignErrorDto implements Serializable {

    @ApiModelProperty(value = "退款记录id")
    private Long id;

    @ApiModelProperty(value = "订单ID")
    private Long orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "商户单号")
    private String tradeNo;

    @ApiModelProperty(value = "退款号")
    private String refundNo;

    /**
     * {@link PaywayEnum}
     */
    @ApiModelProperty(value = "退款方式类型")
    private Integer refundType;

    /**
     * {@link PaymentBizTypeEnum}
     */
    @ApiModelProperty(value = "退款业务类型")
    private Integer bizType;

    /**
     * {@link RefundStatus}
     */
    @ApiModelProperty(value = "退款状态")
    private Integer status;

    @ApiModelProperty(value = "退款金额")
    private Integer amount;

    @ApiModelProperty(value = "货币类型")
    private String currency;

    @ApiModelProperty(value = "三方退款流水号")
    private String outTraceNo;

    @ApiModelProperty(value = "退款时间")
    private Date refundTime;

    @ApiModelProperty(value = "退款原因")
    private String reason;

    @ApiModelProperty(value = "系统")
    private String system;

    @ApiModelProperty(value = "错误原因")
    private String errorMsg;

    @ApiModelProperty(value = "退款记录创建时间")
    private Date createTime;

    @ApiModelProperty(value = "退款记录修改时间")
    private Date updateTime;
}
