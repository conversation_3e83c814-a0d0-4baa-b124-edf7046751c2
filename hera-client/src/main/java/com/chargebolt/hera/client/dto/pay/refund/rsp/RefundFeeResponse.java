/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.hera.client.dto.pay.refund.rsp;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: RefundFeeResponse.java, v 1.0 2024-12-06 18:25 Exp $
 */
@Data
public class RefundFeeResponse implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202412341182547L;

    /**
     * 支付单号
     */
    private String payTradeNo;

    /**
     * 服务费金额，最小单位：分
     */
    private Long amount;

    /**
     * 来源 1.押金退款
     */
    private Integer sourceType;

    /**
     * 备注
     */
    private String note;


    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 创建时间戳
     */
    private Long gmtCreate;

    /**
     * 更新时间戳
     */
    private Long gmtUpdate;
}