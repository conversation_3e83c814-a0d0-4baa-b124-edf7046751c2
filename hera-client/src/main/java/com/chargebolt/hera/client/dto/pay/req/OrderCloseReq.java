package com.chargebolt.hera.client.dto.pay.req;

import com.chargebolt.hera.client.dto.PayCommonReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @date 2017/11/4
 */
@Data
public class OrderCloseReq extends PayCommonReq implements Serializable {

    private static final long serialVersionUID = 5023954927126692467L;

    @ApiModelProperty(value = "业务单号", required = true)
    @NotEmpty(message = "orderNo不能为空")
    private String orderNo;

    @ApiModelProperty(value = "关单原因", required = false)
    private String reason;

}

