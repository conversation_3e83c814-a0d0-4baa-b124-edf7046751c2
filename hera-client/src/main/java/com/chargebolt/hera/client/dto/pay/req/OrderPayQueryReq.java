package com.chargebolt.hera.client.dto.pay.req;

import com.chargebolt.hera.client.dto.PayCommonReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class OrderPayQueryReq extends PayCommonReq implements Serializable {

    private static final long serialVersionUID = 5098487343748526717L;

    @ApiModelProperty(value = "业务订单号")
    @NotEmpty(message = "orderNo不能为空")
    private String orderNo;

    @ApiModelProperty(value = "是否查询第三方（默认不查询），true-查询第三方渠道订单信息，false-查询支付渠道信息")
    private Boolean qryThirdChannel = false;

}
