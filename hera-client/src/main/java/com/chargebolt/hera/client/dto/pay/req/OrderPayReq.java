package com.chargebolt.hera.client.dto.pay.req;

import com.chargebolt.hera.client.dto.PayCommonReq;
import com.chargebolt.hera.client.dto.common.CurrencyExchangeInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class OrderPayReq extends PayCommonReq implements Serializable {

    private static final long serialVersionUID = -6050659592192439259L;
    @ApiModelProperty(value = "业务订单编号", required = true)
    @NotEmpty(message = "orderNo不能为空")
    private String orderNo;

    @ApiModelProperty(value = "订单金额", required = true)
    @Min(value = 0, message = "订单金额不能小于0")
    private Integer orderAmount;

    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "平台用户ID")
    private String platOpenId;

    @ApiModelProperty(value = "货币类型,默认CNY")
    private String currency;

    @ApiModelProperty(value = "客户端ip")
    private String clientIp;

    @ApiModelProperty(value = "默认10分钟,单位：分钟")
    private String expireTime = "10";

    @ApiModelProperty(value = "商品标题描述")
    private String description;

    private Long agentId;
    private String deviceNo;

    /**
     * 需要做汇率转换的支付使用对象
     */
    private CurrencyExchangeInfo exchangeInfo;

}
