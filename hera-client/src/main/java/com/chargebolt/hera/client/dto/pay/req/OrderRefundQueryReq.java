package com.chargebolt.hera.client.dto.pay.req;

import com.chargebolt.hera.client.dto.PayCommonReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class OrderRefundQueryReq extends PayCommonReq implements Serializable {

    private static final long serialVersionUID = 821096066984079292L;

    @ApiModelProperty(value = "退款订单号", required = true)
    @NotEmpty(message = "退款订单号不能为空")
    private String refundOrderNo;

    @ApiModelProperty(value = "是否查询第三方（默认不查询），true-查询第三方渠道订单信息，false-查询支付渠道信息")
    private Boolean qryThirdChannel = false;

}
