package com.chargebolt.hera.client.dto.pay.req;

import com.chargebolt.hera.client.dto.PayCommonReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @date 2017/11/6
 */
@Data
public class OrderRefundReq extends PayCommonReq implements Serializable {

    private static final long serialVersionUID = -7654859553727370672L;

    @ApiModelProperty(value = "业务退款订单号", required = true)
    @NotEmpty(message = "退款订单号不能为空")
    private String refundOrderNo;

    @ApiModelProperty(value = "原业务支付订单号", required = true)
    @NotEmpty(message = "原订单号不能为空")
    private String oriOrderNo;

    @ApiModelProperty(value = "支付网关订单号")
    private String payNo;

    @ApiModelProperty(value = "退款金额", required = true)
    @Min(value = 0, message = "退款金额不能小于0")
    @NotNull(message = "退款金额不能为空")
    private Integer refundAmount;

    @ApiModelProperty(value = "原始订单金额")
    @NotNull(message = "原始订单金额不能为空")
    private Long oriOrderAmount;

    @ApiModelProperty(value = "退款理由")
    private String refundReason;

}
