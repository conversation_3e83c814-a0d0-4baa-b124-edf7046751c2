package com.chargebolt.hera.client.dto.pay.rsp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @date 2017/11/7
 */
@Data
public class OrderCloseRsp implements Serializable {

    private static final long serialVersionUID = 889442973004327000L;

    @ApiModelProperty(value = "业务订单号")
    private String orderNo;

    @ApiModelProperty(value = "支付订单号")
    private String payTradeNo;

    /**
     * @see com.chargebolt.hera.client.enums.status.PayStatus 取枚举值中的key值
     */
    @ApiModelProperty(value = "支付状态")
    private String status;

}
