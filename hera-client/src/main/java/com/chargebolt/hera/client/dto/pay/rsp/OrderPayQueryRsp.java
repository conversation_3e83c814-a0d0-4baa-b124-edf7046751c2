package com.chargebolt.hera.client.dto.pay.rsp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class OrderPayQueryRsp implements Serializable {

    private static final long serialVersionUID = 713893781696466833L;

    @ApiModelProperty(value = "三方支付订单号，比如微信transaction_id")
    private String thirdPayTradeNo;

    @ApiModelProperty(value = "业务订单号")
    private String orderNo;

    @ApiModelProperty(value = "支付订单号")
    private String payTradeNo;

    @ApiModelProperty(value = "订单总金额，单位为分")
    private Integer totalFee;

    @ApiModelProperty(value = "支付完成时间,必填：否")
    private Date payTime;

    /**
     * @see com.chargebolt.hera.client.enums.status.PayStatus  取枚举值中的key值
     */
    @ApiModelProperty(value = "支付状态")
    private String status;

    /**
     * 查询三方，有就返回
     */
    @ApiModelProperty(value = "营销信息，json格式")
    private String data;

    /**
     * 三方渠道应答体，不返回给业务方
     */
    @JsonIgnore
    private String content;

}
