package com.chargebolt.hera.client.dto.pay.rsp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class OrderPayRsp implements Serializable {
    private static final long serialVersionUID = -4047122951016247093L;

    @ApiModelProperty(value = "业务订单号")
    private String orderNo;

    @ApiModelProperty(value = "支付订单号")
    private String payTradeNo;

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "业务类型")
    private Integer bizType;

    @ApiModelProperty(value = "支付签名信息（json格式），参考：https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_1_4.shtml")
    private String paySign;


    @ApiModelProperty(value = "zaloPay的支付token，用于调起支付")
    private String zpTransToken;

}
