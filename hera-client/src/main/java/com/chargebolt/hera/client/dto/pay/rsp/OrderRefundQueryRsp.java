package com.chargebolt.hera.client.dto.pay.rsp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2020-08-25 6:08 下午
 * @desc 三方支付平台退款结果查询（优惠券）
 */
@Data
public class OrderRefundQueryRsp implements Serializable {

    @ApiModelProperty(value = "业务退款订单号")
    private String refundOrderNo;

    @ApiModelProperty(value = "支付退款订单号")
    private String payRefundNo;

    @ApiModelProperty(value = "三方退款订单号，必填：否")
    private String thirdRefundNo;

    @ApiModelProperty(value = "退款所使用资金对应的资金账户类型，必填：否")
    private String refundAccount;

    @ApiModelProperty(value = "支付金额，单位分")
    private Integer payAmount;

    @ApiModelProperty(value = "退款金额，单位分")
    private Integer refundAmount;

    @ApiModelProperty(value = "退款成功时间，必填：否")
    private Date refundTime;

    /**
     * @see com.chargebolt.hera.client.enums.status.RefundStatus 取枚举值中的key值
     */
    @ApiModelProperty(value = "退款状态：INIT-退款初始化，REFUNDING-退款中，已退款-REFUNDED，FAIL-退款失败")
    private String status;

    /**
     * 三方渠道应答体，不返回给业务方
     */
    @JsonIgnore
    private String content;
}
