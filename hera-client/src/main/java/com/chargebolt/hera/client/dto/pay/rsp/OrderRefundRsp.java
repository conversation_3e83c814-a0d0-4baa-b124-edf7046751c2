package com.chargebolt.hera.client.dto.pay.rsp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * @date 2017/11/6
 */
@Data
public class OrderRefundRsp implements Serializable {

    private static final long serialVersionUID = -4294962209661069721L;

    @ApiModelProperty(value = "业务退款订单号")
    private String refundOrderNo;

    @ApiModelProperty(value = "支付退款订单号")
    private String payRefundNo;

    /**
     * @see com.chargebolt.hera.client.enums.status.RefundStatus 取枚举值中的key值
     */
    @ApiModelProperty(value = "退款状态：INIT-退款初始化，REFUNDING-退款中，已退款-REFUNDED，FAIL-退款失败")
    private String status;


}
