package com.chargebolt.hera.client.dto.platPay.rsp;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
public class WxPaymentDetailQueryRsp {

    /**
     * 应结订单金额
     */
    private Long settlementTotalFee;
    /**
     * 现金支付金额
     */
    private Long cashFee;
    /**
     * 代金券金额
     */
    private Long couponTotalFee;
    /**
     * 代金券使用数量
     */
    private Integer couponCount;
    /**
     * 代金券详情
     */
    private List<Coupon> coupons;


    @Data
    @Accessors(chain = true)
    public static class Coupon {
        /**
         * 代金券类型
         * CASH--充值代金券
         * NO_CASH---非充值优惠券
         */
        private String couponType;
        /**
         * 代金券ID
         */
        private String couponId;
        /**
         * 单个代金券支付金额
         */
        private Long couponFee;
    }
}
