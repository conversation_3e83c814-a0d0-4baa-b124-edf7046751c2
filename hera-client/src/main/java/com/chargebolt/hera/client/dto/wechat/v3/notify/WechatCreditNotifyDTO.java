package com.chargebolt.hera.client.dto.wechat.v3.notify;

import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class WechatCreditNotifyDTO {

    private String appid;
    private String mchid;
    private String service_id;
    private String out_order_no;
    private String state;
    private String state_description;
    private Integer total_amount;
    private String service_introduction;
    private List post_payments;
    private List post_discounts;
    private Map risk_fund;
    private Map time_range;
    private Map location;

    private String attach;
    private String notify_url;
    private String order_id;

    private Boolean need_collection;
    private Collection collection;

    private String openid;

//    @JSONField(serialize = false)
    private Date payTime;

    private String createTime;

    @Data
    public static class Collection{
        private String state;
        private Integer total_amount;
        private Integer paying_amount;
        private Integer paid_amount;
        private List<Detail> details;
    }

    @Data
    public static class Detail{
        // 从1开始递增
        private Integer seq;
        // 单笔收款动作的金额，只能为整数，
        private Integer amount;
        // 收款成功渠道
        // NEWTON：微信支付分
        // MCH：商户渠道
        private String paid_type;
        // 收款成功时间
        // 支持两种格式:yyyyMMddHHmmss和yyyyMMdd
        // 传入20091225091010表示2009年12月25日9点10分10秒
        // 传入20091225默认认为时间为2009年12月25日0点0分0秒
        private String paid_time;
        // 结单交易单号，等于普通支付接口中的transaction_id，可以使用该订单号在APP支付->API列表->查询订单、申请退款。
        // 只有单据状态为USER_PAID，且收款成功渠道为支付分渠道，收款金额大于0，才会返回结单交易单号。
        private String transaction_id;
        // 优惠功能
        private List<PromotionDetail> 	promotion_detail;
    }

    @Data
    public static class PromotionDetail{
        // 券ID
        private String coupon_id;
        // 优惠名称
        private String name;
        // 优惠券范围
        // GLOBAL：全场代金券；
        // SINGLE：单品优惠
        private String scope;
        // 枚举值：
        // CASH：充值；
        // NOCASH：免充值。
        private String type;
        // 优惠券面额
        private Integer amount;
        // 活动ID，批次ID
        private String stock_id;
        // 微信出资
        private Long wechatpay_contribute;
        // 商户出资
        private Long merchant_contribute;
        // 其他出资
        private Long other_contribute;
        // CNY：人民币，境内商户号仅支持人民币
        private String currency;
        // 单品列表
        private List<GoodDetail> good_detail;
    }

    @Data
    public static class GoodDetail{
        // 商品编码
        private String goods_id;
        // 商品数量
        private Integer quantity;
        // 商品价格
        private Long unit_price;
        // 商品优惠金额
        private Integer discount_amount;
        // 商品备注
        private String goods_remark;
    }
}
