package com.chargebolt.hera.client.dto.wechat.v3.request;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class WechatCreditCreateReqDto extends WechatCreditCommonReqDto {
    private String service_introduction;
    private Map<String, String> time_range;
    private Map risk_fund;
    // 如果为空则由hera设置
    private String notify_url;
    private String openid;
    private Boolean need_user_confirm;
    private List<WechatCreditPostPaymentsReqDto> post_payments;
}
