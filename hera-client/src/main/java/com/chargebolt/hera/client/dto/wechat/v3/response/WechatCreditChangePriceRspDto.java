package com.chargebolt.hera.client.dto.wechat.v3.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WechatCreditChangePriceRspDto extends WechatCreditCommonRspDto {

    private String service_introduction;
    private String state;
    private String state_description;
    private Integer total_amount;
    private List post_payments;
    private List post_discounts;
    private Map risk_fund;
    private Map time_range;
    private Map location;
    private String attach;
    private String notify_url;
    private String order_id;

    private Boolean need_collection;
    private Map collection;
}
