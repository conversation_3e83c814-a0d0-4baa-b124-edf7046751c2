package com.chargebolt.hera.client.dto.wechat.v3.response;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class WechatCreditConfirmRspDto {

    private String mch_id;
    private String service_id;
    private String out_order_no;
    @SerializedName("package")
    private String _package;
    private String timestamp;
    private String nonce_str;
    private String sign_type = "HMAC-SHA256";
    private String sign;
    private String queryString;

    public String getPackage() {
        return _package;
    }

    public void setPackage(String _package) {
        this._package = _package;
    }
}
