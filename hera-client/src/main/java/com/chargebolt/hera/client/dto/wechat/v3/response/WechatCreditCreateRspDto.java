package com.chargebolt.hera.client.dto.wechat.v3.response;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class WechatCreditCreateRspDto extends WxCreditCommonRspDto {

    private String service_introduction;
    private String state;
    private String state_description;
    private List<Map> post_payments;
    private List<Map> post_discounts;
    private Map time_range;
    private Map location;
    private Map risk_fund;
    private String attach;
    private String notify_url;
    private String openid;
    private Boolean need_user_confirm;
    private String _package;
    private String order_id;

    public String getPackage() {
        return _package;
    }

    public void setPackage(String _package) {
        this._package = _package;
    }

    private WechatCreditJumpParamRspDto jumpParam;
}
