package com.chargebolt.hera.client.dto.wechat.v3.response;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/5/28
 */
@Data
public class WechatCreditJumpParamRspDto {

    // app用的字段
    private String _package;

    public String getPackage() {
        return _package;
    }

    public void setPackage(String _package) {
        this._package = _package;
    }

    // 下面字段是小程序用的
    private String mch_id;
    private String timestamp;
    private String nonce_str;
    private String sign_type = "HMAC-SHA256";
    private String sign;
    //微信支付分平台配置
    private String platAppId;
    private String platPath;
}
