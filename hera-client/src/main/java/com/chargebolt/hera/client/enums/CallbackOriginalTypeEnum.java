package com.chargebolt.hera.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum CallbackOriginalTypeEnum {


    PAY("transaction","immediate","交易通知（支付）"),

    REFUND("refund","immediate","退款通知"),

    PAYSCORE("transaction","credit","支付分交易通知"),


    ;


    /**
     * 枚举码
     */
    private String code;

    /**
     *  支付类型
     *  immediate-即时支付
     *  credit-信用支付
     */
    private String payProdType;

    /**
     * 枚举描述
     */
    private String desc;

    /**
     * 根据枚举码获取枚举类型，无效返回null
     * @param code
     * @return
     */
    public static CallbackOriginalTypeEnum getByCode(String code){
        if(StringUtils.isBlank(code)){
            return null;
        }
        for (CallbackOriginalTypeEnum item : CallbackOriginalTypeEnum.values()) {
            if(code.equals(item.getCode())){
                return item;
            }
        }

        return null;
    }

    public static CallbackOriginalTypeEnum getByCodeAndPayProdType(String code, String payProdType){
        if(StringUtils.isBlank(code)||StringUtils.isBlank(payProdType)){
            return null;
        }
        String oriKey = code + payProdType;
        for (CallbackOriginalTypeEnum item : CallbackOriginalTypeEnum.values()) {
            String tarKey = item.getCode() + item.getPayProdType();
            if(oriKey.equals(tarKey)){
                return item;
            }
        }
        return null;
    }

    public static String getKey(CallbackOriginalTypeEnum callbackOriginalTypeEnum){
        return callbackOriginalTypeEnum.getPayProdType() + "-" + callbackOriginalTypeEnum.getCode();
    }

}
