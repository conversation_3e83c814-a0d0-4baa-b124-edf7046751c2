package com.chargebolt.hera.client.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 渠道编号
 *
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum ChannelEnum {

    WECHAT(1, "Wechat"),
    ALIPAY(2, "<PERSON><PERSON><PERSON>"),
    WOOSH_PAY(3, "WooshPay"),
    PINGPONG(4, "Pingpong"),
    ZALOMINI_PAY(5, "ZaloMiniPay"),
    VIETQR(6, "VIETQR"),
    ZALO_PAY(7, "ZaloPay"),
    MIDTRANS_PAY(8, "MidtransPay"),
    ANTOM(9, "<PERSON>tom"),
    AIRWALLEX(10,"Airwallex"),
    ACCOUNT(99, "Wallet"),

    ;

    /**
     * 渠道编号（前4位固定值 1000 + 2位业务编号 + 2位序号）
     */
    private Integer channelNo;

    /**
     * 描述
     */
    private String desc;

    public static ChannelEnum explain(int code) {
        for (ChannelEnum certifyEnum : ChannelEnum.values()) {
            if (certifyEnum.getChannelNo() == code) {
                return certifyEnum;
            }
        }
        return null;
    }


}
