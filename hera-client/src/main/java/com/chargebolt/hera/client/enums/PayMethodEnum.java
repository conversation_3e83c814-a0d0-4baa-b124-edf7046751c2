package com.chargebolt.hera.client.enums;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
public enum PayMethodEnum {

    Unknown(0),

    MasterCard(101),
    VISA(102),
    UnionPay(103),
    AmericanExpress(104),
    JCB(105),

    Alipay(201),
    AlipayHK(202),
    Wechat(203),
    GCash(204),
    PromptPay(205),
    RabbitLinePay(206),
    KakaoPay(207),
    ZaloPay(208),
    /**
     * 印尼支付方式
     */
    GoPay(209),
    ShopeePay(210),
    QRIS(211),
    Dana(216),

    GooglePay(212),
    ApplePay(213),

    /**
     * 新加坡支付方式
     */
    Grabpay(214),
    PayNow(215),
    ;

    private Integer id;

    PayMethodEnum(Integer id) {
        this.id = id;
    }

    public static PayMethodEnum explain(Integer id) {
        for (PayMethodEnum payMethodEnum : PayMethodEnum.values()) {
            if (payMethodEnum.getId().equals(id)) {
                return payMethodEnum;
            }
        }
        log.warn("No PayMethodEnum with id:" + id);
        return null;
    }
}
