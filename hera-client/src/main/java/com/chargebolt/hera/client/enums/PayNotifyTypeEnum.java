package com.chargebolt.hera.client.enums;

import lombok.Getter;

/**
 * Created with lhc
 *
 * @Author: bailong Date: 17/6/5 Time: 下午6:09 北京伊电园网络科技有限公司 2016-2017 © 版权所有 京ICP备17000101号
 */
@Getter
public enum PayNotifyTypeEnum {
    PAY(0, "支付回调"),
    AUTHRORISED(1, "授权回调"),
    CANCELED(2, "撤销回调"),
    ADJUSTED(3, "改价回调"),
    CAPTURED(4, "请款回调"),
    REFUNDED(5, "退款回调"),
    FREEZE(6, "冻结回调"),
    UNFREEZE(7, "解冻回调"),

    ;

    private Integer code;
    private String desc;

    PayNotifyTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
