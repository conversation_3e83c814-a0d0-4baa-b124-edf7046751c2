package com.chargebolt.hera.client.enums;

import org.springframework.util.Assert;

import java.util.HashSet;
import java.util.Set;

/**
 * Created with lhc
 *
 * @Author: bailong Date: 17/6/5 Time: 下午6:09 北京伊电园网络科技有限公司 2016-2017 © 版权所有 京ICP备17000101号
 */
public enum PaymentBizTypeEnum {

    PAY_ORDER(11, "支付订单"),
    PAY_DEPOSIT(12, "支付押金"),

//    PAYMENT_WALLET(13, "充值余额"),

//    REPAYMENT(14, "余额还款"),

//    DEPOSIT_WALLET(15, "押金转余额"),

//    DEPOSIT_2_ORDER(16, "押金抵订单"),

//    WALLET_2_ORDER(18, "余额抵订单"),

//    BIND_CARD_ORDER(19, "绑卡订单"),

//    FREEZE_ORDER(20, "冻结订单"),

//    MULTI_REFUND_ORDER(21, "多次退款订单"),

//    PAYMENT_MEMBER(22, "充值会员"),

    CARD_PRE_AUTH(23, "信用卡预授权"),

//    APM_AUTHORIZATION(24, "电子钱包一键支付"),

    ;

    private Integer code;
    private String desc;

    PaymentBizTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PaymentBizTypeEnum explain(Integer code) {
        Assert.notNull(code, "code should not be null");
        for (PaymentBizTypeEnum enume : PaymentBizTypeEnum.values()) {
            if (enume.getCode() == code) {
                return enume;
            }
        }
        return null;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
}
