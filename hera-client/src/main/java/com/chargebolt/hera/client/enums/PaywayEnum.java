package com.chargebolt.hera.client.enums;

import lombok.Getter;

/**
 * Created with lhc
 *
 * @Author: bailong Date: 17/6/11 Time: 下午6:44 北京伊电园网络科技有限公司 2016-2017 © 版权所有 京ICP备********号
 */
@Getter
public enum PaywayEnum {

    //    WECHAT_H5(1, "微信H5", PaywayEnum.RK_WECHAT),
//    WECHAT_APP(2, "微信APP", PaywayEnum.RK_WECHAT),
//    ALIPAY_H5(3, "支付宝H5", PaywayEnum.RK_ALIPAY),
//    ALIPAY_APP(4, "支付宝APP", PaywayEnum.RK_ALIPAY),
    WECHAT_MINIPROGRAM(5, ChannelEnum.WECHAT, "微信小程序", PaywayEnum.RK_WECHAT_V3),
    WALLET(6, ChannelEnum.ACCOUNT, "Wallet", "account"),
    //    ACCOUNT_WALLET(6, "账户支付", "accountWallet"),
    DEPOSIT(7, ChannelEnum.ACCOUNT, "押金抵扣", "account"),
//    WECHAT_XIAOCHENGXU_PLUG(8, "小程序插件", PaywayEnum.RK_WECHAT),
//    QQPAY_H5(10, "QQ钱包支付H5", PaywayEnum.RK_QPAY),
    //    JDPAY_H5(11, "京东支付H5", "京东"),
//    JDPAY_APP(12, "京东支付APP", "京东"),
//    SNPAY_H5(13, "SN支付H5", PaywayEnum.RK_SUNING),

    WECHAT_CREDIT(16, ChannelEnum.WECHAT, "微信支付分", PaywayEnum.RK_WECHAT_V3),
//    WECHAT_WITHHOLD(17, "微信代扣", PaywayEnum.RK_WECHAT),
//    WECHAT_CREDIT_APP_V3(18, "app端微信支付分V3", PaywayEnum.RK_WECHAT_V3),
//    WECHAT_CREDIT_MINI_V3(19, "小程序微信支付分V3", PaywayEnum.RK_WECHAT_V3),
//    WECHAT_CREDIT_FACE_V3(20, "刷脸微信支付分V3", PaywayEnum.RK_WECHAT_V3),
//    ADYEN(20, "adyen支付", "adyen"),
//    ALIPAY_WITHHOLD(21, "支付宝代扣", PaywayEnum.RK_ALIPAY),
//    APPLEPAY(22, "apple pay支付", "adyen"),
//    SNEBUY_H5(24, "苏宁易购H5", PaywayEnum.RK_SUNING),
//    ALIPAY_XIAOCHENGXU(25, "支付宝小程序", PaywayEnum.RK_ALIPAY),
//    ALIPAY_MINI_WITHHOLD(26, "支付宝小程序代扣", PaywayEnum.RK_ALIPAY),

//    UNION_PAY_ACTIVITY(27, "银联支付", PaywayEnum.RK_UNION_PAY),
//    UNION_PAY_NONACTIVITY(28, "银联支付", PaywayEnum.RK_UNION_PAY),
//    WEXIN_H5_BROWER(30, "微信H5浏览器", PaywayEnum.RK_WECHAT),


//    ADYEN_WECHAT(31, "adyen 微信支付", "adyen"),
//    SWIFTPASS_WECHAT(32, "swiftpass 微信支付", "swiftpass"),
//    FUSIONPAY_WECHAT(33, "fusionpay 微信支付", "fusionpay"),
//    TELR(34, "telr 卡支付", "telr"),

    PRE_AUTH_CAPTURE(35, ChannelEnum.ACCOUNT, "预授权支付", "account"),
//    ALIPAY_FUND_AUTH(44, "支付宝资金授权代扣", PaywayEnum.RK_ALIPAY),
//    ZHIMA_PAY_AFTER_USE_MINI(45, "芝麻先享后付-小程序", PaywayEnum.RK_ALIPAY),
//    ZHIMA_PAY_AFTER_USE_APP(46, "芝麻先享后付-APP", PaywayEnum.RK_ALIPAY),
//    ZHIMA_PAY_AFTER_USE_H5(47, "芝麻先享后付-H5", PaywayEnum.RK_ALIPAY),
//    AGENT_WALLET(48, "代理商钱包支付", PaywayEnum.RK_ACCOUNT_PAY),
//    AGENT_CREDIT(49, "代理商授信抵扣", PaywayEnum.RK_ACCOUNT_PAY),

    WOOSHPAY_CARD(51, ChannelEnum.WOOSH_PAY, "WooshPay Card Authorization", PaywayEnum.RK_WOOSH_PAY),
    PINGPONG_CHECKOUT_CARD(52, ChannelEnum.PINGPONG, "Pingpong Card Authorization", PaywayEnum.RK_PINGPONG_CHECKOUT),
    PINGPONG_CHECKOUT_APM(53, ChannelEnum.PINGPONG, "Pingpong APM Wallet", PaywayEnum.RK_PINGPONG_CHECKOUT),
    PINGPONG_ONE_CLICK(54, ChannelEnum.PINGPONG, "Pingpong One-Click", PaywayEnum.RK_PINGPONG_ONE_CLICK),
    ZALOPAY_MINI(55, ChannelEnum.ZALOMINI_PAY, "Zalo mini", PaywayEnum.RK_ZALO_MINI),

    VIETQR_STATIC(56, ChannelEnum.VIETQR, "VietQR-static", PaywayEnum.RK_VIETQR_STATIC),
    ZALOPAY(57, ChannelEnum.ZALO_PAY, "ZaloPay", PaywayEnum.RK_ZALOPAY),
    MIDTRANS_CHECKOUT_APM(58, ChannelEnum.MIDTRANS_PAY, "Midtrans APM Wallet", PaywayEnum.RK_MIDTRANS_PAY),
    AIRWALLEX_CHECKOUT_CARD(59, ChannelEnum.AIRWALLEX, "Airwallex Card Authorization", PaywayEnum.RK_AIRWALLEX_PAY),
    ANTOM_CHECKOUT_CARD(61, ChannelEnum.ANTOM, "Antom Card Authorization", PaywayEnum.RK_ANTOM_CHECKOUT),
    ANTOM_CHECKOUT_APM(62, ChannelEnum.ANTOM, "Antom APM Wallet", PaywayEnum.RK_ANTOM_CHECKOUT),
//    OFFLINE_TRANSACTION(999, "线下交易", PaywayEnum.RK_OFFLINE_TRANSACTION),


    ;


    //    public static final String RK_WECHAT = "wechat";
    public static final String RK_WECHAT_V3 = "wechatV3";
//    public static final String RK_ALIPAY = "alipay";
//    public static final String RK_QPAY = "qpay";
//    public static final String RK_SUNING = "suning";
//    public static final String RK_UNION_PAY = "unionPay";
//    public static final String RK_ACCOUNT_PAY = "accountPay";

    public static final String RK_OFFLINE_TRANSACTION = "offlineTransaction";

    public static final String RK_WOOSH_PAY = "wooshPay";
    public static final String RK_PINGPONG_CHECKOUT = "pingpongCheckout";
    public static final String RK_PINGPONG_ONE_CLICK = "pingpongAuthorization";
    public static final String RK_ZALO_MINI = "zaloMini";
    public static final String RK_VIETQR_STATIC = "vietqrStatic";
    public static final String RK_ZALOPAY = "zaloPay";

    public static final String RK_ANTOM_CHECKOUT = "antomCheckout";
    public static final String RK_MIDTRANS_PAY = "MidtransPay";
    public static final String RK_AIRWALLEX_PAY = "AirwallexPay";




    private Integer payway;
    private ChannelEnum channel;
    private String name;
    private String routeKey;


    PaywayEnum(Integer payway, ChannelEnum channel, String name, String routeKey) {
        this.payway = payway;
        this.channel = channel;
        this.name = name;
        this.routeKey = routeKey;
    }

    public static PaywayEnum explain(Integer payway) {
        for (PaywayEnum paywayEnum : PaywayEnum.values()) {
            if (paywayEnum.getPayway().equals(payway)) {
                return paywayEnum;
            }
        }
        return null;
    }

}
