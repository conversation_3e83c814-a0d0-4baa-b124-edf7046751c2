package com.chargebolt.hera.client.enums.status;

import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * Created by mc on 2017/11/4.
 */
@Getter
public enum PayStatus implements EnumInterface<PayStatus> {

    INIT("INIT", 1, "初始化"),
    PAID("PAID", 2, "已支付"),

    REFUNDING("REFUNDING", 6, "退款中"),
    REFUNDED("REFUNDED", 7, "已退款"),
    REFUND_FAILED("REFUND_FAILED", 8, "退款失败"),

    CANCEL("CANCEL", 9, "支付取消"),

    CARD_SEND("CARD_SEND", 12, "处理中"),
    CARD_ACCEPT("CARD_ACCEPT", 13, "授理成功"),
    CARD_AUTHORIZED("CARD_AUTHORIZED", 14, "授权成功"),
    CARD_CANCELING("CARD_CANCELING", 15, "授权撤销中"),
    CARD_CANCELED("CARD_CANCELED", 16, "授权已撤销"),
    CARD_AJUSTING("CARD_AJUSTING", 17, "修改中"),
    CARD_CAPTURING("CARD_CAPTURING", 18, "请款中"),
    CARD_CAPTURED("CARD_CAPTURED", 19, "已支付"),
    CARD_AUTH_FAILED("CARD_AUTH_FAILED", 20, "预授权失败"),

    APM_TOKEN_AUTH_INIT("APM_TOKEN_AUTH_INIT", 21, "免密支付初始化"),
    APM_TOKEN_AUTH_SUCCESS("APM_TOKEN_AUTH_SUCCESS", 22, "免密支付已授权"),
    APM_TOKEN_AUTH_CANCELED("APM_TOKEN_AUTH_CANCELED", 23, "免密支付已取消"),


    FAIL("FAIL", 99, "失败"),

    ;


    private String key;

    private Integer code;

    private String value;

    PayStatus(String key, int code, String value) {
        this.key = key;
        this.code = code;
        this.value = value;
    }


    public static PayStatus explain(int code) {
        for (PayStatus status : PayStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String getDesc() {
        return value;
    }

    @Override
    public PayStatus getDefault() {
        return null;
    }
}
