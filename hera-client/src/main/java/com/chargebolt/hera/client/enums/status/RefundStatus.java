package com.chargebolt.hera.client.enums.status;

import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;


@Getter
public enum RefundStatus implements EnumInterface<RefundStatus> {
    INIT("INIT", PayStatus.INIT.getCode(), "发起退款"),
    REFUNDING("REFUNDING", PayStatus.REFUNDING.getCode(), "退款中"),
    REFUNDED("REFUNDED", PayStatus.REFUNDED.getCode(), "已退款"),
    FAIL("FAIL", PayStatus.FAIL.getCode(), "退款失败"),
    ;

    private String key;

    private Integer code;

    private String value;

    RefundStatus(String key, int code, String value) {
        this.key = key;
        this.code = code;
        this.value = value;
    }

    public static boolean isDone(int code) {
        return code == REFUNDED.code || code == FAIL.code;
    }

    public static RefundStatus explain(int code) {
        for (RefundStatus status : RefundStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String getDesc() {
        return value;
    }

    @Override
    public RefundStatus getDefault() {
        return null;
    }
}
