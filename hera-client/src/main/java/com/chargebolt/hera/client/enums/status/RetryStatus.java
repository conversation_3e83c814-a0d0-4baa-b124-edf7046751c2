package com.chargebolt.hera.client.enums.status;

import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

/**
 * Created by mc on 2017/11/4.
 */
@Getter
public enum RetryStatus implements EnumInterface<RetryStatus> {

    PROCESSING("Processing", 1, "处理中"),
    SUCCESSFUL("Successful", 2, "成功"),
    FAILED("Failed", 3, "失败"),
    ;

    private String key;

    private Integer code;

    private String value;

    RetryStatus(String key, int code, String value) {
        this.key = key;
        this.code = code;
        this.value = value;
    }


    public static RetryStatus explain(int code) {
        for (RetryStatus status : RetryStatus.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    @Override
    public String getDesc() {
        return value;
    }

    @Override
    public RetryStatus getDefault() {
        return null;
    }
}
