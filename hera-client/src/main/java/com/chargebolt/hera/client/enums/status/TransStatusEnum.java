package com.chargebolt.hera.client.enums.status;

import lombok.Getter;

/**
 * Created by mc on 2017/11/4.
 */
@Getter
public enum TransStatusEnum {
    PROCESSING("PROCESSING","处理中"),
    SUCCESS("SUCCESS","成功"),
    FAIL("FAIL","失败"),
    ;

    private String key;

    private String value;

    TransStatusEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }
}
