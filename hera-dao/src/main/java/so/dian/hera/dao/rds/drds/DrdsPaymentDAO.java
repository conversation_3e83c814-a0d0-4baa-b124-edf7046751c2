package so.dian.hera.dao.rds.drds;

import com.chargebolt.hera.domain.sharding.PaymentDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Mapper
public interface DrdsPaymentDAO {

    PaymentDO getByPaymentTradeNo(@Param("tradeNo") String tradeNo);

    PaymentDO getByPaymentOrderNo(@Param("orderNo") String orderNo);

    List<PaymentDO> findByPage(@Param("statusList") Set<Integer> statusList, @Param("bizType") Integer bizType,
                                 @Param("payBizType") Integer payBizType,
                                 @Param("userId") Long userId, @Param("tradeNo") String tradeNo,
                                 @Param("orderNo") String orderNo,
                                 @Param("offset") int offset, @Param("pageSize") int pageSize);

    int findTotal(@Param("statusList") Set<Integer> statusList, @Param("bizType") Integer bizType,
                  @Param("payBizType") Integer payBizType,
                  @Param("userId") Long userId, @Param("tradeNo") String tradeNo, @Param("orderNo") String orderNo);
}