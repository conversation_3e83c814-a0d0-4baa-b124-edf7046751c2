package so.dian.hera.dao.rds.drds;

import com.chargebolt.hera.domain.PaymentOrderMappingDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Mapper
public interface PaymentOrderMappingDAO {

    /**
     * 根据tradeNo查询orderNo列表 TODO 待验证 1个tradeNo只能对应1个OrderNo？
     */
    @Deprecated
    @Select("SELECT order_no FROM cb_lhc.payment_order_mapping WHERE trade_no = #{tradeNo} LIMIT 1")
    String getByTradeNo(@Param("tradeNo") String tradeNo);

    List<PaymentOrderMappingDO> getByOrderNos(@Param("orderNos") Set<String> orderNos);

    /**
     * 通过orderNo查询tradeNo
     */
    List<String> getTradeNoByOrderNo(String orderNo);
}