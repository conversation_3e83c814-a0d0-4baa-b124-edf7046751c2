package so.dian.hera.dao.rds.hera;

import com.chargebolt.hera.domain.ApmTokenDO;
import com.chargebolt.hera.domain.RefundDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ApmTokenMapper {

    int insert(ApmTokenDO refundDO);

    int update(ApmTokenDO refundDO);

    List<ApmTokenDO> select(@Param("userId") Long userId, @Param("status") Integer status);

    ApmTokenDO getByToken(@Param("token") String token);
}
