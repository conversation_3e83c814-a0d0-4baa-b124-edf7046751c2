/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.dao.rds.hera;
import com.chargebolt.hera.domain.PaymentRetryRecord;
import org.apache.ibatis.annotations.Param;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: PaymentRetryRecordMapper.java, v 1.0 2024-05-06 下午2:55 Exp $
 */
public interface PaymentRetryRecordMapper {
    int insert(PaymentRetryRecord model);

    int update(PaymentRetryRecord model);

    PaymentRetryRecord selectProcessingByTradeNo(@Param("tradeNo") String tradeNo);
}