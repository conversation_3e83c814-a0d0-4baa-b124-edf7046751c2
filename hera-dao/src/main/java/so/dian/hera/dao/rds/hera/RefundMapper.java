package so.dian.hera.dao.rds.hera;

import com.chargebolt.hera.domain.RefundDO;

import java.util.List;

public interface RefundMapper {

    /**
     * 生成退款凭证
     */
    int insert(RefundDO refundDO);

    int update(RefundDO refundDO);

    /**
     * 查询退款记录
     */
    RefundDO select(String refundNo);

    /**
     * 查询退款记录
     */
    RefundDO selectLastByTradeNo(String tradeNo);


    /**
     * 查询退款记录
     */
    List<RefundDO> selectByTradeNo(String tradeNo);

    /**
     * 查询退款记录
     */
    RefundDO selectLastByOrderNo(String orderNo);
    RefundDO selectByOutTraceNo(String outTradeNo);

    /**
     * 查询退款记录
     */
    List<RefundDO> selectByOrderNo(String orderNo);
}
