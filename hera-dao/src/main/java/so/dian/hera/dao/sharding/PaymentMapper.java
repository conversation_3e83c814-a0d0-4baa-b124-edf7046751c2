package so.dian.hera.dao.sharding;

import com.chargebolt.hera.domain.sharding.PaymentDO;
import org.apache.ibatis.annotations.Param;

public interface PaymentMapper {

    /**
     * 生成支付凭证
     */
    Long insert(PaymentDO paymentDO);

    /**
     * 根据tradeNo获取支付凭证信息
     */
    PaymentDO selectByTradeNo(String tradeNo);

    /**
     * 更新支付凭证
     */
    int updateStatus(PaymentDO paymentDO);

    int updateRefundStatus(PaymentDO paymentDO);

    int reverseRefund(PaymentDO paymentDO);

    int updateNotifyTag (PaymentDO paymentDO);

    int updateNotifyFlag (PaymentDO paymentDO);

    int updateRefundAmount(@Param("item") PaymentDO paymentDO, @Param("oldRefundAmount") Integer oldRefundAmount);

    /**
     * 查询会员支付凭证
     * @param payNo
     * @return
     */
    PaymentDO selectByPayNo(@Param("payNo") String payNo);

    void updateNote(PaymentDO paymentDO);

    int updateRefundAmountByTradeNo(PaymentDO paymentDO);
}
