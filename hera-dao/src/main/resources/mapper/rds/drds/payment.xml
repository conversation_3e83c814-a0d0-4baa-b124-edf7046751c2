<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.hera.dao.rds.drds.DrdsPaymentDAO">
    <resultMap id="BaseResultMap" type="com.chargebolt.hera.domain.sharding.PaymentDO">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="INTEGER" property="orderId"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="trade_no" jdbcType="VARCHAR" property="tradeNo"/>
        <result column="pay_no" jdbcType="VARCHAR" property="payNo"/>
        <result column="device_no" jdbcType="VARCHAR" property="deviceNo"/>
        <result column="user_id" jdbcType="INTEGER" property="userId"/>
        <result column="biz_type" jdbcType="TINYINT" property="bizType"/>
        <result column="pay_type" jdbcType="INTEGER" property="payType"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="pay_amount" jdbcType="INTEGER" property="payAmount"/>
        <result column="refund_amount" jdbcType="INTEGER" property="refundAmount"/>
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
        <result column="refund_time" jdbcType="TIMESTAMP" property="refundTime"/>
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="biz_id" jdbcType="BIGINT" property="bizId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="currency" jdbcType="VARCHAR" property="currency"/>
        <result column="pay_method" jdbcType="INTEGER" property="payMethod"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , order_id, order_no, trade_no, pay_no, device_no, user_id, biz_type, pay_type,
    status, pay_amount, refund_amount, pay_time, refund_time, note, biz_id, create_time,
    update_time, currency, pay_method
    </sql>

    <select id="getByPaymentTradeNo" resultType="com.chargebolt.hera.domain.sharding.PaymentDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM drds.payment
        WHERE trade_no = #{tradeNo}
        LIMIT 1
    </select>

    <select id="getByPaymentOrderNo" resultType="com.chargebolt.hera.domain.sharding.PaymentDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM drds.payment
        WHERE order_no = #{orderNo}
        LIMIT 1;
    </select>

    <select id="findByPage" resultType="com.chargebolt.hera.domain.sharding.PaymentDO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM drds.payment
        <where>
            <if test="statusList != null">
                AND status IN
                <foreach collection="statusList" item="status" index="index" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="bizType != null">
                AND biz_type = #{bizType}
            </if>
            <if test="payBizType != null">
                AND pay_biz_type = #{payBizType}
            </if>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="tradeNo != null">
                AND trade_no = #{tradeNo}
            </if>
            <if test="orderNo != null">
                AND order_no = #{orderNo}
            </if>
        </where>
        ORDER BY id DESC
        LIMIT ${offset}, ${pageSize}
    </select>

    <select id="findTotal" resultType="int">
        SELECT count(id)
        FROM drds.payment
        <where>
            <if test="statusList != null">
                AND status IN
                <foreach collection="statusList" item="status" index="index" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="bizType != null">
                AND biz_type = #{bizType}
            </if>
            <if test="payBizType != null">
                AND pay_biz_type = #{payBizType}
            </if>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="tradeNo != null">
                AND trade_no = #{tradeNo}
            </if>
            <if test="orderNo != null">
                AND order_no = #{orderNo}
            </if>
        </where>
    </select>
</mapper>