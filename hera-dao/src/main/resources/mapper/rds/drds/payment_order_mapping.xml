<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.hera.dao.rds.drds.PaymentOrderMappingDAO">
  <resultMap id="BaseResultMap" type="com.chargebolt.hera.domain.PaymentOrderMappingDO">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, order_no, trade_no, create_time, update_time, status
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from drds.payment_order_mapping
    where id = #{id,jdbcType=INTEGER}
    LIMIT 1
  </select>

  <select id="getByOrderNos" resultType="com.chargebolt.hera.domain.PaymentOrderMappingDO">
    SELECT order_no, trade_no
    FROM drds.payment_order_mapping
    WHERE
    order_no IN
    <foreach collection="orderNos" item="orderNo" open="(" separator="," close=")">
      #{orderNo}
    </foreach>
    LIMIT 10
  </select>

  <select id="getTradeNoByOrderNo" resultType="String">
    SELECT trade_no
    FROM drds.payment_order_mapping
    WHERE order_no = #{orderNo}
    LIMIT 10
  </select>

</mapper>