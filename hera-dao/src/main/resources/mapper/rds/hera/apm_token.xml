<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.hera.dao.rds.hera.ApmTokenMapper">

    <insert id="insert" parameterType="com.chargebolt.hera.domain.ApmTokenDO">
        INSERT INTO cb_hera.apm_token (gmt_create, channel, payway, tool, user_id, status, trade_no)
        VALUES (#{gmtCreate}, #{channel}, #{payway}, #{tool}, #{userId}, #{status}, #{tradeNo});
    </insert>

    <update id="update" parameterType="com.chargebolt.hera.domain.ApmTokenDO">
        UPDATE cb_hera.apm_token t
        SET t.gmt_update = #{gmtUpdate},
            t.status     = #{status},
            t.token      = #{token},
            t.expire_at  = #{expireAt}
        WHERE t.id = #{id}
    </update>

    <select id="select" resultType="com.chargebolt.hera.domain.ApmTokenDO">
        SELECT t.*
        FROM cb_hera.apm_token t
        WHERE user_id = #{userId}
          and status = #{status}
        order by id desc
    </select>

    <select id="getByToken" resultType="com.chargebolt.hera.domain.ApmTokenDO">
        SELECT t.*
        FROM cb_hera.apm_token t
        WHERE token = #{token}
    </select>
</mapper>