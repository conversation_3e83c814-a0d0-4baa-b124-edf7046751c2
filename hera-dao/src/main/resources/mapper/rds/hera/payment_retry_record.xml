<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.hera.dao.rds.hera.PaymentRetryRecordMapper">


  <insert id="insert" parameterType="com.chargebolt.hera.domain.PaymentRetryRecord">
    INSERT INTO
    `cb_hera`.`payment_retry_record`(trade_no, pay_no, payway, state, description,deleted,gmt_create, gmt_update)
    VALUES
    (#{tradeNo}, #{payNo}, #{payway}, #{state}, #{description},#{deleted}, #{gmtCreate}, #{gmtUpdate})
  </insert>

  <update id="update" parameterType="com.chargebolt.hera.domain.PaymentRetryRecord">
    UPDATE `cb_hera`.`payment_retry_record`
    <set>
        <if test="state != null">state = #{state},</if>
        <if test="description != null">description = #{description},</if>
        <if test="gmtUpdate != null">gmt_update = #{gmtUpdate},</if>
    </set>
    WHERE id = #{id}
  </update>

  <select id="selectProcessingByTradeNo" resultType="com.chargebolt.hera.domain.PaymentRetryRecord">
    SELECT * FROM `cb_hera`.`payment_retry_record`
    WHERE state= 1 and trade_no = #{tradeNo}
    LIMIT 1
  </select>

</mapper>