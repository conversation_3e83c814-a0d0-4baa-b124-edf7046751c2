<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.hera.dao.rds.hera.PaymentVietqrMappingMapper">
  <resultMap id="BaseResultMap" type="com.chargebolt.hera.domain.PaymentVietqrMappingDO">
    <id column="id" property="id"/>
    <result column="trade_no" property="tradeNo"/>
    <result column="transaction_id" property="transactionId"/>
    <result column="reference_number" property="referenceNumber"/>
    <result column="bank_account" property="bankAccount"/>
    <!-- 逻辑删除：0 未删除，1 已删除 -->
    <result column="deleted" property="deleted" />
    <!-- 创建时间 -->
    <result column="gmt_create" property="gmtCreate" />
    <!-- 更新时间 -->
    <result column="gmt_update" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <if test="id != null">
        id = #{id}
      </if>
      <if test="tradeNo != null">
        AND trade_no = #{tradeNo}
      </if>
      <if test="transactionId != null">
        AND transaction_id = #{transactionId}
      </if>
      <if test="referenceNumber != null">
        AND reference_number = #{referenceNumber}
      </if>
      <if test="deleted != null">
        AND deleted = #{deleted}
      </if>
      <if test="gmtCreate != null">
        AND gmt_create = #{gmtCreate}
      </if>
      <if test="gmtUpdate != null">
        AND gmt_update = #{gmtUpdate}
      </if>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, trade_no, transaction_id, reference_number, bank_account, deleted, gmt_create, gmt_update
  </sql>

  <!-- ============ listRecord 列表查询 ============ -->
  <select id="listRecord" parameterType="com.chargebolt.hera.domain.PaymentVietqrMappingDO" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM cb_hera.payment_vietqr_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <select id="getRecord" parameterType="com.chargebolt.hera.domain.PaymentVietqrMappingDO" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM cb_hera.payment_vietqr_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <!-- 不要删除、不要删除、不要删除 -->
    LIMIT 2
  </select>

  <insert id="insert" parameterType="com.chargebolt.hera.domain.PaymentVietqrMappingDO">
    INSERT INTO cb_hera.payment_vietqr_mapping (trade_no, transaction_id, reference_number, bank_account, deleted, gmt_create, gmt_update)
    VALUES
    ( #{tradeNo},#{transactionId},#{referenceNumber},#{bankAccount},#{deleted},#{gmtCreate},#{gmtUpdate})
  </insert>

</mapper>