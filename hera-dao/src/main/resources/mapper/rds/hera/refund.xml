<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.hera.dao.rds.hera.RefundMapper">
  <resultMap id="BaseResultMap" type="com.chargebolt.hera.domain.RefundDO">
    <id column="id" jdbcType="INTEGER" property="id"/>
    <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo"/>
    <result column="refund_no" jdbcType="VARCHAR" property="refundNo"/>
    <result column="out_trace_no" jdbcType="VARCHAR" property="outTraceNo"/>
    <result column="amount" jdbcType="INTEGER" property="amount"/>
    <result column="currency" jdbcType="TINYINT" property="currency"/>
    <result column="refund_time" jdbcType="TIMESTAMP" property="refundTime"/>
    <result column="status" jdbcType="TINYINT" property="status"/>
    <result column="biz_type" jdbcType="TINYINT" property="bizType"/>
    <result column="refund_type" jdbcType="TINYINT" property="refundType"/>
    <result column="reason" jdbcType="VARCHAR" property="reason"/>
    <result column="system" jdbcType="VARCHAR" property="system"/>
    <result column="error_msg" jdbcType="VARCHAR" property="errorMsg"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <sql id="insertColumn">
    id, order_no, trade_no, refund_no, out_trace_no, amount, currency,
    refund_time, status, refund_type, biz_type, reason, system, error_msg, create_time
  </sql>

  <sql id="column">
    <include refid="insertColumn"/>, update_time
  </sql>

  <insert id="insert" parameterType="com.chargebolt.hera.domain.RefundDO">
    INSERT INTO
    `cb_hera`.`refund`(<include refid="insertColumn"/>)
    VALUES
    (#{id}, #{orderNo}, #{tradeNo}, #{refundNo}, #{outTraceNo}, #{amount}, #{currency},
    #{refundTime}, #{status}, #{refundType}, #{bizType}, #{reason}, #{system}, #{errorMsg}, now())
  </insert>

  <update id="update" parameterType="com.chargebolt.hera.domain.RefundDO">
    UPDATE `cb_hera`.`refund`
    SET
    <if test="status != null">status = #{status},</if>
    <if test="refundTime != null">refund_time = #{refundTime},</if>
    <if test="outTraceNo != null">out_trace_no = #{outTraceNo},</if>
    <if test="errorMsg != null">error_msg = #{errorMsg},</if>
    update_time = now()
    WHERE refund_no = #{refundNo}
  </update>

  <select id="select" resultType="com.chargebolt.hera.domain.RefundDO">
    SELECT <include refid="column"/> FROM `cb_hera`.`refund`
    WHERE refund_no = #{refundNo}
  </select>

  <select id="selectLastByTradeNo" resultType="com.chargebolt.hera.domain.RefundDO">
    SELECT <include refid="column"/> FROM `cb_hera`.`refund`
    WHERE trade_no = #{tradeNo}
    ORDER BY `create_time` DESC
    LIMIT 1;
  </select>

  <select id="selectByTradeNo" resultType="com.chargebolt.hera.domain.RefundDO">
    SELECT <include refid="column"/> FROM `cb_hera`.`refund`
    WHERE trade_no = #{tradeNo}
    ORDER BY `create_time` DESC
  </select>

  <select id="selectLastByOrderNo" resultType="com.chargebolt.hera.domain.RefundDO">
    SELECT <include refid="column"/> FROM `cb_hera`.`refund`
    WHERE order_no = #{orderNo}
    ORDER BY `create_time` DESC
    LIMIT 1;
  </select>

  <select id="selectByOrderNo" resultType="com.chargebolt.hera.domain.RefundDO">
    SELECT <include refid="column"/> FROM `cb_hera`.`refund`
    WHERE order_no = #{orderNo}
    ORDER BY `create_time` DESC
  </select>
  <select id="selectByOutTraceNo" resultType="com.chargebolt.hera.domain.RefundDO">
    SELECT <include refid="column"/> FROM `cb_hera`.`refund`
    WHERE out_trace_no = #{outTraceNo}
    ORDER BY `create_time` DESC
  </select>
</mapper>