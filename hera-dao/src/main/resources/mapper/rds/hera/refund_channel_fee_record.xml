<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.hera.dao.rds.hera.RefundChannelFeeRecordMapper">
  <resultMap id="BaseResultMap" type="com.chargebolt.hera.domain.RefundChannelFeeRecord">
    <id column="id" property="id" />
    <!-- 支付单号 -->
    <result column="pay_trade_no" property="payTradeNo" />
    <!-- 退款单号 -->
    <result column="refund_no" property="refundNo" />
    <!-- 币种代码 -->
    <result column="currency_code" property="currencyCode" />
    <!-- 服务费金额，最小单位：分 -->
    <result column="amount" property="amount" />
    <!-- 来源 1.押金退款 -->
    <result column="source_type" property="sourceType" />
    <!-- 备注 -->
    <result column="note" property="note" />
    <!-- 1.成功 2.失败 -->
    <result column="state" property="state" />
    <!-- 用户ID -->
    <result column="user_id" property="userId" />
    <!-- 代理商ID -->
    <result column="agent_id" property="agentId" />
    <!-- 逻辑删除：0 未删除，1 已删除 -->
    <result column="deleted" property="deleted" />
    <!-- 创建时间 -->
    <result column="create_time" property="createTime" />
    <!-- 更新时间 -->
    <result column="update_time" property="updateTime" />
    <!-- 创建时间戳 -->
    <result column="gmt_create" property="gmtCreate" />
    <!-- 更新时间戳 -->
    <result column="gmt_update" property="gmtUpdate" />
  </resultMap>
  
  <!-- ============ WHERE条件组装 ============ -->
  <sql id="Example_Where_Clause">
    <where>
      <if test="id != null">
        id = #{id}
      </if>
      <if test="payTradeNo != null">
        AND pay_trade_no = #{payTradeNo}
      </if>
      <if test="refundNo != null">
        AND refund_no = #{refundNo}
      </if>
      <if test="currencyCode != null">
        AND currency_code = #{currencyCode}
      </if>
      <if test="amount != null">
        AND amount = #{amount}
      </if>
      <if test="sourceType != null">
        AND source_type = #{sourceType}
      </if>
      <if test="note != null">
        AND note = #{note}
      </if>
      <if test="state != null">
        AND state = #{state}
      </if>
      <if test="userId != null">
        AND user_id = #{userId}
      </if>
      <if test="agentId != null">
        AND agent_id = #{agentId}
      </if>
      <if test="deleted != null">
        AND deleted = #{deleted}
      </if>
      <if test="createTime != null">
        AND create_time = #{createTime}
      </if>
      <if test="updateTime != null">
        AND update_time = #{updateTime}
      </if>
      <if test="gmtCreate != null">
        AND gmt_create = #{gmtCreate}
      </if>
      <if test="gmtUpdate != null">
        AND gmt_update = #{gmtUpdate}
      </if>
    </where>
  </sql>
  
  <!-- ============ 表基础字段 ============ -->
  <sql id="Base_Column_List">
    id, pay_trade_no, refund_no, currency_code, amount, source_type, note, state, user_id, 
    agent_id, deleted, create_time, update_time, gmt_create, gmt_update
  </sql>
  
  <!-- ============ listRecord 列表查询 ============ -->
  <select id="listRecord" parameterType="com.chargebolt.hera.domain.RefundChannelFeeRecord" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM cb_hera.refund_channel_fee_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  
  <!-- ============ getRecord 单条查询 ============ -->
  <select id="getRecord" parameterType="com.chargebolt.hera.domain.RefundChannelFeeRecord" resultMap="BaseResultMap">
    SELECT 
    <include refid="Base_Column_List" />
    FROM cb_hera.refund_channel_fee_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <!-- 不要删除、不要删除、不要删除 -->
     LIMIT 2
  </select>
  
  <!-- ============ saveRecord 单条插入 ============ -->
  <insert id="saveRecord" parameterType="com.chargebolt.hera.domain.RefundChannelFeeRecord">
    INSERT INTO cb_hera.refund_channel_fee_record (pay_trade_no, refund_no, currency_code, amount, source_type, note,
      state, user_id, agent_id, deleted, create_time, update_time, gmt_create, 
      gmt_update)
    VALUES (#{payTradeNo}, #{refundNo}, #{currencyCode}, #{amount}, #{sourceType}, #{note}, 
      #{state}, #{userId}, #{agentId}, #{deleted}, #{createTime}, #{updateTime}, #{gmtCreate}, 
      #{gmtUpdate})
  </insert>
  
  <!-- ============ removeRecord 逻辑删除 ============ -->
  <update id="removeRecord" parameterType="com.chargebolt.hera.domain.RefundChannelFeeRecord">
    UPDATE cb_hera.refund_channel_fee_record SET deleted = 1 WHERE id = #{id}
  </update>
  
  <!-- ============ updateRecord 更新by 主键 ============ -->
  <update id="updateRecord" parameterType="com.chargebolt.hera.domain.RefundChannelFeeRecord">
    UPDATE cb_hera.refund_channel_fee_record
    <set>
      <if test="payTradeNo != null">
        pay_trade_no = #{payTradeNo},
      </if>
      <if test="refundNo != null">
        refund_no = #{refundNo},
      </if>
      <if test="currencyCode != null">
        currency_code = #{currencyCode},
      </if>
      <if test="amount != null">
        amount = #{amount},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType},
      </if>
      <if test="note != null">
        note = #{note},
      </if>
      <if test="state != null">
        state = #{state},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
      <if test="agentId != null">
        agent_id = #{agentId},
      </if>
      <if test="deleted != null">
        deleted = #{deleted},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate},
      </if>
    </set>
    WHERE id = #{id}
  </update>

  <!-- ============ saveBatchRecord 批量插入 ============ -->
  <insert id="saveBatchRecord" parameterType="java.util.List">
    INSERT INTO cb_hera.refund_channel_fee_record (pay_trade_no, refund_no, currency_code, amount, source_type,
    note, state, user_id, agent_id, deleted,
    create_time, update_time, gmt_create, gmt_update) VALUES
    <foreach collection="list" index="index" item="item" separator=",">
      (#{item.payTradeNo}, #{item.refundNo}, #{item.currencyCode}, #{item.amount}, #{item.sourceType},
      #{item.note}, #{item.state}, #{item.userId}, #{item.agentId}, #{item.deleted},
      #{item.createTime}, #{item.updateTime}, #{item.gmtCreate}, #{item.gmtUpdate})
    </foreach>
  </insert>
</mapper>