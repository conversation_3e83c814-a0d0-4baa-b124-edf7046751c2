<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="so.dian.hera.dao.sharding.CreditOrdersDAO" >
  <resultMap id="BaseResultMap" type="com.chargebolt.hera.domain.CreditOrdersDO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="user_id" property="userId" jdbcType="BIGINT" />
    <result column="channel" property="channel" jdbcType="TINYINT" />
    <result column="biz_type" property="bizType" jdbcType="TINYINT" />
    <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
    <result column="out_order_no" property="outOrderNo" jdbcType="VARCHAR" />
    <result column="order_amount" property="orderAmount" jdbcType="BIGINT" />
    <result column="rent_amount" property="rentAmount" jdbcType="BIGINT" />
    <result column="compensation_amount" property="compensationAmount" jdbcType="BIGINT" />
    <result column="refund_amount" property="refundAmount" jdbcType="BIGINT" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="loan_time" property="loanTime" jdbcType="TIMESTAMP" />
    <result column="return_time" property="returnTime" jdbcType="TIMESTAMP" />
    <result column="pay_time" property="payTime" jdbcType="TIMESTAMP" />
    <result column="fail_reason" property="failReason" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="deleted" property="deleted" jdbcType="TINYINT" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="is_returned" property="isReturned" jdbcType="TINYINT" />
    <result column="price_change_degree" property="priceChangeDegree" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, user_id, channel, biz_type, order_no, out_order_no, order_amount,rent_amount,compensation_amount,refund_amount, status, loan_time,
    return_time, pay_time, fail_reason, create_time, update_time, deleted, remark,is_returned, price_change_degree
  </sql>
  <select id="selectByOrderNo" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from credit_orders
    where order_no = #{orderNo,jdbcType=VARCHAR}
  </select>

  <insert id="insertSelective" parameterType="com.chargebolt.hera.domain.CreditOrdersDO" >
    insert into credit_orders
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
      <if test="channel != null" >
        channel,
      </if>
      <if test="bizType != null" >
        biz_type,
      </if>
      <if test="orderNo != null" >
        order_no,
      </if>
      <if test="outOrderNo != null" >
        out_order_no,
      </if>
      <if test="orderAmount != null" >
        order_amount,
      </if>
      <if test="rentAmount != null" >
        rent_amount,
      </if>
      <if test="compensationAmount != null" >
        compensation_amount,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="loanTime != null" >
        loan_time,
      </if>
      <if test="returnTime != null" >
        return_time,
      </if>
      <if test="payTime != null" >
        pay_time,
      </if>
      <if test="failReason != null" >
        fail_reason,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="deleted != null" >
        deleted,
      </if>
      <if test="remark != null" >
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="channel != null" >
        #{channel,jdbcType=TINYINT},
      </if>
      <if test="bizType != null" >
        #{bizType,jdbcType=TINYINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="outOrderNo != null" >
        #{outOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderAmount != null" >
        #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="rentAmount != null" >
        #{rentAmount,jdbcType=BIGINT},
      </if>
      <if test="compensationAmount != null" >
        #{compensationAmount,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        #{status,jdbcType=TINYINT},
      </if>
      <if test="loanTime != null" >
        #{loanTime,jdbcType=TIMESTAMP},
      </if>
      <if test="returnTime != null" >
        #{returnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payTime != null" >
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="failReason != null" >
        #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null" >
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateStatus" parameterType="com.chargebolt.hera.domain.CreditOrdersDO" >
    update credit_orders
    <set >
      <if test="outOrderNo != null" >
        out_order_no = #{outOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderAmount != null" >
        order_amount = #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="rentAmount != null" >
        rent_amount = #{rentAmount,jdbcType=BIGINT},
      </if>
      <if test="compensationAmount != null" >
        compensation_amount = #{compensationAmount,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="loanTime != null" >
        loan_time = #{loanTime,jdbcType=TIMESTAMP},
      </if>
      <if test="returnTime != null" >
        return_time = #{returnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="payTime != null" >
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="failReason != null" >
        fail_reason = #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isReturned != null" >
        is_returned = #{isReturned,jdbcType=TINYINT},
      </if>
      <if test="priceChangeDegree != null" >
        price_change_degree = #{priceChangeDegree,jdbcType=TINYINT},
      </if>
      update_time = now()
    </set>
    where order_no = #{orderNo,jdbcType=VARCHAR}
    <if test="oriStatus != null" >
      and status = #{oriStatus,jdbcType=TINYINT}
    </if>
  </update>

  <update id="processUsingStatus" parameterType="com.chargebolt.hera.domain.CreditOrdersDO" >
    update credit_orders
    <set >
      status = #{status,jdbcType=TINYINT},
      update_time = now()
    </set>
    where order_no = #{orderNo,jdbcType=VARCHAR}
      and status in (1,2);
  </update>

  <update id="update2Paid" parameterType="com.chargebolt.hera.domain.CreditOrdersDO" >
    update credit_orders
    <set >
      status = 9,
      <if test="payTime != null" >
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderAmount != null" >
        order_amount = #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="rentAmount != null" >
        rent_amount = #{rentAmount,jdbcType=BIGINT},
      </if>
      <if test="compensationAmount != null" >
        compensation_amount = #{compensationAmount,jdbcType=BIGINT},
      </if>
      <if test="priceChangeDegree != null" >
        price_change_degree = #{priceChangeDegree,jdbcType=TINYINT},
      </if>
      update_time = now()
    </set>
    where order_no = #{orderNo,jdbcType=VARCHAR}
    and status in (6,7,8,10);
  </update>

</mapper>