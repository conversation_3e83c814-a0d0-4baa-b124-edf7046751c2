<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.hera.dao.sharding.PaymentMapper">

    <!--该表的分表键为trade_no，务必确保所有的查询条件带有该分表键，否则造成全表扫描影响系统性能-->
    <insert id="insert" keyProperty="id" parameterType="com.chargebolt.hera.domain.sharding.PaymentDO">
        <if test="id == null">
            INSERT INTO
            payment(order_id,order_no,user_id,biz_type,pay_biz_type,pay_type,pay_amount,refund_amount,currency,status,trade_no,
            delay_time,pay_no,note,create_time,update_time,pay_time,refund_time,device_no,biz_id,req_system)
            VALUES
            (#{orderId},#{orderNo},#{userId},#{bizType},#{payBizType},#{payType},#{payAmount},#{refundAmount},#{currency},#{status},
            #{tradeNo},#{delayTime},#{payNo},#{note},now(),now(),#{payTime},#{refundTime},#{deviceNo},#{bizId},#{reqSystem})
        </if>
        <if test="id != null">
            INSERT INTO
            payment(id,order_id,order_no,user_id,biz_type,pay_biz_type,pay_type,pay_amount,refund_amount,currency,status,trade_no,delay_time,pay_no,note,create_time,update_time,pay_time,refund_time,device_no,biz_id,req_system)
            VALUES
            (#{id},#{orderId},#{orderNo},#{userId},#{bizType},#{payBizType},#{payType},#{payAmount},#{refundAmount},#{currency},#{status},#{tradeNo},#{delayTime},#{payNo},#{note},now(),now(),#{payTime},#{refundTime},#{deviceNo},#{bizId},#{reqSystem})
        </if>
    </insert>

    <select id="selectByTradeNo" resultType="com.chargebolt.hera.domain.sharding.PaymentDO">
        SELECT *
        FROM payment
        WHERE trade_no = #{tradeNo}
    </select>

    <update id="updateStatus" parameterType="com.chargebolt.hera.domain.sharding.PaymentDO">
        UPDATE payment
        SET
        <if test="status != null">status = #{status},</if>
        <if test="payTime != null">pay_time = #{payTime},</if>
        <if test="payAmount != null">pay_amount = #{payAmount},</if>
        <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
        <if test="refundTime != null">refund_time = #{refundTime},</if>
        <if test="payNo != null">pay_no = #{payNo},</if>
        <if test="note != null">note = #{note},</if>
        <if test="bizId != null">biz_id = #{bizId},</if>
        <if test="failReason != null and failReason != ''">
            fail_reason = #{failReason},
        </if>
        <if test="payMethod != null">
            pay_method = #{payMethod},
        </if>
        <if test="note != null">note = #{note}, </if>
        update_time = now()
        WHERE trade_no = #{tradeNo}
        AND status = #{oriStatus}
    </update>

    <update id="updateRefundStatus" parameterType="com.chargebolt.hera.domain.sharding.PaymentDO">
        UPDATE payment
        SET
        status = #{status},
        <if test="refundAmount != null">
            refund_amount = #{refundAmount},
        </if>
        <if test="refundTime != null">
            refund_time = #{refundTime},
        </if>
        <if test="note != null">
            note = #{note},
        </if>
        update_time = now()
        WHERE trade_no = #{tradeNo}
        AND status = #{oriStatus}
    </update>

    <update id="reverseRefund" parameterType="com.chargebolt.hera.domain.sharding.PaymentDO">
        UPDATE payment
        SET status        = #{status},
            refund_amount = null,
            refund_time   = null,
            update_time=now()
        WHERE trade_no = #{tradeNo}
          AND status = #{oriStatus}
    </update>

    <update id="updateNotifyTag" parameterType="com.chargebolt.hera.domain.sharding.PaymentDO">
        UPDATE payment
        SET
        notify_tag = #{notifyTag},
        <if test="reqSystem != null">
            req_system = #{reqSystem},
        </if>
        update_time=now()
        WHERE trade_no = #{tradeNo}
    </update>

    <update id="updateNotifyFlag" parameterType="com.chargebolt.hera.domain.sharding.PaymentDO">
        UPDATE payment
        SET notify_flag = #{notifyFlag,jdbcType=TINYINT},
            update_time=now()
        WHERE trade_no = #{tradeNo}
    </update>

    <update id="updateRefundAmount">
        UPDATE payment
        SET
        refund_amount = #{item.refundAmount},
        <if test="item.refundTime != null">
            refund_time = #{item.refundTime},
        </if>
        update_time = now()
        WHERE trade_no = #{item.tradeNo}
        <choose>
            <when test="oldRefundAmount != null">
                AND refund_amount = #{oldRefundAmount}
            </when>
            <otherwise>
                AND refund_amount = null
            </otherwise>
        </choose>
    </update>

    <update id="updateRefundAmountByTradeNo">
        UPDATE payment
        SET
        refund_amount = #{refundAmount},
        update_time = now()
        WHERE trade_no = #{tradeNo}
    </update>

    <update id="updateNote">
        UPDATE payment
        SET note = #{note},
        update_time=now()
        WHERE trade_no = #{tradeNo}
    </update>

    <!-- 查询会员支付凭证 @云谷 2021/05/07-->
    <select id="selectByPayNo" resultType="com.chargebolt.hera.domain.sharding.PaymentDO">
        select *
        from payment
        where pay_no = #{payNo}
    </select>
</mapper>