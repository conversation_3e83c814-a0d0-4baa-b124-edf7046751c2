<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.hera.dao.sharding.PaymentOrderMapMapper">

  <!--该表的分表键为order_no，务必确保所有的查询条件带有该分表键，否则造成全表扫描影响系统性能-->
  <insert id="insert" keyProperty="id" parameterType="com.chargebolt.hera.domain.PaymentOrderMapPO">
    <if test="id == null">
      INSERT INTO payment_order_mapping(`order_no`,`trade_no`,`create_time`,`update_time`)
      VALUES (#{orderNo},#{tradeNo},now(),now())
    </if>
    <if test="id != null">
      INSERT INTO payment_order_mapping(`id`,`order_no`,`trade_no`,`create_time`,`update_time`)
      VALUES (#{id},#{orderNo},#{tradeNo},now(),now())
    </if>
  </insert>
  <select id="selectTradeNoByOrderNo" resultType="String">
    SELECT trade_no
    FROM payment_order_mapping
    WHERE order_no = #{orderNo}
  </select>
</mapper>