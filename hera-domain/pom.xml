<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <artifactId>chargebolt-hera</artifactId>
        <groupId>com.chargebolt.hera</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.chargebolt.hera</groupId>
    <artifactId>hera-domain</artifactId>
    <packaging>jar</packaging>
    <version>${hera.domain.version}</version>
    <name>hera-domain</name>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chargebolt</groupId>
            <artifactId>hera-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chargebolt</groupId>
            <artifactId>commons-eden</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>5.3.5.Final</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
    </dependencies>

</project>
