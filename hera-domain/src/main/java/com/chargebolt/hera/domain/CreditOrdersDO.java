package com.chargebolt.hera.domain;

import lombok.Data;

import java.util.Date;

@Data
public class CreditOrdersDO {
    private Long id;

    private Long userId;

    private Integer channel;

    private Integer bizType;

    private String orderNo;

    private String outOrderNo;

    private Integer orderAmount;

    private Integer rentAmount;

    private Integer compensationAmount;

    private Integer refundAmount;

    private Integer status;

    private Date loanTime;

    private Date returnTime;

    private Date payTime;

    private String failReason;

    private Date createTime;

    private Date updateTime;

    private Integer deleted;

    private String remark;

    private Integer oriStatus;

    private Boolean isReturned;

    private Integer priceChangeDegree;

    @Override
    public CreditOrdersDO clone(){
        try {
            return (CreditOrdersDO) super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return null;
    }

}