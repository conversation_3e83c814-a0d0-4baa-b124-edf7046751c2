package com.chargebolt.hera.domain;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class PayDataFetchTaskPO {

    private Long id;
    private Integer type;
    private Integer accountType;
    private Date fetchDate;
    private Date planTime;
    private Date startFetchTime;
    private Integer status;
    private Boolean locked;
    private String remark;
    private String failReason;
    private Date createTime;
    private Date updateTime;

    private Long gmtCreate;
    private Long gmtUpdate;

    private Boolean deleted;
    private Integer oriStatus;

    public PayDataFetchTaskPO(Integer type,Integer accountType,Date fetchDate){
        this.type = type;
        this.accountType = accountType;
        this.fetchDate = fetchDate;
    }

    public Boolean getLocked() {
        return locked == null ? false:locked;
    }

    public String getRemark() {
        return remark == null ? "":remark;
    }

    public String getFailReason() {
        return failReason == null ? "":failReason;
    }

    public Boolean getDeleted() {
        return deleted == null ? false:deleted;
    }
}