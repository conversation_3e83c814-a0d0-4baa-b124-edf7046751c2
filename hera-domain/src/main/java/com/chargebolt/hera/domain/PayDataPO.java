package com.chargebolt.hera.domain;

import lombok.Data;
import org.springframework.util.StringUtils;

import java.util.Date;

@Data
public class PayDataPO {

    private Long id;
    private Long planId;
    private Date periodDate;
    private Integer type;
    private String tradeNo;
    private String outPayNo;
    private Date billTime;
    private Integer payWay;
    private Integer channel;
    private String paymentMethod;
    private Integer payStatus;
    private String currency;
    private Integer totalAmount;
    private Integer refundAmount;
    private Integer channelFee;

    private String payerNo;
    private String payerAccountName;
    private String payeeNo;
    private String payeeAccountName;

    private String failReason;
    private Date createTime;
    private Date updateTime;

    private Long gmtCreate;
    private Long gmtUpdate;

    private Boolean deleted;
    private String remark;

    public String getRemark() {
        return StringUtils.isEmpty(remark) ? "" : remark;
    }

    public String getOutPayNo() {
        return StringUtils.isEmpty(outPayNo) ? "" : outPayNo;
    }

    public String getFailReason() {
        return StringUtils.isEmpty(failReason) ? "" : failReason;
    }

    public String getPaymentMethod() {
        return StringUtils.isEmpty(paymentMethod) ? "" : paymentMethod;
    }

    public Integer getRefundAmount() {
        return refundAmount == null ? 0:refundAmount;
    }

    public Integer getChannelFee() {
        return channelFee == null ? 0:channelFee;
    }

    public String getPayerNo() {
        return StringUtils.isEmpty(payerNo) ? "" : payerNo;
    }

    public String getPayerAccountName() {
        return StringUtils.isEmpty(payerAccountName) ? "" : payerAccountName;
    }

    public String getPayeeNo() {
        return StringUtils.isEmpty(payeeNo) ? "" : payeeNo;
    }

    public String getPayeeAccountName() {
        return StringUtils.isEmpty(payeeAccountName) ? "" : payeeAccountName;
    }
}