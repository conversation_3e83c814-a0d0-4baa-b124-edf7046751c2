package com.chargebolt.hera.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import so.dian.commons.eden.entity.BaseDO;

import java.util.Date;

/**
 * Generator生成 支付-订单映射DO
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@RequiredArgsConstructor(staticName = "of")
public class PaymentOrderMappingDO extends BaseDO {

    private Long id;

    @NonNull
    private String orderNo;

    @NonNull
    private String tradeNo;

    private Date createTime;

    private Date updateTime;

    private Integer status;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }

        PaymentOrderMappingDO that = (PaymentOrderMappingDO) o;

        return orderNo.equals(that.orderNo) && tradeNo.equals(that.tradeNo);
    }

    @Override
    public int hashCode() {
        int result = super.hashCode();
        result = 31 * result + orderNo.hashCode();
        result = 31 * result + tradeNo.hashCode();
        return result;
    }
}