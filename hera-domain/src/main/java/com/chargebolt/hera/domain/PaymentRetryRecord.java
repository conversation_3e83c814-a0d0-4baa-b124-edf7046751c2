/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.hera.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: PaymentRetryRecord.java, v 1.0 2024-04-30 2:12 PM Exp $
 */
@Data
public class PaymentRetryRecord implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202404121141228L;
    private Long id;
    private String tradeNo;
    private String payNo;
    /**
     * PaywayEnum值
     * @see com.chargebolt.hera.client.enums.PaywayEnum
     */
    private Integer payway;
    private Integer state;
    private String description;
    private Integer deleted;
    private Long gmtCreate;
    private Long gmtUpdate;
}