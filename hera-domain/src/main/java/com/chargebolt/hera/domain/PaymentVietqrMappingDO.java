package com.chargebolt.hera.domain;

import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import com.chargebolt.hera.client.enums.status.RefundStatus;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-12-26 14:22
 */
@Data
public class PaymentVietqrMappingDO implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202407190150833L;

    private Long id;
    private String tradeNo;
    private String transactionId;
    private String referenceNumber;
    private String bankAccount;
    private Integer deleted;
    private Long gmtCreate;
    private Long gmtUpdate;
}
