/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.hera.domain;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * refund_channel_fee_record
 *
 * <AUTHOR>
 * @version $Id: RefundChannelFeeRecord.java, v 0.1 2024-12-05 10:19:51 Exp $
 */
@Data
public class RefundChannelFeeRecord implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202412340102512L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 支付单号
     */
    private String payTradeNo;

    /**
     * 退款单号
     */
    private String refundNo;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 服务费金额，最小单位：分
     */
    private Long amount;

    /**
     * 来源 1.押金退款
     */
    private Integer sourceType;

    /**
     * 备注
     */
    private String note;

    /**
     * 1.成功 2.失败
     */
    private Integer state;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 代理商ID
     */
    private Long agentId;

    /**
     * 逻辑删除：0 未删除，1 已删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间戳
     */
    private Long gmtCreate;

    /**
     * 更新时间戳
     */
    private Long gmtUpdate;
}