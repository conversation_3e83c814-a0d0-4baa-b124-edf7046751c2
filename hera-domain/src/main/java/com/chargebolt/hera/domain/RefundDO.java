package com.chargebolt.hera.domain;

import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import com.chargebolt.hera.client.enums.status.RefundStatus;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-12-26 14:22
 */
@Data
public class RefundDO {

    private Long id;
    /**
     * 业务退款单号
     */
    private String orderNo;
    /**
     * 支付单号  对应payment表的 trade_no
     */
    private String tradeNo;
    /**
     * 退款流水号
     */
    private String refundNo;
    /**
     * 支付类型
     * @see com.chargebolt.hera.client.enums.PaywayEnum
     */
    private Integer refundType;
    /**
     * 业务类型
     * @see PaymentBizTypeEnum
     */
    private Integer bizType;
    /**
     * 三方退款单号
     */
    private String outTraceNo;
    /**
     * 退款金额，单位分
     */
    private Integer amount;
    /**
     * 币种
     */
    private String currency;
    /**
     * 退款时间
     */
    private Date refundTime;
    /**
     * 退款原因
     */
    private String reason;
    /**
     * 退款系统
     */
    private String system;
    /**
     * @see com.chargebolt.hera.client.enums.status.RefundStatus
     */
    private Integer status;
    /**
     * 失败原因描述
     */
    private String errorMsg;

    /**
     * 创建时间
     */
    private Date createTime = new Date();

    /**
     * 修改时间
     */
    private Date updateTime = new Date();

    public boolean isDone() {
        return RefundStatus.isDone(status);
    }
}
