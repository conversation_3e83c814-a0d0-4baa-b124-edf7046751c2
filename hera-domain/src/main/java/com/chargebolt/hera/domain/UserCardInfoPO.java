package com.chargebolt.hera.domain;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * Created by 希罗 on 2019-03-04
 */
@Data
@NoArgsConstructor
public class UserCardInfoPO {

    public UserCardInfoPO(Long userId){
        this.userId = userId;
    }

    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 卡号（后四位）
     */
    private String cardNo;

    /**
     * 卡id
     */
    private String cardId;

    private String cardCode;

    /**
     * 扣款优先级
     */
    private Integer priority;

    /**
     * 卡类型
     */
    private Integer cardType;

    /**
     * 卡绑定渠道
     */
    private Integer cardChannel;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 授权金额
     */
    private Integer authAmount;

    /**
     * 币种
     */
    private String currency;

    /**
     * 有效期
     */
    private String expiryDate;

    /**
     * 授权时间
     */
    private Date authTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 其他信息
     */
    private String extend;

    private Date createTime;

    private Date updateTime;

    private Integer deleted;

    private Integer oriStatus;

}
