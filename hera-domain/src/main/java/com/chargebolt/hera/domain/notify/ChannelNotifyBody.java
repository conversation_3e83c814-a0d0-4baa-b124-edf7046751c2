package com.chargebolt.hera.domain.notify;

import com.chargebolt.hera.client.enums.PayMethodEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Data
public class ChannelNotifyBody {

    private String refTradeNo;
    /**
     * 银行卡授权时的支付单号
     */
    private String refPayNo;
    private String currentTradeNo;
    /**
     * 与 refPayNo 一致
     */
    private String outerNo;
    private PaywayEnum payway;
    private PayStatus status;
    private Date date;
    private PayMethodEnum payMethod;
    /**
     * 支付通知扩展信息
     */
    private Map<String,Object> extend = new HashMap<>();

}
