/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package com.chargebolt.hera.domain.notify;

import com.chargebolt.hera.client.dto.pay.refund.req.RefundFeeRequest;
import lombok.Data;
import so.dian.commons.eden.constant.MqDelayLevelConst;

import java.io.Serializable;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: DelayRefundBody.java, v 1.0 2024-06-27 下午4:32 Exp $
 */
@Data
public class DelayRefundBody implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202406179163248L;
    /**
     * 支付交易订单号
     */
    private String tradeNo;
    /**
     * 退款原因
     */
    private String refundReason;
    /**
     * 退款金额
     */
    private Long refundAmount;
    /**
     * 退款发起系统
     */
    private String system;

    private Integer delayLevel= MqDelayLevelConst.DELAY_1M;
    /**
     * 退款服务费
     */
    private List<RefundFeeRequest> refundFeeRequestList;
}