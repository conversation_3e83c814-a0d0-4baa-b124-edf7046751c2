package com.chargebolt.hera.domain.notify;

import com.chargebolt.hera.client.enums.ChannelEnum;
import lombok.Data;

import java.util.Date;

@Data
public class RefundNotifyBody {

    private ChannelEnum channel;
    private String merchantTradeNo;
    private String merchantRefundNo;
    private String outerRefundNo;
    private String outerPayNo;
    private Date date;
    /**
     * 状态，true：退款成功，false：退款失败
     */
    private Boolean refundStatus;
}
