package com.chargebolt.hera.domain.sharding;

import com.alibaba.fastjson.JSONObject;
import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 17/6/3
 */

@Data
public class PaymentDO {

    private Long id;
    /**
     * 业务订单ID
     */
    private Long orderId;
    /**
     * 业务订单编号
     */
    private String orderNo;
    /**
     * 支付订单号
     */
    private String tradeNo;
    /**
     * 三方支付单号
     */
    private String payNo;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 业务类型
     *
     * @see PaymentBizTypeEnum
     */
    private Integer bizType;
    /**
     * 支付方式
     *
     * @see com.chargebolt.hera.client.enums.PaywayEnum
     */
    private Integer payType;
    /**
     * 支付状态
     *
     * @see com.chargebolt.hera.client.enums.status.PayStatus
     */
    private Integer status;

    /**
     * 支付状态（修改前的状态）
     */
    private Integer oriStatus;
    /**
     * 支付金额，单位分
     */
    private Integer payAmount;
    /**
     * 退款金额
     */
    private Integer refundAmount;
    /**
     * 支付时间
     */
    private Date payTime;
    /**
     * 退款时间
     */
    private Date refundTime;

    /**
     * 创建时间
     */
    private Date createTime = new Date();
    /**
     * 更新时间
     */
    private Date updateTime = new Date();
    /**
     * 设备编号
     */
    private String deviceNo;
    /**
     * 业务id
     */
    private Long bizId;
    /**
     * 业务订单编号
     */
    private Long cooperatorId;
    /**
     * 延迟时长
     */
    private Long delayTime;
    /**
     * 通知状态：0初始化 1待通知 2已通知 3关闭通知
     */
    private Integer notifyFlag;
    /**
     * 通知tag
     */
    private String notifyTag;
    /**
     * 币种，默认cny
     */
    private String currency;
    private Integer payBizType = 0;
    /**
     * 请求系统名
     */
    private String reqSystem;
    /**
     * 失败描述
     */
    private String failReason;

    private JSONObject noteObject = new JSONObject();

    public String getNote() {
        return noteObject.toJSONString();
    }

    public void setNote(String note) {
        if (note == null) {
            noteObject = new JSONObject();
        } else {
            if (note.startsWith("{")) {
                noteObject = JSONObject.parseObject(note);
            } else {
                noteObject = new JSONObject();
                noteObject.put("note", note);
            }
        }
    }

    /**
     * 支付方式ID
     */
    private Integer payMethod;
}
