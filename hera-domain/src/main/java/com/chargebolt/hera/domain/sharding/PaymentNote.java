package com.chargebolt.hera.domain.sharding;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PaymentNote {

    /**
     * 请款类型
     * @see com.chargebolt.hera.client.enums.CaptureModeEnum
     */
    private Integer captureMode;

    /**
     * 银行卡token，用于记录用户在antom存储的银行卡信息，代扣时上送
     */
    private String cardToken;

    /**
     * 银行卡组织为识别交易而分配的交易ID
     */
    private String networkTransactionId;

    /**
     * 支付金额，单位分
     */
    private Integer authPayAmount;

    /**
     * 授权订单号
     */
    private String authTradeNo;

    /**
     * 请款三方单号
     */
    private String payNo;

    /**
     * 请款业务单号
     */
    private String captureTradeNo;


}
