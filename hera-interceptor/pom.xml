<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <groupId>com.chargebolt.hera</groupId>
  <version>${hera.interceptor.version}</version>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>hera-interceptor</artifactId>
  <parent>
    <groupId>com.chargebolt.hera</groupId>
    <artifactId>chargebolt-hera</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <dependencies>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.8.5</version>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>fluent-hc</artifactId>
      <version>4.5.14</version>
    </dependency>
    <dependency>
      <groupId>com.chargebolt</groupId>
      <artifactId>hera-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.chargebolt.hera</groupId>
      <artifactId>hera-domain</artifactId>
    </dependency>

    <dependency>
      <groupId>so.dian.mofa3</groupId>
      <artifactId>common-lang</artifactId>
    </dependency>
  </dependencies>

<!--  <build>-->
<!--    <plugins>-->
<!--      &lt;!&ndash; 要将源码上传到maven仓库 &ndash;&gt;-->
<!--      <plugin>-->
<!--        <groupId>org.apache.maven.plugins</groupId>-->
<!--        <artifactId>maven-source-plugin</artifactId>-->
<!--        <version>3.0.1</version>-->
<!--        <configuration>-->
<!--          <attach>true</attach>-->
<!--        </configuration>-->
<!--        <executions>-->
<!--          <execution>-->
<!--            <phase>compile</phase>-->
<!--            <goals>-->
<!--              <goal>jar</goal>-->
<!--            </goals>-->
<!--          </execution>-->
<!--        </executions>-->
<!--      </plugin>-->
<!--      <plugin>-->
<!--        <groupId>org.apache.maven.plugins</groupId>-->
<!--        <artifactId>maven-compiler-plugin</artifactId>-->
<!--        <configuration>-->
<!--          <source>1.8</source>-->
<!--          <target>1.8</target>-->
<!--        </configuration>-->
<!--      </plugin>-->
<!--    </plugins>-->
<!--  </build>-->
<!--  <distributionManagement>-->
<!--    <snapshotRepository>-->
<!--      <id>snapshots</id>-->
<!--      <name>User Porject Snapshot</name>-->
<!--      <url>http://maven.dian.so/nexus/content/repositories/snapshots/</url>-->
<!--      <uniqueVersion>true</uniqueVersion>-->
<!--    </snapshotRepository>-->
<!--    <repository>-->
<!--      <id>releases</id>-->
<!--      <name>User Porject Release</name>-->
<!--      <url>http://maven.dian.so/nexus/content/repositories/releases/</url>-->
<!--    </repository>-->
    <!--<snapshotRepository>-->
      <!--<id>snapshots</id>-->
      <!--<name>User Porject Snapshot</name>-->
      <!--<url>http://maven.xpowerplus.net/nexus/content/repositories/snapshots/</url>-->
      <!--<uniqueVersion>true</uniqueVersion>-->
    <!--</snapshotRepository>-->
    <!--<repository>-->
      <!--<id>releases</id>-->
      <!--<name>User Porject Release</name>-->
      <!--<url>http://maven.xpowerplus.net/nexus/content/repositories/releases/</url>-->
    <!--</repository>-->
<!--  </distributionManagement>-->


</project>