package so.dian.hera.interceptor;

import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.checkout.req.*;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.*;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import so.dian.hera.interceptor.dto.req.PaymentQueryRequest;
import so.dian.hera.interceptor.dto.rsp.PaymentQueryResultDTO;

import java.util.List;

public interface CheckoutPayProcessor extends Processor{

    PrepayCreateResultDTO doPrePay(PrepayCreateRequest prePayReq, PaymentDO paymentDO);

    PreAuthCancelResultDTO doCancel(PaymentDO paymentDO);

    PreAuthCaptureResultDTO doCapture(PreAuthCaptureRequest preauthCaptureRequest, String captureId, PaymentDO capturePaymentDO);

//    CheckoutRefundResultDTO doRefund(CheckoutRefundRequest request);

    /**
     * 使用{@link QueryPayProcessor}
     *
     * @param request
     * @return
     */
//    @Deprecated
//    PaymentQueryResultDTO paymentQuery(PaymentQueryRequest request);

//    CaptureQueryResultDTO captureQuery(CaptureQueryRequest captureQueryRequest);

    List<PaywayEnum> getPayway();
}
