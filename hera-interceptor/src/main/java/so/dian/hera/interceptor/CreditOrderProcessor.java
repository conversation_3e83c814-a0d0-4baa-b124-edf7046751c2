package so.dian.hera.interceptor;


import com.chargebolt.hera.client.dto.credit.req.*;
import com.chargebolt.hera.client.dto.credit.rsp.*;

public interface CreditOrderProcessor extends Processor{

    CreditOrderCreateRsp createCreditOrder(CreditOrderCreateReq req);

    CreditOrderCancelRsp cancelCreditOrder(CreditOrderCancelReq req);

    CreditOrderCompleteRsp completeCreditOrder(CreditOrderCompleteReq req);

    CreditOrderModifyRsp modifyCreditOrder(CreditOrderModifyReq req);

    CreditOrderJumpRsp jumpCreditOrder(CreditOrderJumpReq req);

    CreditOrderQueryRsp queryCreditOrder(CreditOrderQueryReq req);

}
