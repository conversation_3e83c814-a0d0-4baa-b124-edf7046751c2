package so.dian.hera.interceptor;

import com.chargebolt.hera.client.dto.pay.req.*;
import com.chargebolt.hera.client.dto.pay.rsp.*;
import com.chargebolt.hera.client.enums.PaywayEnum;

/**
 * <AUTHOR>
 */
public interface PayOrderProcessor extends Processor{

    /**
     * 支付预下单
     * @param req
     * @return
     */
    OrderPayRsp payOrder(OrderPayReq req);

    /**
     * 支付单关闭
     * @param req
     * @return
     */
    OrderCloseRsp closeOrder(OrderCloseReq req);

    /**
     * 退款申请
     * @param req
     * @return
     */
    OrderRefundRsp refundOrder(OrderRefundReq req);

    /**
     * 支付单查询
     * @param req
     * @return
     */
    OrderPayQueryRsp queryPayOrder(OrderPayQueryReq req);

    /**
     * 退款单查询
     * @param req
     * @return
     */
    OrderRefundQueryRsp queryRefundOrder(OrderRefundQueryReq req);

}
