/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.interceptor;

import com.chargebolt.hera.client.enums.status.RetryStatus;
import com.chargebolt.hera.domain.PaymentRetryRecord;
import com.chargebolt.hera.domain.sharding.PaymentDO;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: PaymentCaptureRetryProcessor.java, v 1.0 2024-04-29 6:45 PM Exp $
 */
public interface PaymentCaptureRetryProcessor extends Processor{
    /**
     * 预授权、一键支付重试
     *
     * @param retryRecord
     * @return {@link RetryStatus} 重试结果 1.处理中 2.成功 3.失败
     */
    RetryStatus handleCaptureRetry(PaymentRetryRecord retryRecord, PaymentDO paymentDO);
}