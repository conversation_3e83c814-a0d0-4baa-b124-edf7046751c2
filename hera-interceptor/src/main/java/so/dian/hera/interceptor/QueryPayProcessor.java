/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.interceptor;

import so.dian.hera.interceptor.dto.req.PayQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;

/**
 * 支付结果查询
 *
 * <AUTHOR>
 * @version: QueryPayProcessor.java, v 1.0 2024-04-16 4:13 PM Exp $
 */
public interface QueryPayProcessor extends Processor {
    /**
     * 查询支付结果
     *
     * @param request
     * @return
     */
    PayQueryResultDTO orderPayQuery(PayQueryResultRequest request);
}