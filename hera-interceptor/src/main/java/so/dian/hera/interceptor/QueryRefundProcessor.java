/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.interceptor;

import so.dian.hera.interceptor.dto.req.RefundQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.RefundQueryResultDTO;

/**
 * 退款结果查询
 *
 * <AUTHOR>
 * @version: QueryRefundProcessor.java, v 1.0 2024-04-16 4:13 PM Exp $
 */
public interface QueryRefundProcessor extends Processor {
    /**
     * 查询退款结果
     *
     * @param queryResultRequest
     * @return
     */
    RefundQueryResultDTO orderRefundQuery(RefundQueryResultRequest queryResultRequest);
}