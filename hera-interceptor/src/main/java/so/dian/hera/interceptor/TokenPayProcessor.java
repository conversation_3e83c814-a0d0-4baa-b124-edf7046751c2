package so.dian.hera.interceptor;

import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.apmtoken.req.ApmTokenPayRequest;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenApplyResultDTO;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenCheckResultDTO;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenPayResultDTO;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenPrepareResultDTO;
import com.chargebolt.hera.domain.ApmTokenDO;
import com.chargebolt.hera.domain.sharding.PaymentDO;

public interface TokenPayProcessor extends Processor{

    ApmTokenPrepareResultDTO prepare(PrepayCreateRequest request, PaymentDO paymentDO);
    ApmTokenApplyResultDTO apply(PrepayCreateRequest request, PaymentDO paymentDO);
    ApmTokenPayResultDTO pay(ApmTokenPayRequest request, ApmTokenDO apmTokenDO, PaymentDO paymentDO);

    ApmTokenCheckResultDTO check(ApmTokenDO apmTokenDO);
}
