package so.dian.hera.interceptor.basic;

import com.chargebolt.hera.client.dto.pay.basic.req.SdkPayReq;
import com.chargebolt.hera.client.dto.pay.basic.rsp.SdkPayRsp;
import com.chargebolt.hera.domain.sharding.PaymentDO;

import java.util.Map;
import java.util.function.BiConsumer;

/**
 * 具体支付渠道
 * <AUTHOR>
 * @date 2019-09-23 10:58
 */
public interface PayChannel<Q, R, E> {

    /**
     * 包装调用参数
     * @param sdkPayReq
     * @param paymentDO
     * @return
     */
    Q toPayment(SdkPayReq sdkPayReq, PaymentDO paymentDO);

    /**
     * 执行支付调用
     * @param req
     * @return
     */
    String doPay(Q req);

    /**
     * 转换返回信息
     * @param res
     * @return
     */
    R toResponse(String res);

    /**
     * 转换错误信息
     * @param res
     * @return
     */
    E toError(String res, R response);

    /**
     * 检测返回信息是否非法
     * @param resDTO
     * @return
     */
    boolean isInvalid(R resDTO);

    /**
     * 失败调用响应
     * @param errDTO
     * @return
     */
    SdkPayRsp failed(Q payment, E errDTO);

    /**
     * 成功调用响应
     * @param resDTO
     * @return
     */
    SdkPayRsp success(Q payment, R resDTO);

    default <V> void mapperLowerKey(Map<String, V> map, BiConsumer<String, ? super V> action) {
        for (Map.Entry<String, V> entry : map.entrySet()) {
            String k = entry.getKey().toLowerCase();
            V v = entry.getValue();
            action.accept(k, v);
        }
    }

    /**
     * 支付调用逻辑
     * @param sdkPayReq
     * @param paymentDO
     * @return
     */
    default SdkPayRsp pay(SdkPayReq sdkPayReq, PaymentDO paymentDO) {
        // 转换请求
        Q payment = toPayment(sdkPayReq, paymentDO);
        // 执行请求
        String res = doPay(payment);
        // 转换响应
        R response = toResponse(res);

        SdkPayRsp payRsp;
        if (isInvalid(response)) {
            payRsp = failed(payment, toError(res, response));
        } else {
            payRsp = success(payment, response);
        }
        return payRsp;
    }
}
