package so.dian.hera.interceptor.basic;

import com.chargebolt.hera.client.enums.status.RefundStatus;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-01-10 15:39
 */
@Data
public class RefundResult {

    private RefundStatus status;

    private String code;

    private String message;

    private Date refundTime;

    private RefundResult(RefundStatus status) {
        this.status = status;
    }

    public static RefundResult success() {
        return new RefundResult(RefundStatus.REFUNDED);
    }

    public static RefundResult process() {
        return new RefundResult(RefundStatus.REFUNDING);
    }

    public static RefundResult failed() {
        return new RefundResult(RefundStatus.FAIL);
    }

    public boolean isSuccess() {
        return status == RefundStatus.REFUNDED;
    }
}
