/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.interceptor.dto.req;
import lombok.Data;
import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: PayQueryResultRequest.java, v 1.0 2024-04-17 10:43 AM Exp $
 */
@Data
public class PayQueryResultRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202404108104404L;
    private String tradeNo;
    private String payNo;


}