package so.dian.hera.interceptor.dto.req;

import com.chargebolt.hera.client.dto.pay.CommonReq;
import lombok.Data;

@Data
public class ProcessorRefundRequest extends CommonReq {

    private String reason;
    private String tradeNo;
    private String payNo;
    private String refundNo;
    private String currency;
    private Integer payOriginalAmount;
    private Integer refundAmount;

    /**
     * ===== vietqr退款需要的参数 start =====
     */
    private String bankAccount;
    private String referenceNumber;
    /**
     * ===== vietqr退款需要的参数 end =====
     */
    /**
     * antom银行卡退款需要的参数
     */
    private Integer payWay;

    /**
     * 支付方式ID
     */
    private Integer payMethod;
}
