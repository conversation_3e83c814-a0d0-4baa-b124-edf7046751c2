/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.interceptor.dto.req;

import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: RefundQueryResultRequest.java, v 1.0 2024-04-17 10:45 AM Exp $
 */
@Data
public class RefundQueryResultRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202404108104547L;
    private String refundNo;
    /**
     * 正向交易单号
     */
    private String tradeNo;

    /**
     * ===== vietqr退款需要的参数 start =====
     */
    private String bankAccount;
    private String referenceNumber;
    /**
     * 三方退款单号
     */
    private String outTraceNo;

    /**
     * 三方支付单号
     */
    private String outPayNo;

    /**
     * 支付方式ID
     */
    private Integer payMethod;
}