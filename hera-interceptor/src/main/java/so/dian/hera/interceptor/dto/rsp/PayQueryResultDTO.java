/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.interceptor.dto.rsp;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: PayQueryResultDTO.java, v 1.0 2024-04-16 6:13 PM Exp $
 */
@Data
public class PayQueryResultDTO implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202404107181405L;

    private String tradeNo;
    private String payNo;
    /**
     * 订单状态
     * 三方结果需要转换为订单状态
     * {@link com.chargebolt.hera.client.enums.status.PayStatus}
     */
    private Integer status;
    /**
     * 交易成功时间
     */
    private Date payTime;
    /**
     * 失败信息
     */
    private String responseMsg;
    /**
     * 三方交易支付订单号
     * 240606 zalo mini退款时需要
     * 240730 zalopay 支付订单号
     */
    private String thirdPayTradeNo;
}