package so.dian.hera.interceptor.utils;

import com.alibaba.fastjson.JSON;

public class ChannelExtraDataResolver {



    public static <T> T dataAnalysis(Object payData, Class<T> tClass){
        String json = JsonUtil.toJson(payData);
        return JsonUtil.toObject(json,tClass);
    }

    public static <T> T payDataAnalysis(Object payData, Class<T> tClass){
        String json = JSON.toJSONString(payData);
        return JSON.parseObject(json,tClass);
    }
}
