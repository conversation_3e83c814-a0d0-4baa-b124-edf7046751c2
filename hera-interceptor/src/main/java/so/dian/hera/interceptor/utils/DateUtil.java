package so.dian.hera.interceptor.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-01-13 11:04
 */
public class DateUtil {

    public static final String DEFAULT_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String COMPACT_PATTERN = "yyyyMMddHHmmss";
    public static final String TIMESTAMP_PATTERN = "yyyy-MM-dd'T'HH:mm:ss+mm:ss";
    public static final String TIMESTAMP_PATTERN_01 = "yyyy-MM-dd'T'HH:mm:ss-mm:ss";
    public static final DateTimeFormatter TIMESTAMP_PATTERN_02 = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXXX");


    public static final String DEFAULT_PATTERN_YN = "yyyy-MM-dd HH:mm:ss +0700";


    private static ThreadLocal<SimpleDateFormat> dateFormatThreadLocal = ThreadLocal.withInitial(
            () -> new SimpleDateFormat(COMPACT_PATTERN));

    private static ThreadLocal<SimpleDateFormat> simpleDateFormatThreadLocal = ThreadLocal.withInitial(
            () -> new SimpleDateFormat(TIMESTAMP_PATTERN));

    public static Date parse(String str, String pattern) {
        SimpleDateFormat dateFormat = getFormat(pattern);

        Date date;
        try {
            date = dateFormat.parse(str);
        } catch (ParseException e) {
            e.printStackTrace();
            date = null;
        }
        return date;
    }

    private static SimpleDateFormat getFormat(String pattern) {
        SimpleDateFormat dateFormat = dateFormatThreadLocal.get();
        if (!dateFormat.toPattern().equals(pattern)) {
            dateFormat = new SimpleDateFormat(pattern);
            dateFormatThreadLocal.set(dateFormat);
        }

        return dateFormat;
    }

    public static Date parseDate(String str, String pattern) {
        SimpleDateFormat dateFormat = getDateFormat(pattern);

        Date date;
        try {
            date = dateFormat.parse(str);
        } catch (ParseException e) {
            e.printStackTrace();
            date = null;
        }
        return date;
    }

    private static SimpleDateFormat getDateFormat(String pattern) {
        SimpleDateFormat dateFormat = simpleDateFormatThreadLocal.get();
        if (!dateFormat.toPattern().equals(pattern)) {
            dateFormat = new SimpleDateFormat(pattern);
            simpleDateFormatThreadLocal.set(dateFormat);
        }

        return dateFormat;
    }

    /**
     * 当前时间增加 minute 分钟
     * @param minute
     * @return
     */
    public static Date addMinute(int minute){
        Calendar nowTime = Calendar.getInstance();
        nowTime.add(Calendar.MINUTE, minute);
        return  nowTime.getTime();
    }

    public static Date transferDate(String dateTime){
        // 将字符串解析为 ZonedDateTime 对象
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(dateTime, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
        // 将时间转换为 UTC 时区
        ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneOffset.UTC);
        // 按照所需格式输出转换后的时间
        String formattedDateTime = utcDateTime.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_PATTERN));
        return java.sql.Date.from(utcDateTime.toInstant());
    }

    public static Date transferDateV2(String dateTime){
        // 将字符串解析为 ZonedDateTime 对象
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(dateTime,TIMESTAMP_PATTERN_02);
        // 将时间转换为 UTC 时区
        ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneOffset.UTC);
        // 按照所需格式输出转换后的时间
        String formattedDateTime = utcDateTime.format(DateTimeFormatter.ofPattern(DateUtil.DEFAULT_PATTERN));
        return java.sql.Date.from(utcDateTime.toInstant());
    }

    public static String getFormatDate(Date date,String pattern){
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
        return dateFormat.format(date);
    }

}
