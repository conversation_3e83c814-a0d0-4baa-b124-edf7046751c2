package so.dian.hera.interceptor.utils;

import org.apache.http.NameValuePair;
import org.apache.http.client.fluent.Request;
import org.apache.http.client.fluent.Response;
import org.apache.http.entity.ContentType;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-11-12 15:06
 */
public class HttpUtil {

    public static int DEFAULT_TIMEOUT = 8000;

    public static String toParam(Object obj) {
        Map<String, String> data = JsonUtil.toMap(obj);
        return toStr(data);
    }

    public static NameValuePair[] toPairs(Object obj) {
        Map<String, String> data = JsonUtil.toMap(obj);

        NameValuePair[] params = new NameValuePair[data.size()];
        int index = 0;
        for (Map.Entry<String, String> entry : data.entrySet()) {
            params[index++] = new BasicNameValuePair(entry.getKey(), entry.getValue());
        }

        return params;
    }

    public static String toStr(Map<String, String> data) {
        StringBuilder builder = new StringBuilder(data.size() * 20);

        for (Map.Entry<String, String> entry : data.entrySet()) {
            builder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
        }
        builder.deleteCharAt(builder.length() - 1);
        return builder.toString();
    }

    public static String post(String url, String body) throws IOException {
        return post(url, body, DEFAULT_TIMEOUT);
    }

    public static String post(String url, String body, int timeout) throws IOException {
        Response response = Request.Post(url)
                .bodyString(body, ContentType.APPLICATION_JSON)
                .connectTimeout(timeout)
                .socketTimeout(timeout)
                .execute();
        return EntityUtils.toString(response.returnResponse().getEntity(),"utf-8");
    }

    public static String post(String url, ContentType contentType, NameValuePair... params) throws IOException {
        Response response = Request.Post(url)
                .bodyForm(Arrays.asList(params), StandardCharsets.UTF_8)
                .addHeader("Content-Type", contentType.getMimeType())
                .connectTimeout(DEFAULT_TIMEOUT)
                .socketTimeout(DEFAULT_TIMEOUT)
                .execute();
        return EntityUtils.toString(response.returnResponse().getEntity(),"utf-8");
    }
}
