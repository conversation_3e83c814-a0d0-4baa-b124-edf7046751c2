package so.dian.hera.interceptor.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2019-10-14 14:30
 */
public class JsonUtil {

    private static Gson GSON = new GsonBuilder().create();

    public static String toJson(Object obj){
        return GSON.toJson(obj);
    }

    public static <T> T toObject(String json, Class<T> tClass){
        return GSON.fromJson(json,tClass);
    }

    public static Map<String, String> toMap(Object obj){
        JsonObject object = GSON.toJsonTree(obj).getAsJsonObject();

        Map<String, String> data = new TreeMap<>();
        for (Map.Entry<String, JsonElement> entry : object.entrySet()) {
            String value = getValue(entry.getValue());
            if (value == null) {
                continue;
            }
            data.put(entry.getKey(), value);
        }

        return data;
    }

    private static String getValue(JsonElement element) {
        if (element.isJsonNull()) {
            return null;
        }
        return element.getAsString();
    }
}
