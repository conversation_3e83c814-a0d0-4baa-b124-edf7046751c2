package so.dian.hera.interceptor.utils;

import javax.xml.bind.DatatypeConverter;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 * @date 2019-11-12 14:31
 */
public class MD5Util {

    private static ThreadLocal<MessageDigest> digestThreadLocal = ThreadLocal.withInitial(() -> {
        MessageDigest md5;
        try {
            md5 = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
        return md5;
    });

    public static MessageDigest getInstance() {
        return MD5Util.digestThreadLocal.get();
    }

    public static String encode(byte[] src) {
        MessageDigest md5 = MD5Util.getInstance();

        byte[] md5Bytes = md5.digest(src);
        md5.reset();
        return DatatypeConverter.printHexBinary(md5Bytes);
    }

    public static String encode(String src) {
        return encode(src.getBytes(StandardCharsets.UTF_8));
    }

    public static String encode(String src, Charset charset) {
        return encode(src.getBytes(charset));
    }

    /**
     * 将指定byte数组转换成16进制字符串
     * @param b
     * @return
     */
    private static String byteToHexString(byte[] b) {
        StringBuilder hexString = new StringBuilder();
        for (int i = 0; i < b.length; i++) {
            String hex = Integer.toHexString(b[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
}
