package so.dian.hera.interceptor.utils;


import so.dian.mofa3.lang.money.MultiCurrencyMoney;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @date 2020-03-04 10:27
 */
public class NumberUtil {

    public static double fen2yuan(long num, String currencyCode) {
        MultiCurrencyMoney money= new MultiCurrencyMoney(0, currencyCode);
        money.setCent(num);
        return money.getAmount().doubleValue();
    }
}
