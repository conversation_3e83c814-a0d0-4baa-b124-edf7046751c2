package so.dian.hera.interceptor.utils;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @date 2020-03-11 11:03
 */
public class ObjectUtil {

    public static <T> T inject(String str, T obj) {
        String[] values = str.split("&");
        Class<?> type = obj.getClass();
        for (String value : values) {
            String[] item = value.split("=");
            if (item.length != 2) {
                continue;
            }

            try {
                Field field = type.getDeclaredField(item[0]);
                field.setAccessible(true);
                field.set(obj, item[1]);
            } catch (Exception e) {
                continue;
            }
        }

        return obj;
    }
}
