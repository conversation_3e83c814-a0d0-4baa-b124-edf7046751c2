package so.dian.hera.interceptor.utils;

import java.nio.ByteBuffer;

/**
 * <AUTHOR>
 * @date 2019-11-04 11:01
 */
public class SecurityUtil {

    private static volatile ByteBuffer SALT = getBuffer("ChargeboltTest".getBytes());

    public static String get(String src) {
        byte[] bytes = src.getBytes();

        String sec = MD5Util.encode(bytes).toUpperCase();

        ByteBuffer input = newBuffer(sec, bytes);

        return MD5Util.encode(input.array()).toUpperCase();
    }

    private static ByteBuffer getBuffer(byte[] src) {
        ByteBuffer buffer = ByteBuffer.allocate(src.length + 2);
        buffer.put(src);
        buffer.flip();
        return buffer;
    }

    private static ByteBuffer newBuffer(String sec, byte[] src) {
        int length = sec.length();

        ByteBuffer buffer = ByteBuffer.allocate(length + SALT.capacity());
        int mid = length / 2;

        buffer.put(sec.getBytes(), 0, mid);
        buffer.put(SALT);
        buffer.put(src[src.length - 2]);
        buffer.put(src[src.length - 1]);
        buffer.put(sec.getBytes(), mid, length - mid);
        buffer.flip();
        return buffer;
    }

    public static void setSalt(String salt) {
        if (salt == null) {
            return;
        }
        SecurityUtil.SALT = getBuffer(salt.getBytes());
    }
}
