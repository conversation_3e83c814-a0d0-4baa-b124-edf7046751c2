package so.dian.hera.interceptor.utils;

/**
 * <AUTHOR>
 * @date 2019-11-12 14:36
 */
public class SignUtil {

    public static String get(Object obj) {
        String str = HttpUtil.toParam(obj);
        String sign = MD5Util.encode(str);
        return sign.toUpperCase();
    }

    public static String get(Object obj, String signKey) {
        String str = HttpUtil.toParam(obj);
        str = str + "&key=" + signKey;
        String sign = MD5Util.encode(str);
        return sign.toUpperCase();
    }
}
