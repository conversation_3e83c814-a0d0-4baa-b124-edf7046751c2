<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <artifactId>chargebolt-hera</artifactId>
        <groupId>com.chargebolt.hera</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.chargebolt.hera</groupId>
    <artifactId>hera-web</artifactId>
    <packaging>jar</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <name>hera-web</name>
    <url>http://maven.apache.org</url>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <!-- 请求重试-->
        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
            <version>1.3.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>${springboot.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <version>${springboot.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <version>${springboot.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context-support</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
            <version>${springboot.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
            <version>${springboot.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
            <version>${springboot.version}</version>
        </dependency>
        <!--    springcloud    -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-hystrix</artifactId>
            <version>${springcloud.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-netflix-core</artifactId>
            <version>${springcloud.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
            <version>${springcloud.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>${springcloud.version}</version>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-core</artifactId>
            <version>10.12</version>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-jackson</artifactId>
            <version>9.5.0</version>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-ribbon</artifactId>
            <version>9.5.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>rxjava</artifactId>
                    <groupId>io.reactivex</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>1.26</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>

        <dependency>
            <groupId>com.chargebolt</groupId>
            <artifactId>kronos-client</artifactId>
            <version>1.0.24-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.chargebolt</groupId>
                    <artifactId>commons-eden</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- DB -->
        <!-- druid -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.1.10</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- mybatis -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>2.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>8.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.2.0</version>
        </dependency>
        <dependency>
            <groupId>io.shardingsphere</groupId>
            <artifactId>sharding-core</artifactId>
            <version>3.0.0.M4</version>
        </dependency>
        <dependency>
            <groupId>io.shardingsphere</groupId>
            <artifactId>sharding-jdbc-core</artifactId>
            <version>3.0.0.M4</version>
        </dependency>
        <dependency>
            <groupId>io.shardingsphere</groupId>
            <artifactId>sharding-jdbc-spring-namespace</artifactId>
            <version>3.0.0.M4</version>
        </dependency>
        <dependency>
            <groupId>commons-dbcp</groupId>
            <artifactId>commons-dbcp</artifactId>
            <version>1.4</version>
        </dependency>



        <dependency>
            <groupId>com.chargebolt</groupId>
            <artifactId>commons-eden</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>5.3.5.Final</version>
        </dependency>
        <dependency>
            <groupId>com.chargebolt.hera</groupId>
            <artifactId>hera-interceptor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chargebolt.hera</groupId>
            <artifactId>platform-wechat</artifactId>
            <version>${platform.wechat.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.chargebolt.hera</groupId>
            <artifactId>platform-wooshpay</artifactId>
            <version>${platform.wooshpay.version}</version>
        </dependency>
        <dependency>
            <groupId>com.chargebolt.hera</groupId>
            <artifactId>platform-pingpong</artifactId>
            <version>${platform.pingpong.version}</version>
        </dependency>
        <dependency>
            <groupId>com.chargebolt.hera</groupId>
            <artifactId>platform-zalopay</artifactId>
            <version>${platform.zalopay.version}</version>
        </dependency>

        <dependency>
            <groupId>com.chargebolt.hera</groupId>
            <artifactId>platform-zalomini</artifactId>
            <version>${platform.zalomini.version}</version>
        </dependency>

        <dependency>
            <groupId>com.chargebolt.hera</groupId>
            <artifactId>platform-vietqr</artifactId>
            <version>${platform.vietqr.version}</version>
        </dependency>

        <dependency>
            <groupId>com.chargebolt.hera</groupId>
            <artifactId>platform-antom</artifactId>
            <version>${platform.antom.version}</version>
        </dependency>

        <dependency>
            <groupId>com.chargebolt.hera</groupId>
            <artifactId>platform-airwallex</artifactId>
            <version>${platform.airwallex.version}</version>
        </dependency>
        <dependency>
            <groupId>com.chargebolt.hera</groupId>
            <artifactId>hera-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chargebolt</groupId>
            <artifactId>hera-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chargebolt.hera</groupId>
            <artifactId>platform-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>fluent-hc</artifactId>
            <version>4.5.14</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.30</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <version>${springboot.version}</version>
            <scope>test</scope>
        </dependency>
        <!-- swagger2 -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>${swagger.version}</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>${swagger.version}</version>
        </dependency>
        <!-- swagger2 -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
            <version>2.5</version>
        </dependency>
        <dependency>
            <groupId>so.dian.mofa3</groupId>
            <artifactId>common-client</artifactId>
        </dependency>
        <dependency>
            <groupId>so.dian.mofa3</groupId>
            <artifactId>common-template</artifactId>
        </dependency>
        <dependency>
            <groupId>so.dian.mofa3</groupId>
            <artifactId>common-lang</artifactId>
        </dependency>
        <!-- jwt -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>0.11.5</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>0.11.5</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId> <!-- 或者 jjwt-gson 如果你使用Gson -->
            <version>0.11.5</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>so.dian.mofa3</groupId>-->
        <!--            <artifactId>cache-starter</artifactId>-->
        <!--            <version>${mofa.version}</version>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <artifactId>netty-all</artifactId>-->
        <!--                    <groupId>io.netty</groupId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <artifactId>netty-handler</artifactId>-->
        <!--                    <groupId>io.netty</groupId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <artifactId>netty-transport</artifactId>-->
        <!--                    <groupId>io.netty</groupId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <artifactId>netty-common</artifactId>-->
        <!--                    <groupId>io.netty</groupId>-->
        <!--                </exclusion>-->
        <!--                <exclusion>-->
        <!--                    <artifactId>netty-resolver</artifactId>-->
        <!--                    <groupId>io.netty</groupId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>xml-apis</groupId>
            <artifactId>xml-apis</artifactId>
            <version>1.4.01</version>
        </dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chargebolt.hera</groupId>
            <artifactId>platform-midtrans</artifactId>
            <version>${platform.midtrans.version}</version>
        </dependency>
        <dependency>
            <groupId>com.chargebolt</groupId>
            <artifactId>pheidi-client-starter</artifactId>
            <version>0.0.4.release</version>
        </dependency>

    </dependencies>
    <build>
        <finalName>hera</finalName>
<!--        <directory>../target</directory>-->
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${springboot.version}</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>build-info</goal>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.5</version>
                <executions>
                    <execution>
                        <id>report-aggregate</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report-aggregate</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
          <id>image</id>
          <activation>
            <activeByDefault>false</activeByDefault>
          </activation>
          <build>
            <plugins>
              <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>3.4.0</version>
                <executions>
                  <execution>
                    <phase>package</phase>
                    <goals>
                      <goal>build</goal>
                    </goals>
                  </execution>
                </executions>
                <dependencies>
                  <dependency>
                    <groupId>com.google.cloud.tools</groupId>
                    <artifactId>jib-layer-filter-extension-maven</artifactId>
                    <version>0.3.0</version>
                  </dependency>
                </dependencies>
                <configuration>
                  <containerizingMode>packaged</containerizingMode>
                  <container>
                    <entrypoint>/opt/jboss/container/java/run/run-java.sh</entrypoint>
                  </container>
                  <extraDirectories>
                    <paths>
                      <path>
                        <from>target/</from>
                        <includes>*.jar</includes>
                        <into>/deployments</into>
                      </path>
                    </paths>
                  </extraDirectories>
                  <pluginExtensions>
                    <pluginExtension>
                      <implementation>
                        com.google.cloud.tools.jib.maven.extension.layerfilter.JibLayerFilterExtension
                      </implementation>
                      <configuration
                        implementation="com.google.cloud.tools.jib.maven.extension.layerfilter.Configuration">
                        <filters>
                          <filter>
                            <!-- exclude all jib layers, which is basically anything in /app -->
                            <glob>/deployments/**</glob>
                          </filter>
                          <filter>
                            <!-- this is our fat jar, this should be kept by adding it into its own
                            layer -->
                            <glob>/deployments/${project.build.finalName}.jar</glob>
                            <toLayer>jib-custom-fatJar</toLayer>
                          </filter>
                        </filters>
                      </configuration>
                    </pluginExtension>
                  </pluginExtensions>
                  <from>
                    <image>
                      quay.xiaodiankeji.net/openjdk/openjdk-8-runtime@sha256:241d076fd757a1fecfd10fd7dc7d0bd16bd0f9d8228a22433d6cf2c2ca3cb8f3
                    </image>
                    <platforms>
                      <platform>
                        <architecture>arm64</architecture>
                        <os>linux</os>
                      </platform>
                      <platform>
                        <architecture>amd64</architecture>
                        <os>linux</os>
                      </platform>
                    </platforms>
                  </from>
                  <to>
                    <image>quay.xiaodiankeji.net/chargebolt/${project.build.finalName}:dev</image>
                    <auth>
                      <username>${env.REGISTRY_USR}</username>
                      <password>${env.REGISTRY_PSW}</password>
                    </auth>
                  </to>
                </configuration>
              </plugin>
            </plugins>
          </build>
        </profile>
      </profiles>
</project>
