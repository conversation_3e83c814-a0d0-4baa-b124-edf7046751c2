package so.dian.hera;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.Response;
import feign.ResponseMapper;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Type;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-12-27 16:06
 */
@ConditionalOnProperty(prefix = "feign.request", value = "true", matchIfMissing = true)
public class ContextInterceptor implements RequestInterceptor {

    private static final ThreadLocal<Map<String, Collection<String>>> headerThreadLocal =
            ThreadLocal.withInitial(HashMap::new);

    @Override
    public void apply(RequestTemplate requestTemplate) {
        HttpServletRequest request = getRequest();
        if (request == null) {
            return;
        }
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String name = headerNames.nextElement();
            List<String> header = getHeader(request, name);
            if (header.isEmpty()) {
                continue;
            }
            requestTemplate.header(name, header);
        }
    }

    @ConditionalOnProperty(prefix = "feign.response", value = "true", matchIfMissing = false)
    class ResMap implements ResponseMapper {

        @Override
        public Response map(Response response, Type type) {
            Map<String, Collection<String>> headers = response.headers();
            if (headers.isEmpty()) {
                return response;
            }

            Map<String, Collection<String>> headerMap = headerThreadLocal.get();
            for (Map.Entry<String, Collection<String>> entry : headers.entrySet()) {
                String name = entry.getKey();
                Collection<String> value = entry.getValue();

                if (headerMap.containsKey(name)) {
                    headerMap.get(name).addAll(value);
                } else {
                    headerMap.put(name, value);
                }
            }

            return response;
        }
    }

    private static HttpServletRequest getRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return null;
        }
        if (requestAttributes instanceof ServletRequestAttributes) {
            return ((ServletRequestAttributes) requestAttributes).getRequest();
        }
        return null;
    }

    private static List<String> getHeader(HttpServletRequest request, String name) {
        Enumeration<String> values = request.getHeaders(name);
        List<String> headers = new ArrayList<>();

        while (values.hasMoreElements()) {
            String value = values.nextElement();
            headers.add(value);
        }
        return headers;
    }
}
