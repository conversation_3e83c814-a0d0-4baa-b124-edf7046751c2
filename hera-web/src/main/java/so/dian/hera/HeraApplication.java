package so.dian.hera;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.hystrix.EnableHystrix;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * Created with hera <br/>
 *
 * @Author: dufu
 * @Date 2017年9月18日 下午4:30:01
 * @Copyright 北京伊电园网络科技有限公司 2016-2017 © 版权所有 京ICP备17000101号
 */
@SpringBootApplication
@EnableHystrix
@EnableFeignClients(basePackages = {
		"so.dian.platform.wooshpay.client",
		"so.dian.platform.pingpong.client",
		"so.dian.hera.remote.lvy.api",
		"so.dian.hera.remote.user.api",
		"so.dian.platform.airwallex.client"
})
public class HeraApplication {

	public static void main(String[] args) {
		SpringApplication.run(HeraApplication.class, args);
	}
}
