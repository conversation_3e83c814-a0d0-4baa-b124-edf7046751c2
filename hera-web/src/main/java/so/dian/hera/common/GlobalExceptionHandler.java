package so.dian.hera.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chargebolt.pheidi.client.util.MessageSender;
import com.google.common.collect.Maps;

import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.ServerErrorException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import so.dian.commons.eden.entity.BizResult;
import so.dian.commons.eden.exception.BizException;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 全局异常处理
 * Created by dufu on 2017/10/20 11:21 <br/>
 *
 * @Copyright 北京伊电园网络科技有限公司 2016-2017 © 版权所有 京ICP备17000101号
 */
@ControllerAdvice
@Slf4j(topic = "error")
public class GlobalExceptionHandler {

    private static final Logger bizLog = LoggerFactory.getLogger("biz");

    /**
     * 全局异常捕捉处理
     */
    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public BizResult defaultErrorHandler(HttpServletRequest req, Exception ex) throws Exception {
        log(req, ex, true);
        BizResult result = BizResult.create(null);
        result.setSuccess(false);
        result.setCode(100);
        result.setMsg(ex.getMessage());
        Map<String, String> map = Maps.newHashMap();
        map.put("url", req.getRequestURL().toString());
        MessageSender.send("全局异常", "defaultErrorHandler", ex.getMessage(), map);
        return result;
    }

    /**
     * 自定义异常捕捉处理
     */
    @ResponseBody
    @ExceptionHandler(value = HeraBizException.class)
    public BizResult overseaBizExceptionHandler(HttpServletRequest req, HeraBizException ex) throws Exception {
        log(req, ex, false);
        BizResult result = BizResult.create(null);
        result.setSuccess(false);
        result.setCode(ex.getCode());
        result.setMsg(ex.getMsg());
        Map<String, String> map = Maps.newHashMap();
        map.put("url", req.getRequestURL().toString());
        MessageSender.send("全局异常", "overseaBizExceptionHandler", ex.getMessage(), map);
        return result;
    }

    @ResponseBody
    @ExceptionHandler(value = BizException.class)
    public BizResult bizExceptionHandler(HttpServletRequest req, HeraBizException ex) throws Exception {
        log(req, ex, false);
        BizResult result = BizResult.create(null);
        result.setSuccess(false);
        result.setCode(ex.getCode());
        result.setMsg(ex.getMsg());
        Map<String, String> map = Maps.newHashMap();
        map.put("url", req.getRequestURL().toString());
        MessageSender.send("全局异常", "bizExceptionHandler", ex.getMessage(), map);
        return result;
    }

    @ResponseBody
    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
    public BizResult httpRequestMethodNotSupportedException(HttpServletRequest req, HttpRequestMethodNotSupportedException ex) throws Exception {
        log(req, ex, false);
        BizResult result = BizResult.create(null);
        result.setSuccess(false);
        result.setCode(500);
        result.setMsg(ex.getMessage());
        Map<String, String> map = Maps.newHashMap();
        map.put("url", req.getRequestURL().toString());
        MessageSender.send("全局异常", "httpRequestMethodNotSupportedException", ex.getMessage(), map);
        return result;
    }

    /**
     * 自定义异常捕捉处理
     */
    @ResponseBody
    @ExceptionHandler(value = {MethodArgumentNotValidException.class, BindException.class})
    public BizResult validExceptionHandler(HttpServletRequest req, Exception ex) throws Exception {
        String exMsg;
        if (MethodArgumentNotValidException.class.isInstance(ex)) {
            exMsg = ((MethodArgumentNotValidException) ex).getBindingResult().getAllErrors().get(0).getDefaultMessage();
        } else {
            exMsg = ((BindException) ex).getBindingResult().getAllErrors().get(0).getDefaultMessage();
        }
        log(req, ex, false);
        BizResult result = BizResult.create(null);
        result.setSuccess(false);
        result.setCode(HeraBizErrorCodeEnum.PARAM_ERROR.getCode());
        result.setMsg(exMsg);
        Map<String, String> map = Maps.newHashMap();
        map.put("url", req.getRequestURL().toString());
        MessageSender.send("全局异常", "validExceptionHandler", ex.getMessage(), map);
        return result;
    }

    private void log(HttpServletRequest req, Exception ex, boolean isStackError) {
        bizLog.error("url：{}|data：{}", req.getRequestURL().toString(), JSON.toJSONString(req.getParameterMap()), ex);
        if (isStackError) {
            log.error("url={}|param={}|exp={}|msg={}", req.getRequestURL().toString(), JSON.toJSONString(req.getParameterMap()), ex.getClass(), ex.getMessage(), ex);
        }
    }

    /**
     * 直接响应外部异常
     *
     * @param req
     * @param resp
     * @param ex
     * @return
     * @throws Exception
     */
    @ResponseBody
    @ExceptionHandler(value = ServerErrorException.class)
    public BizResult serverErrorExceptionHandler(HttpServletRequest req, HttpServletResponse resp, ServerErrorException ex) throws Exception {
        log(req, ex, true);
        resp.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, ex.getMessage());
        Map<String, String> map = Maps.newHashMap();
        map.put("url", req.getRequestURL().toString());
        MessageSender.send("全局异常", "serverErrorExceptionHandler", ex.getMessage(), map);
        return null;
    }

}

