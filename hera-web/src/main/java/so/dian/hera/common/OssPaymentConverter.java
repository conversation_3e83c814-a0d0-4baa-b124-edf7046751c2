package so.dian.hera.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chargebolt.hera.client.dto.common.CurrencyExchangeInfo;
import com.chargebolt.hera.client.dto.pay.payment.PaymentDTO;
import com.chargebolt.hera.client.enums.PayMethodEnum;
import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * OssPaymentConverter
 *
 * <AUTHOR>
 * @desc 描述信息
 * @date 18/7/10
 */
@Slf4j
public class OssPaymentConverter {

    public static PaymentDTO convert(PaymentDO paymentDO) {
        if (Objects.isNull(paymentDO)) {
            return null;
        }
        PaymentDTO dto = new PaymentDTO();
        BeanUtils.copyProperties(paymentDO, dto);
        dto.setBizType(PaymentBizTypeEnum.explain(paymentDO.getBizType()));
        dto.setPayway(PaywayEnum.explain(paymentDO.getPayType()));
        dto.setStatus(PayStatus.explain(paymentDO.getStatus()));
        dto.setPayMethod(PayMethodEnum.explain(paymentDO.getPayMethod()));
        String note = paymentDO.getNote();
        if (!StringUtils.isEmpty(note) && note.startsWith("{")) {
            try {
                JSONObject jsonObject = JSON.parseObject(note);
                CurrencyExchangeInfo currencyExchangeInfo = jsonObject.getObject("currencyExchange", CurrencyExchangeInfo.class);
                dto.setCurrencyExchangeInfo(currencyExchangeInfo);
            } catch (Exception e) {
                log.error("转换汇率信息错误：{}", note, e);
            }
        }
        return dto;
    }

    public static List<PaymentDTO> convert(@NonNull List<PaymentDO> pgPaymentDOS) {
        return pgPaymentDOS.stream().map(OssPaymentConverter::convert).collect(Collectors.toList());
    }
}
