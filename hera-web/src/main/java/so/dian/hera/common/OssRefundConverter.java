package so.dian.hera.common;

import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundDTO;
import com.chargebolt.hera.domain.RefundDO;
import lombok.NonNull;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * OssRefundConverter
 *
 * <AUTHOR>
 * @desc 描述信息
 * @date 18/7/10
 */
public class OssRefundConverter {

    public static RefundDTO convert(RefundDO refundDO) {
        if (Objects.isNull(refundDO)) {
            return null;
        }
        RefundDTO dto = new RefundDTO();
        BeanUtils.copyProperties(refundDO, dto);
        return dto;
    }

    public static List<RefundDTO> convert(@NonNull List<RefundDO> refundDOS) {
        return refundDOS.stream().map(OssRefundConverter::convert).collect(Collectors.toList());
    }
}
