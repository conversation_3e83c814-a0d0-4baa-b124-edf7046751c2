package so.dian.hera.common.enums;

import com.chargebolt.hera.client.enums.PaywayEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import so.dian.lvy.pojo.enums.AccountTypeEnum;

/**
 * 支付平台渠道 <br/>
 *
 * <AUTHOR>
 * @date 2018-12-12 11:10
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备********号
 */
@Getter
@AllArgsConstructor
public enum AccountPayTypeEnum {
//    XIAODIAN_WALLET(PaywayEnum.XIAODIAN_WALLET, AccountTypeEnum.WALLET, "钱包支付"),
    DEPOSIT(PaywayEnum.DEPOSIT, AccountTypeEnum.DEPOSIT, "押金抵扣"),
//    FREEZE(PaywayEnum.PRE_AUTH_CAPTURE, AccountTypeEnum.FREEZE, "冻结金抵扣"),
    ;
    private PaywayEnum payWay;
    private AccountTypeEnum accountType;
    private String desc;

    public static AccountPayTypeEnum explain(PaywayEnum paywayEnum){
        for (AccountPayTypeEnum payTypeEnum : AccountPayTypeEnum.values()) {
            if (payTypeEnum.getPayWay().equals(paywayEnum)) {
                return payTypeEnum;
            }
        }
        return null;
    }
}
