package so.dian.hera.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付平台渠道 <br/>
 *
 * <AUTHOR>
 * @date 2018-12-12 11:10
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@Getter
@AllArgsConstructor
public enum NotifyStatusEnum {
    INIT(0, "初始化"),
    WAIT_NOTIFY(1, "待通知"),
    NOTIFIED(2, "已通知"),
    NOT_INFORM(3, "关闭通知"),
    ;
    private Integer code;
    private String desc;

}
