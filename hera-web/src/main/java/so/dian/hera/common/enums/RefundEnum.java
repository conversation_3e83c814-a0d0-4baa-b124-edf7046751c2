package so.dian.hera.common.enums;

import com.chargebolt.hera.client.enums.PaywayEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020-01-10 16:43
 */
@Getter
public enum RefundEnum {

//    OFFLINE(PaywayEnum.OFFLINE_TRANSACTION, TimeUnit.MINUTES.toMillis(1), TimeUnit.MINUTES.toMillis(1)),
//    SWIFTPASS_WECHAT(PaywayEnum.SWIFTPASS_WECHAT, TimeUnit.HOURS.toMillis(1), TimeUnit.DAYS.toMillis(31)),
//    FUSIONPAY_WECHAT(PaywayEnum.FUSIONPAY_WECHAT, TimeUnit.HOURS.toMillis(1), TimeUnit.DAYS.toMillis(31)),
//    TELR(PaywayEnum.TELR, 0, 0),
    ;

    private PaywayEnum paywayEnum;

    private long intervalTime;
    private long expireTime;

    RefundEnum(PaywayEnum paywayEnum, long intervalTime, long expireTime) {
        this.paywayEnum = paywayEnum;
        this.intervalTime = intervalTime;
        this.expireTime = expireTime;
    }

    public static RefundEnum explain(PaywayEnum payway) {
        for (RefundEnum refundEnum : RefundEnum.values()) {
            if (refundEnum.paywayEnum == payway) {
                return refundEnum;
            }
        }
        return null;
    }

    public static RefundEnum explain(Integer payway) {
        for (RefundEnum refundEnum : RefundEnum.values()) {
            if (refundEnum.paywayEnum.getPayway().intValue() == payway) {
                return refundEnum;
            }
        }
        return null;
    }
}
