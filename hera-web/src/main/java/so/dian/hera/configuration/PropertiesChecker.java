package so.dian.hera.configuration;

import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Properties;
import java.util.Set;

@Slf4j
@Component
public class PropertiesChecker {

    @Value("${hera.profile}")
    private String profile;
    @Value("${spring.profiles.active}")
    private String activeProfiles;

    @PostConstruct
    public void init() {
        checkApplication();
        checkProperties("channel");
    }

    private void checkProperties(String name) {
        log.info("检查模块配置 {}", name);
        Properties templateProperties = create(name + "-template.yml");
        Properties moduleProperties = create(name + "-" + profile + ".yml");
        Set diff = Sets.difference(templateProperties.keySet(), moduleProperties.keySet());
        if (diff.size() > 0) {
            log.error("配置文件" + name + "缺少配置项:{}", diff);
            throw new RuntimeException("配置文件" + name + "缺少配置项");
        }
    }

    private void checkApplication() {
        Properties templateProperties = create("application-template.yml");
        if (activeProfiles.contains(",")) {
            for (String activeProfile : activeProfiles.split(",")) {
                if (activeProfile.startsWith("alter")) {
                    continue;
                }
                log.info("检查应用配置 {}", activeProfile);
                Properties moduleProperties = create("application-" + activeProfile + ".yml");
                Set diff = Sets.difference(templateProperties.keySet(), moduleProperties.keySet());
                if (diff.size() > 0) {
                    log.error("应用配置缺少配置项:{}", diff);
                    throw new RuntimeException("应用配置缺少配置项");
                }
            }
        } else {
            log.info("检查应用配置 {}", activeProfiles);
            Properties moduleProperties = create("application-" + activeProfiles + ".yml");
            Set diff = Sets.difference(templateProperties.keySet(), moduleProperties.keySet());
            if (diff.size() > 0) {
                log.error("应用配置缺少配置项:{}", diff);
                throw new RuntimeException("应用配置缺少配置项");
            }
        }

    }

    private Properties create(String classpath) {
        YamlPropertiesFactoryBean templateFactory = new YamlPropertiesFactoryBean();
        org.springframework.core.io.Resource resource = new ClassPathResource(classpath);
        templateFactory.setResources(resource);
        Properties properties = templateFactory.getObject();
        return properties;
    }
}
