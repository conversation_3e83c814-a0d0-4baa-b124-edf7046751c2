package so.dian.hera.configuration.db;

import com.alibaba.druid.pool.DruidDataSource;
import com.google.common.collect.Maps;
import io.shardingsphere.api.config.ShardingRuleConfiguration;
import io.shardingsphere.api.config.TableRuleConfiguration;
import io.shardingsphere.core.rule.ShardingRule;
import io.shardingsphere.shardingjdbc.jdbc.core.datasource.ShardingDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.util.Map;
import java.util.Properties;

@Slf4j
@Configuration
@MapperScan(basePackages = {"so.dian.hera.dao.sharding"},
        sqlSessionFactoryRef = "shardingSqlSessionFactory",
        sqlSessionTemplateRef = "shardingSqlSessionTemplate")
public class DataSourceConfig {

    private static final Integer PHYSICS_DBS = 4;
    private static final Integer DBS_PERCENT_PHYSICS_DBS = 4;

    @Resource
    TableRuleConfiguration paymentTableRule;
    @Resource
    TableRuleConfiguration paymentOrderMapTableRule;

    @Value("${spring.datasource.sharding.urls}")
    private String shardingUrls;
    @Value("${spring.datasource.sharding.username}")
    private String shardingUserName;
    @Value("${spring.datasource.sharding.password}")
    private String shardingPwd;
    @Value("${spring.datasource.sharding.initial-size}")
    private String shardingInitialSize;
    @Value("${spring.datasource.sharding.max-active}")
    private String shardingMaxActive;
    @Value("${spring.datasource.sharding.min-idle}")
    private String shardingMinIdle;

    @Bean(name = "shardingDataSource")
    public DataSource shardingDataSource() throws SQLException {
        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        shardingRuleConfig.getTableRuleConfigs().add(paymentTableRule);
        shardingRuleConfig.getTableRuleConfigs().add(paymentOrderMapTableRule);
//        shardingRuleConfig.getTableRuleConfigs().add(refundMapTableRule);
        ShardingRule shardingRule = new ShardingRule(shardingRuleConfig, buildDBSOfSharding().keySet());
        Properties props = new Properties();
        props.setProperty("sql.show", "false");
        ShardingDataSource dataSource = new ShardingDataSource(buildDBSOfSharding(), shardingRule, Maps.newHashMap(), props);
        return dataSource;
    }

    @Bean(name = "shardingSqlSessionFactory")
    public SqlSessionFactory shardingSqlSessionFactory(@Qualifier("shardingDataSource") DataSource dataSource)
            throws Exception {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        sqlSessionFactoryBean.setMapperLocations(
                new PathMatchingResourcePatternResolver().getResources("classpath:/mapper/sharding/*.xml"));
        sqlSessionFactoryBean
                .setConfigLocation(new DefaultResourceLoader().getResource("classpath:/db/mybatis-config.xml"));
        sqlSessionFactoryBean.setTypeAliasesPackage("so.dian.hera.dao.po");
        return sqlSessionFactoryBean.getObject();
    }

    @Bean(name = "shardingSqlSessionTemplate")
    public SqlSessionTemplate shardingSqlSessionTemplate(@Qualifier("shardingSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }


    @Bean(name = "shardingTransactionManager")
    public DataSourceTransactionManager transactionManager(@Qualifier("shardingDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    public Map<String, DataSource> buildDBSOfSharding() {
        Map<String, DataSource> shardingDataSoucesMap = Maps.newHashMap();
        for (int i = 0; i < PHYSICS_DBS; i++) {
            for (int j = 0; j < DBS_PERCENT_PHYSICS_DBS; j++) {
                Integer dbnum = i * DBS_PERCENT_PHYSICS_DBS + j;
                DecimalFormat dformat = new DecimalFormat("0000");
                String format = dformat.format(dbnum);
                shardingDataSoucesMap.put(DataSourceConstants.SHARDING_HERMES_PERFEX + format, buildDataSource(i, format));
            }
        }
        return shardingDataSoucesMap;
    }

    private DataSource buildDataSource(Integer dbIndex, String dbnum) {
        DruidDataSource dataSource = new DruidDataSource();
        Properties properties = build(dbnum);
        properties.put("druid.url", buildDbUrl(dbIndex, dbnum));
        dataSource.configFromPropety(properties);
        return dataSource;
    }

    private String buildDbUrl(Integer dbIndex, String dbnum) {
        String url = shardingUrls.split(",")[dbIndex];
//        log.warn("dnIndex:{},url:{}", dbIndex, url);
        StringBuilder sb = new StringBuilder();
        return sb.append(url).append("/").append(DataSourceConstants.SHARDING_HERMES_PERFEX).append(dbnum).append("?useSSL=true").toString();
    }

    public Properties build(String dbnum) {
        Properties prop = new Properties();
        //连接字符串
        prop.put("druid.name", DataSourceConstants.SHARDING_HERMES_PERFEX + dbnum);
        //连接字符串
        prop.put("druid.url", shardingUrls);
        //用户名
        prop.put("druid.username", shardingUserName);
        //密码
        prop.put("druid.password", shardingPwd);
        //驱动名
        prop.put("druid.driverClassName", "com.mysql.cj.jdbc.Driver");
        //初始化连接大小
        prop.put("druid.initialSize", shardingInitialSize);
        //最大连接数
        prop.put("druid.maxActive", shardingMaxActive);
        //最小空闲连接数
        prop.put("druid.minIdle", shardingMinIdle);
        //连接有效性验证
        prop.put("druid.validationQuery", "SELECT 1");
        //连接有效性超时时间设置
        prop.put("druid.validationQueryTimeout", "1");

        //指明连接是否被空闲连接回收器(如果有)进行检验.如果检测失败,则连接将被从池中去除.
        prop.put("druid.testWhileIdle", "true");
        //指明是否在从池中取出连接前进行检验,如果检验失败,则从池中去除连接并尝试取出另一个
        prop.put("druid.testOnBorrow", "true");
        //指明是否在归还到池中前进行检验
        prop.put("druid.testOnReturn", "false");
        //1) Destroy线程会检测连接的间隔时间 2) testWhileIdle的判断依据，详细看testWhileIdle属性的说明
        prop.put("druid.timeBetweenEvictionRunsMillis", "60000");

        //开启池的prepared statement 池功能,PSCache对支持游标的数据库性能提升巨大，比如说oracle。在mysql5.5以下的版本中没有PSCache功能，建议关闭掉。5.5及以上版本有PSCache，建议开启。
        prop.put("druid.poolPreparedStatements", "true");
        //开启池的prepared statement 池功能
        prop.put("druid.maxOpenPreparedStatements", "100");

        prop.put("druid.useGlobalDataSourceStat", "true");
        return prop;
    }
}
