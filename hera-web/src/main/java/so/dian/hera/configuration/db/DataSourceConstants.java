package so.dian.hera.configuration.db;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * DataSourceConstants
 *
 * <AUTHOR>
 * @desc 数据源常量
 * @date 17/12/16
 */
public class DataSourceConstants {

    public static final String MYBATIS_CONFIG_PATH = "classpath:db/mybatis-config.xml";
    public static final String TYPE_ALIASES_PACKAGE = "so.dian.hera.domain";
    public static final String TYPE_HANDLERS_PACKAGE = "so.dian.hera.dao.handler";

    public static final String SHARDING_HERMES_PERFEX = "cb_dian_hermes_";

    @Getter
    @AllArgsConstructor
    public enum DataSourceEnum {

//        RDS_R(  "主数据库-读取",      "classpath:mapper/rds/read/*.xml",      "spring.datasource.rds.read."),
//        RDS_W(  "主数据库-写入",      "classpath:mapper/rds/write/*.xml",     "spring.datasource.rds.write."),
//        SHARD(  "分布式数据库-读写",   "classpath:mapper/sharding/*.xml",      "spring.datasource.sharding."),
//        PG(     "只读数据库-",        "classpath:mapper/pg/*.xml",            "spring.datasource.pg."),
//        FEIGN(     "微服务-",        "",            ""),
        ;

        private String desc;
        private String xmlPath;
        private String prefix;
    }
}
