package so.dian.hera.configuration.db;


import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;

import javax.sql.DataSource;
import java.io.IOException;

/**
 * rds数据源配置 <br/>
 *
 * <AUTHOR>
 * @date 2018-12-17 16:45
 * @Copyright 北京伊电园网络科技有限公司 2016-2019 © 版权所有 京ICP备17000101号
 */
@Configuration
@MapperScan(basePackages = "so.dian.hera.dao.rds.drds", sqlSessionTemplateRef = "rdsLhcSqlSessionTemplate")
public class DrdsDataSourceConfig {


//    @Primary
    @Bean(name = "rdsLhcDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.rds.lhc")
    public DataSource rdsDataSource() {
        return DruidDataSourceBuilder.create().build();
    }


//    @Primary
    @Bean(name = "rdsLhcSqlSessionFactory")
    public SqlSessionFactoryBean sqlSessionFactory(@Qualifier("rdsLhcDataSource") DataSource dataSource)
            throws IOException {

        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);

        ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();

        sqlSessionFactoryBean.setConfigLocation(resolver.getResource("classpath:db/mybatis-config.xml"));
        sqlSessionFactoryBean.setMapperLocations(resolver.getResources("classpath:mapper/rds/drds/*.xml"));

        return sqlSessionFactoryBean;
    }

//    @Primary
    @Bean(name = "rdsLhcSqlSessionTemplate")
    public SqlSessionTemplate sqlSessionTemplate(
            @Qualifier("rdsLhcSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }


}
