package so.dian.hera.configuration.db;

import io.shardingsphere.api.config.TableRuleConfiguration;
import io.shardingsphere.api.config.strategy.ShardingStrategyConfiguration;
import io.shardingsphere.api.config.strategy.StandardShardingStrategyConfiguration;
import io.shardingsphere.core.keygen.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.data.redis.core.RedisTemplate;
import so.dian.hera.configuration.db.keygenerator.PrimKeyGenerator;
import so.dian.hera.configuration.db.strategy.SchemeByStringAlgorithm;
import so.dian.hera.configuration.db.strategy.TableByStringAlgorithm;

import javax.annotation.Resource;

@Configuration
@DependsOn("redisTemplate")
public class TableRuleConfig {

    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private TableByStringAlgorithm tableByStringAlgorithm;
    @Resource
    private SchemeByStringAlgorithm schemeByStringAlgorithm;

    @Bean(name = "paymentTableRule")
    public TableRuleConfiguration paymentTableRule() {
        TableRuleConfiguration paymentTableRule = new TableRuleConfiguration();
        paymentTableRule.setLogicTable("payment");
        paymentTableRule.setActualDataNodes(ShardingUtil.genActualDataNodes("payment"));

        ShardingStrategyConfiguration dbStrategy = new StandardShardingStrategyConfiguration("trade_no", schemeByStringAlgorithm);
        ShardingStrategyConfiguration tableStrategy = new StandardShardingStrategyConfiguration("trade_no", tableByStringAlgorithm);

        paymentTableRule.setDatabaseShardingStrategyConfig(dbStrategy);
        paymentTableRule.setTableShardingStrategyConfig(tableStrategy);

        paymentTableRule.setKeyGeneratorColumnName("id");
        paymentTableRule.setKeyGenerator(build("payment"));
        return paymentTableRule;
    }

    @Bean(name = "paymentOrderMapTableRule")
    public TableRuleConfiguration paymentOrderMapTableRule() {
        TableRuleConfiguration paymentOrderMapTableRule = new TableRuleConfiguration();
        paymentOrderMapTableRule.setLogicTable("payment_order_mapping");
        paymentOrderMapTableRule.setActualDataNodes(ShardingUtil.genActualDataNodes("payment_order_mapping"));

        ShardingStrategyConfiguration dbStrategy = new StandardShardingStrategyConfiguration("order_no",
                schemeByStringAlgorithm);
        ShardingStrategyConfiguration tableStrategy = new StandardShardingStrategyConfiguration("order_no",
                tableByStringAlgorithm);

        paymentOrderMapTableRule.setDatabaseShardingStrategyConfig(dbStrategy);
        paymentOrderMapTableRule.setTableShardingStrategyConfig(tableStrategy);

        paymentOrderMapTableRule.setKeyGeneratorColumnName("id");
        paymentOrderMapTableRule.setKeyGenerator(build("payment_order_mapping"));
        return paymentOrderMapTableRule;
    }

    @Bean(name = "refundMapTableRule")
    public TableRuleConfiguration refundMapTableRule() {
        TableRuleConfiguration refundMapTableRule = new TableRuleConfiguration();
        refundMapTableRule.setLogicTable("refund");
        refundMapTableRule.setActualDataNodes(ShardingUtil.genActualDataNodes("refund"));

        ShardingStrategyConfiguration dbStrategy = new StandardShardingStrategyConfiguration("trade_no", schemeByStringAlgorithm);
        ShardingStrategyConfiguration tableStrategy = new StandardShardingStrategyConfiguration("trade_no", tableByStringAlgorithm);

        refundMapTableRule.setDatabaseShardingStrategyConfig(dbStrategy);
        refundMapTableRule.setTableShardingStrategyConfig(tableStrategy);

        refundMapTableRule.setKeyGeneratorColumnName("id");
        refundMapTableRule.setKeyGenerator(build("refund"));
        return refundMapTableRule;
    }

    private KeyGenerator build(String tableName) {
        PrimKeyGenerator keyGenerator = new PrimKeyGenerator();
        keyGenerator.setTableName(tableName);
        keyGenerator.setRedisTemplate(redisTemplate);
        return keyGenerator;
    }

}
