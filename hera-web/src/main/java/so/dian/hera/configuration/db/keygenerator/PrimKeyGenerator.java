package so.dian.hera.configuration.db.keygenerator;

import io.shardingsphere.core.keygen.KeyGenerator;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

@Slf4j
public class PrimKeyGenerator implements KeyGenerator {

    private String key;
    @Setter
    private RedisTemplate<String, String> redisTemplate;

    public void setTableName(String tableName) {
        key = String.format("SHARDING-KEY-GENERATOR-LHC-%s", tableName);
    }

    @Override
    public Number generateKey() {
        Number id = redisTemplate.opsForValue().increment(key, 1);
        log.info("KeyGenerator {}: {}", key, id);
        return id;
    }
}
