package so.dian.hera.configuration.db.strategy;

import io.shardingsphere.api.algorithm.sharding.PreciseShardingValue;
import io.shardingsphere.api.algorithm.sharding.standard.PreciseShardingAlgorithm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import so.dian.hera.configuration.db.ShardingUtil;

import java.util.Collection;

@Component
public class SchemeByLongAlgorithm implements PreciseShardingAlgorithm<Long> {

    private static final Logger LOGGER = LoggerFactory.getLogger("error");

    protected String sharding(Collection<String> availableTargetNames, Long shardingValue) {
        String schemeNameSuffix = ShardingUtil.shardingSchemeSuffix(shardingValue);
        for (String schemeName : availableTargetNames) {
            if (schemeName.endsWith(schemeNameSuffix)) {
                return schemeName;
            }
        }
//        throw new IllegalArgumentException(String.format("sharding failure. availableTargetNames=%s, shardingValue=%s", availableTargetNames, shardingValue));
        return null;
    }

    @Override
    public String doSharding(Collection<String> collection, PreciseShardingValue<Long> preciseShardingValue) {
        return sharding(collection, preciseShardingValue.getValue());
    }
}
