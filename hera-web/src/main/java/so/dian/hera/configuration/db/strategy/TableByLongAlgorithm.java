package so.dian.hera.configuration.db.strategy;

import io.shardingsphere.api.algorithm.sharding.PreciseShardingValue;
import io.shardingsphere.api.algorithm.sharding.standard.PreciseShardingAlgorithm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import so.dian.hera.configuration.db.ShardingUtil;

import java.util.Collection;

@Component
public class TableByLongAlgorithm implements PreciseShardingAlgorithm<Long> {

    private static final Logger LOGGER = LoggerFactory.getLogger(TableByLongAlgorithm.class);

    private String sharding(Collection<String> availableTargetNames, Long shardingValue) {
        String tableNameSuffix = ShardingUtil.shardingTableSuffix(shardingValue);
        for (String tableName : availableTargetNames) {
            if (tableName.endsWith(tableNameSuffix)) {
                return tableName;
            }
        }
//        throw new IllegalArgumentException("分表路由错误. availableTargetNames=" + availableTargetNames.size());
        return null;
    }

    @Override
    public String doSharding(Collection<String> collection, PreciseShardingValue<Long> preciseShardingValue) {
        Long shardingValue = preciseShardingValue.getValue();
        return sharding(collection, shardingValue);
    }
}
