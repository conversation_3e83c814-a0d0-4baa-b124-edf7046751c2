package so.dian.hera.configuration.pg;

import com.alibaba.druid.pool.DruidDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import so.dian.hera.configuration.db.DataSourceConfiguration;
import so.dian.hera.configuration.db.DataSourceConstants;

import javax.sql.DataSource;
import java.util.Properties;

/**
 * DataSourcePgConfiguration
 *
 * <AUTHOR>
 * @date 2017-10-11
 */
//@Configuration
//@MapperScan(basePackages = "so.dian.hera.dao.pg", sqlSessionTemplateRef = "pgSqlSessionTemplate")
public class DataSourcePgConfiguration {

//    @RefreshScope
//    @Bean(name = "pgDataSource")
//    public DataSource dataSource(Environment env) {
//        Properties prop = DataSourceConfiguration.build(env, DataSourceConstants.DataSourceEnum.PG.getPrefix());
//        DruidDataSource dataSource = new DruidDataSource();
//        dataSource.configFromPropety(prop);
//        DataSourceConfiguration.setDefaultDruidProperties(dataSource);
//        return dataSource;
//    }
//
//    @Bean(name = "pgSqlSessionFactory")
//    public SqlSessionFactory sqlSessionFactory(@Qualifier("pgDataSource") DataSource dataSource, Environment env)
//            throws Exception {
//        return DataSourceConfiguration.getSqlSessionFactory(dataSource, DataSourceConstants.DataSourceEnum.PG.getXmlPath(), env);
//    }
//
//    @Bean(name = "pgSqlSessionTemplate")
//    public SqlSessionTemplate sqlSessionTemplate(@Qualifier("pgSqlSessionFactory") SqlSessionFactory sqlSessionFactory)
//            throws Exception {
//        return new SqlSessionTemplate(sqlSessionFactory);
//    }
}
