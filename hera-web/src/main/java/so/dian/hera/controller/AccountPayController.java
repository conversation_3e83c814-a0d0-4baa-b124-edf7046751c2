package so.dian.hera.controller;

import com.chargebolt.hera.client.api.AccountPayApi;
import com.chargebolt.hera.client.dto.pay.account.req.AccountPayReq;
import com.chargebolt.hera.client.dto.pay.account.req.AccountRefundReq;
import com.chargebolt.hera.client.dto.pay.account.rsp.AccountPayRsp;
import com.chargebolt.hera.client.dto.pay.account.rsp.AccountRefundRsp;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import so.dian.hera.service.pay.account.AccountPayService;
import so.dian.hera.service.persist.db.PaymentPersistService;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * Created by ailun on 2017/11/5.
 */
@Api(value = "AccountPayController", description = "账户支付相关API", tags = "AccountPayApi")
@RestController
@Slf4j
public class AccountPayController implements AccountPayApi {

    @Autowired
    private AccountPayService accountPayService;

    @Autowired
    private PaymentPersistService paymentPersistService;

    @Override
    @ApiOperation(value = "账户支付")
    @PostMapping(value = "/account/pay", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public AccountPayRsp pay(@RequestBody @Valid AccountPayReq accountPayReq) {
        log.info("account 支付请求，accountPayReq:{}", accountPayReq);
        AccountPayRsp rsp = accountPayService.accountPay(accountPayReq);
        log.info("account 支付响应，AccountPayRsp:{}", rsp);
        return rsp;
    }

    @Override
    @ApiOperation(value = "账户退款")
    @PostMapping(value = "/account/refund", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public AccountRefundRsp refund(@RequestBody @Valid AccountRefundReq accountRefundReq) {
        log.info("account 退款请求，accountRefundReq:{}", accountRefundReq);
        AccountRefundRsp rsp = accountPayService.accountRefund(accountRefundReq);
        log.info("account 退款响应，AccountPayRsp:{}", rsp);
        return rsp;
    }

    @ApiOperation(value = "账户间转账")
    @PostMapping(value = "/account/trans", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public AccountPayRsp trans(@RequestBody @Valid AccountPayReq payReq) {
        //todo 待lvy迁移
        return null;
    }

    @ApiOperation(value = "查询账户状态")
    @GetMapping(value = "/account/status", consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public String status(@RequestParam("tradeNo") @NotNull String tradeNo) {
        PaymentDO paymentDO = paymentPersistService.getPaymentByBizNo(tradeNo);
        if (paymentDO == null) {
            return null;
        }
        Integer status = paymentDO.getStatus();
        if (status == null) {
            return null;
        }
        PayStatus payStatus = PayStatus.explain(status);
        return payStatus == null ? null : payStatus.getKey();
    }
}
