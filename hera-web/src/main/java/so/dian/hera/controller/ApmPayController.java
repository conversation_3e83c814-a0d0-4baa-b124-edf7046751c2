package so.dian.hera.controller;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.api.ApmPayApi;
import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.checkout.req.CancelCheckRequest;
import com.chargebolt.hera.client.dto.pay.checkout.req.CancelRequest;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.CancelCheckResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.CancelResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PrepayCreateResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.hera.service.pay.apm.ApmPayService;
import javax.annotation.Resource;

@Slf4j
@RestController
public class ApmPayController implements ApmPayApi {
    @Resource
    private ApmPayService apmPayService;

    @Override
    public PrepayCreateResultDTO prepay(@RequestBody PrepayCreateRequest prepayCreateRequest) {
        log.info("apm pay，req:{}", prepayCreateRequest);
        PrepayCreateResultDTO rsp = apmPayService.prepay(prepayCreateRequest);
        log.info("apm pay，rsp:{}", rsp);
        return rsp;
    }

    @Override
    public CancelCheckResultDTO cancelCheck(CancelCheckRequest cancelCheckRequest) {
        log.info("apm 押金订单取消校验，req:{}", JSON.toJSONString(cancelCheckRequest));
        CancelCheckResultDTO rsp = apmPayService.cancelCheck(cancelCheckRequest);
        log.info("apm 押金订单取消校验，rsp:{}", JSON.toJSONString(rsp));
        return rsp;
    }

    @Override
    public CancelResultDTO cancel(CancelRequest cancelRequest) {
        log.info("apm 押金订单取消，req:{}", JSON.toJSONString(cancelRequest));
        CancelResultDTO rsp = apmPayService.cancel(cancelRequest);
        log.info("apm 押金订单取消，rsp:{}", JSON.toJSONString(rsp));
        return rsp;
    }
}
