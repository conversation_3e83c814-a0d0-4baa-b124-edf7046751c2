package so.dian.hera.controller;

import com.chargebolt.hera.client.api.ApmTokenPayApi;
import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.apmtoken.req.ApmTokenCheckRequest;
import com.chargebolt.hera.client.dto.pay.apmtoken.req.ApmTokenPayRequest;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenCheckResultDTO;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenApplyResultDTO;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenPayResultDTO;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenPrepareResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.hera.service.pay.apmtoken.ApmTokenPayService;

import javax.annotation.Resource;
import javax.validation.Valid;

@Slf4j
@RestController
public class ApmTokenPayController implements ApmTokenPayApi {

    @Resource
    private ApmTokenPayService apmTokenPayService;

    @Override
    public ApmTokenPrepareResultDTO prepare(@RequestBody PrepayCreateRequest request) {
        log.debug("apm-token prepare，req:{}", request);
        ApmTokenPrepareResultDTO resultDTO = apmTokenPayService.prepare(request);
        log.debug("apm-token prepare，rsp:{}", resultDTO);
        return resultDTO;
    }

    @Override
    public ApmTokenApplyResultDTO apply(@RequestBody PrepayCreateRequest request) {
        log.debug("apm-token apply，req:{}", request);
        ApmTokenApplyResultDTO resultDTO = apmTokenPayService.apply(request);
        log.debug("apm-token apply，rsp:{}", resultDTO);
        return resultDTO;
    }

    @Override
    public ApmTokenPayResultDTO pay(@RequestBody @Valid ApmTokenPayRequest request) {
        log.debug("apm-token pay，req:{}", request);
        ApmTokenPayResultDTO resultDTO = apmTokenPayService.pay(request);
        log.debug("apm-token pay，rsp:{}", resultDTO);
        return resultDTO;
    }

    @Override
    public ApmTokenCheckResultDTO check(@RequestBody ApmTokenCheckRequest request) {
        log.debug("apm-token check，req:{}", request);
        ApmTokenCheckResultDTO resultDTO = apmTokenPayService.checkToken(request);
        log.debug("apm-token check，rsp:{}", resultDTO);
        return resultDTO;
    }

}
