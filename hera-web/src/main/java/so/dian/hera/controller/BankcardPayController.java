package so.dian.hera.controller;

import com.chargebolt.hera.client.api.BankcardAuthorizationApi;
import com.chargebolt.hera.client.dto.pay.checkout.req.PreAuthCancelRequest;
import com.chargebolt.hera.client.dto.pay.checkout.req.PreAuthCaptureRequest;
import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.hera.service.pay.bankcard.BankcardPayService;
import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * Created by ailun on 2017/11/5.
 */
@RestController
@Slf4j
public class BankcardPayController implements BankcardAuthorizationApi {

    @Resource
    private BankcardPayService bankCardPayService;

    @Override
    public PrepayCreateResultDTO bankcardCreatedPreAuth(@RequestBody @Valid PrepayCreateRequest prepayCreateRequest) {
        log.info("auth 预授权请求，preAuthReq:{}", prepayCreateRequest);
        PrepayCreateResultDTO rsp = bankCardPayService.createPreAuth(prepayCreateRequest);
        log.info("auth 预授权响应，preAuthRsp:{}", rsp);
        return rsp;
    }

    @Override
    public PreAuthCancelResultDTO bankcardCancelPreAuth(@RequestBody @Valid PreAuthCancelRequest preAuthCancelRequest) {
        log.info("auth 撤销请求，cancelReq:{}", preAuthCancelRequest);
        PreAuthCancelResultDTO rsp = bankCardPayService.cancelPreAuth(preAuthCancelRequest);
        log.info("auth 撤销响应，cancelRsp:{}", rsp);
        return rsp;
    }

    @Override
    public PreAuthCaptureResultDTO bankcardCapture(@RequestBody @Valid PreAuthCaptureRequest preAuthCaptureRequest) {
        log.info("auth 请款请求，captureReq:{}", preAuthCaptureRequest);
        PreAuthCaptureResultDTO rsp = bankCardPayService.capture(preAuthCaptureRequest);
        log.info("auth 请款响应，captureRsp:{}", rsp);
        return rsp;
    }

}
