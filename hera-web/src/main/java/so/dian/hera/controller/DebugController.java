package so.dian.hera.controller;

import cn.hutool.core.codec.Base64Encoder;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chargebolt.hera.domain.RefundDO;
import com.chargebolt.hera.domain.notify.DelayQueryResultBody;
import com.chargebolt.hera.domain.notify.PaymentRetryBody;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import lombok.extern.slf4j.Slf4j;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.*;
import so.dian.commons.eden.entity.BizResult;
import so.dian.hera.controller.notify.WooshPayNotifyController;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;
import so.dian.hera.interceptor.dto.rsp.RefundQueryResultDTO;
import so.dian.hera.mq.consumer.self.PayRefundQueryHandler;
import so.dian.hera.mq.consumer.self.PaymentRetryHandler;
import so.dian.hera.service.pay.PayResultQueryService;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.hera.service.persist.db.RefundPersistService;
import so.dian.hera.service.refund.RefundResultQueryService;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.airwallex.client.AirwallexPayClient;
import so.dian.platform.airwallex.dto.response.RefundQueryResponse;
import so.dian.platform.airwallex.dto.response.RetrieveResponse;
import so.dian.platform.airwallex.service.TokenService;
import so.dian.platform.pingpong.processor.PingpongApmTokenProcessor;
import so.dian.platform.wooshpay.client.WooshPayClient;
import so.dian.platform.wooshpay.dto.notify.WooshPayWebhookNotifyDTO;
import so.dian.platform.wooshpay.dto.request.WooshPayPaymentIntentCancelRequest;
import so.dian.platform.wooshpay.dto.request.WooshPayPaymentIntentCaptureRequest;
import so.dian.platform.wooshpay.processor.WooshPayProcessor;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;

@Slf4j

@Api("debugController")
@RestController
@Profile({"local", "dev", "oversea-real"})
public class DebugController {

    @Resource
    private WooshPayClient wooshPayClient;

    @Resource
    private AirwallexPayClient airwallexPayClient;

    @Resource
    private WooshPayProcessor wooshPayProcessor;

    @Resource
    private TokenService tokenService;

    @Resource
    private PaymentRetryHandler paymentRetryHandler;

    @Resource
    private PayRefundQueryHandler payRefundQueryHandler;

    @Value("${channel.wooshpay.skKey}")
    private String skKey;

    private Map header = Maps.newHashMap();

    @PostConstruct
    public void init() {
        header.put("Authorization", "Basic " + Base64Encoder.encode(skKey + ":"));
    }


    @ApiOperation("")
    @PostMapping("/debug/wooshpay/capture")
    public Object capture(@RequestParam String id, @RequestBody WooshPayPaymentIntentCaptureRequest request) {
        return wooshPayClient.paymentIntentsCapture(id, request, header);
    }

    @ApiOperation("")
    @PostMapping("/debug/wooshpay/cancel")
    public Object cancel(@RequestParam String id, @RequestBody WooshPayPaymentIntentCancelRequest request) {
        return wooshPayClient.paymentIntentsCancel(id, request, header);
    }

    @ApiOperation("")
    @GetMapping("/debug/wooshpay/retrieve")
    public Object retrieve(@RequestParam String id) {
        return wooshPayClient.paymentIntentsRetrieve(id, header);
    }

    @ApiOperation("")
    @GetMapping("/debug/wooshpay/retrieveRefund")
    public Object retrieveRefund(@RequestParam String id) {
        return wooshPayClient.retrieveRefund(id, header);
    }

    @ApiOperation("")
    @GetMapping("/debug/wooshpay/retrieveEvent")
    public Object retrieveEvent(@RequestParam String id) {
        return wooshPayClient.retrieveEvent(id, header);
    }

    @ApiOperation("")
    @PostMapping("/debug/wooshpay/createWebhook")
    public void webhook() {
        wooshPayProcessor.createWebhook();
    }

    @ApiOperation("")
    @PostMapping("/debug/wooshpay/listWebhook")
    public Object listWebhook() {
        return wooshPayClient.webhook_endpoints(header);
    }

    @ApiOperation("")
    @PostMapping("/debug/wooshpay/deleteWebhook")
    public Object deleteWebhook(@RequestParam String id) {
        return wooshPayClient.webhook_endpoints(id, header);
    }

    @Resource
    private WooshPayNotifyController wooshPayNotifyController;

    @PostMapping(value = "/notify/wooshpay0")
    public BizResult wooshpayNotify0(@RequestBody JSONObject object) throws InterruptedException {
        return wooshPayNotifyController.wooshpayNotify(JSON.parseObject(object.toJSONString(), WooshPayWebhookNotifyDTO.class));
    }

    @GetMapping("/debug/pingpong/resultUrl")
    public BizResult pingpongResultUrl(@RequestParam String token,
                                       @RequestParam String merchantTransactionId,
                                       @RequestParam String transactionId,
                                       @RequestParam String preAuthOrderNo,
                                       @RequestParam String deviceNo) {
        JSONObject object = new JSONObject();
        object.put("token", token);
        object.put("merchantTransactionId", merchantTransactionId);
        object.put("transactionId", transactionId);
        object.put("preAuthOrderNo", preAuthOrderNo);
        object.put("deviceNo", deviceNo);
        return BizResult.create(object);
    }

    @GetMapping("/debug/pingpong/cancelUrl")
    public BizResult pingpongCancelUrl(@RequestParam String token, @RequestParam String merchantTransactionId, @RequestParam String transactionId) {
        JSONObject object = new JSONObject();
        object.put("token", token);
        object.put("merchantTransactionId", merchantTransactionId);
        object.put("transactionId", transactionId);
        return BizResult.create(object);
    }
    @Resource
    private PaymentPersistService paymentPersistService;
    @Resource
    private PayResultQueryService payResultQueryService;
    @Resource
    private RefundResultQueryService refundResultQueryService;
    @Resource
    private RefundPersistService refundPersistService;
    @GetMapping("/debug/pay/query")
    public BizResult payQuery(@RequestParam String transNo) {
        PaymentDO paymentDO= paymentPersistService.getPaymentByTradeNo(transNo);
        if(Objects.isNull(paymentDO)){
            return BizResult.create(paymentDO);
        }

        // 查询支付结果
        PayQueryResultDTO resultDTO= payResultQueryService.queryPayResult(paymentDO);
        return BizResult.create(resultDTO);
    }

    @GetMapping("/debug/refund/query")
    public BizResult refundQuery(@RequestParam String refundNo) {

        RefundDO refundDO = refundPersistService.getRefund(refundNo);
        if(Objects.isNull(refundDO)){
            return BizResult.create(refundDO);
        }
        // 查询支付结果
        RefundQueryResultDTO resultDTO=refundResultQueryService.queryRefundResult(refundDO);
        return BizResult.create(resultDTO);
    }

    @Resource
    private PingpongApmTokenProcessor processor;

    @ApiOperation("")
    @GetMapping("/debug/pingpong/apmToken/query")
    public Object apmTokenQuery(@RequestParam String token, @RequestParam Long userId) {
        return processor.apmTokenQuery(token, userId);
    }

    @ApiOperation("")
    @GetMapping("/debug/pingpong/apmToken/cancel")
    public Object apmTokenCancel(@RequestParam String token, @RequestParam Long userId) {
        return processor.apmTokenCancel(token, userId);
    }

    @GetMapping("/debug/payment/update")
    public BizResult updatePayment(@RequestParam String tradeNo, @RequestParam Integer state) {
        PaymentDO paymentDO= paymentPersistService.getPaymentByTradeNo(tradeNo);
        if(Objects.isNull(paymentDO)){
            return BizResult.create(false);
        }
        log.info("更新付款单：{}", JsonUtil.beanToJson(paymentDO));
        paymentDO.setStatus(state);
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentPersistService.updateStatus(paymentDO);
        return BizResult.create(true);
    }

    private HashMap builderHeader(){
        HashMap header = new HashMap<>(2);
        String authorization = "Bearer " + tokenService.getToken();
        header.put("Authorization",authorization);
        header.put("Content-Type","application/json");
        return header;
    }

    /**
     * 空中云汇订单查询
     * @param id
     * @return
     */
    @ApiOperation("")
    @PostMapping("/debug/airwallex/orderQuery")
    public RetrieveResponse debugOrderQuery(@RequestParam String id) {
        return airwallexPayClient.orderQuery(id, builderHeader());
    }

    /**
     * 空中云汇退款查询
     * @param id
     * @return
     */
    @ApiOperation("")
    @PostMapping("/debug/airwallex/refundQuery")
    public RefundQueryResponse debugRefundQuery(@RequestParam String id) {
        return airwallexPayClient.refundQuery(id, builderHeader());
    }


    /**
     * 支付单重试（请款）
     * @param retryBody
     * @return
     */
    @ApiOperation("")
    @PostMapping("/debug/paymentRetry")
    public Boolean paymentRetry(@RequestBody PaymentRetryBody retryBody) {
        return paymentRetryHandler.handle(JSON.toJSONString(retryBody));
    }

    /**
     * 支付｜退款补偿
     * @param retryBody
     * @return
     */
    @ApiOperation("")
    @PostMapping("/debug/payRefundQueryRetry")
    public Boolean payRefundQueryRetry(@RequestBody DelayQueryResultBody retryBody) {
        return payRefundQueryHandler.handle(JSON.toJSONString(retryBody));
    }


    @GetMapping("/debug/zone")
    public BizResult zone() {
        log.info("new date:{}", new Date());
        log.info("currentTimeMillis: {}", System.currentTimeMillis());
        log.info("zoneId: {}", ZoneId.systemDefault());
        log.info("zoneOffset: {}", ZoneOffset.systemDefault());
        log.info("Default Time Zone: {}", TimeZone.getDefault().getID());
        return BizResult.create(true);
    }
}
