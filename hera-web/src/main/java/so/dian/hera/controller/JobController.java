package so.dian.hera.controller;


import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.hera.common.enums.RefundEnum;
import so.dian.hera.domain.RefundJob;
import so.dian.hera.service.listener.RefundListener;

import java.time.Instant;
import java.util.Date;

@Slf4j
@RestController
@RequestMapping("job")
public class JobController {

    @Autowired
    private RefundListener refundListener;

    @GetMapping("refund/add")
    public void addRefundJob(@RequestParam("tradeNo") String tradeNo,
                             @RequestParam("refundNo") String refundNo,
                             @RequestParam("refundType") Integer refundType) {
        RefundEnum refundEnum = RefundEnum.explain(refundType);
        if (refundEnum == null) {
            throw new HeraBizException(HeraBizErrorCodeEnum.PARAM_ERROR.getCode(), "refundType error");
        }

        RefundJob job = new RefundJob();
        job.setTradeNo(tradeNo);
        job.setRefundNo(refundNo);
        job.setRefundType(refundType);
        Instant now = Instant.now();
        job.setCheckTime(Date.from(now.plusMillis(refundEnum.getIntervalTime())));
        job.setExpireTime(Date.from(now.plusMillis(refundEnum.getExpireTime())));
        refundListener.processJob(job);
    }
}