package so.dian.hera.controller;

import com.chargebolt.hera.client.api.PaySettingApi;
import com.chargebolt.hera.client.enums.PayMethodEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.google.common.collect.Lists;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.platform.airwallex.common.enmus.AirwallexPayMethodEnum;
import so.dian.platform.airwallex.config.AirwallexProperty;
import so.dian.platform.antom.common.enmus.AntomPayMethodEnum;
import so.dian.platform.antom.processor.AntomProperty;
import so.dian.platform.pingpong.common.enums.PingpongPayMethodEnum;
import so.dian.platform.pingpong.processor.PingpongProperty;
import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@RestController
public class PaySettingController implements PaySettingApi {

    @Resource
    private PingpongProperty pingpongProperty;

    @Resource
    private AntomProperty antomProperty;

    @Resource
    private AirwallexProperty airwallexProperty;

    @Override
    public BizResult<List<PayMethodEnum>> getWooshpaySupportsCards() {
        return BizResult.create(Lists.newArrayList(
                PayMethodEnum.MasterCard,
                PayMethodEnum.VISA
        ));
    }

    @Override
    public BizResult<List<PayMethodEnum>> getPingpongSupportsCards() {
        List<PayMethodEnum> payMethodEnums = Lists.newArrayList();
        for (PingpongPayMethodEnum card : pingpongProperty.getPaymentMethodsOfCard()) {
            payMethodEnums.add(card.getPayMethodEnum());
        }
        return BizResult.create(payMethodEnums);
    }

    @Override
    public BizResult<List<PayMethodEnum>> getPingpongSupportsApms() {
        List<PayMethodEnum> payMethodEnums = Lists.newArrayList();
        for (PingpongPayMethodEnum apm : pingpongProperty.getPaymentMethodsOfApm()) {
            payMethodEnums.add(apm.getPayMethodEnum());
        }
        return BizResult.create(payMethodEnums);
    }

    public BizResult<List<PayMethodEnum>> getAntomSupportsCards() {
        List<PayMethodEnum> payMethodEnums = Lists.newArrayList();
        for (AntomPayMethodEnum card : antomProperty.getPaymentMethodsOfCard()) {
            payMethodEnums.add(card.getPayMethodEnum());
        }
        return BizResult.create(payMethodEnums);
    }

    public BizResult<List<PayMethodEnum>> getAntomSupportsApms() {
        List<PayMethodEnum> payMethodEnums = Lists.newArrayList();
        for (AntomPayMethodEnum apm : antomProperty.getPaymentMethodsOfApm()) {
            payMethodEnums.add(apm.getPayMethodEnum());
        }
        return BizResult.create(payMethodEnums);
    }

    public BizResult<List<PayMethodEnum>> getAirwallexSupportsCards() {
        List<PayMethodEnum> payMethodEnums = Lists.newArrayList();
        for (AirwallexPayMethodEnum apm : airwallexProperty.getPaymentMethodsOfCard()) {
            payMethodEnums.add(apm.getPayMethodEnum());
        }
        return BizResult.create(payMethodEnums);
    }

    @Override
    public BizResult<List<PayMethodEnum>> getPayMethods(Integer paywayEnum) {
        if (PaywayEnum.WOOSHPAY_CARD.getPayway().equals(paywayEnum)) {
            return getWooshpaySupportsCards();
        } else if (PaywayEnum.PINGPONG_CHECKOUT_CARD.getPayway().equals(paywayEnum)) {
            return getPingpongSupportsCards();
        } else if (PaywayEnum.PINGPONG_CHECKOUT_APM.getPayway().equals(paywayEnum)) {
            return getPingpongSupportsApms();
        } else if (PaywayEnum.PINGPONG_ONE_CLICK.getPayway().equals(paywayEnum)) {
            List<PayMethodEnum> payMethodEnums = Lists.newArrayList();
            payMethodEnums.add(PayMethodEnum.Alipay);
            payMethodEnums.add(PayMethodEnum.AlipayHK);
            return BizResult.create(payMethodEnums);
        } else if (PaywayEnum.ANTOM_CHECKOUT_CARD.getPayway().equals(paywayEnum)) {
            return getAntomSupportsCards();
        } else if (PaywayEnum.ANTOM_CHECKOUT_APM.getPayway().equals(paywayEnum)) {
            return getAntomSupportsApms();
        } else if (PaywayEnum.MIDTRANS_CHECKOUT_APM.getPayway().equals(paywayEnum)) {
            List<PayMethodEnum> payMethodEnums = Lists.newArrayList();
            payMethodEnums.add(PayMethodEnum.GoPay);
            payMethodEnums.add(PayMethodEnum.ShopeePay);
            payMethodEnums.add(PayMethodEnum.QRIS);
            return BizResult.create(payMethodEnums);
        } else if (PaywayEnum.AIRWALLEX_CHECKOUT_CARD.getPayway().equals(paywayEnum)) {
            return getAirwallexSupportsCards();
        } else {
            return BizResult.create(Collections.emptyList());
        }
    }
}
