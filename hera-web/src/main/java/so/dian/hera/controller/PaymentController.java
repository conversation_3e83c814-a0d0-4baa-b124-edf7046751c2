package so.dian.hera.controller;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.api.PaymentApi;
import com.chargebolt.hera.client.dto.pay.PageDTO;
import com.chargebolt.hera.client.dto.pay.QueryPageDTO;
import com.chargebolt.hera.client.dto.pay.QueryPageResultDTO;
import com.chargebolt.hera.client.dto.pay.basic.req.PaymentPayNoReq;
import com.chargebolt.hera.client.dto.pay.basic.req.PaymentQueryReq;
import com.chargebolt.hera.client.dto.pay.payment.PaymentDTO;
import com.chargebolt.hera.client.dto.pay.payment.PaymentExtInfoResponse;
import com.chargebolt.hera.client.dto.pay.payment.PaymentQueryDTO;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import okhttp3.Cache;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.hera.common.OssPaymentConverter;
import so.dian.hera.domain.context.PaymentExtCacheContext;
import so.dian.hera.service.pay.PaymentCacheService;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.hera.service.persist.db.PaymentQueryService;
import so.dian.hera.utils.AssertUtils;
import so.dian.platform.common.configuration.redis.RedisClient;
import so.dian.platform.common.enums.CacheEnum;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * PaymentController
 *
 * <AUTHOR>
 * @desc 支付凭证分库分表查询相关
 * @date 17/11/18
 */
@Api(value = "PaymentController", description = "支付凭证分库分表相关API", tags = "PaymentApi")
@Slf4j
@RestController
public class PaymentController implements PaymentApi {

    @Autowired
    private PaymentQueryService paymentQueryService;
    @Resource
    private PaymentPersistService paymentPersistService;
    @Resource
    private RedisClient redisClient;
    @Resource
    private PaymentCacheService paymentCacheService;

    @Resource
    private so.dian.hera.service.persist.db.PaymentPersistService paymentService;

    @Override
    @ApiOperation(value = "分库分表查询原支付凭证DTO")
    public BizResult<PaymentDTO> get(@RequestParam("tradeNo") String tradeNo) {
        AssertUtils.notBlankWithBizExp(tradeNo, HeraBizErrorCodeEnum.TRADE_NO_BLANK);
        // PaymentDO paymentDO = paymentQueryService.getByTradeNo(tradeNo);
        // 查询订单主库
        PaymentDO paymentDO = paymentService.getPaymentByTradeNo(tradeNo);
        PaymentDTO paymentDTO = Objects.nonNull(paymentDO) ? OssPaymentConverter.convert(paymentDO) : null;
        log.info("根据支付单号查询主库订单信息 tradeNo: {},paymentDTO: {}", tradeNo, JSON.toJSONString(paymentDTO));
        return BizResult.create(paymentDTO);
    }

    @Override
    public BizResult<PaymentDTO> getByCondition(@RequestBody PaymentQueryReq queryReq) {
        log.info("查询支付单请求参数 queryReq: {}", JSON.toJSONString(queryReq));
        AssertUtils.notNullWithBizExp(queryReq, HeraBizErrorCodeEnum.PARAM_ERROR);
        AssertUtils.trueWithBizExp(
                StringUtils.isBlank(queryReq.getBizNo()) && StringUtils.isBlank(queryReq.getTradeNo()),
                HeraBizErrorCodeEnum.PARAM_ERROR);
        AssertUtils.trueWithBizExp(
                Objects.equals(queryReq.getTradeNo(), "null") && StringUtils.isBlank(queryReq.getBizNo()),
                HeraBizErrorCodeEnum.PARAM_ERROR);
        PaymentDO paymentDO = paymentQueryService.get(queryReq);
        PaymentDTO paymentDTO = null;
        if (paymentDO == null) {
            if (StringUtils.isNotBlank(queryReq.getTradeNo())) {
                paymentDO = paymentPersistService.getPaymentByTradeNo(queryReq.getTradeNo());
            }
            paymentDTO = Objects.nonNull(paymentDO) ? OssPaymentConverter.convert(paymentDO) : null;
        } else {
            paymentDTO = Objects.nonNull(paymentDO) ? OssPaymentConverter.convert(paymentDO) : null;
        }
        return BizResult.create(paymentDTO);
    }

    @Override
    public BizResult<List<PaymentDTO>> query(@RequestBody QueryPageDTO<PaymentQueryDTO> queryDTO) {
        AssertUtils.notNullWithBizExp(queryDTO, HeraBizErrorCodeEnum.PARAM_ERROR);
        PaymentQueryDTO paymentQueryDTO = queryDTO.getCondition();
        PageDTO pageInfo = queryDTO.getPageInfo();
        if (paymentQueryDTO == null) {
            paymentQueryDTO = new PaymentQueryDTO();
        }
        if (pageInfo == null) {
            pageInfo = new PageDTO();
        }

        List<PaymentDO> result = paymentQueryService.getByQuery(paymentQueryDTO, pageInfo);
        if (result.isEmpty()) {
            return BizResult.create(Collections.emptyList());
        }
        return BizResult.create(OssPaymentConverter.convert(result));
    }

    @Override
    public BizResult<QueryPageResultDTO<PaymentDTO>> queryByPage(@RequestBody QueryPageDTO<PaymentQueryDTO> queryDTO) {
        PaymentQueryDTO condition = queryDTO.getCondition();
        if (condition == null) {
            condition = new PaymentQueryDTO();
        }

        int total = paymentQueryService.getTotal(condition);
        if (total == 0) {
            return BizResult.create(QueryPageResultDTO.empty());
        }

        PageDTO pageInfo = queryDTO.getPageInfo();
        if (pageInfo == null) {
            pageInfo = new PageDTO();
        }
        List<PaymentDO> data = paymentQueryService.getByQuery(condition, pageInfo);
        QueryPageResultDTO<PaymentDTO> result = new QueryPageResultDTO<>(OssPaymentConverter.convert(data), total);
        return BizResult.create(result);
    }

    @Override
    public BizResult<Integer> total(@RequestBody PaymentQueryDTO queryDTO) {
        AssertUtils.notNullWithBizExp(queryDTO, HeraBizErrorCodeEnum.PARAM_ERROR);
        int result = paymentQueryService.getTotal(queryDTO);
        return BizResult.create(result);
    }

    @Override
    public BizResult<Integer> updatePayNo(final PaymentPayNoReq request) {
        PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(request.getTradeNo());
        AssertUtils.notNullWithBizExp(paymentDO, HeraBizErrorCodeEnum.PAYMENT_NOT_EXIST);
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setPayNo(request.getPayNo());
        paymentPersistService.updateStatus(paymentDO);
        return BizResult.create(1);
    }

    @Override
    public BizResult<String> getBindingDeviceNo(final String tradeNo) {
        String deviceNo = redisClient.get(CacheEnum.PAYMENT_DEVICE_MAPPING.getNs(), tradeNo, String.class);
        return BizResult.create(deviceNo);
    }

    @Override
    public BizResult<PaymentExtInfoResponse> getPaymentExtInfo(final String tradeNo) {
        PaymentExtCacheContext contextExt = paymentCacheService.getPaymentExtCache(tradeNo);
        PaymentExtInfoResponse response = new PaymentExtInfoResponse();
        BeanUtils.copyProperties(contextExt, response);
        return BizResult.create(response);
    }

}
