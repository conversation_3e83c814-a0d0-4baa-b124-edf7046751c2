package so.dian.hera.controller;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.api.RefundApi;
import com.chargebolt.hera.client.dto.pay.refund.req.RefundFeeRequest;
import com.chargebolt.hera.client.dto.pay.refund.req.RefundRequest;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundDTO;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundFeeResponse;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundResultDTO;
import com.chargebolt.hera.domain.RefundChannelFeeRecord;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.hera.service.refund.RefundChannelFeeRecordService;
import so.dian.hera.service.refund.RefundService;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Api(value = "RefundController", tags = "RefundApi")
@Slf4j
@RestController
public class RefundController implements RefundApi {

    @Resource
    private RefundService refundService;
    @Resource
    private RefundChannelFeeRecordService refundChannelFeeRecordService;

    @Override
    @ApiOperation(value = "退款")
    public BizResult<RefundResultDTO> refund(@RequestBody RefundRequest req) {
        if (StringUtils.isEmpty(req.getTradeNo())) {
            throw new HeraBizException(HeraBizErrorCodeEnum.PARAM_ERROR.getCode(), "tradeNo is null");
        }
        if (req.getAmount() == null) {
            throw new HeraBizException(HeraBizErrorCodeEnum.PARAM_ERROR.getCode(), "amount is null");
        }
        if (req.getAmount() <= 0) {
            throw new HeraBizException(HeraBizErrorCodeEnum.PARAM_ERROR.getCode(), "amount < 0");
        }
        if (StringUtils.isEmpty(req.getSystem())) {
            throw new HeraBizException(HeraBizErrorCodeEnum.PARAM_ERROR.getCode(), "system is null");
        }
        log.info("退款请求: {}", JSON.toJSONString(req));
        return BizResult.create(refundService.refund(req));
    }

    @Override
    @ApiOperation(value = "最新退款记录")
    public BizResult<RefundDTO> getLast(@RequestParam("tradeNo") String tradeNo, @RequestParam("orderNo") String orderNo) {
        return BizResult.create(refundService.getLastRefund(tradeNo, orderNo));
    }

    @Override
    @ApiOperation(value = "退款记录")
    public BizResult<List<RefundDTO>> record(@RequestParam("tradeNo") String tradeNo, @RequestParam("orderNo") String orderNo) {
        return BizResult.create(refundService.getRecordList(tradeNo, orderNo));
    }

    @Override
    public BizResult<Boolean> delayRefund(final RefundRequest req) {
        refundService.handleRefundDelay(req);
        return BizResult.create(Boolean.TRUE);
    }

    @Override
    public BizResult<String> saveRefundFee(final RefundFeeRequest request) {
        if (org.apache.commons.lang3.StringUtils.isBlank(request.getTradeNo())){
            throw new HeraBizException(HeraBizErrorCodeEnum.PARAM_ERROR.getCode(), "tradeNo is null");
        }
        if (Objects.isNull(request.getAmount())){
            throw new HeraBizException(HeraBizErrorCodeEnum.PARAM_ERROR.getCode(), "amount is null");
        }
        if (request.getAmount() <= 0) {
            throw new HeraBizException(HeraBizErrorCodeEnum.PARAM_ERROR.getCode(), "amount < 0");
        }
        if(Objects.isNull(request.getAgentId())){
            throw new HeraBizException(HeraBizErrorCodeEnum.PARAM_ERROR.getCode(), "agent id is null");
        }
        if(Objects.isNull(request.getSourceType())){
            throw new HeraBizException(HeraBizErrorCodeEnum.PARAM_ERROR.getCode(), "source type is null");
        }
        refundChannelFeeRecordService.saveRefundChannelFeeRecord(request);
        return BizResult.create(request.getTradeNo());
    }

    @Override
    public BizResult<List<RefundFeeResponse>> getRefundChannelFeeRecord(final String tradeNo) {
        return BizResult.create(refundChannelFeeRecordService.getRefundChannelFeeRecord(tradeNo));
    }
}
