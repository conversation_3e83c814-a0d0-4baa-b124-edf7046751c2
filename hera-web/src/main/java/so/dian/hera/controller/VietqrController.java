/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.controller;

import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.domain.PaymentVietqrMappingDO;
import com.chargebolt.hera.domain.RefundDO;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.chargebolt.zeus.controller.request.VietqrRechargeRequest;
import io.jsonwebtoken.SignatureAlgorithm;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.hera.controller.vietqr.AccessToken;
import so.dian.hera.controller.vietqr.VietQRAuth;
import so.dian.hera.controller.vietqr.VietQRCallbackDTO;
import so.dian.hera.controller.vietqr.VietQRCallbackResponse;
import so.dian.hera.dao.rds.hera.PaymentVietqrMappingMapper;
import so.dian.hera.remote.lvy.UserAccountManager;
import so.dian.hera.remote.lvy.api.LoanClient;
import so.dian.hera.remote.user.UserManager;
import so.dian.hera.service.pay.apm.ApmPayService;
import so.dian.hera.service.pay.vietqr.PaymentVietqrMappingService;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.hera.service.persist.db.RefundPersistService;
import so.dian.hera.utils.TradeNoGenerator;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.common.configuration.redis.RedisClient;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.time.ZoneId;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import io.jsonwebtoken.Jwts;
import so.dian.platform.common.utils.BizUtil;
import so.dian.prometheus.client.dto.LoginRequestDTO;
import so.dian.prometheus.client.dto.UserDTO;

/**
 * vietqr交互接口
 * path不能修改
 *
 * <AUTHOR>
 * @version: VietqrController.java, v 1.0 2024-05-20 下午2:16 Exp $
 */
@Slf4j
@RestController
public class VietqrController {
    @Resource
    private RedisClient redisClient;
    @Resource
    private LoanClient loanClient;
    @Resource
    private UserManager userManager;
    @Resource
    private ApmPayService apmPayService;
    @Resource
    private UserAccountManager userAccountManager;
    @Resource
    private PaymentPersistService paymentPersistService;
    @Autowired
    private PaymentVietqrMappingService paymentVietqrMappingService;
    @Autowired
    private RefundPersistService refundPersistService;
    /**
     * api 请求token获取，强制要求expiresIn=59
     *
     * @param request
     * @return
     */
    @PostMapping("/vqr/api/token_generate")
    public Object getAccessToken(HttpServletRequest request) {

        String authorization = request.getHeader("Authorization");
        String encodedCredentials = authorization.substring("Basic ".length()).trim();
        byte[] decodedBytes = Base64.getDecoder().decode(encodedCredentials);
        String credentials = new String(decodedBytes, StandardCharsets.UTF_8);
        String[] parts = credentials.split(":");
        String username = parts[0];
        String password = parts.length > 1 ? parts[1] : "";
//        String userName= "customer-bl-user05";
//        String pwd= "Y3VzdG9tZXItYmwtdXNlcjA1";

        if(!Objects.equals(username, "vietqr-dev-erx01")
                && !Objects.equals(password, "dmlldHFyLWFwaS1hY3ZpMHNxZw==")){
            VietQRCallbackResponse response= new VietQRCallbackResponse();
            response.setError(Boolean.TRUE);
            response.setErrorReason("999");
            response.setToastMessage("Username or password incorrect.");
            return response;
        }
//        byte[] encodedBytes =Base64.getEncoder().encode((username+":"+password+":"+ radomStr).getBytes());
//        String encodedString = new String(encodedBytes, StandardCharsets.UTF_8);

        String base64Credentials = Base64.getEncoder().encodeToString((username + ":" + password).getBytes());
        log.info("base64Credentials:{}",base64Credentials);
        String jwtToken = Jwts.builder()
                .setSubject(username) // 主题可以是用户名或其他标识符
                .setIssuedAt(new Date()) // 设置签发时间
                .setExpiration(new DateBuild().addToMin(1).toDate()) // 设置过期时间
                .signWith(SignatureAlgorithm.HS256, base64Credentials.getBytes()) // 使用Base64编码的凭据作为密钥
                .compact(); // 生成最终的JWT字符串

        AccessToken token= new AccessToken();
        token.setAccessToken(jwtToken);
        token.setTokenType("Bearer");
        token.setExpiresIn(59);
        redisClient.set("viteqr", username+password, jwtToken, 59);
        return token;
    }

    /**
     * vietQR支付结果通知接口
     *
     * @param requestBody
     * @return
     */
    @VietQRAuth
    @ApiOperation("viet QR")
    @PostMapping("/vqr/bank/api/transaction-sync")
    public VietQRCallbackResponse vietqrCallBack(@RequestBody Map<String, Object> requestBody) {
        log.info("vietqr callback request body:{}", JsonUtil.beanToJson(requestBody));
//        transType=D terminalCode is null需要考虑
        String reqStr= JsonUtil.beanToJson(requestBody);
        VietQRCallbackDTO vietQRCallbackDTO= JsonUtil.jsonToBean(reqStr, VietQRCallbackDTO.class);

        VietQRCallbackResponse response= new VietQRCallbackResponse();
        response.setError(Boolean.FALSE);
        response.setErrorReason("000");
        response.setToastMessage("");
        Map<String, String> obj= new HashMap<>();
        if(Objects.equals(vietQRCallbackDTO.getTransType(), "D")|| StringUtils.isBlank(vietQRCallbackDTO.getTerminalCode())){
            response.setToastMessage("The transaction does not comply with business rules.");
            // 退款单号返回
            RefundDO refundDO= refundPersistService.getRefundByOutTraceNo(vietQRCallbackDTO.getReferenceNumber());
            if(Objects.isNull(refundDO)){
                // 不存在，随机返回，可能不是业务产生，账户变动会有此消息
                SecureRandom secureRandom = new SecureRandom();
                long randomNumber = 10000 + secureRandom.nextInt(90000);
                obj.put("reftransactionid", TradeNoGenerator.generateRefundNo(PaywayEnum.VIETQR_STATIC.getPayway(),
                        randomNumber));
            }else{
                obj.put("reftransactionid", refundDO.getRefundNo());
            }
            response.setObject(obj);
            log.info("response data：{}", JsonUtil.beanToJson(response));
            return response;
        }
        // 重复请求处理
        PaymentDO paymentDO= paymentPersistService.getPaymentPOByPayNo(vietQRCallbackDTO.getTransactionId());
        if(Objects.nonNull(paymentDO)){
            response.setToastMessage("The transaction has already been processed.");
            obj.put("reftransactionid", paymentDO.getTradeNo());
            response.setObject(obj);
            log.info("response data：{}", JsonUtil.beanToJson(response));
            return response;
        }
        if (StringUtils.isNotBlank(vietQRCallbackDTO.getTerminalCode())){
            // 创建用户
            LoginRequestDTO loginRequestDTO= new LoginRequestDTO();
            loginRequestDTO.setNick("vietqr");
            UserDTO userDTO= userManager.vietqrLogin(loginRequestDTO);
            // 创建支付订单
            PaymentDO payment= apmPayService.createVietqrPayment(buildVietqrPayment(vietQRCallbackDTO, userDTO.getId()),
                    vietQRCallbackDTO.getTransactionId());

            // 创建订单关联
            PaymentVietqrMappingDO vietqrMappingDO= new PaymentVietqrMappingDO();
            vietqrMappingDO.setTradeNo(payment.getTradeNo());
            vietqrMappingDO.setTransactionId(vietQRCallbackDTO.getTransactionId());
            vietqrMappingDO.setReferenceNumber(vietQRCallbackDTO.getReferenceNumber());
            vietqrMappingDO.setBankAccount(vietQRCallbackDTO.getBankAccount());
            paymentVietqrMappingService.saveRecord(vietqrMappingDO);
            // 押金充值
            userAccountManager.vietqrRechargeDeposit(buildVietqrRechargeRequest(vietQRCallbackDTO, payment));
            // 弹出充电宝
            loanClient.vietqrLoan(vietQRCallbackDTO.getTerminalCode(), payment.getTradeNo(), userDTO.getId());
            obj.put("reftransactionid", payment.getTradeNo());
        }
        response.setObject(obj);
        log.info("response data：{}", JsonUtil.beanToJson(response));

        return response;
    }


//    @VietQRAuth
//    @ApiOperation("viet QR")
    @PostMapping("/vqr/bank/api/transaction-sync-1")
    public VietQRCallbackResponse vietqrCallBack_1(@RequestBody Map<String, Object> requestBody) {
        log.info("vietqr callback request body:{}", JsonUtil.beanToJson(requestBody));

//        transType=D terminalCode is null需要考虑

        String reqStr= JsonUtil.beanToJson(requestBody);
        VietQRCallbackDTO vietQRCallbackDTO= JsonUtil.jsonToBean(reqStr, VietQRCallbackDTO.class);

        VietQRCallbackResponse response= new VietQRCallbackResponse();
        response.setError(Boolean.FALSE);
        response.setErrorReason("000");
        response.setToastMessage("");
        if(Objects.equals(vietQRCallbackDTO.getTransType(),"D")|| StringUtils.isBlank(vietQRCallbackDTO.getTerminalCode())){
            response.setToastMessage("The transaction does not comply with business rules.");
            return response;
        }
        Map<String, String> obj= new HashMap<>();
        // 重复请求处理
        PaymentDO paymentDO= paymentPersistService.getPaymentPOByPayNo(vietQRCallbackDTO.getTransactionId());
        if(Objects.nonNull(paymentDO)){
            response.setToastMessage("The transaction has already been processed.");
            obj.put("reftransactionid", paymentDO.getTradeNo());
            response.setObject(obj);
            return response;
        }
        if (StringUtils.isNotBlank(vietQRCallbackDTO.getTerminalCode())){
            // 创建用户
            LoginRequestDTO loginRequestDTO= new LoginRequestDTO();
            loginRequestDTO.setNick("vietqr");
            UserDTO userDTO= userManager.vietqrLogin(loginRequestDTO);
            // 创建支付订单
            PaymentDO payment= apmPayService.createVietqrPayment(buildVietqrPayment(vietQRCallbackDTO, userDTO.getId()),
                    vietQRCallbackDTO.getTransactionId());
            // 创建订单关联
            PaymentVietqrMappingDO vietqrMappingDO= new PaymentVietqrMappingDO();
            vietqrMappingDO.setTradeNo(payment.getTradeNo());
            vietqrMappingDO.setTransactionId(vietQRCallbackDTO.getTransactionId());
            vietqrMappingDO.setReferenceNumber(vietQRCallbackDTO.getReferenceNumber());
            vietqrMappingDO.setBankAccount(vietQRCallbackDTO.getBankAccount());
            paymentVietqrMappingService.saveRecord(vietqrMappingDO);
            // 押金充值
            userAccountManager.vietqrRechargeDeposit(buildVietqrRechargeRequest(vietQRCallbackDTO, payment));
            // 弹出充电宝
            loanClient.vietqrLoan(vietQRCallbackDTO.getTerminalCode(), payment.getTradeNo(), userDTO.getId());
            obj.put("reftransactionid", payment.getTradeNo());
        }
        response.setObject(obj);
        log.info("response data：{}", JsonUtil.beanToJson(response));

        return response;
    }

    private VietqrRechargeRequest buildVietqrRechargeRequest(VietQRCallbackDTO vietQRCallbackDTO, PaymentDO paymentDO){
        VietqrRechargeRequest request= new VietqrRechargeRequest();
        request.setUserId(paymentDO.getUserId());
        request.setAmount(vietQRCallbackDTO.getAmount());
        // 初始0
        request.setCurrentWalletMoney(0L);
        request.setTradeNo(paymentDO.getTradeNo());
        request.setDeviceNo(vietQRCallbackDTO.getTerminalCode());
        request.setBizDesc("vietqr");
        return request;
    }
    private PrepayCreateRequest buildVietqrPayment(VietQRCallbackDTO vietQRCallbackDTO, Long userId){
        PrepayCreateRequest prepayCreateRequest= new PrepayCreateRequest();
        prepayCreateRequest.setCurrency("VND");
        prepayCreateRequest.setPayAmount(vietQRCallbackDTO.getAmount().intValue());
        prepayCreateRequest.setDelayTime(10 * 60 * 60 * 24L);
        prepayCreateRequest.setIsAutoCancel(Boolean.TRUE);
        prepayCreateRequest.setUserId(userId);
        prepayCreateRequest.setCooperatorId(1L);
        prepayCreateRequest.setSystem("vietqr");
        prepayCreateRequest.setIsNotify(Boolean.TRUE);
        prepayCreateRequest.setReqDate(new DateBuild(vietQRCallbackDTO.getTransactionTime()).toDate());
        prepayCreateRequest.setPayway(PaywayEnum.VIETQR_STATIC);
        prepayCreateRequest.setBizNo(BizUtil.generateTradeNo(PaymentBizTypeEnum.PAY_DEPOSIT, userId));
        prepayCreateRequest.setBizType(PaymentBizTypeEnum.PAY_DEPOSIT.getCode());
//        apmPayService.createVietqrPayment();
        return prepayCreateRequest;
    }
}
