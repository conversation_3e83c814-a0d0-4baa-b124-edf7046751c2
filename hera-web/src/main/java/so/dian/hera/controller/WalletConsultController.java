package so.dian.hera.controller;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RestController;

import com.chargebolt.hera.client.api.WalletConsultApi;
import com.chargebolt.hera.client.dto.pay.apmconsult.ConsultFreezeApmMoneyRequest;
import com.chargebolt.hera.client.dto.pay.apmconsult.ConsultFreezeResponse;

import lombok.extern.slf4j.Slf4j;
import so.dian.commons.eden.entity.BizResult;
import so.dian.hera.service.pay.wallet.WalletConsultService;


@Slf4j
@RestController
public class WalletConsultController implements WalletConsultApi {

    @Resource
    private WalletConsultService walletConsultService;

    @Override
    public BizResult<ConsultFreezeResponse> consultFreeze(ConsultFreezeApmMoneyRequest request) {
        log.info("收到钱包预授权请求令牌并冻结余额通知消息 request:{}", request);
        return BizResult.create(walletConsultService.consultFreeze(request));
    }



}
