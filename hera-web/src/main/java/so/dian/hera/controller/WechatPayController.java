package so.dian.hera.controller;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.api.WechatPayApi;
import com.chargebolt.hera.client.dto.pay.req.*;
import com.chargebolt.hera.client.dto.pay.rsp.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.hera.service.pay.gateway.GatewayPayService;
import so.dian.platform.wechat.processor.RefundRechargeBalanceQueryProcessor;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 统一支付下单接口
 * <AUTHOR>
 */
@Slf4j
@RestController
public class WechatPayController implements WechatPayApi {

    @Resource
    private GatewayPayService gatewayPayService;
    @Resource
    private RefundRechargeBalanceQueryProcessor refundRechargeBalanceQueryProcessor;

    @Override
    @ApiOperation("支付预下单")
    public BizResult<OrderPayRsp> doPayOrder(@RequestBody @Valid OrderPayReq req) {
        log.info("doOrderPay支付请求，req:{}", JSON.toJSONString(req));
        OrderPayRsp rsp = gatewayPayService.payOrder(req);
        log.info("doOrderPay支付响应，rsp:{}", JSON.toJSONString(rsp));
        return BizResult.create(rsp);
    }

    @Override
    @ApiOperation("支付关单")
    public BizResult<OrderCloseRsp> doCloseOrder(@RequestBody @Valid OrderCloseReq req) {
        log.info("doCloseOrder支付请求，req:{}", JSON.toJSONString(req));
        OrderCloseRsp rsp = gatewayPayService.closeOrder(req);
        log.info("doCloseOrder支付响应，rsp:{}", JSON.toJSONString(rsp));
        return BizResult.create(rsp);
    }

    @Override
    @ApiOperation("支付单查询")
    public BizResult<OrderPayQueryRsp> doQueryOrder(@RequestBody @Valid OrderPayQueryReq req) {
        log.info("doOrderQuery支付查询请求，req:{}", JSON.toJSONString(req));
        OrderPayQueryRsp rsp = gatewayPayService.queryPayOrder(req);
        log.info("doOrderQuery支付查询响应，rsp:{}", JSON.toJSONString(rsp));
        return BizResult.create(rsp);
    }

    @Deprecated
    @Override
    @ApiOperation("支付单退款")
    public BizResult<OrderRefundRsp> doRefundOrder(@RequestBody @Valid OrderRefundReq req) {
        log.info("doOrderRefund退款请求，req:{}", JSON.toJSONString(req));
        OrderRefundRsp rsp = gatewayPayService.refundOrder(req);
        log.info("doOrderRefund退款响应，rsp:{}", JSON.toJSONString(rsp));
        return BizResult.create(rsp);
    }

    @Override
    @ApiOperation("退款单查询")
    public BizResult<OrderRefundQueryRsp> doQueryRefund(@RequestBody @Valid OrderRefundQueryReq req) {
        log.info("doRefundQuery退款查询请求，req:{}", JSON.toJSONString(req));
        OrderRefundQueryRsp rsp = gatewayPayService.queryRefundOrder(req);
        log.info("doRefundQuery退款查询响应，rsp:{}", JSON.toJSONString(rsp));
        return BizResult.create(rsp);
    }

    @ApiOperation("查询充值余额")
    @GetMapping(value = "/refund/recharge-balance")
    public BizResult<String> doQueryRechargeBalance() {
        log.info("doQueryRechargeBalance查询充值余额请求");
        return BizResult.create(refundRechargeBalanceQueryProcessor.queryRechargeBalance());
    }

}

