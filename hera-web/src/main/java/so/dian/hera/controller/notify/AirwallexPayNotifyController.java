package so.dian.hera.controller.notify;

import com.alibaba.fastjson.JSONObject;
import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.domain.notify.ChannelNotifyBody;
import com.chargebolt.hera.domain.notify.RefundNotifyBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.hera.interceptor.utils.DateUtil;
import so.dian.hera.service.pay.bankcard.BankcardPayService;
import so.dian.hera.service.refund.RefundService;
import so.dian.platform.airwallex.common.enmus.EventTypeEnum;
import so.dian.platform.airwallex.config.AirwallexProperty;
import so.dian.platform.airwallex.dto.notify.EventNotify;
import so.dian.platform.airwallex.dto.notify.PaymentAttemptEventDataObject;
import so.dian.platform.airwallex.dto.notify.PaymentIntentEventDataObject;
import so.dian.platform.airwallex.dto.notify.RefundEventDataObject;
import so.dian.platform.common.utils.MACUtil;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@SuppressWarnings("all")
public class AirwallexPayNotifyController {

    @Resource
    private AirwallexProperty airwallexProperty;
    @Resource
    private BankcardPayService bankcardPayService;
    @Resource
    private RefundService refundService;

    @PostMapping(value = {"/notify/airwallex/normal"})
    public void payNotify(HttpServletRequest request, @RequestBody String eventBodyString) {
        log.info("Axwallex webhook notify requestBodyString: {}", eventBodyString);
        // 从http请求中检索所需的参数
        String requestUri = request.getRequestURI();
        String requestMethod = request.getMethod();

        // 从请求标头中检索所需的参数
        String timestamp = request.getHeader("x-timestamp");
        String signature = request.getHeader("x-signature");

        try{
            String macRes = MACUtil.sha256Mac(timestamp + eventBodyString, airwallexProperty.getApiKey());
            if (Objects.equals(macRes, signature)) {
                log.error("空中云汇 webhook 回调通知签名验证失败");
                return;
            }
            EventNotify eventNotifyObj = JSONObject.parseObject(eventBodyString, EventNotify.class);

            if (EventTypeEnum.PAYMENT_INTENT_CANCELLED.getEventType().equals(eventNotifyObj.getName())) {
                log.info("空中云汇交易订单取消通知");
                PaymentIntentEventDataObject intentEvent = JSONObject.parseObject(eventNotifyObj.getData().getObject(), PaymentIntentEventDataObject.class);
                ChannelNotifyBody channelNotifyBody = new ChannelNotifyBody();
                channelNotifyBody.setRefTradeNo(intentEvent.getMerchantOrderId());
                channelNotifyBody.setRefPayNo(intentEvent.getPaymentIntentId());
                channelNotifyBody.setOuterNo(intentEvent.getPaymentIntentId());
                channelNotifyBody.setCurrentTradeNo(intentEvent.getMerchantOrderId());
                channelNotifyBody.setPayway(PaywayEnum.AIRWALLEX_CHECKOUT_CARD);
                channelNotifyBody.setDate(DateUtil.transferDateV2(intentEvent.getUpdatedAt()));
                channelNotifyBody.setStatus(PayStatus.CANCEL);
                // 没有异步
                bankcardPayService.handleCanceledSelfMsg(channelNotifyBody);
            } else if (EventTypeEnum.PAYMENT_ATTEMPT_AUTHENTICATION_FAILED.getEventType().equals(eventNotifyObj.getName())) {
                log.warn("空中云汇预授权验证失败通知");
                // 非终态，用户可重试验证操作，这里不做处理
            } else if (EventTypeEnum.PAYMENT_ATTEMPT_AUTHORIZATION_FAILED.getEventType().equals(eventNotifyObj.getName())) {
                log.info("空中云汇预授权失败通知");
                PaymentAttemptEventDataObject attemptEvent = JSONObject.parseObject(eventNotifyObj.getData().getObject(),PaymentAttemptEventDataObject.class);
                ChannelNotifyBody channelNotifyBody = new ChannelNotifyBody();
                channelNotifyBody.setRefTradeNo(attemptEvent.getMerchantOrderId());
                channelNotifyBody.setRefPayNo(attemptEvent.getPaymentIntentId());
                channelNotifyBody.setCurrentTradeNo(attemptEvent.getMerchantOrderId());
                channelNotifyBody.setOuterNo(attemptEvent.getPaymentIntentId());
                channelNotifyBody.setPayway(PaywayEnum.AIRWALLEX_CHECKOUT_CARD);
                channelNotifyBody.setDate(DateUtil.transferDateV2(attemptEvent.getUpdatedAt()));
                channelNotifyBody.setStatus(PayStatus.CARD_AUTH_FAILED);
                bankcardPayService.handlePreAuthFailedNotify(channelNotifyBody);
            } else if (EventTypeEnum.PAYMENT_ATTEMPT_AUTHORIZED.getEventType().equals(eventNotifyObj.getName())) {
                log.info("空中云汇预授权成功通知");
                PaymentAttemptEventDataObject attemptEvent = JSONObject.parseObject(eventNotifyObj.getData().getObject(),PaymentAttemptEventDataObject.class);
                ChannelNotifyBody channelNotifyBody = new ChannelNotifyBody();
                channelNotifyBody.setRefTradeNo(attemptEvent.getMerchantOrderId());
                channelNotifyBody.setRefPayNo(attemptEvent.getPaymentIntentId());
                channelNotifyBody.setCurrentTradeNo(attemptEvent.getMerchantOrderId());
                channelNotifyBody.setOuterNo(attemptEvent.getPaymentIntentId());
                channelNotifyBody.setPayway(PaywayEnum.AIRWALLEX_CHECKOUT_CARD);
                channelNotifyBody.setDate(DateUtil.transferDateV2(attemptEvent.getUpdatedAt()));
                channelNotifyBody.setStatus(PayStatus.CARD_AUTHORIZED);
                bankcardPayService.handlePreAuthCreationNotify(channelNotifyBody);
                bankcardPayService.handlePreAuthCreatedNotify(channelNotifyBody);
            } else if (EventTypeEnum.PAYMENT_ATTEMPT_CAPTURE_FAILED.getEventType().equals(eventNotifyObj.getName())) {
                log.info("空中云汇交易请款失败通知，请款会重试，这里不进行处理");
            } else if (EventTypeEnum.PAYMENT_INTENT_SUCCEEDED.getEventType().equals(eventNotifyObj.getName())) {
                log.info("空中云汇交易订单支付成功通知");
                PaymentIntentEventDataObject intentEvent = JSONObject.parseObject(eventNotifyObj.getData().getObject(), PaymentIntentEventDataObject.class);
                ChannelNotifyBody channelNotifyBody = new ChannelNotifyBody();
                channelNotifyBody.setRefTradeNo(intentEvent.getMerchantOrderId());
                channelNotifyBody.setRefPayNo(intentEvent.getPaymentIntentId());
                channelNotifyBody.setOuterNo(intentEvent.getPaymentIntentId());
                channelNotifyBody.setPayway(PaywayEnum.AIRWALLEX_CHECKOUT_CARD);
                channelNotifyBody.setDate(DateUtil.transferDateV2(intentEvent.getUpdatedAt()));
                channelNotifyBody.setStatus(PayStatus.PAID);
                bankcardPayService.asyncCapturedNotify(channelNotifyBody);
            } else if (EventTypeEnum.REFUND_SUCCEEDED.getEventType().equals(eventNotifyObj.getName())) {
                log.info("空中云汇退款成功通知");
                RefundEventDataObject refundEvent = JSONObject.parseObject(eventNotifyObj.getData().getObject(),RefundEventDataObject.class);
                RefundNotifyBody refundNotifyBody = new RefundNotifyBody();
                refundNotifyBody.setChannel(ChannelEnum.AIRWALLEX);
                // refundNo
                refundNotifyBody.setMerchantRefundNo(refundEvent.getRequestId());
                refundNotifyBody.setOuterRefundNo(refundEvent.getId());
                refundNotifyBody.setOuterPayNo(refundEvent.getPaymentIntentId());
                refundNotifyBody.setDate(DateUtil.transferDateV2(refundEvent.getUpdatedAt()));
                refundNotifyBody.setRefundStatus(true);
                // 发送自消费消息 RefundSelfMsgHandler
                refundService.asyncRefundNotify(refundNotifyBody);
            } else if (EventTypeEnum.REFUND_FAILED.getEventType().equals(eventNotifyObj.getName())) {
                log.info("空中云汇退款失败通知");
                RefundEventDataObject refundEvent = JSONObject.parseObject(eventNotifyObj.getData().getObject(), RefundEventDataObject.class);
                RefundNotifyBody refundNotifyBody = new RefundNotifyBody();
                refundNotifyBody.setChannel(ChannelEnum.AIRWALLEX);
                refundNotifyBody.setMerchantRefundNo(refundEvent.getRequestId());
                refundNotifyBody.setOuterRefundNo(refundEvent.getId());
                refundNotifyBody.setOuterPayNo(refundEvent.getPaymentIntentId());
                refundNotifyBody.setDate(DateUtil.transferDateV2(refundEvent.getUpdatedAt()));
                refundNotifyBody.setRefundStatus(false);
                // 发送自消费消息 RefundSelfMsgHandler
                refundService.asyncRefundNotify(refundNotifyBody);
            } else {
                log.info("空中云汇通知类型暂无处理逻辑,notify.name={}", eventNotifyObj.getName());
            }
        } catch (Exception e) {
            log.error("Axwallex webhook notify error", e);
            throw new RuntimeException(e);
        }

    }
}
