package so.dian.hera.controller.notify;

import com.alibaba.fastjson.JSON;
import com.alipay.global.api.model.Result;
import com.alipay.global.api.model.ResultStatusType;
import com.alipay.global.api.model.ams.CustomerBelongsTo;
import com.alipay.global.api.response.AlipayResponse;
import com.alipay.global.api.response.ams.pay.AlipayPayResponse;
import com.alipay.global.api.tools.WebhookTool;
import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.client.enums.PayMethodEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.domain.notify.ChannelNotifyBody;
import com.chargebolt.hera.domain.notify.RefundNotifyBody;
import com.chargebolt.hera.domain.sharding.PaymentDO;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import so.dian.hera.interceptor.utils.DateUtil;
import so.dian.hera.service.pay.apm.ApmPayService;
import so.dian.hera.service.pay.bankcard.BankcardPayService;
import so.dian.hera.service.persist.db.PaymentQueryService;
import so.dian.hera.service.refund.RefundService;
import so.dian.platform.antom.common.enmus.AlipayResultStatusEnum;
import so.dian.platform.antom.common.enmus.AntomNotifyTypeEnum;
import so.dian.platform.antom.common.enmus.NotifyTypeEnum;
import so.dian.platform.antom.common.enmus.PaymentMethodTypeEnum;
import so.dian.platform.antom.common.enmus.RefundStatusEnum;
import so.dian.platform.antom.dto.*;
import so.dian.platform.antom.processor.AntomCheckoutHostedProcessor;
import so.dian.platform.antom.processor.AntomProperty;
import so.dian.platform.common.constants.BusinessConstants;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
public class AntomPayNotifyController {

    @Resource
    private AntomProperty antomProperty;

    @Resource
    private RefundService refundService;

    @Resource
    private ApmPayService apmPayService;

    @Resource
    private BankcardPayService bankcardPayService;
    @Resource
    private PaymentQueryService paymentQueryService;
    @Resource
    private AntomCheckoutHostedProcessor antomCheckoutHostedProcessor;

    @RequestMapping(value = { "/notify/antom/consult/{payMethod}/{deviceNo}" })
    public void consultNotify(HttpServletRequest request, HttpServletResponse response,
            @PathVariable(value = "payMethod", required = false) Integer payMethod,
            @PathVariable(value = "deviceNo", required = false) String deviceNo,
            @RequestParam(value = "authState", required = true) String authState,
            @RequestParam(value = "authCode", required = true) String authCode) throws IOException {
        log.info("收到antom预授权通知消息 authState = {},authCode = {}", authState, authCode);
        String customerBelongsTo = null;
        if (PayMethodEnum.Alipay.getId().equals(payMethod)) {
            customerBelongsTo = CustomerBelongsTo.ALIPAY_CN.name();
        } else if (PayMethodEnum.AlipayHK.getId().equals(payMethod)) {
            customerBelongsTo = CustomerBelongsTo.ALIPAY_HK.name();
        } else if (PayMethodEnum.GCash.getId().equals(payMethod)) {
            customerBelongsTo = CustomerBelongsTo.GCASH.name();
        }

        // 根据 authState 查询 paymentDO
        PaymentDO paymentDO = paymentQueryService.getByTradeNo(authState);
        log.info("paymentDO = {}", JSON.toJSONString(paymentDO));

        String accessToken = null;
        try {
            // 申请支付令牌
            accessToken = antomCheckoutHostedProcessor.applyToken(authCode, customerBelongsTo);
        } catch (Exception e) {
            log.error("antom applyToken fail. errorMsg = {}", e.getMessage());
            // response.setResult(buildAlipayResult(AlipayResultStatusEnum.ERROR,
            // ResultStatusType.F, e.getMessage()));
            // return response;
            response.sendRedirect(antomProperty.getPayResultUrl().getH5() + "?deviceNo="
                    + deviceNo + "&merchantTransactionId=" + paymentDO.getTradeNo());
            return;
        }

        // 发起钱包余额冻结操作
        AlipayPayResponse freezeResponse = antomCheckoutHostedProcessor.freezeApmBalance(paymentDO, accessToken,
                customerBelongsTo);
        if (!Objects.equals(freezeResponse.getResult().getResultCode(), AlipayResultStatusEnum.SUCCESS.name())) {
            log.info("antom 预授权失败,result = {},channelNotifyBody = {}", JSON.toJSONString(freezeResponse.getResult()));
            // response.setResult(buildAlipayResult(AlipayResultStatusEnum.ERROR,
            // ResultStatusType.F, ""));
            // return response;
            response.sendRedirect(antomProperty.getPayResultUrl().getH5() + "?deviceNo="
                    + paymentDO.getDeviceNo() + "&merchantTransactionId=" + paymentDO.getTradeNo());
            return;
        }

        // 业务处理逻辑
        ChannelNotifyBody channelNotifyBody = new ChannelNotifyBody();
        channelNotifyBody.setCurrentTradeNo(freezeResponse.getPaymentRequestId());
        channelNotifyBody.setOuterNo(freezeResponse.getPaymentId());
        channelNotifyBody.setPayway(PaywayEnum.ANTOM_CHECKOUT_CARD);
        if (StringUtils.isNotBlank(freezeResponse.getPaymentTime())) {
            channelNotifyBody.setDate(DateUtil.transferDate(freezeResponse.getPaymentTime()));
        }
        if (Objects.deepEquals(freezeResponse.getResult().getResultStatus(), ResultStatusType.S)) {
            channelNotifyBody.setStatus(PayStatus.CARD_AUTHORIZED);
            channelNotifyBody.setPayMethod(PayMethodEnum.explain(payMethod));
            // 更新payment授权结果
            bankcardPayService.handlePreAuthCreationNotify(channelNotifyBody);
            // 将授权结果同步给上有业务 kronos
            bankcardPayService.handlePreAuthCreatedNotify(channelNotifyBody);
            response.sendRedirect(antomProperty.getPayResultUrl().getH5() + "?deviceNo="
                    + deviceNo + "&merchantTransactionId=" + paymentDO.getTradeNo());
            return;
        } else if (Objects.equals(freezeResponse.getResult().getResultStatus(), ResultStatusType.F)) {
            // 更新支付单状态
            bankcardPayService.handlePreAuthFailedNotify(channelNotifyBody);
            response.sendRedirect(antomProperty.getPayResultUrl().getH5() + "?deviceNo="
                    + deviceNo + "&merchantTransactionId=" + paymentDO.getTradeNo());
            return;
        } else {
            response.sendRedirect(antomProperty.getPayResultUrl().getH5() + "?deviceNo="
                    + deviceNo + "&merchantTransactionId=" + paymentDO.getTradeNo());
            return;
        }
    }

    /**
     * antom 预授权结果通知
     * 
     * @param request
     * @param notifyBody
     * @param payMethod
     * @return
     */
    @RequestMapping(value = { "/notify/antom/consult/callback/{payMethod}" })
    public AlipayResponse consultAsyncNotify(HttpServletRequest request,
            @PathVariable(value = "payMethod") Integer payMethod,
            @RequestBody String notifyBody) {

        // 从http请求中检索所需的参数
        String requestUri = request.getRequestURI();
        String requestMethod = request.getMethod();

        // 从请求标头中检索所需的参数
        String requestTime = request.getHeader("request-time");
        String clientId = request.getHeader("client-id");
        String signature = request.getHeader("signature");

        log.info("收到 antom 授权结果通知消息 requestUri = {},requestMethod = {},requestTime = {},clientId = {},signature = {}",
                requestUri, requestMethod, requestTime, clientId, signature);

        AlipayResponse response = new AlipayResponse();
        try {
            // 核实通知的签名
            boolean verifyResult = WebhookTool.checkSignature(requestUri, requestMethod, clientId, requestTime,
                    signature, notifyBody, antomProperty.getAntomPublicKey());
            if (!verifyResult) {
                throw new RuntimeException("Invalid notify signature");
            }

            String customerBelongsTo = null;
            if (PayMethodEnum.Alipay.getId().equals(payMethod)) {
                customerBelongsTo = CustomerBelongsTo.ALIPAY_CN.name();
            } else if (PayMethodEnum.AlipayHK.getId().equals(payMethod)) {
                customerBelongsTo = CustomerBelongsTo.ALIPAY_HK.name();
            } else if (PayMethodEnum.GCash.getId().equals(payMethod)) {
                customerBelongsTo = CustomerBelongsTo.GCASH.name();
            }

            ConsultResultNotifyDTO notifyDTO = JSON.parseObject(notifyBody, ConsultResultNotifyDTO.class);

            // 钱包预授权走的是代扣的授权，这里只处理 AUTHCODE_CREATED 的场景
            if (NotifyTypeEnum.AUTHCODE_CREATED.name().equals(notifyDTO.getAuthorizationNotifyType())) {

                // 授权结果判断是否成功
                if (AlipayResultStatusEnum.isSuccess(notifyDTO.getResult())) {
                    String authCode = notifyDTO.getAuthCode();
                    String payTradeNo = notifyDTO.getAuthState();

                    PaymentDO paymentDO = paymentQueryService.getByTradeNo(payTradeNo);
                    // payment 状态判断是否已处理
                    if (PayStatus.CARD_AUTHORIZED.getCode().equals(paymentDO.getStatus())) {
                        log.info("支付单已处理，无需重复处理");
                        // 向支付宝服务器回复已收到通知且处理成功
                        response.setResult(buildAlipayResult(AlipayResultStatusEnum.SUCCESS, ResultStatusType.S, ""));
                        return response;
                    }

                    // 申请支付令牌
                    String accessToken = antomCheckoutHostedProcessor.applyToken(authCode, customerBelongsTo);

                    // 发起钱包余额冻结操作
                    AlipayPayResponse freezeResponse = antomCheckoutHostedProcessor.freezeApmBalance(paymentDO,
                            accessToken, customerBelongsTo);

                    ChannelNotifyBody channelNotifyBody = new ChannelNotifyBody();
                    channelNotifyBody.setCurrentTradeNo(freezeResponse.getPaymentRequestId());
                    channelNotifyBody.setOuterNo(freezeResponse.getPaymentId());
                    channelNotifyBody.setPayway(PaywayEnum.ANTOM_CHECKOUT_CARD);
                    if (StringUtils.isNotBlank(freezeResponse.getPaymentTime())) {
                        channelNotifyBody.setDate(DateUtil.transferDate(freezeResponse.getPaymentTime()));
                    }
                    if (Objects.deepEquals(freezeResponse.getResult().getResultStatus(), ResultStatusType.S)) {
                        channelNotifyBody.setStatus(PayStatus.CARD_AUTHORIZED);
                        channelNotifyBody.setPayMethod(PayMethodEnum.explain(payMethod));
                        // 更新payment授权结果
                        bankcardPayService.handlePreAuthCreationNotify(channelNotifyBody);
                        // 将授权结果同步给上有业务 kronos
                        bankcardPayService.handlePreAuthCreatedNotify(channelNotifyBody);
                        response.setResult(buildAlipayResult(AlipayResultStatusEnum.SUCCESS, ResultStatusType.S, ""));
                        return response;
                    } else if (Objects.equals(freezeResponse.getResult().getResultStatus(), ResultStatusType.F)) {
                        // 更新支付单状态
                        bankcardPayService.handlePreAuthFailedNotify(channelNotifyBody);
                        response.setResult(buildAlipayResult(AlipayResultStatusEnum.SUCCESS, ResultStatusType.S, ""));
                        return response;
                    } else {
                        // 需要重试的场景
                        response.setResult(buildAlipayResult(AlipayResultStatusEnum.ERROR, ResultStatusType.F, ""));
                        return response;
                    }
                }
            }
            // 向支付宝服务器回复已收到通知且处理成功
            response.setResult(buildAlipayResult(AlipayResultStatusEnum.SUCCESS, ResultStatusType.S, ""));
            return response;
        } catch (Exception e) {
            log.error("payNotify process error. ", e);
            // 向支付宝服务器回复已收到通知且处理异常
            response.setResult(buildAlipayResult(AlipayResultStatusEnum.ERROR, ResultStatusType.F, e.getMessage()));
            return response;
        }

    }

    /**
     * 通知验签：https://global.alipay.com/docs/ac/ams_zh-cn/digital_signature
     * 支付结果通知（银行卡授权&钱包支付）：https://global.alipay.com/docs/ac/ams_zh-cn/paymentrn_online
     * 请款通知：https://global.alipay.com/docs/ac/ams_zh-cn/notify_capture
     * 退款通知：https://global.alipay.com/docs/ac/ams_zh-cn/notify_refund
     *
     * @param request
     * @param notifyBody
     * @return
     */
    @RequestMapping(value = { "/notify/antom/{notifyType}/{payMethod}", "/notify/antom/{notifyType}" })
    public AlipayResponse payNotify(HttpServletRequest request, @RequestBody String notifyBody,
            @PathVariable("notifyType") String notifyType,
            @PathVariable(value = "payMethod", required = false) Integer payMethod) {
        // 从http请求中检索所需的参数
        String requestUri = request.getRequestURI();
        String requestMethod = request.getMethod();

        // 从请求标头中检索所需的参数
        String requestTime = request.getHeader("request-time");
        String clientId = request.getHeader("client-id");
        String signature = request.getHeader("signature");

        log.info(
                "收到antom通知消息 requestUri = {},requestMethod = {},requestTime = {},clientId = {},signature = {},notifyBody = {}，notifyType = {},payMethod = {}",
                requestUri, requestMethod, requestTime, clientId, signature, notifyBody, notifyType, payMethod);
        AlipayResponse response = new AlipayResponse();
        try {
            // 核实通知的签名
            boolean verifyResult = WebhookTool.checkSignature(requestUri, requestMethod, clientId, requestTime,
                    signature, notifyBody, antomProperty.getAntomPublicKey());
            if (!verifyResult) {
                log.info("antom verify fail. verifyResult = {}", verifyResult);
                throw new RuntimeException("Invalid notify signature");
            }
            log.info("antom verify success. antom notifyBody = {}", notifyBody);

            // 反序列化通知正文,解析出 notifyType 类型参数，再根据类型泛解析不通场景的业务对象
            AntomNotifyBaseDTO notifyDTO = JSON.parseObject(notifyBody, AntomNotifyBaseDTO.class);

            ChannelNotifyBody channelNotifyBody = new ChannelNotifyBody();

            // 用户已完成支付。商家需要等待最终的支付结果
            if (Objects.equals(notifyDTO.getNotifyType(), NotifyTypeEnum.PAYMENT_PENDING.name())) {
                log.info("用户已完成支付。商家需要等待最终的支付结果 notifyBody = {}", notifyBody);
            }
            // 支付结果处理（银行卡授权 & 钱包支付）
            else if (Objects.equals(notifyDTO.getNotifyType(), NotifyTypeEnum.PAYMENT_RESULT.name())) {
                AntomPayNotifyDTO payNotifyDTO = JSON.parseObject(notifyBody, AntomPayNotifyDTO.class);
                channelNotifyBody.setCurrentTradeNo(payNotifyDTO.getPaymentRequestId());
                channelNotifyBody.setOuterNo(payNotifyDTO.getPaymentId());
                if (StringUtils.isNotBlank(payNotifyDTO.getPaymentTime())) {
                    channelNotifyBody.setDate(DateUtil.transferDate(payNotifyDTO.getPaymentTime()));
                }
                if (Objects.equals(notifyType, AntomNotifyTypeEnum.BANK.getCode())) {
                    channelNotifyBody.setPayway(PaywayEnum.ANTOM_CHECKOUT_CARD);
                    if (Objects.equals(payNotifyDTO.getResult().getResultStatus(), ResultStatusType.S)) {
                        log.info("antom预授权通知");
                        channelNotifyBody.setStatus(PayStatus.CARD_AUTHORIZED);
                        // 存储用户保存的银行卡token信息，下次支付时传递给antom可免输银行卡信息
                        channelNotifyBody.getExtend().put(BusinessConstants.CARD_TOKEN,
                                payNotifyDTO.getPaymentResultInfo().getCardToken());
                        channelNotifyBody.getExtend().put(BusinessConstants.NETWORK_TRANSACTION_ID,
                                payNotifyDTO.getPaymentResultInfo().getNetworkTransactionId());
                        channelNotifyBody.setPayMethod(PayMethodEnum.explain(payMethod));
                        // 更新payment授权结果
                        bankcardPayService.handlePreAuthCreationNotify(channelNotifyBody);
                        // 将授权结果同步给上有业务 kronos
                        bankcardPayService.handlePreAuthCreatedNotify(channelNotifyBody);
                    } else if (Objects.equals(payNotifyDTO.getResult().getResultStatus(), ResultStatusType.F)) {
                        bankcardPayService.handlePreAuthFailedNotify(channelNotifyBody);
                    }
                }
                // 钱包支付
                else if (Objects.equals(notifyType, AntomNotifyTypeEnum.APM.getCode())) {
                    channelNotifyBody.setPayway(PaywayEnum.ANTOM_CHECKOUT_APM);
                    if (Objects.equals(payNotifyDTO.getResult().getResultStatus(), ResultStatusType.S)) {
                        log.info("antom钱包支付通知");
                        PayMethodEnum payMethodEnum = PayMethodEnum.explain(payMethod);
                        if (Objects.isNull(payMethodEnum)) {
                            throw new RuntimeException("un support payMethod");
                        }
                        channelNotifyBody.setStatus(PayStatus.PAID);
                        channelNotifyBody.setPayMethod(PayMethodEnum.explain(payMethod));
                        // 发送自消费消息，AntomApmPaidNotifyHandler
                        apmPayService.asyncPaidNotify(channelNotifyBody);
                    } else {
                        log.info("支付结果处理。订单状态不处理 result = {}", JSON.toJSONString(notifyDTO.getResult()));
                    }
                }
            }
            // 请款结果处理
            else if (Objects.equals(notifyDTO.getNotifyType(), NotifyTypeEnum.CAPTURE_RESULT.name())) {
                AntomCapturePayNotifyDTO capturePayNotifyDTO = JSON.parseObject(notifyBody,
                        AntomCapturePayNotifyDTO.class);
                if (Objects.equals(capturePayNotifyDTO.getResult().getResultStatus(), ResultStatusType.S)) {
                    log.info("antom请款通知");
                    channelNotifyBody.setPayway(PaywayEnum.ANTOM_CHECKOUT_CARD);
                    channelNotifyBody.setStatus(PayStatus.CARD_CAPTURED);
                    channelNotifyBody.setOuterNo(capturePayNotifyDTO.getCaptureId());
                    if (StringUtils.isNotBlank(capturePayNotifyDTO.getCaptureTime())) {
                        channelNotifyBody.setDate(DateUtil.transferDate(capturePayNotifyDTO.getCaptureTime()));
                    }
                    channelNotifyBody.setRefPayNo(capturePayNotifyDTO.getPaymentId());
                    channelNotifyBody.setCurrentTradeNo(capturePayNotifyDTO.getCaptureRequestId());
                    channelNotifyBody.setPayMethod(PayMethodEnum.explain(payMethod));
                    // 发送自消费消息，AntomCaptureNotifyHandler
                    bankcardPayService.asyncCapturedNotify(channelNotifyBody);
                }
            }
            // 退款通知处理
            else if (Objects.equals(notifyDTO.getNotifyType(), NotifyTypeEnum.REFUND_RESULT.name())) {
                log.info("antom退款通知");
                AntomRefundNotifyDTO refundNotifyDTO = JSON.parseObject(notifyBody, AntomRefundNotifyDTO.class);
                RefundNotifyBody refundNotifyBody = new RefundNotifyBody();
                refundNotifyBody.setChannel(ChannelEnum.ANTOM);
                if (StringUtils.isNotBlank(refundNotifyDTO.getRefundTime())) {
                    refundNotifyBody.setDate(DateUtil.transferDate(refundNotifyDTO.getRefundTime()));
                }
                refundNotifyBody.setOuterRefundNo(refundNotifyDTO.getRefundId());
                refundNotifyBody.setMerchantRefundNo(refundNotifyDTO.getRefundRequestId());
                refundNotifyBody.setRefundStatus(
                        Objects.equals(refundNotifyDTO.getRefundStatus(), RefundStatusEnum.SUCCESS.getCode()));
                // 发送自消费消息 RefundSelfMsgHandler
                refundService.asyncRefundNotify(refundNotifyBody);
            }

            // 向支付宝服务器回复已收到通知且处理成功
            response.setResult(buildAlipayResult(AlipayResultStatusEnum.SUCCESS, ResultStatusType.S, ""));
            return response;
        } catch (Exception e) {
            log.error("payNotify process error. ", e);
            // 向支付宝服务器回复已收到通知且处理异常
            response.setResult(buildAlipayResult(AlipayResultStatusEnum.ERROR, ResultStatusType.F, e.getMessage()));
            return response;
        }
    }

    /**
     * 构建应答支付宝结果
     * 
     * @param statusType
     * @param resultMessage
     * @return
     */
    private Result buildAlipayResult(AlipayResultStatusEnum statusEnum, ResultStatusType statusType,
            String resultMessage) {
        Result result = new Result();
        result.setResultCode(statusEnum.name());
        result.setResultStatus(statusType);
        result.setResultMessage(StringUtils.isBlank(resultMessage) ? statusEnum.name().toLowerCase() : resultMessage);
        return result;
    }

}
