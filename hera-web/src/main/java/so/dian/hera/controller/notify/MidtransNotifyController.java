package so.dian.hera.controller.notify;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.client.enums.PayMethodEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.domain.RefundDO;
import com.chargebolt.hera.domain.notify.ChannelNotifyBody;
import com.chargebolt.hera.domain.notify.RefundNotifyBody;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.hera.interceptor.utils.DateUtil;
import so.dian.hera.service.pay.apm.ApmPayService;
import so.dian.hera.service.refund.RefundService;
import so.dian.platform.common.constants.BusinessConstants;
import so.dian.platform.common.utils.SHA512SignatureUtils;
import so.dian.platform.common.configuration.redis.RedisClient;
import so.dian.platform.common.enums.CacheEnum;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.ServerErrorException;
import so.dian.platform.midtrans.common.enums.PaymentTypeEnum;
import so.dian.platform.midtrans.common.enums.TransactionStatusEnum;
import so.dian.platform.midtrans.config.MidtransProperty;
import so.dian.platform.midtrans.dto.notify.MidtransNotifyDTO;
import so.dian.platform.midtrans.dto.notify.RefundDetail;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.Objects;
import static so.dian.hera.interceptor.utils.DateUtil.DEFAULT_PATTERN;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
public class MidtransNotifyController {

    @Resource
    private MidtransProperty midtransProperty;

    @Resource
    private ApmPayService apmPayService;

    @Resource
    private RefundService refundService;

    @Resource
    private RedisClient redisClient;

    /**
     * 支付、退款回调
     * 此接口地址需要在midtrans商户后台配置：https://dashboard.sandbox.midtrans.com/settings/payment
     * 接口文档：https://docs.midtrans.com/docs/https-notification-webhooks
     *
     * 2XX	无需重试，即视为成功。
     * 500	仅重试一次。
     * 503	重试四次。
     * 400/404	重试两次。
     * 301/302/303	无需重试。在“设置”菜单中更新通知端点，而不是回复这些状态代码。
     * 307/308	使用 POST 方法和相同的通知主体跟踪新的 URL。重定向的最大次数为五次。
     * 其他	重试五次。
     *
     * @param notifyBody
     * @return
     */
    @PostMapping(value = "/notify/midtrans/normal")
    public String notify(@RequestBody String notifyBody,HttpServletResponse response) {

        log.info("midtrans notify notifyBody: {}",notifyBody);
        MidtransNotifyDTO notifyDTO = JSON.parseObject(notifyBody,MidtransNotifyDTO.class);

        // 校验签名 SHA512(order_id+status_code+gross_amount+ServerKey)
        String signString = notifyDTO.getOrder_id() + notifyDTO.getStatus_code() + notifyDTO.getGross_amount() + midtransProperty.getServerKey();
        String localSign = SHA512SignatureUtils.generateSHA512(signString);
        // 验签失败，可重试一次 GlobalExceptionHandler.serverErrorExceptionHandler
        if(!Objects.equals(localSign,notifyDTO.getSignature_key())){
            throw new ServerErrorException(HeraBizErrorCodeEnum.SIGN_VERIFY_ERROR);
        }

        // 幂等验证
        String cacheKey = notifyDTO.getOrder_id() + "-" + notifyDTO.getPayment_type() + "-" + notifyDTO.getTransaction_status();
        MidtransNotifyDTO cachedRequest = redisClient.get(CacheEnum.MIDTRANS_NOTIFY.getNs(),cacheKey,MidtransNotifyDTO.class);
        if (Objects.isNull(cachedRequest)) {
            redisClient.set(CacheEnum.MIDTRANS_NOTIFY.getNs(), cacheKey, notifyDTO, 10);
        } else {
            log.info("midtrans notify request cached.");
            return "OK";
        }

        // 构建需要的通知信息
        ChannelNotifyBody channelNotifyBody = new ChannelNotifyBody();
        channelNotifyBody.setCurrentTradeNo(notifyDTO.getOrder_id());
        channelNotifyBody.setOuterNo(notifyDTO.getTransaction_id());
        try {
            // 支付结果处理
            if(Objects.equals(notifyDTO.getTransaction_status(), TransactionStatusEnum.settlement.name())){
                channelNotifyBody.setStatus(PayStatus.PAID);
                channelNotifyBody.setDate(DateUtil.parse(notifyDTO.getSettlement_time(),DEFAULT_PATTERN));
                channelNotifyBody.setPayway(PaywayEnum.MIDTRANS_CHECKOUT_APM);
                if (Objects.equals(notifyDTO.getPayment_type(), PaymentTypeEnum.GOPAY.name().toLowerCase())) {
                    channelNotifyBody.setPayMethod(PayMethodEnum.GoPay);
                } else if (Objects.equals(notifyDTO.getPayment_type(), PaymentTypeEnum.SHOPEEPAY.name().toLowerCase())) {
                    channelNotifyBody.setPayMethod(PayMethodEnum.ShopeePay);
                } else if (Objects.equals(notifyDTO.getPayment_type(), PaymentTypeEnum.QRIS.name().toLowerCase())) {
                    channelNotifyBody.setPayMethod(PayMethodEnum.QRIS);
                } else if (Objects.equals(notifyDTO.getPayment_type(), PaymentTypeEnum.DANA.name().toLowerCase())) {
                    channelNotifyBody.setPayMethod(PayMethodEnum.Dana);
                }else {
                    throw new ServerErrorException(HeraBizErrorCodeEnum.BIZ_NOT_SUPPORT);
                }
                // 实现类MidtransApmNotifyHandler
                apmPayService.asyncPaidNotify(channelNotifyBody);
            // 退款结果处理
            } else if (Objects.equals(notifyDTO.getTransaction_status(), TransactionStatusEnum.refund.name()) ||
                    Objects.equals(notifyDTO.getTransaction_status(), TransactionStatusEnum.partial_refund.name())) {
                RefundNotifyBody refundNotifyBody = new RefundNotifyBody();
                refundNotifyBody.setChannel(ChannelEnum.MIDTRANS_PAY);
                refundNotifyBody.setMerchantTradeNo(notifyDTO.getOrder_id());
                // 存在多次退款的情况，每次取最新一次退款记录，则根据refund_chargeback_id倒序排列，然后取第一个
                Collections.sort(notifyDTO.getRefunds(), new Comparator<RefundDetail>() {
                    @Override
                    public int compare(RefundDetail r1, RefundDetail r2) {
                        return Long.compare(r2.getRefund_chargeback_id(), r1.getRefund_chargeback_id());
                    }
                });

                // 印尼的dana支付退款申请接口不返回三方单号，需要先更新三方退款单号
                RefundDetail refundDetail = notifyDTO.getRefunds().get(0);
                RefundDO refundDO = refundService.getRefund(refundDetail.getRefund_key());
                if(Objects.equals(refundDO.getRefundNo(),refundDO.getOutTraceNo())){
                    refundDO.setOutTraceNo(refundDetail.getRefund_chargeback_uuid());
                    log.info("退款处理中，更新外部退款单号 refundNo: {},outRefundNo: {}",refundDetail.getRefund_key(),refundDO.getOutTraceNo());
                    refundService.update(refundDO);
                }

                refundNotifyBody.setMerchantRefundNo(refundDetail.getRefund_key());
                refundNotifyBody.setOuterRefundNo(refundDetail.getRefund_chargeback_uuid());
                refundNotifyBody.setDate(StringUtils.isNotBlank(refundDetail.getBank_confirmed_at()) ? DateUtil.parse(refundDetail.getBank_confirmed_at(),DEFAULT_PATTERN) : new Date());
                // 实现类RefundSelfMsgHandler处理
                refundService.asyncRefundNotify(refundNotifyBody);
            } else {
                // pending状态，下单成功后会发送一条通知，不是最终状态，不做处理
                log.info("midtrans notify. State that does not require processing. status = {}",notifyDTO.getTransaction_status());
            }

        } catch (Exception e) {
            log.error("midtrans notify unknow err. ",e);
            throw new ServerErrorException(HeraBizErrorCodeEnum.DEFAULT_ERROR);
        }

        return "OK";
    }


}
