package so.dian.hera.controller.notify;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.client.enums.PayMethodEnum;
import com.chargebolt.hera.domain.notify.ChannelNotifyBody;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.domain.notify.RefundNotifyBody;
import org.springframework.util.StringUtils;
import so.dian.hera.service.refund.RefundService;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.hera.service.pay.apm.ApmPayService;
import so.dian.hera.service.pay.apmtoken.ApmTokenPayService;
import so.dian.hera.service.pay.bankcard.BankcardPayService;
import so.dian.hera.utils.AssertUtils;
import so.dian.hera.utils.EnvUtil;
import so.dian.platform.common.configuration.redis.RedisClient;
import so.dian.platform.common.enums.CacheEnum;
import so.dian.platform.pingpong.common.enums.PingPongConstant;
import so.dian.platform.pingpong.common.enums.PingpongPayMethodEnum;
import so.dian.platform.pingpong.dto.notify.*;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@RestController
public class PingpongNotifyController {

    @Resource
    private RedisClient redisClient;
    @Resource
    private BankcardPayService bankcardPayService;
    @Resource
    private ApmPayService apmPayService;
    @Resource
    private ApmTokenPayService apmTokenPayService;
    @Resource
    private RefundService refundService;

    @PostMapping(value = "/notify/pingpong/normal")
    public BizResult cardNotify(@RequestBody PingpongNotifyBaseDTO notifyDTO) {
        log.info("pingpong通知#/notify/pingpong/normal接口入参 reqDTO: {}", JSON.toJSONString(notifyDTO, true));
        String transactionId = notifyDTO.getBizContentObject().getString("transactionId");
        String status = notifyDTO.getBizContentObject().getString("status");
        String notifyType = notifyDTO.getNotifyType();
        String cacheKey = transactionId + "-" + notifyType + "-" + status;
        if ("REFUND".equals(notifyType)) {
            cacheKey += "-" + notifyDTO.getBizContentObject().getString("refundId");
        }
        PingpongNotifyBaseDTO cachedRequest = redisClient.get(CacheEnum.PINGPONG_NOTIFY.getNs(),
                // key
                cacheKey,
                PingpongNotifyBaseDTO.class);
        if (cachedRequest == null || !EnvUtil.isReal()) {
            redisClient.set(CacheEnum.PINGPONG_NOTIFY.getNs(), cacheKey, notifyDTO, 10);
        } else {
            log.info("request cached.");
            return BizResult.create(true);
        }

        AssertUtils.notBlankWithBizExp(notifyType, HeraBizErrorCodeEnum.PARAM_ERROR, "notifyType is blank");
        ChannelNotifyBody channelNotifyBody = new ChannelNotifyBody();
        channelNotifyBody.setPayway(PaywayEnum.PINGPONG_CHECKOUT_CARD);
//        if (!PingPongConstant.STATUS_SUCCESS.equals(status) && !PingPongConstant.STATUS_AUTH_SUCCESS.equals(status)) {
//            log.warn("pingpong notify status: {}", status);
//            return BizResult.create(true);
//        }
        log.warn("pingpong notify notifyType: {} status: {}", notifyType, status);
        if (PingPongConstant.NOTIFY_TYPE_RECHARGE.equals(notifyType)) {
            // 预授权确认也是进这里
            PingpongRechargeNotifyDTO rechargeNotifyDTO = JSON.parseObject(notifyDTO.getBizContent(), PingpongRechargeNotifyDTO.class);
            channelNotifyBody.setCurrentTradeNo(rechargeNotifyDTO.getMerchantTransactionId());
            channelNotifyBody.setOuterNo(rechargeNotifyDTO.getTransactionId());
            channelNotifyBody.setDate(new Date(Long.parseLong(rechargeNotifyDTO.getTransactionTime())));
            channelNotifyBody.setStatus(PayStatus.CARD_AUTHORIZED);
            // 预授权
            if (PingPongConstant.STATUS_AUTH_SUCCESS.equals(rechargeNotifyDTO.getStatus())) {
                // pingpong的预授权没有【授理成功】这个状态，所以这里需要两个更新操作
                bankcardPayService.handlePreAuthCreationNotify(channelNotifyBody);
                bankcardPayService.handlePreAuthCreatedNotify(channelNotifyBody);
            } else if (PingPongConstant.STATUS_FAILED.equals(rechargeNotifyDTO.getStatus())) {
                bankcardPayService.handlePreAuthFailedNotify(channelNotifyBody);
            } else if (PingPongConstant.STATUS_SUCCESS.equals(rechargeNotifyDTO.getStatus())) {
                bankcardPayService.asyncCapturedNotify(channelNotifyBody);
            }
        } else if (PingPongConstant.NOTIFY_TYPE_CAPTURE.equals(notifyType)) {
            // 交易
            /**
             * merchantTransactionId	商户网站的原交易流水号
             * transactionId	        PingPong原交易流水号
             * merchantCaptureId	    商户网站预授权确认流水号，全局唯一 = captureTradeNo
             * */
            PingpongCaptureNotifyDTO captureNotifyDTO = JSON.parseObject(notifyDTO.getBizContent(), PingpongCaptureNotifyDTO.class);
            channelNotifyBody.setStatus(PayStatus.CARD_CAPTURED);
            channelNotifyBody.setOuterNo(captureNotifyDTO.getTransactionId());
            channelNotifyBody.setDate(new Date(Long.parseLong(captureNotifyDTO.getCaptureEndingTime())));
            channelNotifyBody.setRefTradeNo(captureNotifyDTO.getMerchantTransactionId());
            channelNotifyBody.setCurrentTradeNo(captureNotifyDTO.getMerchantCaptureId());
            if (PingPongConstant.STATUS_SUCCESS.equals(captureNotifyDTO.getStatus())) {
                bankcardPayService.asyncCapturedNotify(channelNotifyBody);
            }
        } else if (PingPongConstant.NOTIFY_TYPE_VOID.equals(notifyType)) {
            // 撤销
            PingpongVoidNotifyDTO voidNotifyDTO = JSON.parseObject(notifyDTO.getBizContent(), PingpongVoidNotifyDTO.class);
            channelNotifyBody.setCurrentTradeNo(voidNotifyDTO.getMerchantTransactionId());
            bankcardPayService.handleCanceledSelfMsg(channelNotifyBody);
        } else if (PingPongConstant.NOTIFY_TYPE_REFUND.equals(notifyType)) {
            // 退款
            PingpongRefundNotifyDTO refundNotifyDTO = JSON.parseObject(notifyDTO.getBizContent(), PingpongRefundNotifyDTO.class);
            RefundNotifyBody refundNotifyBody = new RefundNotifyBody();
            refundNotifyBody.setChannel(ChannelEnum.PINGPONG);
            refundNotifyBody.setDate(new Date(Long.parseLong(refundNotifyDTO.getRefundEndingTime())));
            refundNotifyBody.setOuterPayNo(refundNotifyDTO.getTransactionId());
            refundNotifyBody.setOuterRefundNo(refundNotifyDTO.getRefundId());
            refundNotifyBody.setMerchantRefundNo(refundNotifyDTO.getMerchantRefundId());
            refundNotifyBody.setMerchantTradeNo(refundNotifyDTO.getMerchantTransactionId());
            refundService.asyncRefundNotify(refundNotifyBody);
        } else {
            log.warn("未处理的notifyType {}", notifyType);
        }
        log.info("pingpong通知处理完毕");
        return BizResult.create(null);
    }

    private ChannelNotifyBody apm(PingpongNotifyBaseDTO notifyDTO) {
        String transactionId = notifyDTO.getBizContentObject().getString("transactionId");
        String status = notifyDTO.getBizContentObject().getString("status");
        String cacheKey = transactionId + "-" + status;
        PingpongNotifyBaseDTO cachedRequest = redisClient.get(CacheEnum.PINGPONG_NOTIFY.getNs(),
                // key
                cacheKey,
                PingpongNotifyBaseDTO.class);
        if (cachedRequest == null || !EnvUtil.isReal()) {
            redisClient.set(CacheEnum.PINGPONG_NOTIFY.getNs(), cacheKey, notifyDTO, 60);
        } else {
            log.info("request cached.");
            return null;
        }

        String notifyType = notifyDTO.getNotifyType();
        AssertUtils.notBlankWithBizExp(notifyType, HeraBizErrorCodeEnum.PARAM_ERROR, "notifyType is blank");
        ChannelNotifyBody channelNotifyBody = new ChannelNotifyBody();
        channelNotifyBody.setPayway(PaywayEnum.PINGPONG_CHECKOUT_APM);
        log.warn("pingpong notify notifyType: {} status: {}", notifyType, status);

        if (PingPongConstant.NOTIFY_TYPE_RECHARGE.equals(notifyType)) {
            PingpongRechargeNotifyDTO rechargeNotifyDTO = JSON.parseObject(notifyDTO.getBizContent(), PingpongRechargeNotifyDTO.class);
            channelNotifyBody.setCurrentTradeNo(rechargeNotifyDTO.getMerchantTransactionId());
            channelNotifyBody.setOuterNo(rechargeNotifyDTO.getTransactionId());
            channelNotifyBody.setDate(new Date(Long.parseLong(rechargeNotifyDTO.getTransactionTime())));
            channelNotifyBody.setStatus(PayStatus.PAID);
            String pingpongPayMethod = rechargeNotifyDTO.getPaymentMethod() == null ? null : rechargeNotifyDTO.getPaymentMethod().getType();
            if (!StringUtils.isEmpty(pingpongPayMethod)) {
                PingpongPayMethodEnum pingpongPayMethodEnum = PingpongPayMethodEnum.explainMethod(pingpongPayMethod);
                if (pingpongPayMethodEnum != null) {
                    channelNotifyBody.setPayMethod(pingpongPayMethodEnum.getPayMethodEnum());
                }
            }
            if (PingPongConstant.STATUS_SUCCESS.equals(rechargeNotifyDTO.getStatus())) {
                return channelNotifyBody;
            } else if (PingPongConstant.STATUS_FAILED.equals(rechargeNotifyDTO.getStatus())) {
                log.warn(notifyDTO.getDescription());
            }
        } else {
            log.warn("未处理的notifyType {}", notifyType);
        }
        return null;
    }

    @PostMapping(value = "/notify/pingpong/apm")
    public BizResult apmNotify(@RequestBody PingpongNotifyBaseDTO notifyDTO) {
        log.info("pingpong通知#/notify/pingpong/apm接口入参 reqDTO: {}", JSON.toJSONString(notifyDTO, true));
        ChannelNotifyBody channelNotifyBody = apm(notifyDTO);
        if (channelNotifyBody == null) {
            return BizResult.create(null);
        }
        apmPayService.asyncPaidNotify(channelNotifyBody);
        log.info("pingpong apm 通知处理完毕");
        return BizResult.create(null);
    }

    @PostMapping(value = "/notify/pingpong/apmToken")
    public BizResult apmTokenApplyNotify(@RequestBody PingpongNotifyBaseDTO notifyDTO) {
        log.info("pingpong通知#/notify/pingpong/apmToken接口入参 reqDTO: {}", JSON.toJSONString(notifyDTO, true));
        String notifyType = notifyDTO.getNotifyType();
        PingpongApmTokenNotifyDTO apmTokenNotifyDTO = JSON.parseObject(notifyDTO.getBizContent(), PingpongApmTokenNotifyDTO.class);
        if (PingPongConstant.ACCESS_TOKEN_CREATION.equals(notifyType)) {
            // 一键支付授权成功
            apmTokenPayService.handleTokenCreationNotify(apmTokenNotifyDTO.getToken());
        } else if (PingPongConstant.ACCESS_TOKEN_CANCEL_OF_MERCHANT.equals(notifyType)
                || PingPongConstant.ACCESS_TOKEN_CANCEL_OF_MERCHANT_USER.equals(notifyType)) {
            // 一键支付撤销授权
            apmTokenPayService.handleTokenCancelNotify(apmTokenNotifyDTO.getToken());
        }
        log.info("pingpong apmToken 通知处理完毕");
        return BizResult.create(null);
    }

    @PostMapping(value = "/notify/pingpong/apmToken/paid")
    public BizResult apmTokenNotify(@RequestBody PingpongNotifyBaseDTO notifyDTO) {
        log.info("pingpong通知#/notify/pingpong/apmToken/paid接口入参 reqDTO: {}", JSON.toJSONString(notifyDTO, true));
        ChannelNotifyBody channelNotifyBody = apm(notifyDTO);
        if (channelNotifyBody == null) {
            return BizResult.create(null);
        }
        String notifyType = notifyDTO.getNotifyType();
        if (PingPongConstant.NOTIFY_TYPE_RECHARGE.equals(notifyType)) {
            apmTokenPayService.asyncApmPaidNotify(channelNotifyBody);
        }
        log.info("pingpong apmToken 通知处理完毕");
        return BizResult.create(null);
    }
}
