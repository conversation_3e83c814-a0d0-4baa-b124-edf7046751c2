package so.dian.hera.controller.notify;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import so.dian.platform.wechat.dto.notify.WechatNotifyRspDTO;
import so.dian.platform.wechat.dto.notify.WxAppCreditConfirmNotifyDTO;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
public class WechatPayscoreNotifyController {

    /**
     * 微信信用分订单通知回调
     *
     * @param response
     * @param req
     * @param creditProdType
     * @param parentClientType
     * @param payTypeRoute
     * @return
     * @throws IOException
     */
    @RequestMapping(value = {"/hera/{version}/credit/wx/notify/confirm/{creditProdType}/{parentClientType}"
            , "/hera/{version}/credit/wx/notify/confirm/{creditProdType}/{parentClientType}/{payTypeRoute}"}, method = RequestMethod.POST)
    public WechatNotifyRspDTO confirmNotify(HttpServletResponse response, @RequestBody WxAppCreditConfirmNotifyDTO req,
                                            @PathVariable("creditProdType") Integer creditProdType,
                                            @PathVariable("parentClientType") Integer parentClientType,
                                            @PathVariable(value = "payTypeRoute", required = false) Integer payTypeRoute) throws IOException {
        req.setCreditProdType(creditProdType);
        req.setParentClientType(parentClientType);
        req.setPayTypeRoute(payTypeRoute);
        log.info("微信v3回调通知原始内容: {}", JSON.toJSONString(req));
        return new WechatNotifyRspDTO();
    }

}
