package so.dian.hera.controller.notify;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import so.dian.hera.service.notify.WechatV3NotifyService;
import so.dian.platform.wechat.dto.notify.NotifyBody;
import so.dian.platform.wechat.dto.notify.WechatNotifyReqDTO;
import so.dian.platform.wechat.dto.notify.WechatNotifyRspDTO;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * 微信回调接口
 * 微信官方工具：https://github.com/wechatpay-apiv3/wechatpay-apache-httpclient/blob/master/src/test/java/com/wechat/pay/contrib/apache/httpclient/NotificationHandlerTest.java#105
 *
 * 服务商模式-支付通知：https://pay.weixin.qq.com/wiki/doc/apiv3_partner/apis/chapter4_5_5.shtml
 * 服务商模式-退款通知：https://pay.weixin.qq.com/wiki/doc/apiv3_partner/apis/chapter4_5_11.shtml
 * 服务商模式-分账动账通知：https://pay.weixin.qq.com/wiki/doc/apiv3_partner/apis/chapter8_1_10.shtml
 */
@Slf4j
@RestController
@SuppressWarnings("all")
public class WechatV3NotifyController {

    @Resource
    private WechatV3NotifyService wechatV3NotifyManager;

    /**
     * 微信v3即时支付&退款通知回调接口
     * 商户模式-即时支付-支付通知：https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_5_5.shtml
     * 商户模式-即时支付-退款通知：https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_5_11.shtml
     * 商户模式-信用支付-支付通知：https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter6_1_22.shtml
     * 商户模式-信用支付-退款通知：https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter6_1_28.shtml
     * @param request
     * @param notification
     * @param payProdType 支付产品类型 immediate-即时支付，credit-信用支付
     * @param applicationId 支付配置应用ID pay_channel表application_id值
     * @param channelId 支付配置渠道ID pay_channel表ID值
     * @return
     */
    @RequestMapping(value = "/trade/wechatV3/callback/{payProdType}/{applicationId}/{channelId}")
    @ResponseBody
    public String wechatV3Notify(HttpServletRequest request,
                                 @RequestBody NotifyBody notification,
                                 @PathVariable("payProdType") String payProdType,
                                 @PathVariable("applicationId") String applicationId,
                                 @PathVariable("channelId") Integer channelId,
                                 @RequestHeader("Wechatpay-Signature") String wechatPaySignature,
                                 @RequestHeader("Wechatpay-Serial") String wechatPaySerial,
                                 @RequestHeader("Wechatpay-Timestamp") String wechatPayTimestamp,
                                 @RequestHeader("Wechatpay-Nonce") String wechatPayNonce) {
        WechatNotifyReqDTO reqDTO = WechatNotifyReqDTO.builder()
                .notifyBody(notification)
                .payProdType(payProdType)
                .channelId(channelId)
                .wechatPaySignature(wechatPaySignature)
                .wechatPaySerial(wechatPaySerial)
                .wechatPayTimestamp(wechatPayTimestamp)
                .wechatPayNonce(wechatPayNonce)
                .build();
//        log.info("微信V3通知#接口入参 reqDTO: {}",JSON.toJSONString(reqDTO));
        WechatNotifyRspDTO rspDTO = wechatV3NotifyManager.process(reqDTO);
//        log.info("微信V3通知#接口出参 rspDTO: {}",JSON.toJSONString(rspDTO));
        return JSON.toJSONString(rspDTO);
    }

}
