package so.dian.hera.controller.notify;


import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.domain.notify.ChannelNotifyBody;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.domain.notify.RefundNotifyBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.commons.eden.entity.BizResult;
import so.dian.hera.service.pay.bankcard.BankcardPayService;
import so.dian.hera.service.refund.RefundService;
import so.dian.platform.common.configuration.redis.RedisClient;
import so.dian.platform.common.enums.CacheEnum;
import so.dian.platform.wooshpay.common.enums.WooshPayEventEnum;
import so.dian.platform.wooshpay.common.enums.WooshPayStatusEnum;
import so.dian.platform.wooshpay.dto.notify.WooshPayWebhookNotifyDTO;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * 接口文档：https://docs.wooshpay.com/doc-2654461
 * 创建要接收的通知：https://docs.wooshpay.com/doc-2517709
 */
@Slf4j
@RestController
public class WooshPayNotifyController {

    @Resource
    private RedisClient redisClient;
    @Resource
    private BankcardPayService bankcardPayService;
    @Resource
    private RefundService refundService;


    /**
     *{
     *   "livemode": false,
     *   "data": {
     *     "object": {
     *       "id": "pi_1700212081539678208",
     *       "object": "payment_intent",
     *       "created": *************,
     *       "livemode": false,
     *       "currency": "CNY",
     *       "amount": 100,
     *       "status": "succeeded",
     *       "merchant_order_id": "CNT432309090218422550757664",
     *       "client_secret": "pi_1700212081539678208_secret_8eWgvUX5LTBMQiCCObtMPi6Q",
     *       "payment_method_types": [
     *         "alipay",
     *         "wechat_pay",
     *         "card"
     *       ],
     *       "confirmation_method": "automatic",
     *       "payment_method_options": {
     *         "card": {
     *           "request_three_d_secure": "auto",
     *           "capture_method": "automatic"
     *         }
     *       },
     *       "amount_capturable": 100,
     *       "return_url": "https://jstest.wooshpay.com/v1/payment/success?redirect=result",
     *       "payment_method": "pm_1700212482385117184",
     *       "capture_method": "manual",
     *       "latest_charge": "ch_1700212081619369984"
     *     }
     *   },
     *   "created": 1694197225330,
     *   "id": "evt_1700212509446766592",
     *   "type": "payment_intent.succeeded",
     *   "object": "event"
     * }
     * */
    /**
     * @param requestDTO
     * @return
     */
    @PostMapping(value = "/notify/wooshpay")
    public BizResult wooshpayNotify(@RequestBody WooshPayWebhookNotifyDTO requestDTO) throws InterruptedException {
        WooshPayWebhookNotifyDTO cachedRequest = redisClient.get(CacheEnum.WOOSHPAY_NOTIFY.getNs(),
                // key
                requestDTO.getType() + "-" + requestDTO.getData().getObject().getId(),
                WooshPayWebhookNotifyDTO.class);
        if (cachedRequest == null) {
            redisClient.set(CacheEnum.WOOSHPAY_NOTIFY.getNs(), requestDTO.getType() + "-" + requestDTO.getData().getObject().getId(), requestDTO, 60);
        } else {
            log.info("request cached. {}", requestDTO.getId());
            return BizResult.create(true);
        }
        log.info("wooshpay通知#接口入参 reqDTO: {}", JSON.toJSONString(requestDTO, true));
        ChannelNotifyBody channelNotifyBody = new ChannelNotifyBody();
        channelNotifyBody.setCurrentTradeNo(requestDTO.getData().getObject().getMerchantOrderId());
        channelNotifyBody.setOuterNo(requestDTO.getData().getObject().getId());
        channelNotifyBody.setPayway(PaywayEnum.WOOSHPAY_CARD);
        channelNotifyBody.setDate(new Date(requestDTO.getCreated()));

        String eventType = requestDTO.getType();
        String status = requestDTO.getData().getObject().getStatus();
        Boolean result = true;
        Thread.sleep(2000L);
        log.info("eventType:{}, status:{}", eventType, status);
        if (WooshPayEventEnum.PAYMENT_INTENT_CREATE.getValue().equals(eventType)) {
            if (WooshPayStatusEnum.REQUIRES_PAYMENT_METHOD.getValue().equals(status)) {
                channelNotifyBody.setStatus(PayStatus.CARD_AUTHORIZED);
                bankcardPayService.handlePreAuthCreationNotify(channelNotifyBody);
            }
        } else if (WooshPayEventEnum.PAYMENT_INTENT_AMOUNT_CAPTURABLE_UPDATED.getValue().equals(eventType)) {
            if (WooshPayStatusEnum.REQUIRES_CAPTURE.getValue().equals(status)) {
                bankcardPayService.handlePreAuthCreatedNotify(channelNotifyBody);
            }
        } else if (WooshPayEventEnum.PAYMENT_INTENT_SUCCEEDED.getValue().equals(eventType)) {
            if (WooshPayStatusEnum.SUCCEEDED.getValue().equals(status)) {
                bankcardPayService.asyncCapturedNotify(channelNotifyBody);
            }
        } else if (WooshPayEventEnum.CHARGE_CAPTURED.getValue().equals(eventType)) {
            if (WooshPayStatusEnum.SUCCEEDED.getValue().equals(status)) {
                bankcardPayService.asyncCapturedNotify(channelNotifyBody);
            }
        } else if (WooshPayEventEnum.CHARGE_REFUND_UPDATED.getValue().equals(eventType)) {
            if (WooshPayStatusEnum.SUCCEEDED.getValue().equals(status)) {
                RefundNotifyBody refundNotifyBody = new RefundNotifyBody();
                refundNotifyBody.setChannel(ChannelEnum.WOOSH_PAY);
                refundNotifyBody.setMerchantTradeNo(requestDTO.getData().getObject().getMerchantOrderId());
                refundNotifyBody.setDate(new Date(requestDTO.getCreated()));
                refundNotifyBody.setOuterRefundNo(requestDTO.getData().getObject().getId());

                refundService.asyncRefundNotify(refundNotifyBody);
            }
        } else if (WooshPayEventEnum.PAYMENT_INTENT_CANCELED.getValue().equals(eventType)) {
            if (WooshPayStatusEnum.CANCELED.getValue().equals(status)) {
                // 没有异步
                bankcardPayService.handleCanceledSelfMsg(channelNotifyBody);
            }
        } else {
            log.warn("未处理的eventType {}", eventType);
        }
        log.info("wooshpay通知#接口返回 result:{}", result);
        return BizResult.create(result);
    }

}
