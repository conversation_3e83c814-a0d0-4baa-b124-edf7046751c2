/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.controller.notify;

import com.chargebolt.hera.domain.notify.ZaloMiniNotifyBody;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.chargebolt.zeus.controller.request.VietqrRechargeRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import so.dian.hera.remote.lvy.UserAccountManager;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.common.mq.producer.SelfMqProducer;
import so.dian.platform.common.utils.MACUtil;
import so.dian.platform.zalomini.config.ZaloMiniProperties;
import so.dian.platform.zalomini.dto.request.ZaloMiniCallbackRequest;
import so.dian.platform.zalopay.dto.request.CallbackData;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloPayNotifyController.java, v 1.0 2024-04-11 10:13 AM Exp $
 */
@Slf4j
@RestController
public class ZaloMiniPayNotifyController {
    @Resource
    private SelfMqProducer selfMqProducer;
    @Resource
    private ZaloMiniProperties zaloMiniProperties;
    @Resource
    private PaymentPersistService paymentPersistService;
    @Resource
    private UserAccountManager userAccountManager;
    /**
     * zalo小程序支付结果回调
     * @param request
     * @return
     */
    @RequestMapping(value = "/notify/zalomini")
    public Map<String, Object> zaloMiniNotify(@RequestBody Object request) {
        Map<String, Object> result = new HashMap();
        log.info("Zalopay支付结果回调,请求参数:{}", JsonUtil.beanToJson(request));
        ZaloMiniCallbackRequest miniCallbackRequest= JsonUtil.jsonToBean(JsonUtil.beanToJson(request), ZaloMiniCallbackRequest.class);

        String reqOveralMac= getMac(miniCallbackRequest);
        log.info("callback        Mac：{}", miniCallbackRequest.getMac());
        log.info("localMac OverallMac：{}", reqOveralMac);
        // mac 校验
        if(!StringUtils.equalsIgnoreCase(miniCallbackRequest.getMac(), reqOveralMac)){
            log.error("mac 校验不同过，拒绝处理");
            result.put("returnCode", -3);
            result.put("returnMessage", "Invalid mac");
            return result;
        }
        try {
            String decodedExtraData = URLDecoder.decode(miniCallbackRequest.getData().getExtradata(), "UTF-8");
            Map extraDataMap = JsonUtil.jsonToBean(decodedExtraData, Map.class);
            String tradeNo = extraDataMap.getOrDefault("tradeNo", "").toString();
            if(StringUtils.isBlank(tradeNo)){
                result.put("returnCode", -1);
                result.put("returnMessage", "Invalid request [extradata]");
                return result;
            }else{
                // 获取支付订单
                PaymentDO paymentDO= paymentPersistService.getPaymentByTradeNo(tradeNo);
                if(Objects.isNull(paymentDO)){
                    result.put("returnCode", -2);
                    result.put("returnMessage", "Order does not exist");
                    return result;
                }
                // 押金充值
                userAccountManager.addUserDepositMoney(buildRechargeRequest(miniCallbackRequest.getData().getAmount(), paymentDO));
                ZaloMiniNotifyBody notifyBody= new ZaloMiniNotifyBody();
                if(Objects.equals(miniCallbackRequest.getData().getResultCode(), 1)){
                    // 发送消息
                    notifyBody.setTradeNo(paymentDO.getTradeNo());
                    notifyBody.setThirdPartyPayNo(miniCallbackRequest.getData().getOrderId());
                    notifyBody.setPayTimestamp(miniCallbackRequest.getData().getTransTime());
                    notifyBody.setType(1);
                    notifyBody.setResult(Boolean.TRUE);
                }else {
                    notifyBody.setResult(Boolean.FALSE);
                    notifyBody.setResponseMsg(miniCallbackRequest.getData().getMessage());
                }
                selfMqProducer.sendSelfZaloMiniMsg(notifyBody);
            }
        } catch (UnsupportedEncodingException e) {
            log.info("zalo mini原始支付单号获取异常", e);
            result.put("returnCode", -1);
            result.put("returnMessage", "Invalid request [extradata]");
            return result;
        }
        return result;
    }

    private String getMac(ZaloMiniCallbackRequest payCallbackRequest) {
        // 它不是字典排序，所以硬拼接
        StringBuilder stringBuilder = new StringBuilder();
//        dataForMac =
//                "appId={appId}&amount={amount}&description={description}&orderId={orderId}
//                &message={message}&resultCode={resultCode}&transId={transId}";
        stringBuilder.append("appId=").append(payCallbackRequest.getData().getAppId())
                .append("&amount=").append(payCallbackRequest.getData().getAmount())
                .append("&description=").append(payCallbackRequest.getData().getDescription())
                .append("&orderId=").append(payCallbackRequest.getData().getOrderId())
                .append("&message=").append(payCallbackRequest.getData().getMessage())
                .append("&resultCode=").append(payCallbackRequest.getData().getResultCode())
                .append("&transId=").append(payCallbackRequest.getData().getTransId());
        return MACUtil.sha256Mac(stringBuilder.toString(), zaloMiniProperties.getPrivateKey());

    }
    private VietqrRechargeRequest buildRechargeRequest(Long amount, PaymentDO paymentDO){
        VietqrRechargeRequest request= new VietqrRechargeRequest();
        request.setUserId(paymentDO.getUserId());
        request.setAmount(amount);
        request.setTradeNo(paymentDO.getTradeNo());
        return request;
    }
}