/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.controller.notify;

import com.chargebolt.hera.domain.notify.ZalopayNotifyBody;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.chargebolt.zeus.controller.request.VietqrRechargeRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import so.dian.hera.controller.vietqr.VietQRCallbackDTO;
import so.dian.hera.controller.vietqr.VietQRCallbackResponse;
import so.dian.hera.remote.lvy.UserAccountManager;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.common.constants.BusinessConstants;
import so.dian.platform.common.mq.producer.SelfMqProducer;
import so.dian.platform.common.utils.MACUtil;
import so.dian.platform.zalopay.config.ZaloPayProperties;
import so.dian.platform.zalopay.dto.request.CallbackData;
import so.dian.platform.zalopay.dto.request.PayCallbackRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloPayNotifyController.java, v 1.0 2024-04-11 10:13 AM Exp $
 */
@Slf4j
@RestController
public class ZaloPayNotifyController {
    @Resource
    private SelfMqProducer selfMqProducer;
    @Resource
    private ZaloPayProperties zaloPayProperties;
    @Resource
    private UserAccountManager userAccountManager;
    @Resource
    private PaymentPersistService paymentPersistService;
    @RequestMapping(value = "/notify/zalopay")
    @ResponseBody
    public Map<String, Object> zalopayNotify(HttpServletRequest httpServletRequest, @RequestBody PayCallbackRequest request) {
//    public Map<String, Object> zalopayNotify(@RequestBody Map<String, Object> requestBody) {
            log.info("zalopay callback request body:{}", JsonUtil.beanToJson(request));

        Map<String, Object> result = new HashMap();
        if (Objects.equals(request.getType(), 1)){
            // mac校验
            String mac = MACUtil.sha256Mac(request.getData(), zaloPayProperties.getAppKey2());
            log.info("calback mac: {}", request.getMac());
            log.info("local   mac: {}", mac);
            if(!mac.equalsIgnoreCase(request.getMac())){
                result.put("return_code", -1);
                result.put("return_message", "Invalid request");
                return result;
            }
            CallbackData callbackData= JsonUtil.jsonToBean(request.getData(), CallbackData.class);
            //获取订单
            PaymentDO payment= paymentPersistService.getPaymentByTradeNo(callbackData.getAppTransId());
            if(Objects.isNull(payment)){
                result.put("return_code", -1);
                result.put("return_message", "Order does not exist");
                return result;
            }
            ZalopayNotifyBody notifyBody= new ZalopayNotifyBody();
            notifyBody.setType(BusinessConstants.TRANSACTION_PAY);
            notifyBody.setTradeNo(callbackData.getAppTransId());
            notifyBody.setThirdPartyPayNo(callbackData.getZpTransId());
            notifyBody.setPayTimestamp(callbackData.getServerTime());
            // 交易成功
            notifyBody.setResult(Boolean.TRUE);
            selfMqProducer.sendSelfZaloPayMsg(notifyBody);
        }else{
            log.error("类型不支持处理：{}", JsonUtil.beanToJson(request));
            result.put("return_code", -1);
            result.put("return_message", "Type not supported");
        }

        return new HashMap<>();
    }
    private VietqrRechargeRequest buildRechargeRequest(CallbackData callbackData, PaymentDO paymentDO){
        VietqrRechargeRequest request= new VietqrRechargeRequest();
        request.setUserId(paymentDO.getUserId());
        request.setAmount(callbackData.getAmount());
        request.setTradeNo(paymentDO.getTradeNo());
        return request;
    }
}