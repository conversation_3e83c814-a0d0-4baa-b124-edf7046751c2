/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.controller.vietqr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: AccessToken.java, v 1.0 2024-06-18 上午9:54 Exp $
 */
@Data
public class AccessToken implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202406170095452L;

    @JsonProperty("access_token")
    private String accessToken;
    @JsonProperty("token_type")
    private String tokenType;
    @JsonProperty("expires_in")
    private Integer expiresIn;
}