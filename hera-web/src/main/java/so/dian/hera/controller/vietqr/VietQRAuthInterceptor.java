/*
 * Dian.com Inc.
 * Copyright (c) 2004-2018 All Rights Reserved.
 */
package so.dian.hera.controller.vietqr;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import so.dian.platform.common.configuration.redis.RedisClient;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Date;

/**
 * PC端用户登录身份验证拦截器
 *
 * <AUTHOR>
 * @version $Id: AppAuthInterceptor.java, v 0.1 2023年10月31日 11:34 AM Exp $
 */
@Slf4j
public class VietQRAuthInterceptor implements HandlerInterceptor {
    @Resource
    private RedisClient redisClient;
    /**
     * 用户身份鉴权
     * <p>
     * 鉴权对象：对于有@VietQRAuth注解的方法
     * 鉴权步骤:
     * 1 从header中获取token字符串
     * 2 从缓存中获取token对象
     * 3 如果没有，鉴权失败，返回登录
     *
     * @param request  request
     * @param response response
     * @param handler  handler
     * @return true/false
     * @throws Exception exception
     */
    @Override
    public boolean preHandle(final HttpServletRequest request, final HttpServletResponse response, final Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return Boolean.FALSE;
        }
        HandlerMethod method = (HandlerMethod) handler;
        VietQRAuth auth = method.getMethod().getAnnotation(VietQRAuth.class);
        if (auth == null) {
            return Boolean.TRUE;
        }
        String authorization = request.getHeader("Authorization");
        if (StringUtils.isBlank(authorization)) {
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.getWriter().write("Unauthorized: Missing or invalid Authorization header.");
            return Boolean.FALSE;
        }

        if (authorization.startsWith("Bearer ")) {
            String encodedCredentials = authorization.substring("Bearer ".length()).trim();
            Boolean isValid = verifyAndExtractUserInfo(encodedCredentials);
            if(Boolean.TRUE.equals(isValid)){
                return Boolean.TRUE;
            }else{
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                response.getWriter().write("Unauthorized: Missing or invalid Authorization header.");
                return Boolean.FALSE;
            }
//            byte[] decodedBytes = Base64.getDecoder().decode(encodedCredentials);
//            String credentials = new String(decodedBytes, StandardCharsets.UTF_8);
//            String[] parts = credentials.split(":");
//            String username = parts[0];
//            String password = parts.length > 1 ? parts[1] : "";
//            if (StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
//                response.setStatus(HttpStatus.UNAUTHORIZED.value());
//                response.getWriter().write("Unauthorized: Missing or invalid Authorization header.");
//                return Boolean.FALSE;
//            }
//            String token =redisClient.get("viteqr", username+password, String.class);
//            if(StringUtils.isBlank(token)){
//                response.setStatus(HttpStatus.UNAUTHORIZED.value());
//                response.getWriter().write("Unauthorized: Missing or invalid Authorization header.");
//                return Boolean.FALSE;
//            }
//            if(StringUtils.equalsAnyIgnoreCase(username, "vietqr-dev-erx01")
//                    &&StringUtils.equalsAnyIgnoreCase(password, "dmlldHFyLWFwaS1hY3ZpMHNxZw==")){
//                return Boolean.TRUE;
//            }else{
//                response.setStatus(HttpStatus.UNAUTHORIZED.value());
//                response.getWriter().write("Unauthorized: Missing or invalid Authorization header.");
//                return Boolean.FALSE;
//            }
        }else{
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.getWriter().write("Unauthorized: Missing or invalid Authorization header.");
            return Boolean.FALSE;
        }
    }
     private boolean verifyAndExtractUserInfo(String jwtToken) {
        try {
            // 使用SECRET_KEY解析JWT
            Claims claims = Jwts.parser()
                    .setSigningKey(VietQRConstant.VIETQR_SECRET_KEY.getBytes())
                    .parseClaimsJws(jwtToken)
                    .getBody();

            // 验证JWT是否过期
            Date expiration = claims.getExpiration();
            if (expiration.before(new Date())) { // JWT已过期
                return false;
            }

            // JWT有效，提取并输出用户信息（用户名存储在"sub" claim中）
//            String username = claims.getSubject();

            return true;
        } catch (ExpiredJwtException e) { // JWT过期异常
            log.error("Token has expired.");
            return false;
        } catch (Exception e) { // 其他解析异常，如签名不匹配
            log.error("Invalid token.");
            return false;
        }
    }

    @Override
    public void postHandle(final HttpServletRequest request, final HttpServletResponse response, final Object handler,
                           final ModelAndView modelAndView) {

    }

    @Override
    public void afterCompletion(final HttpServletRequest request, final HttpServletResponse response, final Object handler,
                                final Exception ex) {

    }
}