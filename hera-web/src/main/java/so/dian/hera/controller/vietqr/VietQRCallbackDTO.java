/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.controller.vietqr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: VietQRCallbackDTO.java, v 1.0 2024-05-22 下午6:20 Exp $
 */
@Data
public class VietQRCallbackDTO implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202405143182103L;
    @JsonProperty("transactionid")
    private String transactionId;
    @JsonProperty("transactiontime")
    private Long transactionTime;
    @JsonProperty("referencenumber")
    private String referenceNumber;
    @JsonProperty("amount")
    private Long amount;
    private String content;
    @JsonProperty("bankaccount")
    private String bankAccount;
    private String transType;
    private String orderId;
    private String sign;
    private String terminalCode;
    private String urlLink;
}