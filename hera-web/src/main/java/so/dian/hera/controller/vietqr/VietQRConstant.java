/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.controller.vietqr;

import java.security.SecureRandom;
import java.util.Base64;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: VietQRConstant.java, v 1.0 2024-05-24 下午9:16 Exp $
 */
public class VietQRConstant {
    public static final String VIETQR_SECRET_KEY = "dmlldHFyLWRldi1lcngwMTpkbWxsZEhGeUxXRndhUzFoWTNacE1ITnhadz09";
//    public static void main(String[] args) {
//        // 生成256位的随机字节数组
//        byte[] secretKeyBytes = new byte[32]; // 32字节对应256位
//        SecureRandom random = new SecureRandom();
//        random.nextBytes(secretKeyBytes);
//
//        // 使用Base64编码为字符串形式
//        String secretKey = Base64.getEncoder().encodeToString(secretKeyBytes);
//
//        System.out.println("Generated SECRET_KEY: " + secretKey);
//    }
}