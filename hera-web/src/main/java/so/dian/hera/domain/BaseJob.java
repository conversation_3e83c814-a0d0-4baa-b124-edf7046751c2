package so.dian.hera.domain;

import lombok.Data;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-01-10 14:19
 */
@Data
public abstract class BaseJob {

    private final String type;

    private Date deliverTime;

    private Date checkTime;

    private Date expireTime;

    private boolean deliverEmail = false;

    public BaseJob() {
        this.type = getClass().getName();
        this.deliverTime = Timestamp.from(Instant.now().plus(1, ChronoUnit.DAYS));
    }

    /**
     * 是否需要发送邮件
     */
    public boolean needDeliver() {
        if (deliverEmail) {
            return false;
        }
        if (new Date().after(deliverTime)) {
            return true;
        }
        return false;
    }

    /**
     * 是否超过任务检测周期
     */
    public boolean isExpire() {
        if (new Date().after(expireTime)) {
            return true;
        }
        return false;
    }
}
