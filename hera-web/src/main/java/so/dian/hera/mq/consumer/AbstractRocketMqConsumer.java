package so.dian.hera.mq.consumer;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.PostConstruct;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractRocketMqConsumer {

    @Value("${spring.rocketmq.name-server}")
    private String nameServerAddr;

    private DefaultMQPushConsumer consumer;

    @PostConstruct
    protected void start() throws MQClientException {
        consumer = new DefaultMQPushConsumer(getGroup());
        consumer.setNamesrvAddr(nameServerAddr);
        StringBuilder tags = new StringBuilder();
        Iterator<String> tagIterator = getTags().iterator();
        while (tagIterator.hasNext()) {
            tags.append(tagIterator.next());
            if (tagIterator.hasNext()) {
                tags.append("||");
            }
        }
        consumer.subscribe(getTopic(), tags.toString());
        consumer.registerMessageListener(this::process);
        consumer.start();
        log.info("rocketmq consumer started. topic: {}, group: {}, tags: {}", getTopic(), getGroup(), getTags());
    }

    /**
     * 获取topic
     * @return
     */
    protected abstract String getTopic();

    /**
     * 获取group
     * @return
     */
    protected abstract String getGroup();

    /**
     * 获取tag
     * @return
     */
    protected abstract List<String> getTags();

    protected abstract ConsumeConcurrentlyStatus process(final List<MessageExt> msgs, final ConsumeConcurrentlyContext context);

}
