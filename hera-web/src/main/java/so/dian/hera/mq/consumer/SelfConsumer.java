package so.dian.hera.mq.consumer;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.util.LogColorUtils;
import so.dian.hera.mq.consumer.self.MqHandler;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * 接收hera自发消息
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SelfConsumer extends AbstractRocketMqConsumer {


    @Autowired
    private ApplicationContext applicationContext;

    @Value("${notify.rocketmq.self.topic}")
    private String topic;
    @Value("${notify.rocketmq.self.producerGroup}")
    private String group;

    private Map<String, MqHandler> tagHandlerMap = Maps.newHashMap();

    @PostConstruct
    public void init() {
        Map<String, MqHandler> map = applicationContext.getBeansOfType(MqHandler.class);
        for (Map.Entry<String, MqHandler> entry : map.entrySet()) {
            MqHandler mqHandler = entry.getValue();
            tagHandlerMap.put(mqHandler.subscribeTag(), mqHandler);
        }
        log.info("self tagHandler inited. {}", tagHandlerMap.size());
    }

    @Override
    protected String getTopic() {
        return topic;
    }

    @Override
    protected String getGroup() {
        return group;
    }

    /**
     * 将需要消费的tag注入，AbstractRocketMqConsumer#start 启动时创建客户端连接
     *
     * @return
     */
    @Override
    protected List<String> getTags() {
        return Lists.newArrayList(tagHandlerMap.keySet());
    }

    @Override
    protected ConsumeConcurrentlyStatus process(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        for (MessageExt messageExt : msgs) {
            String tags = messageExt.getTags();
            String bodyStr = new String(messageExt.getBody(), StandardCharsets.UTF_8);
            log.info("HeraSelfConsumer tags: {},bodyStr: {}", tags, bodyStr);
            try {
                MqHandler mqHandler = tagHandlerMap.get(tags);
                if (mqHandler == null) {
                    log.error(LogColorUtils.redMessage("no TagHandler for tag: {}"), tags);
                    return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
                }
                Boolean handleResult = mqHandler.handle(bodyStr);
                return handleResult ? ConsumeConcurrentlyStatus.CONSUME_SUCCESS : ConsumeConcurrentlyStatus.RECONSUME_LATER;
            } catch (Throwable t) {
                log.error("HeraSelfConsumer 消息消费失败, tag:{}, body:{}", tags, bodyStr, t);
                return ConsumeConcurrentlyStatus.RECONSUME_LATER;
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
}
