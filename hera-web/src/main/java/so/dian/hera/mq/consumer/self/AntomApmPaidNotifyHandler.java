package so.dian.hera.mq.consumer.self;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.domain.notify.ChannelNotifyBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.hera.service.pay.apm.ApmPayService;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AntomApmPaidNotifyHandler implements MqHandler{

    @Value("${notify.rocketmq.self.tag.antom.apmPaidNotify}")
    private String antomApmPaidNotify;

    @Resource
    private ApmPayService apmPayService;

    @Override
    public Boolean handle(String msgBody) {
        ChannelNotifyBody channelNotifyBody = JSON.parseObject(msgBody, ChannelNotifyBody.class);
        boolean handleResult = apmPayService.handlePaidSelfMsg(channelNotifyBody);
        return handleResult;
    }

    @Override
    public String subscribeTag() {
        return antomApmPaidNotify;
    }

}
