/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.mq.consumer.self;

import com.chargebolt.hera.client.dto.pay.refund.req.RefundFeeRequest;
import com.chargebolt.hera.client.dto.pay.refund.req.RefundRequest;
import com.chargebolt.hera.client.enums.status.RetryStatus;
import com.chargebolt.hera.domain.notify.DelayRefundBody;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.hera.service.refund.RefundService;
import so.dian.mofa3.lang.util.JsonUtil;

import java.util.List;
import java.util.Objects;

/**
 * 付款重试消息处理
 *
 * <AUTHOR>
 * @version: PaymentRetryHandler.java, v 1.0 2024-05-06 下午5:21 Exp $
 */
@Slf4j
@Component
public class DelayRefundHandler implements MqHandler {
    @Value("${notify.rocketmq.self.tag.refund-delay-initiate}")
    private String refundDelayInitiate;

    private final RefundService refundService;
    public DelayRefundHandler(ObjectProvider<RefundService> refundServiceProvider){
        this.refundService= refundServiceProvider.getIfUnique();
    }
    @Override
    public Boolean handle(final String msgBody) {
        log.info("延迟退款消息消费:{}", msgBody);
        DelayRefundBody body = JsonUtil.jsonToBean(msgBody, new TypeReference<DelayRefundBody>() {});
        if(StringUtils.isBlank(body.getTradeNo())|| Objects.isNull(body.getRefundAmount())){
            log.error("消息体内容错误,丢弃: {}", msgBody);
            return Boolean.TRUE;
        }
        RefundRequest refundRequest= new RefundRequest();
        refundRequest.setTradeNo(body.getTradeNo());
        refundRequest.setAmount(body.getRefundAmount().intValue());
        refundRequest.setRefundReason(body.getRefundReason());
        refundRequest.setSystem(body.getSystem());
        refundRequest.setRefundFeeRequestList(body.getRefundFeeRequestList());
        try{
            log.info("延迟退款发起：{}", JsonUtil.beanToJson(refundRequest));
            refundService.refund(refundRequest);
        }catch (Exception e){
            log.error("退款失败",e);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


    @Override
    public String subscribeTag() {
        return refundDelayInitiate;
    }
}