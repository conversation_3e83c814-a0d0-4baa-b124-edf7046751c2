/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.mq.consumer.self;

import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.client.enums.status.RefundStatus;
import com.chargebolt.hera.domain.RefundDO;
import com.chargebolt.hera.domain.notify.DelayQueryResultBody;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;
import so.dian.hera.interceptor.dto.rsp.RefundQueryResultDTO;
import so.dian.hera.service.pay.PayResultQueryService;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.hera.service.persist.db.RefundPersistService;
import so.dian.hera.service.refund.RefundResultQueryService;
import so.dian.hera.utils.AssertUtils;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.common.constants.BusinessConstants;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.mq.producer.BizMqProducer;

import java.util.List;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloPayPayNotifyHandler.java, v 1.0 2024-04-10 4:40 PM Exp $
 */
@Slf4j
@Component
public class PayRefundQueryHandler implements MqHandler {
    @Value("${notify.rocketmq.self.tag.pay-refund-delay-query}")
    private String payRefundDelayQueryTag;

    private final PaymentPersistService paymentPersistService;
    private final RefundPersistService refundPersistService;
    private final PayResultQueryService payResultQueryService;
    private final RefundResultQueryService refundResultQueryService;
    private final BizMqProducer bizMqProducer;

    private final List<Integer> supportMultiRefund = Lists.newArrayList(PaywayEnum.ANTOM_CHECKOUT_CARD.getPayway(),PaywayEnum.ANTOM_CHECKOUT_APM.getPayway());

    public PayRefundQueryHandler(ObjectProvider<PaymentPersistService> paymentPersistServiceProvider,
                                 ObjectProvider<RefundPersistService> refundPersistServiceProvider,
                                 ObjectProvider<PayResultQueryService> payResultQueryServiceProvider,
                                 ObjectProvider<RefundResultQueryService> refundResultQueryServiceProvider,
                                 ObjectProvider<BizMqProducer> bizMqProducerProvider){
        this.paymentPersistService= paymentPersistServiceProvider.getIfUnique();
        this.refundPersistService= refundPersistServiceProvider.getIfUnique();
        this.payResultQueryService= payResultQueryServiceProvider.getIfUnique();
        this.refundResultQueryService= refundResultQueryServiceProvider.getIfUnique();
        this.bizMqProducer= bizMqProducerProvider.getIfUnique();
    }
    @Override
    public Boolean handle(final String msgBody) {
        log.info("支付、退款结果查询消息：{}", msgBody);
        DelayQueryResultBody delayQueryBody= JsonUtil.jsonToBean(msgBody, DelayQueryResultBody.class);
        // 支付
        if(Objects.equals(delayQueryBody.getType(), BusinessConstants.TRANSACTION_PAY)){
            log.info("支付消息处理：{}",JsonUtil.beanToJson(delayQueryBody));
            PaymentDO paymentDO= paymentPersistService.getPaymentByTradeNo(delayQueryBody.getTransNo());
            if(Objects.isNull(paymentDO)){
                log.error("支付订单不存在:{}",msgBody);
                return Boolean.TRUE;
            }

            // 查询支付结果
            PayQueryResultDTO resultDTO= payResultQueryService.queryPayResult(paymentDO);
            // 成功、失败、取消是终态，直接更新
            if(Objects.equals(resultDTO.getStatus(), PayStatus.PAID.getCode())
                    ||Objects.equals(resultDTO.getStatus(), PayStatus.CANCEL.getCode())
                    ||Objects.equals(resultDTO.getStatus(), PayStatus.FAIL.getCode())){
                // 使用支付成功回调的逻辑
                if(Objects.equals(paymentDO.getStatus(), resultDTO.getStatus())){
                    log.info("支付订单 tradeNo: {}，状态一致，不更新", paymentDO.getTradeNo());
                    return Boolean.TRUE;
                }
                updatePayment(paymentDO, resultDTO);
                paymentDO.setStatus(resultDTO.getStatus());
                // 消息发送逻辑
                sendMsg(paymentDO);
                return Boolean.TRUE;
            }else if(Objects.equals(resultDTO.getStatus(), PayStatus.INIT.getCode())){
                log.info("订单处理中,放回队列,TradeNo:{}", paymentDO.getTradeNo());
                return Boolean.FALSE;
            }else{
                log.info("支付查询其他状态,放回队列:{}", JsonUtil.beanToJson(resultDTO));
                return Boolean.FALSE;
            }
            // 退款
        }else if(Objects.equals(delayQueryBody.getType(), BusinessConstants.TRANSACTION_REFUND)){
            log.info("退款消息处理：{}",JsonUtil.beanToJson(delayQueryBody));
            RefundDO refundDO = refundPersistService.getRefund(delayQueryBody.getTransNo());
            if(Objects.isNull(refundDO)){
                log.error("退款订单不存在:{}",msgBody);
                return Boolean.TRUE;
            }
           if(Objects.equals(refundDO.getStatus(),RefundStatus.REFUNDED.getCode())||Objects.equals(refundDO.getStatus(),RefundStatus.FAIL.getCode())){
               log.warn("退款订单已至终态，不做处理 refundNo: {}",refundDO.getRefundNo());
               return Boolean.TRUE;
           }
            PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(refundDO.getTradeNo());
            if(Objects.isNull(paymentDO)){
                log.error("支付订单不存在:{}",msgBody);
                return Boolean.TRUE;
            }

            // 如果 钱包支付 押金抵部分订单的且原支付单是 midtrans 支付的订单则直接退款补偿，具体退款结果以退款通知为准
            if(Objects.equals(paymentDO.getPayType(),PaywayEnum.DEPOSIT.getPayway())){
                PaymentDO oriPaymentDO = paymentPersistService.getPaymentByTradeNo(paymentDO.getPayNo());
                if(Objects.nonNull(oriPaymentDO) && Objects.equals(oriPaymentDO.getPayType(),PaywayEnum.MIDTRANS_CHECKOUT_APM.getPayway())){
                    return Boolean.TRUE;
                }
            }

            RefundQueryResultDTO resultDTO = refundResultQueryService.queryRefundResult(refundDO);
            if(Objects.equals(resultDTO.getStatus(), PayStatus.REFUNDED.getCode())
                    || Objects.equals(resultDTO.getStatus(), PayStatus.FAIL.getCode())){
                // 更新退款状态
                updateRefund(refundDO, paymentDO, resultDTO);
                return Boolean.TRUE;
            }else if(Objects.equals(resultDTO.getStatus(), PayStatus.REFUNDING.getCode())){
                log.info("订单处理中,放回队列,refundNo:{}", refundDO.getRefundNo());
                return Boolean.FALSE;
            }else{
                log.info("退款查询其他状态,放回队列:{}", JsonUtil.beanToJson(resultDTO));
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 根据支付单bizType 和 payWay分发投递消息
     *
     * @param paymentDO
     */
    private void sendMsg(PaymentDO paymentDO){
        if(Objects.equals(paymentDO.getBizType(), PaymentBizTypeEnum.PAY_ORDER.getCode())
                && Objects.equals(paymentDO.getPayType(), PaywayEnum.PRE_AUTH_CAPTURE.getPayway())){
            bizMqProducer.sendBankcardPreAuthCaptureMsg(paymentDO);
        }else if(Objects.equals(paymentDO.getBizType(), PaymentBizTypeEnum.PAY_DEPOSIT.getCode())
                && Objects.equals(paymentDO.getPayType(), PaywayEnum.WECHAT_MINIPROGRAM.getPayway())){
            bizMqProducer.sendPayMsg(paymentDO);
        }else if(Objects.equals(paymentDO.getBizType(), PaymentBizTypeEnum.PAY_DEPOSIT.getCode())
                && (Objects.equals(paymentDO.getPayType(), PaywayEnum.PINGPONG_CHECKOUT_APM.getPayway())
                || Objects.equals(paymentDO.getPayType(), PaywayEnum.ZALOPAY_MINI.getPayway()))){
            bizMqProducer.sendApmPaidMsg(paymentDO);
        } else if(Objects.equals(paymentDO.getBizType(), PaymentBizTypeEnum.PAY_DEPOSIT.getCode())
                && Objects.equals(paymentDO.getPayType(), PaywayEnum.PINGPONG_ONE_CLICK.getPayway())){
            bizMqProducer.sendApmTokenAppliedMsg(paymentDO);
        }else if(Objects.equals(paymentDO.getBizType(), PaymentBizTypeEnum.CARD_PRE_AUTH.getCode())
                && Objects.equals(paymentDO.getPayType(), PaywayEnum.PINGPONG_CHECKOUT_APM.getPayway())){
            bizMqProducer.sendApmPaidMsg(paymentDO);
        }else if(Objects.equals(paymentDO.getBizType(), PaymentBizTypeEnum.CARD_PRE_AUTH.getCode())
                && (Objects.equals(paymentDO.getPayType(), PaywayEnum.WOOSHPAY_CARD.getPayway())
                || Objects.equals(paymentDO.getPayType(), PaywayEnum.PINGPONG_CHECKOUT_CARD.getPayway()))){
            bizMqProducer.sendBankcardPreAuthCreateMsg(paymentDO);
        }
    }


    @Override
    public String subscribeTag() {
        return payRefundDelayQueryTag;
    }

    private void updatePayment(PaymentDO paymentDO, PayQueryResultDTO dto){

        paymentDO.setOriStatus(paymentDO.getStatus());
        if(Objects.nonNull(dto.getPayTime())){
            paymentDO.setPayTime(dto.getPayTime());
        }
        if(StringUtils.isNotBlank(dto.getPayNo())){
            paymentDO.setPayNo(dto.getPayNo());
        }
        // zalopay更新三方支付订单号
        if(Objects.equals(PaywayEnum.ZALOPAY.getPayway(), paymentDO.getPayType())){
            paymentDO.setPayNo(dto.getThirdPayTradeNo());
        }
        paymentDO.setStatus(dto.getStatus());
        paymentPersistService.updateStatus(paymentDO);
    }

    private void updateRefund(RefundDO refundDO, PaymentDO paymentDO, RefundQueryResultDTO dto){
            if (Objects.equals(dto.getStatus(), PayStatus.REFUNDED.getCode())
                    && Objects.equals(refundDO.getStatus(), RefundStatus.REFUNDING.getCode())) {
                refundDO.setStatus(RefundStatus.REFUNDED.getCode());
                if(Objects.nonNull(dto.getRefundTime())){
                    if(Objects.equals(paymentDO.getStatus(), PayStatus.REFUNDING.getCode())){
                        paymentDO.setRefundAmount(refundDO.getAmount());
                    }else if(Objects.equals(paymentDO.getStatus(), PayStatus.REFUNDED.getCode())){
                        paymentDO.setRefundAmount((Objects.isNull(paymentDO.getRefundAmount())?0:paymentDO.getRefundAmount())
                                + refundDO.getAmount());
                    }
                    // 支持多次退款的情况下 支付单退款申请时已经累计退款金额,无需重复累计
                    if(supportMultiRefund.contains(paymentDO.getPayType())){
                        paymentDO.setRefundAmount(null);
                    }
                    refundDO.setRefundTime(dto.getRefundTime());
                }
                if(StringUtils.isNotBlank(dto.getOutTraceNo())){
                    refundDO.setOutTraceNo(dto.getOutTraceNo());
                }
                boolean updateResult = refundPersistService.update(refundDO);
                AssertUtils.notTrueWithBizExp(updateResult, HeraBizErrorCodeEnum.ILLEGAL_ERROR, "退款更新失败");

                // 更新支付单为已退款
                paymentDO.setOriStatus(paymentDO.getStatus());
                paymentDO.setStatus(dto.getStatus());
                paymentPersistService.updateStatus(paymentDO);
                // 发送退款失败消息 krons应用RefundMsgHandler
                bizMqProducer.sendRefundMsg(paymentDO, refundDO);
            }else if(Objects.equals(dto.getStatus(), PayStatus.FAIL.getCode())
                    &&Objects.equals(refundDO.getStatus(), RefundStatus.REFUNDING.getCode())){
                refundDO.setStatus(RefundStatus.FAIL.getCode());
                if(StringUtils.isNotBlank(dto.getOutTraceNo())){
                    refundDO.setOutTraceNo(dto.getOutTraceNo());
                }
//                refundDO.setErrorMsg(dto.getResponseMsg());
                boolean updateResult = refundPersistService.update(refundDO);
                AssertUtils.notTrueWithBizExp(updateResult, HeraBizErrorCodeEnum.ILLEGAL_ERROR, "退款更新失败");

                // 支付状态还原为已支付
                paymentDO.setOriStatus(paymentDO.getStatus());
                paymentDO.setStatus(PayStatus.PAID.getCode());
                paymentPersistService.updateStatus(paymentDO);
                // 发送退款失败消息 krons应用RefundMsgHandler
                bizMqProducer.sendRefundMsg(paymentDO, refundDO);
            }

    }
}