/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.mq.consumer.self;

import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.client.enums.status.RetryStatus;
import com.chargebolt.hera.domain.PaymentRetryRecord;
import com.chargebolt.hera.domain.notify.PaymentRetryBody;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.PaymentCaptureRetryProcessor;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.hera.service.persist.db.PaymentRetryRecordService;
import so.dian.hera.service.route.RouteService;
import so.dian.mofa3.lang.util.JsonUtil;
import java.util.Objects;

/**
 * 付款重试消息处理
 *
 * <AUTHOR>
 * @version: PaymentRetryHandler.java, v 1.0 2024-05-06 下午5:21 Exp $
 */
@Slf4j
@Component
public class PaymentRetryHandler implements MqHandler {
    @Value("${notify.rocketmq.self.tag.payment-retry-delay}")
    private String paymentRetryDelay;

    private final RouteService routeService;
    private final PaymentPersistService paymentPersistService;
    private final PaymentRetryRecordService paymentRetryRecordService;

    public PaymentRetryHandler(ObjectProvider<RouteService> routeServiceProvider,
                               ObjectProvider<PaymentRetryRecordService> paymentRetryRecordServiceProvider,
                               ObjectProvider<PaymentPersistService> paymentPersistServiceProvider){
        this.routeService= routeServiceProvider.getIfUnique();
        this.paymentRetryRecordService= paymentRetryRecordServiceProvider.getIfUnique();
        this.paymentPersistService= paymentPersistServiceProvider.getIfUnique();
    }

    @Override
    public Boolean handle(final String msgBody) {
        log.info("预授权扣款重试消息:{}", msgBody);
        PaymentRetryBody retryBody = JsonUtil.jsonToBean(msgBody, PaymentRetryBody.class);
        if(StringUtils.isBlank(retryBody.getTradeNo())){
            log.error("消息体内容错误,丢弃: {}", msgBody);
            return Boolean.TRUE;
        }
        // 查询补偿重试记录状态为处理中的记录
        PaymentRetryRecord paymentRetryRecord = paymentRetryRecordService.selectProcessingByTradeNo(retryBody.getTradeNo());
        if(Objects.isNull(paymentRetryRecord)){
            log.error("重试数据记录不存在: {}", retryBody.getTradeNo());
            return Boolean.TRUE;
        }
        // 查询扣款（请款）的Payment记录
        PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(retryBody.getTradeNo());
        if(Objects.isNull(paymentDO)){
            log.error("预授权支付凭证不存在: {}", retryBody.getTradeNo());
            return Boolean.TRUE;
        }

        // 订单已至终态(支付或退款)，无需重试
        if(Objects.equals(paymentDO.getStatus(), PayStatus.CARD_CAPTURED.getCode()) ||
                Objects.equals(paymentDO.getStatus(), PayStatus.PAID.getCode()) ||
                Objects.equals(paymentDO.getStatus(), PayStatus.REFUNDING.getCode()) ||
                Objects.equals(paymentDO.getStatus(), PayStatus.REFUNDED.getCode())  ||
                Objects.equals(paymentDO.getStatus(), PayStatus.REFUND_FAILED.getCode())
        ){
            log.warn("订单已至终态，无需补偿重试: {},status: {}", retryBody.getTradeNo(),paymentDO.getStatus());
            return Boolean.TRUE;
        }

        // 渠道解析获取渠道处理器
        PaywayEnum paywayEnum = routeService.channelAnalysis(paymentRetryRecord.getPayway());
        PaymentCaptureRetryProcessor processor = routeService.getCaptureRetryProcessor(paywayEnum);
        RetryStatus status= processor.handleCaptureRetry(paymentRetryRecord, paymentDO);

        // 订单状态已至终态（成功或失败）结束任务
        if(Objects.equals(status, RetryStatus.SUCCESSFUL)||Objects.equals(status, RetryStatus.FAILED)){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    @Override
    public String subscribeTag() {
        return paymentRetryDelay;
    }

}