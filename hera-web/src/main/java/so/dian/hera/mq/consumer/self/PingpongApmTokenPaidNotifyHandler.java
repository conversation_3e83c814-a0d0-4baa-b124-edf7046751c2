package so.dian.hera.mq.consumer.self;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.domain.notify.ChannelNotifyBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.hera.service.pay.apmtoken.ApmTokenPayService;

import javax.annotation.Resource;

@Slf4j
@Component
public class PingpongApmTokenPaidNotifyHandler implements MqHandler {
    @Resource
    private ApmTokenPayService apmTokenPayService;
    @Value("${notify.rocketmq.self.tag.pingpong.apmTokenPaidNotify}")
    private String pingpongApmTokenPaidNotify;

    @Override
    public Boolean handle(String msgBody) {
        ChannelNotifyBody channelNotifyBody = JSON.parseObject(msgBody, ChannelNotifyBody.class);
        boolean handleResult = apmTokenPayService.handleApmTokenPaidSelfMsg(channelNotifyBody);
        return handleResult;
    }

    @Override
    public String subscribeTag() {
        return pingpongApmTokenPaidNotify;
    }
}
