package so.dian.hera.mq.consumer.self;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.domain.notify.RefundNotifyBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.hera.service.refund.RefundService;

import javax.annotation.Resource;

@Slf4j
@Component
public class RefundSelfMsgHandler implements MqHandler {

    @Resource
    private RefundService refundService;
    @Value("${notify.rocketmq.self.tag.refund}")
    private String refundTag;

    @Override
    public Boolean handle(String msgBody) {
        RefundNotifyBody refundNotifyBody = JSON.parseObject(msgBody, RefundNotifyBody.class);
        return refundService.handleRefundNotify(refundNotifyBody);
    }

    @Override
    public String subscribeTag() {
        return refundTag;
    }
}
