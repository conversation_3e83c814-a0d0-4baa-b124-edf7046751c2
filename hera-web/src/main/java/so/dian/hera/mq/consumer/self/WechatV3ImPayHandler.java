package so.dian.hera.mq.consumer.self;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.callback.impay.WxImPayCallbackDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.hera.service.pay.gateway.GatewayPayService;

import javax.annotation.Resource;

@Slf4j
@Component
public class WechatV3ImPayHandler implements MqHandler {

    @Resource
    private GatewayPayService gatewayPayService;
    @Value("${notify.rocketmq.self.tag.wechatV3ImPay}")
    private String wechatV3ImPayTag;

    @Override
    public Boolean handle(String msgBody) {
        gatewayPayService.handlePayCallback(JSON.parseObject(msgBody, WxImPayCallbackDTO.class));
        return true;
    }

    @Override
    public String subscribeTag() {
        return wechatV3ImPayTag;
    }
}
