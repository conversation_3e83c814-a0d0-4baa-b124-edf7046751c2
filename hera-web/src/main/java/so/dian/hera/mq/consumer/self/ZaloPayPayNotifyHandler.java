/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.mq.consumer.self;

import com.chargebolt.hera.client.dto.callback.impay.BaseImPayCallbackDTO;
import com.chargebolt.hera.domain.notify.ZalopayNotifyBody;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.hera.service.pay.gateway.GatewayPayService;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.hera.utils.AssertUtils;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;

import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloPayPayNotifyHandler.java, v 1.0 2024-04-10 4:40 PM Exp $
 */
@Slf4j
@Component
public class ZaloPayPayNotifyHandler implements MqHandler {
    @Value("${notify.rocketmq.self.tag.zalopay.pay-notify}")
    private String zaloPayPayTag;

    private final GatewayPayService gatewayPayService;
    private final PaymentPersistService paymentPersistService;
    public ZaloPayPayNotifyHandler(ObjectProvider<GatewayPayService> gatewayPayServiceProvider,
                                   ObjectProvider<PaymentPersistService> paymentPersistServiceProvider){
        this.gatewayPayService= gatewayPayServiceProvider.getIfUnique();
        this.paymentPersistService= paymentPersistServiceProvider.getIfUnique();
    }
    @Override
    public Boolean handle(final String msgBody) {
        log.info("zalopay 支付消息消费：{}", msgBody);
        ZalopayNotifyBody zalopayNotifyBody= JsonUtil.jsonToBean(msgBody, ZalopayNotifyBody.class);
        BaseImPayCallbackDTO callbackDTO= new BaseImPayCallbackDTO();
        callbackDTO.setPayStatus(zalopayNotifyBody.getResult());
        callbackDTO.setPayTime(new DateBuild(zalopayNotifyBody.getPayTimestamp()).toDate());
        PaymentDO paymentDO= paymentPersistService.getPaymentByTradeNo(zalopayNotifyBody.getTradeNo());
        AssertUtils.trueWithBizExp(Objects.isNull(paymentDO), HeraBizErrorCodeEnum.ORDER_NON_EXIST);
        callbackDTO.setOutTradeNo(paymentDO.getTradeNo());
        callbackDTO.setPaymentTradeNo(zalopayNotifyBody.getThirdPartyPayNo());
        gatewayPayService.handlePayCallback(callbackDTO);
        return Boolean.TRUE;
    }

    @Override
    public String subscribeTag() {
        return zaloPayPayTag;
    }
}