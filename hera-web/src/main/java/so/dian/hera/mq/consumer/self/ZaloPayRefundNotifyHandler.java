/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.mq.consumer.self;

import com.chargebolt.hera.client.dto.callback.refund.BaseRefundCallbackDTO;
import com.chargebolt.hera.domain.notify.ZalopayNotifyBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.hera.service.pay.gateway.GatewayPayService;
import so.dian.mofa3.lang.util.JsonUtil;

import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloPayPayNotifyHandler.java, v 1.0 2024-04-10 4:40 PM Exp $
 */
@Slf4j
@Component
@Deprecated
public class ZaloPayRefundNotifyHandler implements MqHandler {
    @Value("${notify.rocketmq.self.tag.zalopay.refund-notify}")
    private String zaloPayRefundTag;

    private final GatewayPayService gatewayPayService;
    public ZaloPayRefundNotifyHandler(ObjectProvider<GatewayPayService> gatewayPayServiceProvider){
        this.gatewayPayService= gatewayPayServiceProvider.getIfUnique();
    }
    @Deprecated
    @Override
    public Boolean handle(final String msgBody) {
        ZalopayNotifyBody zalopayNotifyBody= JsonUtil.jsonToBean(msgBody, ZalopayNotifyBody.class);
        BaseRefundCallbackDTO callbackDTO= new BaseRefundCallbackDTO();
        callbackDTO.setRefundStatus(zalopayNotifyBody.getResult());
        callbackDTO.setRefundTime(new Date());
        callbackDTO.setRefundOrderNo(zalopayNotifyBody.getRefundNo());
        callbackDTO.setRefundId(zalopayNotifyBody.getThirdPartyRefundNo());
        callbackDTO.setResponseMsg(zalopayNotifyBody.getResponseMsg());
        gatewayPayService.handleRefundCallback(callbackDTO);
        return Boolean.TRUE;
    }

    @Override
    public String subscribeTag() {
        return zaloPayRefundTag;
    }
}