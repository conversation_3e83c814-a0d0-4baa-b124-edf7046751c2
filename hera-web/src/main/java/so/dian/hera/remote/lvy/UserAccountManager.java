package so.dian.hera.remote.lvy;

import com.chargebolt.zeus.controller.request.VietqrRechargeRequest;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.entity.BizResult;
import so.dian.hera.remote.lvy.api.UserAccountClient;
import so.dian.lvy.pojo.dto.UserAccountDTO;
import so.dian.lvy.pojo.enums.AccountOperTypeEnum;
import so.dian.lvy.pojo.param.AccountModifyParam;
import so.dian.lvy.pojo.param.AccountParam;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class UserAccountManager {

    @Resource
    private UserAccountClient userAccountClient;

    public void decAcountAmount(AccountModifyParam modifyParam) {
        modifyParam.setOperType(AccountOperTypeEnum.DEC_MONEY.getCode());
        BizResult<UserAccountDTO> userAccountDTOBizResult = userAccountClient.accountAmountModify(modifyParam);
        log.info("lvy-账户资金扣减，param:{},rsp:{}", JsonUtil.beanToJson(modifyParam), JsonUtil.beanToJson(userAccountDTOBizResult));
        if (!userAccountDTOBizResult.isSuccess()) {
            log.error("lvy-账户资金扣减异常，param:{},rsp:{}", modifyParam, userAccountDTOBizResult);
            throw HeraBizException.create(HeraBizErrorCodeEnum.ACCOUNT_UPDATE_ERROR);
        }
    }

    public void addAccountAmount(AccountModifyParam modifyParam) {
        modifyParam.setOperType(AccountOperTypeEnum.ADD_MAONEY.getCode());
        BizResult<UserAccountDTO> userAccountDTOBizResult = userAccountClient.accountAmountModify(modifyParam);
        log.info("doAccountRefund 账户添加金额：{}，返回结果：{}", JsonUtil.beanToJson(modifyParam), JsonUtil.beanToJson(userAccountDTOBizResult));
        if (!userAccountDTOBizResult.isSuccess()) {
            log.error("lvy-账户资金添加异常！param:{},rsp:{}", modifyParam, userAccountDTOBizResult.getMsg());
            throw HeraBizException.create(HeraBizErrorCodeEnum.ACCOUNT_UPDATE_ERROR);
        }
    }

    public List<UserAccountDTO> accountQuery(AccountParam accountParam){
        BizResult<List<UserAccountDTO>> userAccount = userAccountClient.getUserAccount(accountParam);
        log.info("用户账户获取，请求参数：{}，返回结果：{}", JsonUtil.beanToJson(accountParam), JsonUtil.beanToJson(userAccount));
        if (!userAccount.isSuccess()) {
            log.error("lvy-查询账户信息异常，param:{},rsp:{}", accountParam, userAccount);
        }
        return userAccount.getData();
    }

    public Boolean vietqrRechargeDeposit(VietqrRechargeRequest request){
        BizResult<Boolean> result = userAccountClient.vietqrRechargeDeposit(request);
        if (Boolean.FALSE.equals(result.isSuccess())) {
            log.error("vietQR押金充值异常，param:{},rsp:{}", JsonUtil.beanToJson(request), JsonUtil.beanToJson(result));
        }
        return result.getData();
    }

    public Boolean addUserDepositMoney(VietqrRechargeRequest request){
        BizResult<Boolean> result = userAccountClient.addUserDepositMoney(request);
        if (Boolean.FALSE.equals(result.isSuccess())) {
            log.error("押金充值异常，param:{},rsp:{}", JsonUtil.beanToJson(request), JsonUtil.beanToJson(result));
        }
        return result.getData();
    }
}
