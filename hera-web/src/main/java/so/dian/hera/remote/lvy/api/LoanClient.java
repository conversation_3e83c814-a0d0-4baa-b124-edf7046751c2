package so.dian.hera.remote.lvy.api;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import so.dian.commons.eden.entity.BizResult;
import so.dian.hera.configuration.FeignConfiguration;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;

/**
 * Created with lhc
 *
 * @Author: bailong Date: 2018/10/17 Time: 下午4:39 北京伊电园网络科技有限公司 2016-2017 © 版权所有 京ICP备17000101号
 */
@FeignClient(name = "loan", url = "${hera.feignUrl.internal.kronos}", fallbackFactory = LoanClient.LoanServiceFallbackFactory.class,
        configuration = {FeignConfiguration.class})
public interface LoanClient {

    /**
     * 查询账户——支持查询多个账户
     */
    @RequestMapping(value = "/1.0/vietqr/device/box/loan", method = RequestMethod.GET)
    BizResult<Object> vietqrLoan(@RequestParam("boxNo") String boxNo,
                                 @RequestParam("tradeNo") String tradeNo,
                                 @RequestParam("userId") Long userId);

    @Slf4j
    @Component
    class LoanServiceFallbackFactory implements FallbackFactory<LoanClient> {

        @Override
        public LoanClient create(Throwable throwable) {
            return new LoanClient() {
                @Override
                public BizResult<Object> vietqrLoan(@RequestParam("boxNo") String boxNo,
                                                    @RequestParam("tradeNo") String tradeNo,
                                                    @RequestParam("userId") Long userId) {
                    log.error("VietQR租借，弹出充电宝：{}", boxNo, throwable);
                    throw HeraBizException.create(HeraBizErrorCodeEnum.LVY_FALLBACK);
                }

            };
        }
    }
}
