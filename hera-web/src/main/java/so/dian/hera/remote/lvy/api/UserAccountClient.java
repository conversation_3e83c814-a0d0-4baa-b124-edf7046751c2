package so.dian.hera.remote.lvy.api;

import com.chargebolt.zeus.controller.request.VietqrRechargeRequest;
import org.springframework.web.bind.annotation.PostMapping;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import so.dian.commons.eden.entity.BizResult;
import so.dian.hera.configuration.FeignConfiguration;
import so.dian.lvy.pojo.dto.UserAccountDTO;
import so.dian.lvy.pojo.param.AccountModifyParam;
import so.dian.lvy.pojo.param.AccountParam;

import java.util.List;

/**
 * Created with lhc
 *
 * @Author: bailong Date: 2018/10/17 Time: 下午4:39 北京伊电园网络科技有限公司 2016-2017 © 版权所有 京ICP备********号
 */
@FeignClient(name = "lvy", url = "${hera.feignUrl.internal.kronos}", fallbackFactory = UserAccountClient.UserAccountServiceFallbackFactory.class,
        configuration = {FeignConfiguration.class})
public interface UserAccountClient {

    /**
     * 查询账户——支持查询多个账户
     */
    @RequestMapping(value = "/lvy/account/get", method = RequestMethod.POST)
    BizResult<List<UserAccountDTO>> getUserAccount(@RequestBody AccountParam accountParam);

    /**
     * 账户金额变更
     */
    @RequestMapping(value = "/lvy/account/amountModify", method = RequestMethod.POST)
    BizResult<UserAccountDTO> accountAmountModify(@RequestBody AccountModifyParam modifyParam);

    /**
     * vietqr 支付成功，押金充值
     *
     * @param request
     * @return
     */
    @PostMapping("/1.0/user/vietqr/doRechargeDeposit")
    BizResult<Boolean> vietqrRechargeDeposit(@RequestBody VietqrRechargeRequest request);

    /**
     * 增加用户押金余额，仅仅更新userAccount表
     * @param request
     * @return
     */
    @PostMapping("/1.0/user/addUserDepositMoney")
    BizResult<Boolean> addUserDepositMoney(@RequestBody VietqrRechargeRequest request);

    @Slf4j
    @Component
    class UserAccountServiceFallbackFactory implements FallbackFactory<UserAccountClient> {

        @Override
        public UserAccountClient create(Throwable throwable) {
            return new UserAccountClient() {
                @Override
                public BizResult<List<UserAccountDTO>> getUserAccount(AccountParam accountParam) {
                    log.error("资金账户中心调用失败！{}", accountParam, throwable);
                    throw HeraBizException.create(HeraBizErrorCodeEnum.LVY_FALLBACK);
                }

                @Override
                public BizResult<UserAccountDTO> accountAmountModify(AccountModifyParam modifyParam) {
                    log.error("资金账户中心调用失败！{}", modifyParam, throwable);
                    throw HeraBizException.create(HeraBizErrorCodeEnum.LVY_FALLBACK);
                }

                @Override
                public BizResult<Boolean> vietqrRechargeDeposit(final VietqrRechargeRequest request) {
                    log.error("vietQR押金充值调用失败！{}", JsonUtil.beanToJson(request), throwable);
                    throw HeraBizException.create(HeraBizErrorCodeEnum.LVY_FALLBACK);
                }

                @Override
                public BizResult<Boolean> addUserDepositMoney(final VietqrRechargeRequest request) {
                    log.error("押金充值调用失败！{}", JsonUtil.beanToJson(request), throwable);
                    throw HeraBizException.create(HeraBizErrorCodeEnum.LVY_FALLBACK);
                }
            };
        }
    }
}
