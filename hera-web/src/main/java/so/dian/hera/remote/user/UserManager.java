/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.remote.user;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.entity.BizResult;
import so.dian.hera.remote.lvy.api.UserAccountClient;
import so.dian.hera.remote.user.api.UserClient;
import so.dian.lvy.pojo.dto.UserAccountDTO;
import so.dian.lvy.pojo.param.AccountParam;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.prometheus.client.dto.LoginRequestDTO;
import so.dian.prometheus.client.dto.UserDTO;

import javax.annotation.Resource;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: UserManager.java, v 1.0 2024-06-20 下午2:45 Exp $
 */
@Slf4j
@Component
public class UserManager {

    @Resource
    private UserClient userClient;
    public UserDTO vietqrLogin(LoginRequestDTO requestDTO){
        BizResult<UserDTO> result= userClient.vietqrLogin(requestDTO);
        if (Boolean.FALSE.equals(result.isSuccess())) {
            log.error("vietqr创建用户失败，param:{},rsp:{}", JsonUtil.beanToJson(requestDTO), JsonUtil.beanToJson(result));
        }
        return result.getData();
    }
}