/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.remote.user.api;

import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.commons.eden.entity.BizResult;
import so.dian.hera.configuration.FeignConfiguration;
import so.dian.hera.remote.lvy.api.UserAccountClient;
import so.dian.lvy.pojo.dto.UserAccountDTO;
import so.dian.lvy.pojo.param.AccountModifyParam;
import so.dian.lvy.pojo.param.AccountParam;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.prometheus.client.dto.LoginRequestDTO;
import so.dian.prometheus.client.dto.UserDTO;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: UserClient.java, v 1.0 2024-06-20 下午2:45 Exp $
 */
@FeignClient(name = "user", url = "${hera.feignUrl.internal.kronos}", fallbackFactory = UserClient.UserClientFallbackFactory.class,
        configuration = {FeignConfiguration.class})
public interface UserClient {
    @PostMapping("/1.0/vietqr/user/login")
    BizResult<UserDTO> vietqrLogin(@RequestBody LoginRequestDTO loginRequestDTO);

    @Slf4j
    @Component
    class UserClientFallbackFactory implements FallbackFactory<UserClient> {
        @Override
        public UserClient create(final Throwable cause) {
            return new UserClient() {
                @Override
                public BizResult<UserDTO> vietqrLogin(final LoginRequestDTO loginRequestDTO) {
                    log.error("vietqr 用户创建失败。{}", loginRequestDTO, cause);
                    throw HeraBizException.create(HeraBizErrorCodeEnum.OPERATE_BUSY);
                }
            };
        }

//        @Override
//        public UserAccountClient create(Throwable throwable) {
//            return new UserAccountClient() {
//                @Override
//                public BizResult<List<UserAccountDTO>> getUserAccount(AccountParam accountParam) {
//                    log.error("资金账户中心调用失败！{}", accountParam, throwable);
//                    throw HeraBizException.create(HeraBizErrorCodeEnum.LVY_FALLBACK);
//                }
//
//                @Override
//                public BizResult<UserAccountDTO> accountAmountModify(AccountModifyParam modifyParam) {
//                    log.error("资金账户中心调用失败！{}", modifyParam, throwable);
//                    throw HeraBizException.create(HeraBizErrorCodeEnum.LVY_FALLBACK);
//                }
//            };
//        }
    }
}