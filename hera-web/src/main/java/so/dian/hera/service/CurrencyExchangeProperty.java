package so.dian.hera.service;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@NoArgsConstructor
@Data
@Component
@ConfigurationProperties(prefix = "common")
public class CurrencyExchangeProperty {

    private List<CurrencyExchange> currencyExchange;

    @NoArgsConstructor
    @Data
    public static class CurrencyExchange {
        @JsonProperty("sourceCurrency")
        private String sourceCurrency;
        @JsonProperty("targetCurrency")
        private String targetCurrency;
        @JsonProperty("exchangeRate")
        private Float exchangeRate;
    }
}
