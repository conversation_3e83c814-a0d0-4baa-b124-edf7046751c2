package so.dian.hera.service.component;

import com.chargebolt.hera.client.dto.pay.checkout.rsp.PreAuthCancelResultDTO;
import com.chargebolt.hera.client.enums.PaywayEnum;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.CheckoutPayProcessor;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.hera.service.persist.redis.AutoCancelRedisPersistService;
import so.dian.hera.service.route.RouteService;
import so.dian.platform.common.utils.ValidateUtil;

import javax.annotation.Resource;
import java.util.Set;

@Component
@Slf4j
public class AutoCancelProcessor {
    @Resource
    private AutoCancelRedisPersistService autoCancelRedisPersistService;
    @Autowired
    private PaymentPersistService paymentPersistService;
    @Autowired
    private RouteService routeService;
    @Autowired
    private PaymentProcessor paymentProcessor;

    public void elementAppend(boolean autoCancelFlag,String tradeNo){
        try{
            if(autoCancelFlag){
                autoCancelRedisPersistService.addMemberOfCancelSet(tradeNo);
            }
        }catch (Exception e){
            log.error("【autoCancel】- 单元添加异常，e:{}",e);
        }
    }

    public void elementRemove(String tradeNo){
        try{
            autoCancelRedisPersistService.removeMemberOfCancelSet(tradeNo);
        }catch (Exception e){
            log.error("【autoCancel】- 单元移除异常，e:{}",e);
        }
    }

    public Set getElements(){
        return autoCancelRedisPersistService.getCancelSet();
    }

    public void doHandle(String tradeNo){
        PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(tradeNo);
        if(paymentDO == null){
            log.error("【autoCancel】- 支付记录不存在,trade:{}",tradeNo);
            elementRemove(tradeNo);
            return;
        }
        PayStatus payStatus = PayStatus.explain(paymentDO.getStatus());
        ValidateUtil.requiredNotNull(payStatus, HeraBizErrorCodeEnum.STATUS_ERROR);
        switch (payStatus){
            case CARD_SEND:
            case CARD_ACCEPT:
            case CARD_CANCELING:
                break;
            case CARD_AUTHORIZED:
                cancelProcess(paymentDO);
                break;
            case CARD_CANCELED:
                log.warn("【autoCancel】- 待撤销订单已被撤销，tradeNo:{},paymentPO:{}",tradeNo, paymentDO);
                elementRemove(tradeNo);
                break;
            default:
                log.error("【autoCancel】- 待撤销订单状态异常，tradeNo:{},paymentPO:{}",tradeNo, paymentDO);
                elementRemove(tradeNo);
        }
    }

    private void cancelProcess(PaymentDO paymentDO) {
        //渠道解析
        PaywayEnum paywayEnum = routeService.channelAnalysis(paymentDO.getPayType());
        //获取渠道处理器
        CheckoutPayProcessor payProcessor = routeService.getCheckoutPayProcessor(paywayEnum);
        //撤销预处理
        processPreCancel(paymentDO);
        //发送撤销请求
        PreAuthCancelResultDTO rsp = payProcessor.doCancel(paymentDO);
        //撤销后处理
        processCancelRsp(rsp, paymentDO);
    }

    private void processPreCancel(PaymentDO paymentDO) {
        paymentDO.setOriStatus(PayStatus.CARD_AUTHORIZED.getCode());
        paymentDO.setStatus(PayStatus.CARD_CANCELING.getCode());
        paymentPersistService.updateStatus(paymentDO);
    }

    private void processCancelRsp(PreAuthCancelResultDTO rsp, PaymentDO paymentDO) {
        if (TransStatusEnum.FAIL.getKey().equals(rsp.getStatus())) {
            paymentProcessor.processCancelFail(paymentDO,rsp.getRspMsg());
        }else if(TransStatusEnum.SUCCESS.getKey().equals(rsp.getStatus())){
            paymentProcessor.processCancelSuccess(paymentDO);
        }

    }
}
