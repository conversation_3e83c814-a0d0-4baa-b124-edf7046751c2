package so.dian.hera.service.component;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CompensationProcessor {
//    @Autowired
//    private PendingRedisPersistService redisPersistService;
//    @Autowired
//    private PaymentPersistService paymentPersistService;
//    @Autowired
//    private RefundPersistService refundPersistService;
//    @Autowired
//    private CardPersistService cardPersistService;
//    @Autowired
//    private RouteService routeService;
//    @Autowired
//    private PaymentProcessor paymentProcessor;
//
//    public void elementAppend(String tradeNo){
//        try{
//            redisPersistService.addMemberOfPendingSet(tradeNo);
//        }catch (Exception e){
//            log.error("【compensation】- 单元添加异常，e:{}",e);
//        }
//    }
//
//    public void elementRemove(String tradeNo){
//        try{
//            redisPersistService.removeMemberOfPendingSet(tradeNo);
//        }catch (Exception e){
//            log.error("【compensation】- 单元移除异常，e:{}",e);
//        }
//    }
//
//    public Set getElements(){
//        return redisPersistService.getPendingSet();
//    }
//
//    public void doHandle(String tradeNo, Integer timeInterval) throws Exception{
//        PaymentPO paymentPO = getPayment(tradeNo);
//        if(RiskService.orderQueryDurationManage(paymentPO,timeInterval)){
//            return;
//        }
//        PayBizTypeEnum payBizTypeEnum = explainPayBizType(paymentPO.getPayBizType());
//        PayStatus payStatus = explainPayStatus(paymentPO.getStatus());
//        switch (payBizTypeEnum){
//            case SDK_PAY:
//                sdkPaymentProcess(paymentPO,payStatus);
//                break;
//            case AUTH_PAY:
//                authPaymentProcess(paymentPO,payStatus);
//                break;
//            case WITHHOLD:
//                agreePaymentProcess(paymentPO,payStatus);
//                break;
//            case ACCOUNT_PAY:
//                // TODO: 2019/8/8 待添加
//                break;
//        }
//    }
//
//    private void agreePaymentProcess(PaymentPO paymentPO, PayStatus payStatus) {
//        switch (payStatus){
//            case SEND:
//            case ACCEPT:
////                withholdingOrderProcess(paymentPO);
//                break;
//            case REFUNDING:
//                refundingOrderProcess(paymentPO);
//                break;
//            default:
//                elementRemove(paymentPO.getTradeNo());
//        }
//    }
//
//    private void authPaymentProcess(PaymentPO paymentPO, PayStatus payStatus) {
//        switch (payStatus){
//            case SEND:
//            case ACCEPT:
//                // TODO: 2019/8/8 无法获取Apple pay token，暂无法重试
//                // 无查询接口
//                break;
//            case CANCELING:
//                cancelingOrderProcess(paymentPO);
//                break;
//            case AJUSTING:
//                // TODO: 2019/8/8
//                break;
//            case CAPTURING:
//                // TODO: 2019/8/8
//                break;
//            case REFUNDING:
//                refundingOrderProcess(paymentPO);
//                break;
//            default:
//                elementRemove(paymentPO.getTradeNo());
//        }
//    }
//
//    private void sdkPaymentProcess(PaymentPO paymentPO, PayStatus payStatus) {
//        switch (payStatus){
//            case REFUNDING:
//                refundingOrderProcess(paymentPO);
//                break;
//            default:
//                elementRemove(paymentPO.getTradeNo());
//        }
//    }
//
//    private void cancelingOrderProcess(PaymentPO paymentPO) {
//        //渠道解析
//        PaywayEnum paywayEnum = routeService.channelAnalysis(paymentPO.getPayType());
//        //获取渠道处理器
//        AuthPayProcessor authPayProcessor = routeService.getAuthPayProcessor(paywayEnum);
//        //撤销补偿
//        CancelCompensateRsp compensateRsp = authPayProcessor.cancelCompensate(paymentPO);
//        //处理撤销结果
//        processCancelResult(compensateRsp,paymentPO);
//    }
//
//    private void processCancelResult(CancelCompensateRsp rsp, PaymentPO paymentPO) {
//        if (rsp == null) {
//            return;
//        }
//
//        if (TransStatusEnum.FAIL.getKey().equals(rsp.getStatus())) {
//            paymentProcessor.processCancelFail(paymentPO,rsp.getRspMsg());
//        }else if(TransStatusEnum.SUCCESS.getKey().equals(rsp.getStatus())){
//            paymentProcessor.processCancelSuccess(paymentPO);
//        }
//    }
//
//    private void withholdingOrderProcess(PaymentPO paymentPO) {
//        //渠道解析
//        PaywayEnum paywayEnum = routeService.channelAnalysis(paymentPO.getPayType());
//        //获取渠道处理器
//        AgreePayProcessor agreePayProcessor = routeService.getAgreePayProcessor(paywayEnum);
//        //绑卡信息查询
//        UserCardInfoPO cardInfoPO = cardPersistService.queryById(paymentPO.getBizId());
//        //代扣补偿
//        WithholdCompensateRsp compensateRsp = agreePayProcessor.withholdCompensate(cardInfoPO,paymentPO);
//        //处理代扣结果
//        processWithholdResult(compensateRsp,paymentPO);
//    }
//
//    private void processWithholdResult(WithholdCompensateRsp compensateRsp, PaymentPO paymentPO) {
//        if(TransStatusEnum.SUCCESS.getKey().equals(compensateRsp.getStatus())){
//            paymentProcessor.processCaptureSuccess(paymentPO,compensateRsp.getPaymentNo(),compensateRsp.getPayTime());
//            elementRemove(paymentPO.getTradeNo());
//        }else if(TransStatusEnum.FAIL.getKey().equals(compensateRsp.getStatus())){
//            paymentProcessor.processCaptureFail(paymentPO,compensateRsp.getRspMsg());
//            elementRemove(paymentPO.getTradeNo());
//        }
//    }
//
//    private void refundingOrderProcess(PaymentPO paymentPO) {
//        //渠道解析
//        PaywayEnum paywayEnum = routeService.channelAnalysis(paymentPO.getPayType());
//        //获取渠道处理器
//        BasicPayProcessor basicPayProcessor = routeService.getBasicPayProcessor(paywayEnum);
//        //退款补偿
//        RefundCompensateRsp compensateRsp = basicPayProcessor.refundCompensate(paymentPO,paywayEnum);
//        //处理退款结果
//        processRefundResult(compensateRsp,paymentPO);
//    }
//
//    private void processRefundResult(RefundCompensateRsp compensateRsp, PaymentPO paymentPO) {
//        if (compensateRsp == null) {
//            return;
//        }
//
//        RefundPO refundPO = refundPersistService.getRefundByTrade(paymentPO.getTradeNo());
//        if (refundPO.isDone()) {
//            elementRemove(paymentPO.getTradeNo());
//            return;
//        }
//
//        if(TransStatusEnum.SUCCESS.getKey().equals(compensateRsp.getStatus())){
//            RefundResult result = RefundResult.success();
//            result.setRefundTime(compensateRsp.getRefundTime());
//
//            paymentProcessor.processRefundSuccess(refundPO, result, paymentPO);
//            elementRemove(paymentPO.getTradeNo());
//        }else if(TransStatusEnum.FAIL.getKey().equals(compensateRsp.getStatus())){
//            RefundResult result = RefundResult.failed();
//            result.setMessage(compensateRsp.getRspMsg());
//
//            paymentProcessor.processRefundFail(refundPO, result, paymentPO);
//            elementRemove(paymentPO.getTradeNo());
//        }
//    }
//
//    private PayBizTypeEnum explainPayBizType(Integer payBizType){
//        PayBizTypeEnum payBizTypeEnum = PayBizTypeEnum.explain(payBizType);
//        ValidateUtil.requiredNotNull(payBizTypeEnum, OverseaErrorCodeEnum.PAY_BIZ_TYPE_NOT_EXIST);
//        return payBizTypeEnum;
//    }
//
//    private PayStatus explainPayStatus(Integer status){
//        PayStatus payStatus = PayStatus.explain(status);
//        ValidateUtil.requiredNotNull(payStatus, OverseaErrorCodeEnum.STATUS_ERROR);
//        return payStatus;
//    }
//
//    private PaymentPO getPayment(String tradeNo){
//        PaymentPO paymentPO = paymentPersistService.getPaymentByTradeNo(tradeNo);
//        if(paymentPO == null){
//            log.error("【compensation】- 支付记录不存在，tradeNo：{}",tradeNo);
//            elementRemove(tradeNo);
//            throw OverseaBizException.create(OverseaErrorCodeEnum.ORDER_NON_EXISTENT);
//        }
//        return paymentPO;
//    }
}
