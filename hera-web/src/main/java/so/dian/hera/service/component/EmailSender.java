package so.dian.hera.service.component;

import com.chargebolt.hera.domain.RefundDO;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import so.dian.hera.common.enums.MessageType;
import so.dian.hera.configuration.EmailConfig;
import so.dian.hera.interceptor.basic.RefundResult;
import so.dian.hera.interceptor.utils.NumberUtil;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-03-10 11:05
 */
@Slf4j
@Component
public class EmailSender {

    private final static String REFUND_PATTERN = "退款区域：%s<br/>" +
            "环境：%s<br/>" +
            "退款单号：%s<br/>" +
            "流水编号：%s<br/>" +
            "失败原因：%s";


    private final static String UNFREEZE_PATTERN = "解冻区域：%s<br/>" +
            "环境：%s<br/>" +
            "预授权单号：%s<br/>" +
            "第三方流水号：%s<br/>" +
            "解冻失败时间：%s<br/>" +
            "失败原因：%s";


    private final static String TIMEOUT_PATTERN =
            "用户：%s<br/>" +
            "退款单号：%s<br/>" +
            "退款金额：%s<br/>" +
            "发起退款时间：%s（退款自邮件发出已超过24小时未到账，请及时处理";


    @Resource
    private EmailConfig emailConfig;

    @Resource
    private JavaMailSender mailSender;

    private String getPattern(MessageType type) {
        String pattern;
        switch (type) {
            case REFUND:
                pattern = REFUND_PATTERN;
                break;
            case UNFREEZE:
                pattern = UNFREEZE_PATTERN;
                break;
            case REFUND_TIMEOUT:
                pattern = TIMEOUT_PATTERN;
                break;
            default:
                return null;
        }
        return pattern;
    }

    public boolean pushEmail(MessageType type, PaymentDO paymentDO) {
        String pattern = getPattern(type);
        if (pattern == null) {
            return false;
        }
        String content = String.format(pattern, emailConfig.getLocal(), emailConfig.getEnv(),
                paymentDO.getTradeNo(), paymentDO.getPayNo(), new Date().toString(), paymentDO.getFailReason());
        return pushEmail(content, type);
    }

    public boolean pushEmail(MessageType type, RefundDO refundDO, RefundResult result) {
        String pattern = getPattern(type);
        if (pattern == null) {
            return false;
        }
        String content = String.format(pattern, emailConfig.getLocal(), emailConfig.getEnv(),
                refundDO.getRefundNo(), refundDO.getTradeNo(), result.getMessage());
        return pushEmail(content, type);
    }

    public boolean pushEmail(MessageType type, PaymentDO paymentDO, RefundDO refundDO) {
        String pattern = getPattern(type);
        if (pattern == null) {
            return false;
        }
        String nowTime = new SimpleDateFormat().format(new Date());
        double amount = NumberUtil.fen2yuan(refundDO.getAmount(), refundDO.getCurrency());
        String content = String.format(pattern, paymentDO.getUserId(),
                refundDO.getRefundNo(), amount, nowTime);
        return pushEmail(content, type);
    }

    private boolean pushEmail(String content, MessageType type) {
        MimeMessage message = mailSender.createMimeMessage();

        MimeMessageHelper helper;
        try {
            helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom("<EMAIL>");
            helper.setTo(emailConfig.getTo());
            helper.setSubject(emailConfig.getLocal() + type.getName() + "告警");
            helper.setText(content, true);
        } catch (MessagingException e) {
            log.error("Send 【Failed】" + type.name() + " email  ==> " + content);
            return false;
        }

        log.info("Send " + type.name() + " email ==> " + content);
        mailSender.send(message);
        return true;
    }
}
