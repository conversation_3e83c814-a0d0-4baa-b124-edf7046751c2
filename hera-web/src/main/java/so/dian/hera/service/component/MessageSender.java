package so.dian.hera.service.component;

import com.chargebolt.hera.domain.RefundDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import so.dian.hera.configuration.EmailConfig;
import so.dian.hera.interceptor.basic.RefundResult;

import javax.annotation.Resource;
import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

/**
 * <AUTHOR>
 * @date 2020-03-10 11:05
 */
@Slf4j
@Component
public class MessageSender {

    private final static String EMAIL_PATTERN = "退款区域：%s<br/>" +
            "环境：%s<br/>" +
            "退款单号：%s<br/>" +
            "流水编号：%s<br/>" +
            "失败原因：%s";

    @Resource
    private EmailConfig emailConfig;

    @Autowired
    private JavaMailSender mailSender;

    public boolean pushEmail(RefundDO refundDO, RefundResult result) {
        String content = String.format(EMAIL_PATTERN, emailConfig.getLocal(), emailConfig.getEnv(),
                refundDO.getRefundNo(), refundDO.getTradeNo(), result.getMessage());

        MimeMessage message = mailSender.createMimeMessage();

        MimeMessageHelper helper;
        try {
            helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom("<EMAIL>");
            helper.setTo(emailConfig.getTo());
            helper.setSubject(emailConfig.getLocal() + "退款失败告警");
            helper.setText(content, true);
        } catch (MessagingException e) {
            return false;
        }

        log.info("send refund failed email, refundNo: {}.", refundDO.getRefundNo());
        mailSender.send(message);
        return true;
    }
}
