package so.dian.hera.service.component;

import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.domain.RefundDO;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.hera.common.enums.MessageType;
import so.dian.hera.common.enums.NotifyStatusEnum;
import so.dian.hera.interceptor.basic.RefundResult;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.hera.service.persist.db.RefundPersistService;
import so.dian.platform.common.mq.producer.BizMqProducer;

import javax.annotation.Resource;
import java.util.Date;

@Component
@Slf4j
public class PaymentProcessor {

    @Autowired
    private PaymentPersistService paymentPersistService;
    @Autowired
    private RefundPersistService refundPersistService;
    @Autowired
    private EmailSender emailSender;
    @Resource
    private BizMqProducer bizMqProducer;

    public void processCancelSuccess(PaymentDO paymentDO) {
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.CARD_CANCELED.getCode());
        paymentPersistService.updateStatus(paymentDO);

        // 消息通知
        if (needNotify(paymentDO)) {
            bizMqProducer.sendBankcardPreAuthCancelMsg(paymentDO);
        }
    }

    public void processCancelFail(PaymentDO paymentDO, String failReason) {
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.CARD_AUTHORIZED.getCode());
        paymentDO.setFailReason(failReason);
        paymentPersistService.updateStatus(paymentDO);

        // 消息通知
        if (needNotify(paymentDO)) {
            paymentDO.setStatus(PayStatus.FAIL.getCode());
            bizMqProducer.sendBankcardPreAuthCancelMsg(paymentDO);
        }
    }

    public void processCaptureSuccess(PaymentDO paymentDO, String payNo, Date payTime) {
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setPayNo(payNo);
        paymentDO.setPayTime(payTime);
        paymentDO.setStatus(PayStatus.PAID.getCode());
        paymentPersistService.updateStatus(paymentDO);

    }

    public void processCaptureFail(PaymentDO paymentDO, String failReason) {
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.FAIL.getCode());
        paymentDO.setFailReason(failReason);
        paymentPersistService.updateStatus(paymentDO);
    }

    public void processRefundSuccess(RefundDO refundDO, Date refundTime, PaymentDO paymentDO) {
        RefundResult result = RefundResult.success();
        result.setRefundTime(refundTime);

        processRefundSuccess(refundDO, result, paymentDO);
    }

    public boolean processRefundSuccess(RefundDO refundDO, RefundResult result) {
        PaymentDO paymentDO = paymentPersistService.getPayment(refundDO.getOrderNo(), refundDO.getTradeNo());
        return processRefundSuccess(refundDO, result, paymentDO);
    }

    public boolean processRefundSuccess(RefundDO refundDO, RefundResult result, PaymentDO paymentDO) {
        Integer refundAmount = paymentDO.getRefundAmount() != null ? paymentDO.getRefundAmount() : 0;
        refundAmount += refundDO.getAmount();
        paymentDO.setRefundAmount(refundAmount);
        paymentDO.setRefundTime(new Date());
        if (!paymentPersistService.updateRefundAmount(paymentDO, paymentDO.getRefundAmount())) {
            throw new HeraBizException(HeraBizErrorCodeEnum.OPERATE_BUSY);
        }

        boolean success = refundPersistService.success(refundDO, result);
        if (!success) {
            return false;
        }

        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.REFUNDED.getCode());
        paymentDO.setRefundTime(result.getRefundTime());
        paymentPersistService.updateStatus(paymentDO);

        // 消息通知
        if (needNotify(paymentDO)) {
            bizMqProducer.sendGatewayRefundMsg(paymentDO, refundDO);
        }
//        refundNotifyProcessor.send(refundPO, PayNotifyTypeEnum.REFUNDED, EventStatus.SUCCESS, delay);
        return true;
    }


    public void processRefundSuccess(RefundDO refundDO) {
        PaymentDO paymentDO = paymentPersistService.getPayment(refundDO.getOrderNo(), refundDO.getTradeNo());
        if (paymentDO.getStatus().intValue() != PayStatus.REFUNDED.getCode()) {
            Integer refundAmount = paymentDO.getRefundAmount() != null ? paymentDO.getRefundAmount() : 0;
            refundAmount += refundDO.getAmount();
            paymentDO.setRefundAmount(refundAmount);
            paymentDO.setRefundTime(new Date());
            if (!paymentPersistService.updateRefundAmount(paymentDO, paymentDO.getRefundAmount())) {
                throw new HeraBizException(HeraBizErrorCodeEnum.OPERATE_BUSY);
            }
            paymentDO.setOriStatus(paymentDO.getStatus());
            paymentDO.setStatus(PayStatus.REFUNDED.getCode());
            paymentDO.setRefundTime(refundDO.getRefundTime());
            paymentPersistService.updateStatus(paymentDO);
        }
        // 消息通知
        if (needNotify(paymentDO)) {
            bizMqProducer.sendGatewayRefundMsg(paymentDO, refundDO);
        }
    }

    public boolean processRefundFail(RefundDO refundDO, String reason, PaymentDO paymentDO) {
        RefundResult result = RefundResult.failed();
        result.setMessage(reason);

        return processRefundSuccess(refundDO, result, paymentDO);
    }

    public boolean processRefundFail(RefundDO refundDO, RefundResult result) {
        PaymentDO paymentDO = paymentPersistService.getPayment(refundDO.getOrderNo(), refundDO.getTradeNo());
        return processRefundFail(refundDO, result, paymentDO);
    }

    public boolean processRefundFail(RefundDO refundDO, RefundResult result, PaymentDO paymentDO) {
        boolean success = refundPersistService.failed(paymentDO, refundDO, result);
        if (!success) {
            return false;
        }

        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setFailReason(result.getMessage());
        paymentDO.setStatus(PayStatus.PAID.getCode());
        paymentPersistService.updateStatus(paymentDO);

//        int delay = 0;
//        if (refundDO.getBizType().intValue() == BizTypeEnum.MULTI_REFUND_ORDER.getCode()) {
//            delay = 3;
//        }
//        refundNotifyProcessor.send(refundPO, PayNotifyTypeEnum.REFUNDED, EventStatus.FAILED, delay);
        // 邮件通知
        emailSender.pushEmail(MessageType.REFUND, refundDO, result);
        return true;
    }



    private void pushEmail(PaymentDO paymentDO, MessageType messageType) {
        emailSender.pushEmail(messageType, paymentDO);
    }

    /**
     * 检测是否需要投递消息
     * @param paymentDO
     * @return
     */
    private static boolean needNotify(PaymentDO paymentDO) {
        if (NotifyStatusEnum.NOT_INFORM.getCode().equals(paymentDO.getNotifyFlag())) {
            return false;
        }
        return true;
    }
}

