package so.dian.hera.service.listener;

import lombok.extern.slf4j.Slf4j;
import so.dian.hera.domain.BaseJob;


/**
 * <AUTHOR>
 * @date 2020-01-10 11:48
 */
@Slf4j
public abstract class AbstractListener<T extends BaseJob> implements Listener<T> {

    private final String name = getClass().getSimpleName();

    @Override
    public void executeJob() {
        log.info("Run " + name);

        int i = 0;
        int max = getMaxTime();
        while (i < max) {
            T nextJob = getNextJob();
            if (nextJob == null) {
                break;
            }

            if (!doProcess(nextJob)) {
                break;
            }

//            asyncExecutor.run(() -> doProcess());
            i++;
        }
    }

    protected abstract T getNextJob();

    protected int getMaxTime() {
        return 100;
    }

    /**
     * 处理任务
     */
    protected abstract boolean doProcess(T job);
}
