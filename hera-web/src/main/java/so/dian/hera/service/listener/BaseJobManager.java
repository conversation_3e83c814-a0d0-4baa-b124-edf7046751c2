package so.dian.hera.service.listener;

import so.dian.hera.domain.BaseJob;
import so.dian.hera.interceptor.utils.JsonUtil;
import so.dian.platform.common.configuration.redis.RedisClient;
import so.dian.platform.common.enums.CacheEnum;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020-02-05 10:36
 */
public abstract class BaseJobManager<E extends BaseJob> implements JobManager<E> {

    private final CacheEnum cache = getCacheType();

    @Resource
    private RedisClient redisClient;

    protected abstract CacheEnum getCacheType();

    @Override
    public boolean append(E task) {
        if (task == null) {
            return false;
        }
        String data = JsonUtil.toJson(task);
        redisClient.appendToList(cache.ns, data);
        return true;
    }

    @Override
    public boolean insertFirst(E task) {
        if (task == null) {
            return false;
        }
        String data = JsonUtil.toJson(task);
        redisClient.firstAppendToList(cache.ns, data);
        return true;
    }

    @Override
    public <E extends BaseJob> E next(Class<E> clazz) {
        String data = redisClient.popOfList(cache.ns);
        if (data == null) {
            return null;
        }

        E job = JsonUtil.toObject(data, clazz);
        if (isMatchType(job, clazz)) {
            return job;
        }

        redisClient.firstAppendToList(cache.ns, data);
        return null;
    }

    private <E extends BaseJob> boolean isMatchType(E job, Class<E> clazz) {
        return job.getType().equals(clazz.getName());
    }
}
