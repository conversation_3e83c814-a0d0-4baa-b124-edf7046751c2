package so.dian.hera.service.listener;

import so.dian.hera.domain.BaseJob;

/**
 * <AUTHOR>
 * @date 2020-02-05 10:36
 */
public interface JobManager<E extends BaseJob> {

    /**
     * 添加任务
     * @param task
     */
    boolean append(E task);

    /**
     * 设置第一位任务
     * @param task
     */
    boolean insertFirst(E task);

    /**
     * 下一个任务
     * @return
     */
    <E extends BaseJob> E next(Class<E> clazz);
}
