package so.dian.hera.service.listener;

import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.RefundStatus;
import com.chargebolt.hera.domain.RefundDO;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import so.dian.hera.common.enums.MessageType;
import so.dian.hera.common.enums.RefundEnum;
import so.dian.hera.domain.RefundJob;
import so.dian.hera.interceptor.RefundProcessor;
import so.dian.hera.interceptor.basic.Refund0000000Processor;
import so.dian.hera.interceptor.basic.RefundResult;
import so.dian.hera.service.component.EmailSender;
import so.dian.hera.service.component.PaymentProcessor;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.hera.service.persist.db.RefundPersistService;
import so.dian.hera.service.route.RouteService;

import java.time.Instant;
import java.util.Date;


/**
 * <AUTHOR>
 * @date 2020-01-10 11:48
 */
@Slf4j
@Component
public class RefundListener extends AbstractListener<RefundJob> {

    @Autowired
    private JobManager<RefundJob> jobManager;

    @Autowired
    private RefundPersistService refundPersistService;

    @Autowired
    private PaymentPersistService paymentPersistService;

    @Autowired
    private RouteService routeService;

    @Autowired
    private PaymentProcessor paymentProcessor;

    @Autowired
    private EmailSender emailSender;

    @Override
    public void appendJob(RefundJob task) {
        log.info("add refund job ==> " + task.getRefundNo());
        jobManager.append(task);
    }

    @Override
    protected RefundJob getNextJob() {
        return jobManager.next(RefundJob.class);
    }

    /**
     * 处理单个任务
     *
     * @return 是否继续下个任务
     */
    @Override
    protected boolean doProcess(RefundJob refundJob) {
        if (refundJob.getCheckTime().after(new Date())) {
            // 退款进度还未到检测时间
            appendJob(refundJob);
            return false;
        }

        if (processJob(refundJob)) {
            return true;
        }

        if (refundJob.needDeliver()) {
            deliverTimeoutMessage(refundJob);
            refundJob.setDeliverEmail(true);
        }

        if (refundJob.isExpire()) {
            return true;
        }

        setNextCheckTime(refundJob);
        appendJob(refundJob);
        return true;
    }

    private void deliverTimeoutMessage(RefundJob refundJob) {
        PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(refundJob.getTradeNo());
        RefundDO refundDO = refundPersistService.getRefund(refundJob.getRefundNo());
        emailSender.pushEmail(MessageType.REFUND_TIMEOUT, paymentDO, refundDO);
    }

    /**
     * 设置下次检查时间
     *
     * @param refundJob
     */
    private void setNextCheckTime(RefundJob refundJob) {
        RefundEnum refundEnum = RefundEnum.explain(refundJob.getRefundType());
        Instant time = Instant.now().plusMillis(refundEnum.getIntervalTime());
        refundJob.setCheckTime(Date.from(time));
    }

    /**
     * 处理退款查询任务
     *
     * @param item
     * @return 任务处理是否完成，未完成继续投递任务
     */
    public boolean processJob(RefundJob item) {
        Refund0000000Processor processor = getProcessor(item.getRefundType());
        if (processor == null) {
            log.error("RefundProcessor not found, payType: {}.", item.getRefundType());
            return true;
        }

        RefundDO refundDO = refundPersistService.getRefund(item.getRefundNo());
        if (refundDO == null) {
            log.error("Refund data not exists, tradeNo: {}, refundNo: {}.", item.getTradeNo(), item.getRefundNo());
            return true;
        }

        if (refundDO.isDone()) {
            log.error("Refund transaction is completed, data: {}.", refundDO);
            if (refundDO.getStatus().intValue() == RefundStatus.FAIL.getCode()) {
                RefundResult result = RefundResult.failed();
                result.setMessage("Refundable value of the order is insufficient");
                paymentProcessor.processRefundFail(refundDO, result);
            } else {
                paymentProcessor.processRefundSuccess(refundDO);
            }
            return true;
        }

        RefundResult result = processor.getResult(refundDO);
        if (result == null) {
            log.error("getRefundResult error, data: {}.", refundDO);
            return true;
        }

        log.info("Fetch refund result, tradeNo: {}, refundNo: {}, state: {}.", item.getTradeNo(), item.getRefundNo(), result.getStatus());
        boolean success;
        switch (result.getStatus()) {
            case REFUNDING: // 退款还在处理中
                return false;
            case REFUNDED:
                success = paymentProcessor.processRefundSuccess(refundDO, result);
                break;
            case FAIL:
                success = paymentProcessor.processRefundFail(refundDO, result);
                break;
            default:
                return false;
        }

        return success;
    }

    private Refund0000000Processor getProcessor(Integer payType) {
        PaywayEnum paywayEnum = routeService.channelAnalysis(payType);
        Refund0000000Processor refund0000000Processor;
        try {
            refund0000000Processor = routeService.getRefundProcessor(paywayEnum);
        } catch (Exception e) {
            return null;
        }
        return refund0000000Processor;
    }
}
