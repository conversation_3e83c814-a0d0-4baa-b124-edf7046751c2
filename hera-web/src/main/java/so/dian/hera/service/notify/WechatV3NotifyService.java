package so.dian.hera.service.notify;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.enums.CallbackOriginalTypeEnum;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.ServerErrorException;
import com.wechat.pay.contrib.apache.httpclient.auth.Verifier;
import com.wechat.pay.contrib.apache.httpclient.exception.ParseException;
import com.wechat.pay.contrib.apache.httpclient.exception.ValidationException;
import com.wechat.pay.contrib.apache.httpclient.notification.Notification;
import com.wechat.pay.contrib.apache.httpclient.notification.NotificationHandler;
import com.wechat.pay.contrib.apache.httpclient.notification.NotificationRequest;
import com.wechat.pay.java.core.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.hera.service.notify.factory.PayNotifyHandlerFactory;
import so.dian.hera.utils.AssertUtils;
import so.dian.platform.wechat.config.WechatProperty;
import so.dian.platform.wechat.dto.notify.WechatNotifyReqDTO;
import so.dian.platform.wechat.dto.notify.WechatNotifyRspDTO;
import so.dian.platform.wechat.factory.WechatV3BeanFactory;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WechatV3NotifyService {

    @Resource
    private WechatProperty wechatProperty;

    @Resource
    private WechatV3BeanFactory wechatV3BeanFactory;

    @Resource
    private PayNotifyHandlerFactory payNotifyHandlerFactory;




    /**
     * 微信V3通知，业务处理
     * https://pay.weixin.qq.com/docs/merchant/development/interface-rules/signature-verification.html
     * 应对签名探测流量：签名以 WECHATPAY/SIGNTEST/ 开头
     * 内部处理异常，直接抛500应答码，让微信通知重新发送
     * @param reqDTO
     * @return
     */
    public WechatNotifyRspDTO process(WechatNotifyReqDTO reqDTO){
        try {
            // 1、验证签名并返回解密后的数据报文
            Notification notification = null;
            try {
                notification = validateAndParseBody(reqDTO, wechatProperty);
            } catch (Exception e){
                log.error("微信V3通知#验签解密#未知异常",e);
                throw new ServerErrorException(HeraBizErrorCodeEnum.SIGN_VERIFY_ERROR.getCode(),e.getMessage());
            }
            log.info("微信V3通知#解密后 {}", JSON.toJSONString(notification));
            // 2、加密前的对象类型 枚举值：refund-退款，transaction-交易, 信用分结果通知兜底：通过产品类型和通知类型（event_type）区分
            String originalType = notification.getResource().getOriginalType();
            if(Objects.equals(reqDTO.getPayProdType(), CallbackOriginalTypeEnum.PAYSCORE.getPayProdType())){
                originalType = CallbackOriginalTypeEnum.PAYSCORE.getCode();
            }
            CallbackOriginalTypeEnum originalTypeEnum = CallbackOriginalTypeEnum.getByCodeAndPayProdType(originalType,reqDTO.getPayProdType());
            AssertUtils.trueWithBizExp(Objects.isNull(originalTypeEnum), HeraBizErrorCodeEnum.ROUTE_NOT_SUPPORT);
            // 3、从容器获取实现类进行业务处理
            payNotifyHandlerFactory.getPayCallbackHandler(CallbackOriginalTypeEnum.getKey(originalTypeEnum)).handlerNotify(notification.getDecryptData());
            return new WechatNotifyRspDTO();
        } catch (HeraBizException bizException){
            throw new ServerErrorException(bizException.getCode(),bizException.getMessage());
        }
    }

    /**
     * 验证签名并解密
     * @param reqDTO
     * @return
     */
    private Notification validateAndParseBody(WechatNotifyReqDTO reqDTO, WechatProperty wechatProperty) throws ValidationException, ParseException {
        // 构建request，传入必要参数
        NotificationRequest notificationRequest = new NotificationRequest.Builder()
                .withSerialNumber(reqDTO.getWechatPaySerial())
                .withNonce(reqDTO.getWechatPayNonce())
                .withTimestamp(reqDTO.getWechatPayTimestamp())
                .withSignature(reqDTO.getWechatPaySignature())
                .withBody(GsonUtil.getGson().toJson(reqDTO.getNotifyBody()))
                .build();

        // 获取证书管理
        Verifier verifier = wechatV3BeanFactory.getVerifier(reqDTO.getChannelId(), wechatProperty);

        // 获取解析执行器
        NotificationHandler handler = new NotificationHandler(verifier, wechatProperty.getApiV3Key().getBytes(StandardCharsets.UTF_8));

        // 验签和解析请求体
        return handler.parse(notificationRequest);
    }

    /**
     * 查询回调处理
     * @param typeEnum
     * @param content
     */
    public void handleNotify(CallbackOriginalTypeEnum typeEnum,String content){
        payNotifyHandlerFactory.getPayCallbackHandler(CallbackOriginalTypeEnum.getKey(typeEnum)).handlerNotify(content);
    }

}
