package so.dian.hera.service.notify.factory;

import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.PayNotifyHandler;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class PayNotifyHandlerFactory implements InitializingBean {

    private final ConcurrentHashMap<String, PayNotifyHandler> handlerMap = new ConcurrentHashMap<>();

    @Resource
    private List<PayNotifyHandler> payCallbackHandlerList;

    @Override
    public void afterPropertiesSet() throws Exception {
        payCallbackHandlerList.forEach(payCallbackHandler -> handlerMap.put(payCallbackHandler.key(), payCallbackHandler));
    }

    /**
     * 根据key获取实例
     * @param key
     * @return
     */
    public PayNotifyHandler getPayCallbackHandler(String key){
        PayNotifyHandler handler = handlerMap.get(key);
        if(Objects.isNull(handler)){
            log.error("微信V3通知#未找到通知实现 key: {}",key);
            throw HeraBizException.create(HeraBizErrorCodeEnum.ROUTE_NOT_SUPPORT);
        }
        return handler;
    }

}
