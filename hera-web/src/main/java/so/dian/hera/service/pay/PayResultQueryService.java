/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.service.pay;

import com.chargebolt.hera.client.dto.pay.checkout.rsp.PrepayCreateResultDTO;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.hera.interceptor.CheckoutPayProcessor;
import so.dian.hera.interceptor.QueryPayProcessor;
import so.dian.hera.interceptor.dto.req.PayQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.hera.service.route.RouteService;
import so.dian.hera.utils.AssertUtils;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.utils.ValidateUtil;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: PayResultQueryService.java, v 1.0 2024-04-17 3:38 PM Exp $
 */
@Slf4j
@Service
public class PayResultQueryService {
    private final RouteService routeService;
    private final PaymentPersistService paymentPersistService;

    public PayResultQueryService(ObjectProvider<RouteService> routeServiceProvider,
                                 ObjectProvider<PaymentPersistService> paymentPersistServiceProvider){
        this.routeService= routeServiceProvider.getIfUnique();
        this.paymentPersistService= paymentPersistServiceProvider.getIfUnique();
    }
    public PayQueryResultDTO queryPayResult(PaymentDO paymentDO) {
        //渠道解析
        PaywayEnum paywayEnum = routeService.channelAnalysis(paymentDO.getPayType());
        //获取渠道处理器
        QueryPayProcessor processor = routeService.getQueryPayProcessor(paywayEnum);
        PayQueryResultRequest request= new PayQueryResultRequest();
        request.setPayNo(paymentDO.getPayNo());
        request.setTradeNo(paymentDO.getTradeNo());
        return processor.orderPayQuery(request);
    }
}