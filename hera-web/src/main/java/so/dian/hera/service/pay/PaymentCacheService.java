/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.service.pay;

import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import so.dian.hera.domain.context.PaymentExtCacheContext;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.common.configuration.redis.RedisClient;
import so.dian.platform.common.enums.CacheEnum;

import javax.annotation.Resource;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: PaymentCacheService.java, v 1.0 2024-10-17 下午4:38 Exp $
 */
@Slf4j
@Service
public class PaymentCacheService {
    @Resource
    private RedisClient redisClient;

    public void setPaymentExtCache(PrepayCreateRequest.PrepayCreateExtInfo extInfo, String tradeNo){
        PaymentExtCacheContext context= new PaymentExtCacheContext();
        context.setDeviceNo(extInfo.getDeviceNo());
        context.setTopAgentId(extInfo.getTopAgentId());
        context.setTradeNo(tradeNo);
        log.info("支付关联设备、代理对象缓存：{}", JsonUtil.beanToJson(context));
        redisClient.set(CacheEnum.PAYMENT_EXT_CACHE_INFO.getNs(), tradeNo, context,
                CacheEnum.PAYMENT_EXT_CACHE_INFO.getExpiredTime());
    }

    public PaymentExtCacheContext getPaymentExtCache(String tradeNo){
        return redisClient.get(CacheEnum.PAYMENT_EXT_CACHE_INFO.getNs(), tradeNo, PaymentExtCacheContext.class);
    }
}