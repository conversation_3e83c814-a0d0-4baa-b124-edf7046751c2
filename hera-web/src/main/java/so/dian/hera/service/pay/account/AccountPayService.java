package so.dian.hera.service.pay.account;

import com.chargebolt.hera.client.dto.pay.account.req.AccountPayReq;
import com.chargebolt.hera.client.dto.pay.account.req.AccountRefundReq;
import com.chargebolt.hera.client.dto.pay.account.rsp.AccountPayRsp;
import com.chargebolt.hera.client.dto.pay.account.rsp.AccountRefundRsp;
import com.google.common.collect.Lists;
import so.dian.hera.domain.context.PaymentExtCacheContext;
import so.dian.hera.service.pay.PaymentCacheService;
import so.dian.hera.service.persist.db.PaymentQueryService;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.client.enums.status.RefundStatus;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;
import com.chargebolt.hera.domain.RefundDO;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import so.dian.hera.common.enums.AccountPayTypeEnum;
import so.dian.hera.remote.lvy.UserAccountManager;
import so.dian.hera.service.component.PaymentProcessor;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.hera.service.persist.db.RefundPersistService;
import so.dian.hera.service.route.RouteService;
import so.dian.hera.utils.TradeNoGenerator;
import so.dian.lvy.pojo.dto.UserAccountDTO;
import so.dian.lvy.pojo.enums.AccountStateEnum;
import so.dian.lvy.pojo.enums.AccountTypeEnum;
import so.dian.lvy.pojo.param.AccountModifyParam;
import so.dian.lvy.pojo.param.AccountParam;
import so.dian.platform.common.mq.producer.BizMqProducer;
import so.dian.platform.common.utils.ValidateUtil;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class AccountPayService {
    @Autowired
    private PaymentPersistService paymentPersistService;
    @Autowired
    private UserAccountManager userAccountManager;
    @Autowired
    private RouteService routeService;
    @Autowired
    private PaymentProcessor paymentProcessor;
    @Autowired
    private RefundPersistService refundPersistService;
    @Resource
    private BizMqProducer bizMqProducer;
    @Autowired
    private PaymentCacheService paymentCacheService;

    public AccountPayRsp accountPay(AccountPayReq accountPayReq) {
        log.info("accountPay request: {}", JsonUtil.beanToJson(accountPayReq));
        //获取支付记录
        PaymentDO paymentDO = paymentPersistService.getPaymentByBizNo(accountPayReq.getBizSeqNo());
        //防重校验
        ValidateUtil.requiredNull(paymentDO, HeraBizErrorCodeEnum.PLAT_PAY_ALREADY_EXIST);
        //渠道对应账户解析
        AccountPayTypeEnum payTypeEnum = routeService.accountAnalysis(accountPayReq.getPayway());
        
        //账户金额校验
        accountAmountCheck(accountPayReq, payTypeEnum.getAccountType(), accountPayReq.getAgentId());
        //账户支付预处理
        paymentDO = processPrePay(accountPayReq, accountPayReq.getAgentId());

        UserAccountDTO depositAccount = getUserDepositAccount(accountPayReq.getUserId(), accountPayReq.getAgentId());
        AccountPayRsp rsp = doAccountPay(accountPayReq, paymentDO, payTypeEnum.getAccountType(), depositAccount.getUserAccountId());
        processPayRsp(rsp, paymentDO);
        return rsp;
    }

    /**
     * 获取用户对应代理商下的押金账户
     *
     * @param userId
     * @param agentId
     * @return
     */
    public UserAccountDTO getUserDepositAccount(Long userId, Long agentId) {
        AccountParam accountParam = new AccountParam();
        accountParam.setUserId(userId);
        accountParam.setAccountType(Lists.newArrayList(AccountTypeEnum.DEPOSIT.getCode()));
        accountParam.setAgentId(agentId);
        List<UserAccountDTO> accountDTOS = userAccountManager.accountQuery(accountParam);
        if (!CollectionUtils.isEmpty(accountDTOS)) {
            return accountDTOS.get(0);
        }
        return null;
    }
    private AccountPayRsp doAccountPay(AccountPayReq accountPayReq, PaymentDO paymentDO,
                                       AccountTypeEnum accountTypeEnum, Long accountId) {
        AccountModifyParam accountModifyParam = AccountPaySupport.convert2AccountModifyParam(accountPayReq, accountTypeEnum, paymentDO);
        accountModifyParam.setAccountId(accountId);
        userAccountManager.decAcountAmount(accountModifyParam);
        return new AccountPayRsp(paymentDO.getTradeNo(), TransStatusEnum.SUCCESS);
    }

    private void accountAmountCheck(AccountPayReq accountPayReq, AccountTypeEnum accountTypeEnum, Long agentId) {
        AccountParam accountParam = AccountPaySupport.convert2AccountParam(accountPayReq, accountTypeEnum);
        accountParam.setAgentId(agentId);
        List<UserAccountDTO> accountDTOS = userAccountManager.accountQuery(accountParam);
        if (CollectionUtils.isEmpty(accountDTOS)) {
            throw HeraBizException.create(HeraBizErrorCodeEnum.ACCOUNT_NOT_EXIST);
        }
        for (UserAccountDTO accountDTO : accountDTOS){
            ValidateUtil.assertEquals(AccountStateEnum.NOMAL.getCode(), accountDTO.getStatus(), HeraBizErrorCodeEnum.ACCOUNT_STATUS_ERROR);
            if (accountPayReq.getPayAmount() > accountDTO.getMoney()) {
                throw HeraBizException.create(HeraBizErrorCodeEnum.ACCOUNT_BALANCE_NOT_ENOUGH);
            }
        }
    }

    /**
     * 账户退款流程
     *
     * @param req
     * @return
     */
    public AccountRefundRsp accountRefund(AccountRefundReq req) {
        log.info("accountRefund 退款：{}", JsonUtil.beanToJson(req));
        // 获取支付记录
        PaymentDO paymentDO = paymentPersistService.getPayment(req.getOriOrderNo(), req.getOriTradeNo());

        // 防重处理(一期暂不支持多笔退款)
        ValidateUtil.requiredNotNull(paymentDO, HeraBizErrorCodeEnum.ORDER_NON_EXIST);
        ValidateUtil.assertEquals(PayStatus.PAID.getCode(), paymentDO.getStatus(), HeraBizErrorCodeEnum.STATUS_ERROR);

        //渠道对应账户解析
        AccountPayTypeEnum payTypeEnum = routeService.accountAnalysis(req.getChannel());
        // 退款预处理
        processPreRefund(req.getAmount(), req.getRefundReason(), paymentDO);
        RefundDO refundDO = saveRepeatRefund(req, paymentDO, payTypeEnum);
        // 进行退款
        // FIXME没有传入accountId
        AccountRefundRsp rsp = doAccountRefund(req, paymentDO, payTypeEnum.getAccountType());
        // 退款结果处理
        processRefundRsp(rsp, paymentDO, refundDO);
        return rsp;
    }

    private RefundDO saveRepeatRefund(AccountRefundReq refundReq, PaymentDO paymentDO, AccountPayTypeEnum payTypeEnum) {
        RefundDO refundDO = new RefundDO();
        refundDO.setTradeNo(paymentDO.getTradeNo());
        refundDO.setOrderNo(paymentDO.getOrderNo());
        refundDO.setRefundNo(TradeNoGenerator.generateRefundNo(payTypeEnum.getPayWay().getPayway(), paymentDO.getUserId()));
        refundDO.setSystem(refundReq.getSystem());
        refundDO.setReason(refundReq.getRefundReason());
        refundDO.setRefundType(payTypeEnum.getPayWay().getPayway());
        refundDO.setBizType(paymentDO.getBizType());
        refundDO.setAmount(refundReq.getAmount().intValue());
        refundDO.setCurrency(paymentDO.getCurrency());
        refundDO.setStatus(RefundStatus.INIT.getCode());
        refundPersistService.insert(refundDO);
        return refundDO;
    }

    private AccountRefundRsp doAccountRefund(AccountRefundReq accountRefundReq, PaymentDO paymentDO, AccountTypeEnum accountTypeEnum) {
        log.info("doAccountRefund 退款：accountRefundReq:{} paymentDO:{} accountTypeEnum:{}", JsonUtil.beanToJson(accountRefundReq), JsonUtil.beanToJson(paymentDO), accountTypeEnum.getCode());
        AccountModifyParam accountModifyParam = AccountPaySupport.convert2AccountModifyParam(accountRefundReq, accountTypeEnum, paymentDO);
        userAccountManager.addAccountAmount(accountModifyParam);
        return new AccountRefundRsp(paymentDO.getTradeNo(), TransStatusEnum.SUCCESS);
    }

    private PaymentDO processPrePay(AccountPayReq accountPayReq, Long agentId) {
        PaymentDO paymentDO = AccountPaySupport.convert2PaymentPO(accountPayReq);
        AccountParam accountParam = new AccountParam();
        accountParam.setUserId(accountPayReq.getUserId());
        accountParam.setAccountType(Lists.newArrayList(AccountTypeEnum.DEPOSIT.getCode()));
        accountParam.setAgentId(agentId);
        List<UserAccountDTO> accountDTOS = userAccountManager.accountQuery(accountParam);
        if (!CollectionUtils.isEmpty(accountDTOS)) {
            UserAccountDTO userAccountDTO = accountDTOS.get(0);
            // 关联到押金payment
            paymentDO.setPayNo(userAccountDTO.getTradeNo());
        }
        paymentPersistService.insert(paymentDO);
        return paymentDO;
    }

    private void processPreRefund(Integer amount, String refundReason, PaymentDO paymentDO) {
        paymentDO.setRefundAmount(amount == null ? paymentDO.getPayAmount() : amount);
        paymentDO.setRefundTime(new Date());
        paymentDO.getNoteObject().put("refundReason", refundReason);
        paymentDO.setOriStatus(PayStatus.PAID.getCode());
        paymentDO.setStatus(PayStatus.REFUNDING.getCode());
        paymentPersistService.updateRefundStatus(paymentDO);
    }

    private void processPayRsp(AccountPayRsp rsp, PaymentDO paymentDO) {
        if (TransStatusEnum.FAIL.getKey().equals(rsp.getStatus())) {
            paymentProcessor.processCaptureFail(paymentDO, rsp.getRspMsg());
        } else if (TransStatusEnum.SUCCESS.getKey().equals(rsp.getStatus())) {
            paymentProcessor.processCaptureSuccess(paymentDO, null, new Date());
        }
        bizMqProducer.sendAccountPayMsg(paymentDO);
    }

    private void processRefundRsp(AccountRefundRsp refundRsp, PaymentDO paymentDO, RefundDO refundDO) {
        refundRsp.setTradeNo(paymentDO.getTradeNo());
        if (TransStatusEnum.FAIL.getKey().equals(refundRsp.getStatus())) {
            paymentProcessor.processRefundFail(refundDO, refundRsp.getRspMsg(), paymentDO);
        } else if (TransStatusEnum.SUCCESS.getKey().equals(refundRsp.getStatus())) {
            paymentProcessor.processRefundSuccess(refundDO, new Date(), paymentDO);
        }
    }
}
