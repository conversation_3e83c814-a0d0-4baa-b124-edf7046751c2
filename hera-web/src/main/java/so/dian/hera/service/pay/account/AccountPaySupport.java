package so.dian.hera.service.pay.account;

import com.chargebolt.hera.client.dto.pay.account.req.AccountPayReq;
import com.chargebolt.hera.client.dto.pay.account.req.AccountRefundReq;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import so.dian.hera.common.enums.NotifyStatusEnum;
import so.dian.hera.utils.TradeNoGenerator;
import so.dian.lvy.pojo.enums.AccountTypeEnum;
import so.dian.lvy.pojo.param.AccountModifyParam;
import so.dian.lvy.pojo.param.AccountParam;

import java.util.Collections;

public class AccountPaySupport {

    public static PaymentDO convert2PaymentPO(AccountPayReq payReq) {
        PaymentDO paymentDO = new PaymentDO();
        paymentDO.setOrderNo(payReq.getBizSeqNo());
        paymentDO.setCooperatorId(payReq.getCooperatorId());
        paymentDO.setUserId(payReq.getUserId());
        paymentDO.setBizType(payReq.getBizType());
        paymentDO.setPayType(payReq.getPayway());
        paymentDO.setPayAmount(payReq.getPayAmount());
        paymentDO.setCurrency(payReq.getCurrency());
        paymentDO.setStatus(PayStatus.CARD_SEND.getCode());
        paymentDO.setTradeNo(TradeNoGenerator.generateTradeNo(payReq.getBizType(), payReq.getUserId()));
        paymentDO.setReqSystem(payReq.getSystem());
        paymentDO.setNotifyFlag(payReq.getIsNotify() ? NotifyStatusEnum.INIT.getCode():NotifyStatusEnum.NOT_INFORM.getCode());
        paymentDO.setNotifyTag("");
        return paymentDO;
    }

    public static AccountParam convert2AccountParam(AccountPayReq accountPayReq,AccountTypeEnum accountTypeEnum) {
        AccountParam accountParam = new AccountParam();
        accountParam.setUserId(accountPayReq.getUserId());
        accountParam.setAccountType(Collections.singletonList(accountTypeEnum.getCode()));
        return accountParam;
    }

    public static AccountModifyParam convert2AccountModifyParam(AccountPayReq accountPayReq, AccountTypeEnum accountTypeEnum, PaymentDO paymentDO) {
        AccountModifyParam modifyParam = new AccountModifyParam();
        modifyParam.setUserId(accountPayReq.getUserId());
        modifyParam.setAccountType(accountTypeEnum.getCode());
        modifyParam.setAmount(accountPayReq.getPayAmount().longValue());
        modifyParam.setTradeNo(paymentDO.getTradeNo());
        modifyParam.setBizDesc(accountPayReq.getDesc());
        return modifyParam;
    }

    public static AccountModifyParam convert2AccountModifyParam(AccountRefundReq accountRefundReq, AccountTypeEnum accountTypeEnum, PaymentDO paymentDO) {
        AccountModifyParam modifyParam = new AccountModifyParam();
        modifyParam.setUserId(accountRefundReq.getUserId());
        modifyParam.setAccountType(accountTypeEnum.getCode());
        modifyParam.setAmount(accountRefundReq.getAmount().longValue());
        modifyParam.setTradeNo(paymentDO.getTradeNo());
        modifyParam.setBizDesc(accountRefundReq.getRefundReason());
        return modifyParam;
    }
}
