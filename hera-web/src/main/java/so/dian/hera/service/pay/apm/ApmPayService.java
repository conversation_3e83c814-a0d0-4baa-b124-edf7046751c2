package so.dian.hera.service.pay.apm;

import com.alibaba.fastjson.JSON;
import com.chargebolt.eden.enums.ClientTypeEnum;
import com.chargebolt.hera.client.dto.common.CurrencyExchangeInfo;
import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.checkout.req.CancelCheckRequest;
import com.chargebolt.hera.client.dto.pay.checkout.req.CancelRequest;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.CancelCheckResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.CancelResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PreAuthCancelResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PrepayCreateResultDTO;
import com.chargebolt.hera.client.enums.PaywayEnum;
import so.dian.hera.domain.context.PaymentExtCacheContext;
import so.dian.hera.service.pay.PaymentCacheService;
import so.dian.hera.interceptor.CancelCheckProcessor;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.common.enums.CacheEnum;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.domain.notify.ChannelNotifyBody;
import com.chargebolt.hera.domain.sharding.PaymentDO;

import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import so.dian.hera.common.enums.NotifyStatusEnum;
import so.dian.hera.interceptor.CheckoutPayProcessor;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.hera.service.persist.db.RefundPersistService;
import so.dian.hera.service.route.RouteService;
import so.dian.platform.common.configuration.redis.RedisClient;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.mq.producer.BizMqProducer;
import so.dian.platform.common.mq.producer.SelfMqProducer;
import so.dian.platform.common.utils.ValidateUtil;
import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

@Slf4j
@Service
public class ApmPayService {

    @Resource
    private RouteService routeService;
    @Resource
    private PaymentPersistService paymentPersistService;
    @Resource
    private RefundPersistService refundPersistService;
    @Resource
    private ApmPaySupport apmPaySupport;
    @Resource
    private BizMqProducer bizMqProducer;
    @Resource
    private SelfMqProducer selfMqProducer;
    @Resource
    private RedisClient redisClient;
    @Resource
    private PaymentCacheService paymentCacheService;
    /**
     * 创建预授权
     */
    public PrepayCreateResultDTO prepay(PrepayCreateRequest prepayCreateRequest) {
        //获取支付记录
        PaymentDO paymentDO = paymentPersistService.getPaymentByBizNo(prepayCreateRequest.getBizNo());
        //防重校验
        ValidateUtil.requiredNull(paymentDO, HeraBizErrorCodeEnum.PLAT_PAY_ALREADY_EXIST);
        //渠道解析
        PaywayEnum paywayEnum = routeService.channelAnalysis(prepayCreateRequest.getPayway().getPayway());
        // 转汇处理
        String targetCurrency = prepayCreateRequest.getCurrency();
        Integer targetPayAmount = prepayCreateRequest.getPayAmount();
        CurrencyExchangeInfo currencyExchange = null;
        if (Objects.nonNull(prepayCreateRequest.getCurrencyExchangeInfo())
                && Objects.nonNull(prepayCreateRequest.getCurrencyExchangeInfo().getExchangeRate())
                && Objects.nonNull(prepayCreateRequest.getCurrencyExchangeInfo().getTargetAmount())
                && Objects.nonNull(prepayCreateRequest.getCurrencyExchangeInfo().getTargetCurrency())) {
            currencyExchange =  prepayCreateRequest.getCurrencyExchangeInfo();
            // 替换拉起支付的货币单位和金额
            targetCurrency = prepayCreateRequest.getCurrencyExchangeInfo().getTargetCurrency();
            targetPayAmount = prepayCreateRequest.getCurrencyExchangeInfo().getTargetAmount();
        }
        //获取渠道处理器
        CheckoutPayProcessor payProcessor = routeService.getCheckoutPayProcessor(paywayEnum);
        //预授权预处理
        paymentDO = createPrepayPayment(prepayCreateRequest, currencyExchange);
        
        //发送预授权请求
        prepayCreateRequest.setCurrency(targetCurrency);
        prepayCreateRequest.setPayAmount(targetPayAmount);
        PrepayCreateResultDTO prepayCreateResultDTO = payProcessor.doPrePay(prepayCreateRequest, paymentDO);
        // 缓存支付订单号设备关系
        String deviceNo= prepayCreateRequest.getExtInfo().getDeviceNo();
        if(org.apache.commons.lang.StringUtils.isNotBlank(deviceNo)){
            redisClient.set(CacheEnum.PAYMENT_DEVICE_MAPPING.getNs(), paymentDO.getTradeNo(), deviceNo,
                    CacheEnum.PAYMENT_DEVICE_MAPPING.getExpiredTime());
        }
        paymentCacheService.setPaymentExtCache(prepayCreateRequest.getExtInfo(), paymentDO.getTradeNo());
        return prepayCreateResultDTO;
    }

    public void asyncPaidNotify(ChannelNotifyBody channelNotifyBody) {
        selfMqProducer.sendSelfApmPaidMsg(channelNotifyBody);
    }

    public boolean handlePaidSelfMsg(ChannelNotifyBody channelNotifyBody) {
        String createTradeNo = null;
        if (PaywayEnum.PINGPONG_CHECKOUT_APM.equals(channelNotifyBody.getPayway()) ||
                PaywayEnum.ANTOM_CHECKOUT_APM.equals(channelNotifyBody.getPayway()) ||
                PaywayEnum.MIDTRANS_CHECKOUT_APM.equals(channelNotifyBody.getPayway()) ) {
            createTradeNo = channelNotifyBody.getCurrentTradeNo();
        }

        log.info("createTradeNo: {}", createTradeNo);
        if (StringUtils.isEmpty(createTradeNo)) {
            log.info("no createTradeNo in redis");
            return false;
        }

        // createPayment:   ->  PayStatus.PAID
        // 存支付方法
        PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(createTradeNo);
        // 检查支付状态
        if(Objects.equals(paymentDO.getStatus(), PayStatus.PAID.getCode())){
            log.warn("消息已经处理过：{}", JsonUtil.beanToJson(paymentDO));
        }else{
            paymentDO.setPayNo(channelNotifyBody.getOuterNo());
            paymentDO.setPayTime(new Date());
            paymentDO.setPayMethod(channelNotifyBody.getPayMethod().getId());
            processPaidPayment(paymentDO);

            // notify
            if (needNotify(paymentDO)) {
                bizMqProducer.sendApmPaidMsg(paymentDO);
            }
        }
        return true;
    }

    // -------------------处理payment------------------

    private PaymentDO createPrepayPayment(PrepayCreateRequest prepayCreateRequest, CurrencyExchangeInfo currencyExchange) {
        PaymentDO paymentDO = apmPaySupport.convert2PrepayCreatePaymentPO(prepayCreateRequest);
        if (Objects.nonNull(currencyExchange)) {
            paymentDO.getNoteObject().put("currencyExchange", currencyExchange);
        }
        paymentPersistService.insert(paymentDO);
        return paymentDO;
    }

    /**
     * vietQR支付单创建
     * vietQR是交易成功的通知，订单创建为支付成功保存
     *
     * @param prepayCreateRequest
     * @return
     */
    public PaymentDO createVietqrPayment(PrepayCreateRequest prepayCreateRequest, String payNo) {
        PaymentDO paymentDO = apmPaySupport.convert2PrepayCreatePaymentPO(prepayCreateRequest);
        paymentDO.setPayNo(payNo);
        paymentDO.setStatus(PayStatus.PAID.getCode());
        paymentDO.setNotifyFlag(NotifyStatusEnum.NOTIFIED.getCode());
        paymentDO.setPayTime(prepayCreateRequest.getReqDate());

        log.info("createVietqrPayment:{}", JsonUtil.beanToJson(paymentDO));
        paymentPersistService.insert(paymentDO);
        return paymentDO;
    }

    private void processPaidPayment(PaymentDO paymentDO) {
        log.info("processPaidPayment {} -> {}", paymentDO.getStatus(), PayStatus.PAID.getCode());
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.PAID.getCode());
        paymentPersistService.updateStatus(paymentDO);
    }

    private void processRefundingPayment(PaymentDO paymentDO) {
        log.info("processRefundingPayment {} -> {}", paymentDO.getStatus(), PayStatus.REFUNDING.getCode());
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.REFUNDING.getCode());
        paymentPersistService.updateStatus(paymentDO);
    }

    private void processCancelPayment(PaymentDO paymentDO) {
        log.info("processCancelPayment {} -> {}", paymentDO.getStatus(), PayStatus.REFUNDED.getCode());
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.REFUNDED.getCode());
        paymentPersistService.updateStatus(paymentDO);
    }

    private static boolean needNotify(PaymentDO paymentDO) {
        if (NotifyStatusEnum.NOT_INFORM.getCode().equals(paymentDO.getNotifyFlag())) {
            return false;
        }
        return true;
    }

    /**
     *
     * @param cancelCheckRequest
     * @return
     */
    public CancelCheckResultDTO cancelCheck(CancelCheckRequest cancelCheckRequest) {
        // 获取支付记录
        PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(cancelCheckRequest.getTradeNo());
        ValidateUtil.requiredNotNull(paymentDO, HeraBizErrorCodeEnum.PLAT_PAY_ALREADY_EXIST);
        // 渠道解析
        PaywayEnum paywayEnum = routeService.channelAnalysis(paymentDO.getPayType());
        // 构建应答体
        CancelCheckResultDTO resultDTO = new CancelCheckResultDTO();
        resultDTO.setSuccess(true);

        // 查询押金是否有抵扣记录，存在则不能取消
        PaymentDO depositPaymentDO = paymentPersistService.getPaymentPOByPayNo(cancelCheckRequest.getTradeNo());
        if(Objects.nonNull(depositPaymentDO)){
            resultDTO.setCancelDeposit(false);
            return resultDTO;
        }

        // 获取渠道处理器
        CancelCheckProcessor processor = null;
        try {
            processor = routeService.getCancelCheckProcessor(paywayEnum);
        }catch (HeraBizException ex){
            log.warn("cancelCheck un support tradeNo: {},payWay: {}", cancelCheckRequest.getTradeNo(),JSON.toJSONString(paywayEnum));
            return resultDTO;
        }

        resultDTO.setCancelDeposit(processor.cancelCheck(paymentDO));
        return resultDTO;
    }

    public CancelResultDTO cancel(CancelRequest cancelRequest) {
        //获取支付记录
        PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(cancelRequest.getTradeNo());
        //防重校验
        ValidateUtil.requiredNotNull(paymentDO, HeraBizErrorCodeEnum.PLAT_PAY_ALREADY_EXIST);
        ValidateUtil.assertEquals(paymentDO.getStatus(),PayStatus.PAID.getCode(),HeraBizErrorCodeEnum.ORDER_STATUS_ERROR);
        //渠道解析
        PaywayEnum paywayEnum = routeService.channelAnalysis(paymentDO.getPayType());
        //获取渠道处理器并取消订单
        CheckoutPayProcessor payProcessor = routeService.getCheckoutPayProcessor(paywayEnum);
        PreAuthCancelResultDTO resultDTO = payProcessor.doCancel(paymentDO);

        // 更新订单为取消状态
        if(resultDTO.isSuccess()){
            paymentDO.setRefundAmount(paymentDO.getPayAmount());
            processCancelPayment(paymentDO);
        }

        CancelResultDTO cancelResultDTO = new CancelResultDTO();
        cancelResultDTO.setSuccess(resultDTO.isSuccess());
        return cancelResultDTO;
    }
}
