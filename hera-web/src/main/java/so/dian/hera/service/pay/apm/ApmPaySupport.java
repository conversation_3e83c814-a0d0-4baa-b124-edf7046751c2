package so.dian.hera.service.pay.apm;

import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import org.springframework.stereotype.Component;
import so.dian.hera.common.enums.NotifyStatusEnum;
import so.dian.hera.service.component.AutoCancelProcessor;
import so.dian.hera.utils.TradeNoGenerator;
import so.dian.mofa3.lang.util.DateBuild;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.util.Objects;

@Component
public class ApmPaySupport {
    @Resource
    private AutoCancelProcessor autoCancelProcessor;

    public PaymentDO convert2PrepayCreatePaymentPO(PrepayCreateRequest prepayCreateRequest) {
        PaymentDO paymentDO = new PaymentDO();
        paymentDO.setOrderNo(prepayCreateRequest.getBizNo());
        paymentDO.setCooperatorId(prepayCreateRequest.getCooperatorId());
        paymentDO.setUserId(prepayCreateRequest.getUserId());
        paymentDO.setBizType(prepayCreateRequest.getBizType());
        paymentDO.setPayType(prepayCreateRequest.getPayway().getPayway());
        paymentDO.setPayAmount(prepayCreateRequest.getPayAmount());
        paymentDO.setCurrency(prepayCreateRequest.getCurrency());
        paymentDO.setDelayTime(prepayCreateRequest.getDelayTime());
        paymentDO.setStatus(PayStatus.INIT.getCode());
        // zalopay支付单号有格式要求，特殊处理
        if(Objects.equals(prepayCreateRequest.getPayway(), PaywayEnum.ZALOPAY)){
            paymentDO.setTradeNo(new DateBuild(ZoneId.of("Asia/Ho_Chi_Minh")).formatter(DateBuild.SIMPLE_SHORT_DATE)
                    +"_"+TradeNoGenerator.generateTradeNo(prepayCreateRequest.getBizType(), prepayCreateRequest.getUserId()));
        }else{
            paymentDO.setTradeNo(TradeNoGenerator.generateTradeNo(prepayCreateRequest.getBizType(), prepayCreateRequest.getUserId()));
        }
        paymentDO.setReqSystem(prepayCreateRequest.getSystem());
        paymentDO.setNotifyFlag(prepayCreateRequest.getIsNotify() ? NotifyStatusEnum.INIT.getCode() : NotifyStatusEnum.NOT_INFORM.getCode());
        paymentDO.setNotifyTag("");
        if(Objects.nonNull(prepayCreateRequest.getExtInfo())){
            paymentDO.setDeviceNo(prepayCreateRequest.getExtInfo().getDeviceNo());
        }
//        autoCancelProcessor.elementAppend(checkoutPrepayCreateRequest.getIsAutoCancel(), paymentDO.getTradeNo());
        return paymentDO;
    }
}
