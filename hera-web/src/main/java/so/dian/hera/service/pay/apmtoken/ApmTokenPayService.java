package so.dian.hera.service.pay.apmtoken;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.apmtoken.req.ApmTokenCheckRequest;
import com.chargebolt.hera.client.dto.pay.apmtoken.req.ApmTokenPayRequest;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenCheckResultDTO;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenApplyResultDTO;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenPayResultDTO;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenPrepareResultDTO;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.RetryStatus;
import com.chargebolt.hera.domain.PaymentRetryRecord;
import com.chargebolt.hera.domain.notify.PaymentRetryBody;
import so.dian.hera.service.pay.PaymentCacheService;
import so.dian.hera.service.persist.db.PaymentRetryRecordService;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.domain.ApmTokenDO;
import com.chargebolt.hera.domain.notify.ChannelNotifyBody;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import so.dian.hera.common.enums.NotifyStatusEnum;
import so.dian.hera.interceptor.TokenPayProcessor;
import so.dian.hera.interceptor.utils.DateUtil;
import so.dian.hera.service.persist.db.ApmTokenPersistService;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.hera.service.route.RouteService;
import so.dian.platform.common.mq.producer.BizMqProducer;
import so.dian.platform.common.mq.producer.SelfMqProducer;
import so.dian.platform.common.utils.ValidateUtil;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class ApmTokenPayService {

    @Resource
    private RouteService routeService;
    @Resource
    private PaymentPersistService paymentPersistService;
    @Resource
    private ApmTokenPersistService apmTokenPersistService;
    @Resource
    private ApmTokenPaySupport apmTokenPaySupport;
    @Resource
    private SelfMqProducer selfMqProducer;
    @Resource
    private BizMqProducer bizMqProducer;
    @Resource
    private PaymentRetryRecordService paymentRetryRecordService;
    @Resource
    private PaymentCacheService paymentCacheService;
    public static boolean handleTokenCreationSelfMsg(ChannelNotifyBody channelNotifyBody) {
        return false;
    }

    public ApmTokenPrepareResultDTO prepare(PrepayCreateRequest request) {
        PaymentDO paymentDO = paymentPersistService.getPaymentByBizNo(request.getBizNo());
        //防重校验
        ValidateUtil.requiredNull(paymentDO, HeraBizErrorCodeEnum.PLAT_PAY_ALREADY_EXIST);
        //渠道解析
        PaywayEnum paywayEnum = routeService.channelAnalysis(request.getPayway().getPayway());

        TokenPayProcessor tokenPayProcessor = routeService.getAuthorizationPayProcessor(paywayEnum);
        paymentDO = createPrepayPayment(request);

        ApmTokenPrepareResultDTO apmTokenPrepareResultDTO = tokenPayProcessor.prepare(request, paymentDO);

        if (apmTokenPrepareResultDTO != null && apmTokenPrepareResultDTO.isSuccess()) {
            // 落库
            initToken(request, paymentDO.getTradeNo());
        }
        paymentCacheService.setPaymentExtCache(request.getExtInfo(), paymentDO.getTradeNo());

        return apmTokenPrepareResultDTO;
    }

    public void handleTokenCreationNotify(String token) {
//        selfMqProducer.sendSelfApmAuthorizationPrepareMsg(notifyBody.getToken());
    }

    public void handleTokenCancelNotify(String token) {
        log.info("token canceled. token:{}", token);
        cancelToken(token);
    }

    public ApmTokenApplyResultDTO apply(PrepayCreateRequest request) {
        PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(request.getBizNo());
        PaywayEnum paywayEnum = routeService.channelAnalysis(request.getPayway().getPayway());
        TokenPayProcessor tokenPayProcessor = routeService.getAuthorizationPayProcessor(paywayEnum);
        ApmTokenApplyResultDTO resultDTO = tokenPayProcessor.apply(request, paymentDO);
        if (resultDTO != null && resultDTO.getToken() != null) {
            // 更新token
            applyToken(paymentDO, resultDTO);
            processTokenAuthSuccess(paymentDO);
        }
//        bizMqProducer.sendApmTokenAppliedMsg(paymentDO);
        return resultDTO;
    }

    public ApmTokenCheckResultDTO checkToken(ApmTokenCheckRequest request) {
        List<ApmTokenDO> apmTokenDOList = apmTokenPersistService.findUserToken(request.getUserId(), ApmTokenDO.STATUS_SUCCESS);
        ApmTokenCheckResultDTO resultDTO = new ApmTokenCheckResultDTO();
        resultDTO.setSuccess(true);
        if (CollectionUtils.isEmpty(apmTokenDOList)) {
            resultDTO.setValid(false);
            return resultDTO;
        }
        ApmTokenDO apmTokenDO = null;
        if (request.getPayMethod() == null) {
            apmTokenDO = apmTokenDOList.get(0);
        } else {
            for (ApmTokenDO apmTokenDOInList : apmTokenDOList) {
                if (apmTokenDOInList.getTool().equals(request.getPayMethod().getId())) {
                    apmTokenDO = apmTokenDOInList;
                }
            }
        }
        if (apmTokenDO == null) {
            resultDTO.setValid(false);
            return resultDTO;
        }
        if (apmTokenDO.getExpireAt() < System.currentTimeMillis()) {
            resultDTO.setValid(false);
            return resultDTO;
        } else {
            // 远程检查
            // TODO
//            PaywayEnum paywayEnum = routeService.channelAnalysis(request.getPayway().getPayway());
//            TokenPayProcessor tokenPayProcessor = routeService.getAuthorizationPayProcessor(paywayEnum);
        }
        resultDTO.setValid(true);
        resultDTO.setExpireAt(apmTokenDO.getExpireAt());
        resultDTO.setTradeNo(apmTokenDO.getTradeNo());
        return resultDTO;
    }

    public ApmTokenPayResultDTO pay(ApmTokenPayRequest request) {
        // 支付的payment
        PaymentDO paymentDO = paymentPersistService.getPaymentByBizNo(request.getBizNo());
        //防重校验
        ValidateUtil.requiredNull(paymentDO, HeraBizErrorCodeEnum.PLAT_PAY_ALREADY_EXIST);
        //渠道解析
        PaywayEnum paywayEnum = routeService.channelAnalysis(request.getPayway().getPayway());

        paymentDO = createPayPayment(request);

        List<ApmTokenDO> apmTokenDOList = apmTokenPersistService.findUserToken(paymentDO.getUserId(), ApmTokenDO.STATUS_SUCCESS);
        ApmTokenDO apmTokenDO = apmTokenDOList.get(0);
        TokenPayProcessor tokenPayProcessor = routeService.getAuthorizationPayProcessor(paywayEnum);
        ApmTokenPayResultDTO resultDTO = tokenPayProcessor.pay(request, apmTokenDO, paymentDO);
        if (resultDTO.isSuccess()) {
            processPayingPayment(paymentDO);
            savePaymentRetry(paymentDO, resultDTO, paywayEnum.getPayway());
        } else {
            log.warn("一键支付扣款请求失败: {}", JSON.toJSONString(request));
        }
        return resultDTO;
    }

    private void savePaymentRetry(PaymentDO paymentDO,ApmTokenPayResultDTO resultDTO, Integer payway){
        PaymentRetryRecord retryRecord= new PaymentRetryRecord();
        retryRecord.setTradeNo(paymentDO.getTradeNo());
        // FIXME 新增一键支付通道，需要增加逻辑
        //PINGPONG_ONE_CLICK 54 paymentDO.getPayNo()
        if(Objects.equals(PaywayEnum.PINGPONG_ONE_CLICK.getPayway(), payway)){
            retryRecord.setPayNo(resultDTO.getTransactionId());
        }else {
            retryRecord.setPayNo(paymentDO.getTradeNo());
        }
        retryRecord.setPayway(payway);
        retryRecord.setState(RetryStatus.PROCESSING.getCode());
        paymentRetryRecordService.insert(retryRecord);
        PaymentRetryBody retryBody= new PaymentRetryBody();
        retryBody.setTradeNo(paymentDO.getTradeNo());
        selfMqProducer.sendSelfPaymentRetryDelayQuery(retryBody);
    }

    public void asyncApmPaidNotify(ChannelNotifyBody channelNotifyBody) {
        selfMqProducer.sendSelfApmTokenPaidMsg(channelNotifyBody);
    }

    public boolean handleApmTokenPaidSelfMsg(ChannelNotifyBody channelNotifyBody) {
        String tradeNo = channelNotifyBody.getCurrentTradeNo();
        log.info("createTradeNo: {}", tradeNo);
        if (StringUtils.isEmpty(tradeNo)) {
            log.info("no createTradeNo in redis");
            return false;
        }

        // createPayment:   ->  PayStatus.PAID
        PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(tradeNo);
        paymentDO.setPayNo(channelNotifyBody.getOuterNo());
        paymentDO.setPayTime(new Date());
        processPaidPayment(paymentDO);

        // notify
        if (needNotify(paymentDO)) {
            bizMqProducer.sendApmTokenPaidMsg(paymentDO);
        }
        return true;
    }

    private void initToken(PrepayCreateRequest request, String tradeNo) {
        ApmTokenDO apmTokenDO = new ApmTokenDO();
        apmTokenDO.setGmtCreate(System.currentTimeMillis());
        apmTokenDO.setChannel(request.getPayway().getChannel().getChannelNo());
        apmTokenDO.setPayway(request.getPayway().getPayway());
        apmTokenDO.setTool(request.getExtInfo().getPayMethod());
        apmTokenDO.setUserId(request.getUserId());
        apmTokenDO.setStatus(ApmTokenDO.STATUS_INIT);
        apmTokenDO.setTradeNo(tradeNo);
        apmTokenPersistService.insert(apmTokenDO);
    }

    private void applyToken(PaymentDO paymentDO, ApmTokenApplyResultDTO resultDTO) {
        List<ApmTokenDO> apmTokenDOList = apmTokenPersistService.findUserToken(paymentDO.getUserId(), ApmTokenDO.STATUS_INIT);
        if (CollectionUtils.isEmpty(apmTokenDOList)) {
            log.error("no ApmTokenDO of user:{} status:{}", paymentDO.getUserId(), ApmTokenDO.STATUS_INIT);
            return;
        }
        if (apmTokenDOList.size() > 1) {
            log.warn("size of ApmTokenDO of user:{} status:0 -> size:{}", paymentDO.getUserId(), apmTokenDOList.size());
        }
        // 取最新一个
        ApmTokenDO apmTokenDO = apmTokenDOList.get(0);
        apmTokenDO.setGmtUpdate(System.currentTimeMillis());
        apmTokenDO.setToken(resultDTO.getToken());
        apmTokenDO.setExpireAt(DateUtil.parse(resultDTO.getExpireTime(), DateUtil.DEFAULT_PATTERN).getTime());
        apmTokenDO.setStatus(ApmTokenDO.STATUS_SUCCESS);
        apmTokenPersistService.update(apmTokenDO);
    }

    private void cancelToken(String token) {
        ApmTokenDO apmTokenDO = apmTokenPersistService.getToken(token);
        if (apmTokenDO == null) {
            return;
        }
        apmTokenDO.setStatus(ApmTokenDO.STATUS_CANCELED);
        apmTokenDO.setGmtUpdate(System.currentTimeMillis());
        apmTokenPersistService.update(apmTokenDO);
    }

    private PaymentDO createPrepayPayment(PrepayCreateRequest request) {
        PaymentDO paymentDO = apmTokenPaySupport.convert2PrepayCreatePayment(request);
        paymentPersistService.insert(paymentDO);
        return paymentDO;
    }


    private PaymentDO createPayPayment(ApmTokenPayRequest request) {
        PaymentDO paymentDO = apmTokenPaySupport.convert2PayPayment(request);
        paymentPersistService.insert(paymentDO);
        return paymentDO;
    }

    private void processPayingPayment(PaymentDO paymentDO) {
//        log.info("processPayingPayment {} -> {}", paymentDO.getStatus(), PayStatus.CARD_CAPTURING.getCode());
//        paymentDO.setOriStatus(paymentDO.getStatus());
//        paymentDO.setStatus(PayStatus.CARD_CAPTURING.getCode());
//        paymentPersistService.updateStatus(paymentDO);
    }

    private void processTokenAuthSuccess(PaymentDO paymentDO) {
        log.info("processTokenAuthSuccess {} -> {}", paymentDO.getStatus(), PayStatus.APM_TOKEN_AUTH_SUCCESS.getCode());
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.APM_TOKEN_AUTH_SUCCESS.getCode());
        paymentPersistService.updateStatus(paymentDO);
    }

    private void processPaidPayment(PaymentDO paymentDO) {
        log.info("processPaidPayment {} -> {}", paymentDO.getStatus(), PayStatus.PAID.getCode());
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.PAID.getCode());
        paymentPersistService.updateStatus(paymentDO);
    }

    private static boolean needNotify(PaymentDO paymentDO) {
        if (NotifyStatusEnum.NOT_INFORM.getCode().equals(paymentDO.getNotifyFlag())) {
            return false;
        }
        return true;
    }
}
