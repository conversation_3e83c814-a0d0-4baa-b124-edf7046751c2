package so.dian.hera.service.pay.apmtoken;

import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.apmtoken.req.ApmTokenPayRequest;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import org.springframework.stereotype.Component;
import so.dian.hera.common.enums.NotifyStatusEnum;
import so.dian.hera.utils.TradeNoGenerator;

@Component
public class ApmTokenPaySupport {

    public PaymentDO convert2PrepayCreatePayment(PrepayCreateRequest prepayCreateRequest) {
        PaymentDO paymentDO = new PaymentDO();
        paymentDO.setOrderNo(prepayCreateRequest.getBizNo());
        paymentDO.setCooperatorId(prepayCreateRequest.getCooperatorId());
        paymentDO.setUserId(prepayCreateRequest.getUserId());
        paymentDO.setBizType(prepayCreateRequest.getBizType());
        paymentDO.setPayType(prepayCreateRequest.getPayway().getPayway());
        paymentDO.setPayAmount(prepayCreateRequest.getPayAmount());
        paymentDO.setCurrency(prepayCreateRequest.getCurrency());
        paymentDO.setDelayTime(prepayCreateRequest.getDelayTime());
        paymentDO.setStatus(PayStatus.APM_TOKEN_AUTH_INIT.getCode());
        paymentDO.setTradeNo(TradeNoGenerator.generateTradeNo(prepayCreateRequest.getBizType(), prepayCreateRequest.getUserId()));
        paymentDO.setReqSystem(prepayCreateRequest.getSystem());
        paymentDO.setNotifyFlag(prepayCreateRequest.getIsNotify() ? NotifyStatusEnum.INIT.getCode() : NotifyStatusEnum.NOT_INFORM.getCode());
        paymentDO.setNotifyTag("");
//        autoCancelProcessor.elementAppend(checkoutPrepayCreateRequest.getIsAutoCancel(), paymentDO.getTradeNo());
        return paymentDO;
    }

    public PaymentDO convert2PayPayment(ApmTokenPayRequest request) {
        PaymentDO paymentDO = new PaymentDO();
        paymentDO.setOrderNo(request.getBizNo());
        paymentDO.setCooperatorId(request.getCooperatorId());
        paymentDO.setUserId(request.getUserId());
        paymentDO.setBizType(request.getBizType());
        paymentDO.setPayType(request.getPayway().getPayway());
        paymentDO.setPayAmount(request.getOrderAmount());
        paymentDO.setCurrency(request.getCurrency());
//        paymentDO.setDelayTime(captureRequest.getDelayTime());
        paymentDO.setStatus(PayStatus.INIT.getCode());
        paymentDO.setTradeNo(TradeNoGenerator.generateTradeNo(request.getBizType(), request.getUserId()));
        paymentDO.setReqSystem(request.getSystem());
        paymentDO.setNotifyFlag(request.getIsNotify() ? NotifyStatusEnum.INIT.getCode() : NotifyStatusEnum.NOT_INFORM.getCode());
        paymentDO.setNotifyTag("");
        return paymentDO;
    }
}
