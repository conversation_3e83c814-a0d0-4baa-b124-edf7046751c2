package so.dian.hera.service.pay.bankcard;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chargebolt.hera.domain.notify.ChannelNotifyBody;
import com.chargebolt.hera.client.enums.status.RetryStatus;
import com.chargebolt.hera.domain.PaymentRetryRecord;
import com.chargebolt.hera.client.dto.pay.checkout.req.PreAuthCancelRequest;
import com.chargebolt.hera.client.dto.pay.checkout.req.PreAuthCaptureRequest;
import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PreAuthCancelResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PreAuthCaptureResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PrepayCreateResultDTO;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.domain.notify.PaymentRetryBody;
import com.chargebolt.hera.domain.sharding.PaymentNote;
import so.dian.hera.service.persist.db.PaymentRetryRecordService;
import so.dian.platform.common.constants.BusinessConstants;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import so.dian.hera.common.enums.NotifyStatusEnum;
import so.dian.hera.interceptor.CheckoutPayProcessor;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.hera.service.route.RouteService;
import so.dian.platform.common.configuration.redis.RedisClient;
import so.dian.platform.common.enums.CacheEnum;
import so.dian.platform.common.mq.producer.BizMqProducer;
import so.dian.platform.common.mq.producer.SelfMqProducer;
import so.dian.platform.common.utils.ValidateUtil;
import javax.annotation.Resource;
import java.util.Objects;

/**
 * Created by wanggong
 */
@Slf4j
@Component
public class BankcardPayService {

    @Resource
    private RouteService routeService;
    @Resource
    private PaymentPersistService paymentPersistService;
    @Resource
    private BankcardPaySupport bankcardPaySupport;
    @Resource
    private BizMqProducer bizMqProducer;
    @Resource
    private SelfMqProducer selfMqProducer;
    @Resource
    private RedisClient redisClient;
    @Resource
    private PaymentRetryRecordService paymentRetryRecordService;
    @Resource
    private CapturePaymentCreateService capturePaymentCreateService;

    /**
     * 创建预授权
     */
    public PrepayCreateResultDTO createPreAuth(PrepayCreateRequest prepayCreateRequest) {
        //获取支付记录
        PaymentDO paymentDO = paymentPersistService.getPaymentByBizNo(prepayCreateRequest.getBizNo());
        //防重校验
        ValidateUtil.requiredNull(paymentDO, HeraBizErrorCodeEnum.PLAT_PAY_ALREADY_EXIST);
        //渠道解析
        PaywayEnum paywayEnum = routeService.channelAnalysis(prepayCreateRequest.getPayway().getPayway());
        //获取渠道处理器
        CheckoutPayProcessor payProcessor = routeService.getCheckoutPayProcessor(paywayEnum);
        //预授权预处理
        paymentDO = createPreAuthCreatePayment(prepayCreateRequest);
        //发送预授权请求
        PrepayCreateResultDTO prepayCreateResultDTO = payProcessor.doPrePay(prepayCreateRequest, paymentDO);
        return prepayCreateResultDTO;
    }

    /**
     * 预授权创建回调
     */
    public Boolean handlePreAuthCreationNotify(ChannelNotifyBody channelNotifyBody) {
        PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(channelNotifyBody.getCurrentTradeNo());
        ValidateUtil.requiredNotNull(paymentDO, HeraBizErrorCodeEnum.PAYMENT_NOT_EXIST);
        // antom 银行卡支付时返回
        if(Objects.equals(channelNotifyBody.getPayway(),PaywayEnum.ANTOM_CHECKOUT_CARD)){
            PaymentNote paymentNote = JSON.parseObject(paymentDO.getNote(),PaymentNote.class);
            paymentNote.setCardToken((String) channelNotifyBody.getExtend().get(BusinessConstants.CARD_TOKEN));
            paymentNote.setNetworkTransactionId((String) channelNotifyBody.getExtend().get(BusinessConstants.NETWORK_TRANSACTION_ID));
            paymentDO.setNote(JSON.toJSONString(paymentNote));
            paymentDO.setPayMethod(channelNotifyBody.getPayMethod().getId());
        }
        processPreAuthAcceptPayment(channelNotifyBody, paymentDO);
        return true;
    }

    /**
     * 预授权完成授权回调
     */
    public void handlePreAuthCreatedNotify(ChannelNotifyBody channelNotifyBody) {
        PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(channelNotifyBody.getCurrentTradeNo());
        ValidateUtil.requiredNotNull(paymentDO, HeraBizErrorCodeEnum.PAYMENT_NOT_EXIST);
        processPreAuthAuthorizedPayment(paymentDO);
        if (needNotify(paymentDO)) {
            bizMqProducer.sendBankcardPreAuthCreateMsg(paymentDO);
        }
    }

    // 预授权失败
    public void handlePreAuthFailedNotify(ChannelNotifyBody channelNotifyBody) {
        PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(channelNotifyBody.getCurrentTradeNo());
        ValidateUtil.requiredNotNull(paymentDO, HeraBizErrorCodeEnum.PAYMENT_NOT_EXIST);
        processPreAuthFailedPayment(paymentDO);
    }

    public PreAuthCaptureResultDTO capture(PreAuthCaptureRequest preAuthCaptureRequest) {
        if (preAuthCaptureRequest.getOrderAmount() <= 0) {
            throw HeraBizException.create(HeraBizErrorCodeEnum.AMOUNT_INVALID);
        }
        // 找到预授权的payment
        String preAuthTradeNo = preAuthCaptureRequest.getPreAuthTradeNo();
        PaymentDO preAuthPaymentDO = paymentPersistService.getPaymentByTradeNo(preAuthTradeNo);
        ValidateUtil.requiredNotNull(preAuthPaymentDO, HeraBizErrorCodeEnum.PAYMENT_EXIST);
        // 找到预授权的payway
        PaywayEnum paywayEnum = PaywayEnum.explain(preAuthPaymentDO.getPayType());
        CheckoutPayProcessor payProcessor = routeService.getCheckoutPayProcessor(paywayEnum);
        // 找到预授权单号
        PaymentDO capturePaymentDO = capturePaymentCreateService.create(preAuthCaptureRequest,preAuthPaymentDO);
        log.info("预授权扣款，预授权数据：{}",JSON.toJSONString(preAuthPaymentDO));
        log.info("预授权扣款，支付数据：{}",JSON.toJSONString(capturePaymentDO));
        PreAuthCaptureResultDTO preAuthCaptureResultDTO = payProcessor.doCapture(preAuthCaptureRequest, preAuthPaymentDO.getPayNo(), capturePaymentDO);
        redisClient.set(CacheEnum.BANKCARD_NOTIFY.getNs(), "pre_auth_trade_no-" + preAuthPaymentDO.getPayNo(), preAuthPaymentDO.getTradeNo(), 86400);
        if (preAuthCaptureResultDTO.isSuccess()) {
            // 扣款同步返回的结果
            // 创建订单payment
            // capture的tradeno因为wooshpay带不回来，所以先放缓存，通过payno也就是wooshpay的id再找回来
            processCapturingPayment(preAuthPaymentDO);
            log.info("set capture_trade_no: {}", capturePaymentDO.getTradeNo());
            redisClient.set(CacheEnum.BANKCARD_NOTIFY.getNs(), "capture_trade_no-" + preAuthPaymentDO.getPayNo(), capturePaymentDO.getTradeNo(), 86400);
            // 创建预授权重试记录并同步查询支付结果并更新 PaymentRetryHandler
            savePaymentRetry(capturePaymentDO.getTradeNo(), preAuthPaymentDO, paywayEnum.getPayway());
        } else {
            // 同步调用失败
            log.warn("预授权扣款请求失败: {}", JSON.toJSONString(preAuthCaptureResultDTO));
        }
        return preAuthCaptureResultDTO;
    }

    private void savePaymentRetry(String tradeNo, PaymentDO preAuthPaymentDO, Integer payway){
        PaymentRetryRecord retryRecord= new PaymentRetryRecord();
        retryRecord.setTradeNo(tradeNo);
        // FIXME 新增卡授权通道，需要增加逻辑
        //WOOSHPAY_CARD 51 preAuthPaymentDO.getPayNo()
        if(Objects.equals(PaywayEnum.WOOSHPAY_CARD.getPayway(), payway)||
                Objects.equals(PaywayEnum.AIRWALLEX_CHECKOUT_CARD.getPayway(), payway)){
            retryRecord.setPayNo(preAuthPaymentDO.getPayNo());
        }else if(Objects.equals(PaywayEnum.PINGPONG_CHECKOUT_CARD.getPayway(), payway)){
            retryRecord.setPayNo(preAuthPaymentDO.getTradeNo());
        }else {
            retryRecord.setPayNo(preAuthPaymentDO.getTradeNo());
        }
        retryRecord.setPayway(payway);
        retryRecord.setState(RetryStatus.PROCESSING.getCode());
        paymentRetryRecordService.insert(retryRecord);
        PaymentRetryBody retryBody= new PaymentRetryBody();
        retryBody.setTradeNo(tradeNo);
        retryBody.setPreTradeNo(preAuthPaymentDO.getTradeNo());
        selfMqProducer.sendSelfPaymentRetryDelayQuery(retryBody);
    }

    public void asyncCapturedNotify(ChannelNotifyBody channelNotifyBody) {
        selfMqProducer.sendSelfCaptureMsg(channelNotifyBody);
    }

    public boolean handleCapturedSelfMsg(ChannelNotifyBody channelNotifyBody) {
        String createTradeNo = null;
        String captureTradeNo = null;
        if (PaywayEnum.WOOSHPAY_CARD.equals(channelNotifyBody.getPayway())) {
            createTradeNo = channelNotifyBody.getCurrentTradeNo();
            captureTradeNo = redisClient.get(CacheEnum.BANKCARD_NOTIFY.getNs(), "capture_trade_no-" + channelNotifyBody.getOuterNo(), String.class);
        } else if (PaywayEnum.PINGPONG_CHECKOUT_CARD.equals(channelNotifyBody.getPayway())) {
            createTradeNo = channelNotifyBody.getRefTradeNo();
            captureTradeNo = channelNotifyBody.getCurrentTradeNo();
        } else if (PaywayEnum.ANTOM_CHECKOUT_CARD.equals(channelNotifyBody.getPayway())) {
            PaymentDO capturePaymentDO = paymentPersistService.getPaymentByTradeNo(channelNotifyBody.getCurrentTradeNo());
            if(Objects.isNull(capturePaymentDO)){
                log.info("no createTrade in authPayNo = {}",channelNotifyBody.getRefPayNo());
                return false;
            }
            PaymentNote paymentNote = JSON.parseObject(capturePaymentDO.getNote(),PaymentNote.class);
            createTradeNo = paymentNote.getAuthTradeNo();
            captureTradeNo = capturePaymentDO.getTradeNo();
        }else if (PaywayEnum.AIRWALLEX_CHECKOUT_CARD.equals(channelNotifyBody.getPayway())){
            createTradeNo = channelNotifyBody.getRefTradeNo();
            // capture 请求时，
            PaymentDO preAuthPayment = paymentPersistService.getPaymentByTradeNo(createTradeNo);
            if (preAuthPayment == null || StringUtils.isEmpty(preAuthPayment.getNote())) {
                log.error("preAuthPayment is null or note is null");
                return false;
            }
            PaymentNote paymentNote = JSONObject.parseObject(preAuthPayment.getNote(), PaymentNote.class);
            captureTradeNo = paymentNote.getCaptureTradeNo();
        }

        log.info("createTradeNo: {}", createTradeNo);
        if (StringUtils.isEmpty(createTradeNo)) {
            log.info("no createTradeNo in redis");
            return false;
        }
        log.info("captureTradeNo: {}", captureTradeNo);
        if (StringUtils.isEmpty(captureTradeNo)) {
            log.info("no captureTradeNo in redis");
            return false;
        }
        // createPayment:   ->  PayStatus.CARD_CAPTURED
        PaymentDO preAuthPaymentDO = paymentPersistService.getPaymentByTradeNo(createTradeNo);
        processCapturedPayment(preAuthPaymentDO);

        // capturePayment:  ->  PayStatus.PAID
        PaymentDO capturePaymentDO = paymentPersistService.getPaymentByTradeNo(captureTradeNo);
        ValidateUtil.requiredNotNull(capturePaymentDO, HeraBizErrorCodeEnum.PAYMENT_NOT_EXIST);
        // 使用预授权的tradeNo作为支付payment的payNo，原因是退款时需要拿预授权的payment退，这里作为钩子
        capturePaymentDO.setPayNo(preAuthPaymentDO.getTradeNo());
        // 定制化逻辑：更新antom实际请款模式，后续退款使用
        if (PaywayEnum.ANTOM_CHECKOUT_CARD.equals(channelNotifyBody.getPayway())) {
            capturePaymentDO.setPayMethod(channelNotifyBody.getPayMethod().getId());
        }
        processPaidPayment(capturePaymentDO);
        // notify
        if (needNotify(capturePaymentDO)) {
            bizMqProducer.sendBankcardPreAuthCaptureMsg(capturePaymentDO);
        }
        return true;
    }

    public PreAuthCancelResultDTO cancelPreAuth(PreAuthCancelRequest preAuthCancelRequest) {
        //获取支付记录
        PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(preAuthCancelRequest.getTradeNo());
        //非空校验
        ValidateUtil.requiredNotNull(paymentDO, HeraBizErrorCodeEnum.ORDER_NON_EXIST);
        //状态拦截
        ValidateUtil.assertEquals(PayStatus.CARD_AUTHORIZED.getCode(), paymentDO.getStatus(), HeraBizErrorCodeEnum.STATUS_ERROR);
        //渠道解析
        PaywayEnum paywayEnum = routeService.channelAnalysis(paymentDO.getPayType());
        //获取渠道处理器
        CheckoutPayProcessor payProcessor = routeService.getCheckoutPayProcessor(paywayEnum);
        //撤销预处理
        processPreAuthCancelingPayment(paymentDO);
        //发送撤销请求
        PreAuthCancelResultDTO rsp = payProcessor.doCancel(paymentDO);
        // 撤销结束(antom撤销无通知，需要在此发送通知解除授权)
        processPreAuthCanceledAfter(paymentDO,rsp);
        return rsp;
    }

//    public void handleCancelNotify(BankcardNotifyBody bankcardNotifyBody) {
//        selfMqProducer.sendSelfCancelMsg(bankcardNotifyBody);
//    }

    public boolean handleCanceledSelfMsg(ChannelNotifyBody channelNotifyBody) {
        PaymentDO preAuthPaymentDO = paymentPersistService.getPaymentByTradeNo(channelNotifyBody.getCurrentTradeNo());
        ValidateUtil.requiredNotNull(preAuthPaymentDO, HeraBizErrorCodeEnum.ORDER_NON_EXIST);
        ValidateUtil.assertEquals(PayStatus.CARD_CANCELING.getCode(), preAuthPaymentDO.getStatus(), HeraBizErrorCodeEnum.STATUS_ERROR);
        processPreAuthCanceledPayment(preAuthPaymentDO);
        if (needNotify(preAuthPaymentDO)) {
            bizMqProducer.sendBankcardPreAuthCancelMsg(preAuthPaymentDO);
        }
        return true;
    }

    // -------------------处理payment------------------

    private PaymentDO createPreAuthCreatePayment(PrepayCreateRequest prepayCreateRequest) {
        PaymentDO paymentDO = bankcardPaySupport.convert2PreAuthCreatePaymentPO(prepayCreateRequest);
        paymentPersistService.insert(paymentDO);
        return paymentDO;
    }

    private void processPreAuthAcceptPayment(ChannelNotifyBody rsp, PaymentDO paymentDO) {
        log.info("processPreAuthAcceptPayment {} -> {}", paymentDO.getStatus(), PayStatus.CARD_ACCEPT.getCode());
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.CARD_ACCEPT.getCode());
        paymentDO.setPayNo(rsp.getOuterNo());
        paymentPersistService.updateStatus(paymentDO);
    }

    private void processPreAuthAuthorizedPayment(PaymentDO paymentDO) {
        log.info("processPreAuthAuthorizedPayment {} -> {}", paymentDO.getStatus(), PayStatus.CARD_AUTHORIZED.getCode());
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.CARD_AUTHORIZED.getCode());
        paymentPersistService.updateStatus(paymentDO);
    }

    private void processPreAuthFailedPayment(PaymentDO paymentDO) {
        log.info("processPreAuthFailedPayment {} -> {}", paymentDO.getStatus(), PayStatus.CARD_AUTH_FAILED.getCode());
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.CARD_AUTH_FAILED.getCode());
        paymentPersistService.updateStatus(paymentDO);
    }

    private void processCapturingPayment(PaymentDO paymentDO) {
        log.info("processCapturingPayment {} -> {}", paymentDO.getStatus(), PayStatus.CARD_CAPTURING.getCode());
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.CARD_CAPTURING.getCode());
        paymentPersistService.updateStatus(paymentDO);
    }

    private void processCapturedPayment(PaymentDO paymentDO) {
        log.info("processCapturedPayment {} -> {}", paymentDO.getStatus(), PayStatus.CARD_CAPTURED.getCode());
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.CARD_CAPTURED.getCode());
        paymentPersistService.updateStatus(paymentDO);
    }

    private void processPaidPayment(PaymentDO paymentDO) {
        log.info("processPaidPayment {} -> {}", paymentDO.getStatus(), PayStatus.PAID.getCode());
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.PAID.getCode());
        paymentPersistService.updateStatus(paymentDO);
    }

    private void processPreAuthCancelingPayment(PaymentDO paymentDO) {
        log.info("processPreAuthCancelingPayment {} -> {}", paymentDO.getStatus(), PayStatus.CARD_CANCELING.getCode());
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.CARD_CANCELING.getCode());
        paymentPersistService.updateStatus(paymentDO);
    }

    private void processPreAuthCanceledPayment(PaymentDO paymentDO) {
        log.info("processPreAuthCanceledPayment {} -> {}", paymentDO.getStatus(), PayStatus.CARD_CANCELED.getCode());
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.CARD_CANCELED.getCode());
        paymentPersistService.updateStatus(paymentDO);
    }

    private static boolean needNotify(PaymentDO paymentDO) {
        if (NotifyStatusEnum.NOT_INFORM.getCode().equals(paymentDO.getNotifyFlag())) {
            return false;
        }
        return true;
    }

    private void processPreAuthCanceledAfter(PaymentDO paymentDO,PreAuthCancelResultDTO rsp) {
        if(!rsp.isSuccess() || !Objects.equals(paymentDO.getPayType(),PaywayEnum.ANTOM_CHECKOUT_CARD.getPayway())){
            return;
        }
        processPreAuthCanceledPayment(paymentDO);
        if (needNotify(paymentDO)) {
            bizMqProducer.sendBankcardPreAuthCancelMsg(paymentDO);
        }
    }

}
