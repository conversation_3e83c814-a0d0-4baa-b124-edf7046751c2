package so.dian.hera.service.pay.bankcard;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.pay.checkout.req.PreAuthCaptureRequest;
import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.chargebolt.hera.domain.sharding.PaymentNote;
import org.springframework.stereotype.Component;
import so.dian.hera.common.enums.NotifyStatusEnum;
import so.dian.hera.service.component.AutoCancelProcessor;
import so.dian.hera.utils.TradeNoGenerator;
import javax.annotation.Resource;
import java.util.Objects;

@Component
public class BankcardPaySupport {
    @Resource
    private AutoCancelProcessor autoCancelProcessor;

    public PaymentDO convert2PreAuthCreatePaymentPO(PrepayCreateRequest prepayCreateRequest) {
        PaymentDO paymentDO = new PaymentDO();
        paymentDO.setOrderNo(prepayCreateRequest.getBizNo());
        paymentDO.setCooperatorId(prepayCreateRequest.getCooperatorId());
        paymentDO.setUserId(prepayCreateRequest.getUserId());
        paymentDO.setBizType(PaymentBizTypeEnum.CARD_PRE_AUTH.getCode());
        paymentDO.setPayType(prepayCreateRequest.getPayway().getPayway());
        paymentDO.setPayAmount(prepayCreateRequest.getPayAmount());
        paymentDO.setCurrency(prepayCreateRequest.getCurrency());
        paymentDO.setDelayTime(prepayCreateRequest.getDelayTime());
        paymentDO.setStatus(PayStatus.CARD_SEND.getCode());
        paymentDO.setTradeNo(TradeNoGenerator.generateTradeNo(PaymentBizTypeEnum.CARD_PRE_AUTH, prepayCreateRequest.getUserId()));
        paymentDO.setReqSystem(prepayCreateRequest.getSystem());
        paymentDO.setNotifyFlag(prepayCreateRequest.getIsNotify() ? NotifyStatusEnum.INIT.getCode() : NotifyStatusEnum.NOT_INFORM.getCode());
        paymentDO.setNotifyTag("");
        // 记录银行卡授权模式
        if(Objects.nonNull(prepayCreateRequest.getExtInfo().getCaptureMode())){
            PaymentNote paymentNote = new PaymentNote();
            paymentNote.setCaptureMode(prepayCreateRequest.getExtInfo().getCaptureMode());
            paymentDO.setNote(JSON.toJSONString(paymentNote));
        }
        if(Objects.nonNull(prepayCreateRequest.getExtInfo())){
            paymentDO.setPayMethod(prepayCreateRequest.getExtInfo().getPayMethod());
        }
        autoCancelProcessor.elementAppend(prepayCreateRequest.getIsAutoCancel(), paymentDO.getTradeNo());
        return paymentDO;
    }

    public PaymentDO convert2PreAuthCapturePaymentPO(PreAuthCaptureRequest preauthCaptureRequest) {
        PaymentDO paymentDO = new PaymentDO();
        paymentDO.setOrderNo(preauthCaptureRequest.getBizNo());
        paymentDO.setCooperatorId(preauthCaptureRequest.getCooperatorId());
        paymentDO.setUserId(preauthCaptureRequest.getUserId());
        paymentDO.setBizType(PaymentBizTypeEnum.PAY_ORDER.getCode());
        paymentDO.setPayType(preauthCaptureRequest.getPayway().getPayway());
        paymentDO.setPayAmount(preauthCaptureRequest.getOrderAmount());
        paymentDO.setCurrency(preauthCaptureRequest.getCurrency());
        paymentDO.setStatus(PayStatus.INIT.getCode());
        paymentDO.setTradeNo(TradeNoGenerator.generateTradeNo(PaymentBizTypeEnum.PAY_ORDER, preauthCaptureRequest.getUserId()));
        paymentDO.setReqSystem(preauthCaptureRequest.getSystem());
        paymentDO.setNotifyFlag(preauthCaptureRequest.getIsNotify() ? NotifyStatusEnum.INIT.getCode() : NotifyStatusEnum.NOT_INFORM.getCode());
        paymentDO.setNotifyTag("");
        return paymentDO;
    }
}
