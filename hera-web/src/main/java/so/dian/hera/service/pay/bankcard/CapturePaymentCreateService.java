package so.dian.hera.service.pay.bankcard;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.pay.checkout.req.PreAuthCaptureRequest;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.chargebolt.hera.domain.sharding.PaymentNote;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import so.dian.hera.service.persist.db.PaymentPersistService;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 事务方法单独抽出来
 */
@Service
public class CapturePaymentCreateService {

    @Resource
    private PaymentPersistService paymentPersistService;
    @Resource
    private BankcardPaySupport bankcardPaySupport;

    @Transactional(rollbackFor = Exception.class,transactionManager = "shardingTransactionManager")
    public PaymentDO create(PreAuthCaptureRequest preauthCaptureRequest, PaymentDO preAuthPaymentDO) {
        PaymentDO paymentDO = bankcardPaySupport.convert2PreAuthCapturePaymentPO(preauthCaptureRequest);
        paymentDO.setPayMethod(preAuthPaymentDO.getPayMethod());
        paymentDO.setDeviceNo(preAuthPaymentDO.getDeviceNo());
        // antom免密代扣所需的参数
        if(Objects.equals(preAuthPaymentDO.getPayType(), PaywayEnum.ANTOM_CHECKOUT_CARD.getPayway())){
            PaymentNote paymentNote = JSON.parseObject(preAuthPaymentDO.getNote(),PaymentNote.class);
            paymentNote.setAuthTradeNo(preAuthPaymentDO.getTradeNo());
            paymentNote.setAuthPayAmount(preAuthPaymentDO.getPayAmount());
            paymentDO.setNote(JSON.toJSONString(paymentNote));
        }
        if (Objects.equals(preAuthPaymentDO.getPayType(),PaywayEnum.AIRWALLEX_CHECKOUT_CARD.getPayway())){
            PaymentNote paymentNote = JSON.parseObject(preAuthPaymentDO.getNote(),PaymentNote.class);
            if (paymentNote!=null&& org.apache.commons.lang3.StringUtils.isBlank(paymentNote.getCaptureTradeNo())){
                // 写入请款单的业务单号，方便空中云汇回调时获取（因为空中云汇无法透传这个单号）
                paymentNote.setCaptureTradeNo(paymentDO.getTradeNo());
                preAuthPaymentDO.setNote(JSON.toJSONString(paymentNote));
                paymentPersistService.updateNote(preAuthPaymentDO);
            }

        }
        paymentPersistService.insert(paymentDO);
        return paymentDO;
    }
}
