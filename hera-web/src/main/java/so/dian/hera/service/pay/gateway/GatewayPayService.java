package so.dian.hera.service.pay.gateway;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.PayCommonReq;
import com.chargebolt.hera.client.dto.callback.impay.BaseImPayCallbackDTO;
import com.chargebolt.hera.client.dto.callback.refund.BaseRefundCallbackDTO;
import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.req.OrderCloseReq;
import com.chargebolt.hera.client.dto.pay.req.OrderPayQueryReq;
import com.chargebolt.hera.client.dto.pay.req.OrderPayReq;
import com.chargebolt.hera.client.dto.pay.req.OrderRefundQueryReq;
import com.chargebolt.hera.client.dto.pay.req.OrderRefundReq;
import com.chargebolt.hera.client.dto.pay.rsp.OrderCloseRsp;
import com.chargebolt.hera.client.dto.pay.rsp.OrderPayQueryRsp;
import com.chargebolt.hera.client.dto.pay.rsp.OrderPayRsp;
import com.chargebolt.hera.client.dto.pay.rsp.OrderRefundQueryRsp;
import com.chargebolt.hera.client.dto.pay.rsp.OrderRefundRsp;
import com.chargebolt.hera.client.enums.CallbackOriginalTypeEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.client.enums.status.RefundStatus;
import com.chargebolt.hera.domain.RefundDO;
import com.chargebolt.hera.domain.sharding.PaymentDO;

import lombok.extern.slf4j.Slf4j;
import so.dian.hera.interceptor.PayOrderProcessor;
import so.dian.hera.service.CurrencyExchangeProperty;
import so.dian.hera.service.notify.WechatV3NotifyService;
import so.dian.hera.service.pay.PaymentCacheService;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.hera.service.persist.db.RefundPersistService;
import so.dian.hera.service.route.RouteService;
import so.dian.hera.utils.AssertUtils;
import so.dian.hera.utils.TradeNoGenerator;
import so.dian.platform.common.configuration.redis.lock.DistributedLock;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.mq.producer.BizMqProducer;
import so.dian.platform.common.utils.ValidateUtil;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class GatewayPayService {

    @Resource
    private BizMqProducer bizMqProducer;

    @Resource
    private RouteService routeService;
    @Resource
    private PaymentPersistService paymentPersistService;
    @Resource
    private RefundPersistService refundPersistService;
    @Resource
    private WechatV3NotifyService wechatV3NotifyService;

    @Resource
    private CurrencyExchangeProperty currencyExchangeProperty;
    @Resource
    private PaymentCacheService paymentCacheService;
    @PostConstruct
    public void init() {
        log.info(JSON.toJSONString(currencyExchangeProperty, true));
    }

    private PayOrderProcessor getPayOrderProcessor(PayCommonReq req) {
        PaywayEnum paywayEnum = PaywayEnum.explain(req.getPayWay());
        return routeService.getPayOrderProcessor(paywayEnum);
    }

    /**
     * 支付预下单
     *
     * @param req
     * @return
     */
    @DistributedLock(key = "#req.userId+':'+#req.orderNo+':'+#req.payWay")
    public OrderPayRsp payOrder(OrderPayReq req) {
        // 查询渠道订单信息
        PaymentDO paymentDO = paymentPersistService.getPaymentByBizNo(req.getOrderNo());
        if (Objects.isNull(paymentDO)) {
            // 记录渠道支付单
            paymentDO = GatewayPaySupport.convert(req);
            // 是否需要货币转换
            if (req.getPayWay().equals(PaywayEnum.WECHAT_MINIPROGRAM.getPayway())){
                if(Objects.nonNull(req.getExchangeInfo())
                        && Objects.nonNull(req.getExchangeInfo().getExchangeRate())
                        && Objects.nonNull(req.getExchangeInfo().getTargetAmount())
                        && Objects.nonNull(req.getExchangeInfo().getTargetCurrency())){
                    paymentDO.getNoteObject().put("currencyExchange", req.getExchangeInfo());

                    // 替换拉起支付的货币单位和金额
                    req.setCurrency(req.getExchangeInfo().getTargetCurrency());
                    req.setOrderAmount(req.getExchangeInfo().getTargetAmount());
                }
            }
            paymentPersistService.insert(paymentDO);
            req.setTradeNo(paymentDO.getTradeNo());
        } else {
            AssertUtils.trueWithBizExp(Objects.equals(paymentDO.getStatus(), PayStatus.PAID.getCode()), HeraBizErrorCodeEnum.ILLEGAL_ERROR, "订单已支付成功");
            AssertUtils.notTrueWithBizExp(Objects.equals(req.getOrderAmount(), paymentDO.getPayAmount()), HeraBizErrorCodeEnum.ILLEGAL_ERROR, "订单金额非法");
            req.setTradeNo(paymentDO.getTradeNo());
        }
        PrepayCreateRequest.PrepayCreateExtInfo extInfo= new PrepayCreateRequest.PrepayCreateExtInfo();
        extInfo.setTopAgentId(req.getAgentId());
        extInfo.setDeviceNo(req.getDeviceNo());
        paymentCacheService.setPaymentExtCache(extInfo, paymentDO.getTradeNo());

        // 创建三方渠道支付订单
        return getPayOrderProcessor(req).payOrder(req);
    }

    /**
     * 支付订单关闭
     *
     * @param req
     * @return
     */
    public OrderCloseRsp closeOrder(OrderCloseReq req) {
        // 查询渠道订单信息，合法性验证
        PaymentDO paymentDO = paymentPersistService.getPaymentByBizNo(req.getOrderNo());
        AssertUtils.trueWithBizExp(Objects.isNull(paymentDO), HeraBizErrorCodeEnum.ORDER_NON_EXIST);
        AssertUtils.trueWithBizExp(Objects.equals(paymentDO.getStatus(), PayStatus.PAID.getCode()), HeraBizErrorCodeEnum.ILLEGAL_ERROR, "订单已支付成功");
        if (Objects.equals(paymentDO.getStatus(), PayStatus.CANCEL.getCode())) {
            OrderCloseRsp closeRsp = new OrderCloseRsp();
            closeRsp.setOrderNo(req.getOrderNo());
            closeRsp.setPayTradeNo(req.getTradeNo());
            closeRsp.setStatus(PayStatus.CANCEL.getKey());
            return closeRsp;
        }
        // 关闭三方渠道支付订单
        req.setTradeNo(paymentDO.getTradeNo());
        OrderCloseRsp closeRsp = getPayOrderProcessor(req).closeOrder(req);

        // 更新订单状态
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.CANCEL.getCode());
        paymentDO.setFailReason(StringUtils.isBlank(req.getReason()) ? "订单超时关闭" : req.getReason());
        paymentPersistService.updateStatus(paymentDO);
        return closeRsp;
    }

    /**
     * 支付订单信息查询
     *
     * @param req
     * @return
     */
    public OrderPayQueryRsp queryPayOrder(OrderPayQueryReq req) {
        // 查询渠道订单信息
        PaymentDO paymentDO = paymentPersistService.getPaymentByBizNo(req.getOrderNo());
        AssertUtils.trueWithBizExp(Objects.isNull(paymentDO), HeraBizErrorCodeEnum.ORDER_NON_EXIST);

        // 不查询三方
        if (Objects.nonNull(req.getQryThirdChannel()) && !req.getQryThirdChannel()) {
            OrderPayQueryRsp queryRsp = new OrderPayQueryRsp();
            queryRsp.setThirdPayTradeNo(paymentDO.getPayNo());
            queryRsp.setPayTradeNo(req.getTradeNo());
            queryRsp.setOrderNo(req.getOrderNo());
            queryRsp.setTotalFee(paymentDO.getPayAmount());
            queryRsp.setPayTime(paymentDO.getPayTime());
            queryRsp.setStatus(PayStatus.explain(paymentDO.getStatus()).getKey());
            return queryRsp;
        }

        // 查询三方渠道支付订单
        req.setTradeNo(paymentDO.getTradeNo());
        OrderPayQueryRsp orderPayQueryRsp = getPayOrderProcessor(req).queryPayOrder(req);

        // 本地订单为处理中，三方为成功，则更新本地订单为成功并发送 退款成功消息
        if (Objects.equals(paymentDO.getStatus(), PayStatus.INIT.getCode()) && Objects.equals(orderPayQueryRsp.getStatus(), PayStatus.PAID.getKey())) {
            wechatV3NotifyService.handleNotify(CallbackOriginalTypeEnum.PAY, orderPayQueryRsp.getContent());
        }
        return orderPayQueryRsp;
    }

    /**
     * 退款申请（支持多次部分退款）
     * 三方退款申请成功，更新refund的状态由 1-初始化 更新为 2-退款中，payment的状态由 2-已支付 更新为 3-退款中
     *
     * @param req
     * @return
     */
    @Deprecated
    @DistributedLock(key = "#req.refundOrderNo+':'+#req.oriOrderNo")
    public OrderRefundRsp refundOrder(OrderRefundReq req) {
        // 查询渠道订单信息，合法性验证
        PaymentDO paymentDO = paymentPersistService.getPaymentByBizNo(req.getOriOrderNo());
        AssertUtils.trueWithBizExp(Objects.isNull(paymentDO), HeraBizErrorCodeEnum.ORDER_NON_EXIST);
        // 查询历史退款记录,校验退款申请金额是否大于可退金额
        List<RefundDO> refundDOList = refundPersistService.getRefundListByTrade(paymentDO.getTradeNo());
        List<RefundDO> refundDOS = null;
        RefundDO refundDO = null;
        if (!CollectionUtils.isEmpty(refundDOList)) {
            // 单号已存在，校验状态和金额是否合法
            refundDOS = refundDOList.stream().filter(item -> Objects.equals(req.getRefundOrderNo(), item.getOrderNo())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(refundDOS)) {
                refundDO = refundDOS.get(0);
                AssertUtils.trueWithBizExp(Objects.equals(refundDO.getStatus(), RefundStatus.FAIL.getCode()), HeraBizErrorCodeEnum.ILLEGAL_ERROR, "订单已退款失败，请更换单号重试");
                AssertUtils.trueWithBizExp(Objects.equals(refundDO.getStatus(), RefundStatus.REFUNDED.getCode()), HeraBizErrorCodeEnum.ILLEGAL_ERROR, "订单已退款成功，请勿重复申请");
                AssertUtils.notTrueWithBizExp(Objects.equals(req.getRefundAmount().intValue(), refundDO.getAmount()), HeraBizErrorCodeEnum.ILLEGAL_ERROR, "退款金额非法");
            }
            // refundAmount：已退金额（非失败状态下汇总的退款金额），remainingAmount：可退金额（支付金额-已退金额）
            Long refundAmount = refundDOList.stream().filter(item -> !Objects.equals(item.getStatus(), RefundStatus.FAIL.getCode())).mapToLong(RefundDO::getAmount).sum();
            Long remainingAmount = paymentDO.getPayAmount() - refundAmount;
            log.info("退款申请单号: {},退款申请金额(refundAmount): {},已退金额(refundAmount): {},可退金额(remainingAmount): {}", req.getRefundOrderNo(), req.getRefundAmount(), refundAmount, remainingAmount);
            AssertUtils.trueWithBizExp(req.getRefundAmount() > remainingAmount, HeraBizErrorCodeEnum.ILLEGAL_ERROR, "申请金额大于可退金额");
        }

        // 构建退款记录并入库
        if (CollectionUtils.isEmpty(refundDOS)) {
            refundDO = new RefundDO();
            refundDO.setOrderNo(req.getRefundOrderNo());
            refundDO.setTradeNo(paymentDO.getTradeNo());
//            if(Objects.equals(paymentDO.getPayType(), PaywayEnum.ZALOPAY_MINI.getPayway())){
//                refundDO.setRefundNo(new DateBuild(ZoneId.of("Asia/Ho_Chi_Minh")).formatter(DateBuild.SIMPLE_SHORT_DATE)
//                        +"_"+zaloPayProperties.getAppId()+"_"+TradeNoGenerator.generateRefundNo(paymentDO.getPayType(), paymentDO.getUserId()));
//
//            }else{
                refundDO.setRefundNo(TradeNoGenerator.generateRefundNo(paymentDO.getPayType(), paymentDO.getUserId()));
//            }
            refundDO.setAmount(req.getRefundAmount().intValue());
            refundDO.setCurrency(paymentDO.getCurrency());
            refundDO.setStatus(RefundStatus.INIT.getCode());
            refundDO.setBizType(paymentDO.getBizType());
            refundDO.setRefundType(paymentDO.getPayType());
            refundDO.setReason(req.getRefundReason());
            refundDO.setSystem(req.getReqSystem());
            boolean insertResult = refundPersistService.insert(refundDO);
            AssertUtils.notTrueWithBizExp(insertResult, HeraBizErrorCodeEnum.ILLEGAL_ERROR, "退款申请失败");
        }

        // 创建三方退款订单
        req.setTradeNo(paymentDO.getTradeNo());
        req.setRefundNo(refundDO.getRefundNo());
        req.setCurrency(paymentDO.getCurrency());
        OrderRefundRsp orderRefundRsp = getPayOrderProcessor(req).refundOrder(req);

        // 更新订单状态为已受理
        refundDO.setStatus(RefundStatus.REFUNDING.getCode());
        boolean updateResult = refundPersistService.update(refundDO);
        AssertUtils.notTrueWithBizExp(updateResult, HeraBizErrorCodeEnum.ILLEGAL_ERROR, "退款单更新失败");
        orderRefundRsp.setStatus(RefundStatus.explain(refundDO.getStatus()).getKey());

        // 更新支付单为退款中
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.REFUNDING.getCode());
        paymentPersistService.updateStatus(paymentDO);
        return orderRefundRsp;
    }

    /**
     * 退款查询
     *
     * @param req
     * @return
     */
    public OrderRefundQueryRsp queryRefundOrder(OrderRefundQueryReq req) {
        //  查询渠道退款订单信息
        RefundDO refundDO = refundPersistService.getRefundByOrder(req.getRefundOrderNo());
        AssertUtils.trueWithBizExp(Objects.isNull(refundDO), HeraBizErrorCodeEnum.ORDER_NON_EXIST);

        // 不查询三方
        if (Objects.nonNull(req.getQryThirdChannel()) && !req.getQryThirdChannel()) {
            PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(refundDO.getTradeNo());
            AssertUtils.trueWithBizExp(Objects.isNull(paymentDO), HeraBizErrorCodeEnum.ORDER_NON_EXIST);
            OrderRefundQueryRsp queryRsp = new OrderRefundQueryRsp();
            queryRsp.setRefundOrderNo(req.getRefundOrderNo());
            queryRsp.setPayRefundNo(req.getRefundNo());
            queryRsp.setThirdRefundNo(refundDO.getOutTraceNo());
            queryRsp.setPayAmount(paymentDO.getPayAmount());
            queryRsp.setRefundAmount(refundDO.getAmount());
            queryRsp.setRefundTime(refundDO.getRefundTime());
            queryRsp.setStatus(RefundStatus.explain(refundDO.getStatus()).getKey());
            return queryRsp;
        }

        // 查询三方渠道订单信息
        req.setRefundNo(refundDO.getRefundNo());
        OrderRefundQueryRsp orderRefundQueryRsp = getPayOrderProcessor(req).queryRefundOrder(req);

        // 本地订单为处理中，三方为成功，则更新本地订单为成功并发送 退款成功消息，微信处理逻辑
        if (Objects.equals(refundDO.getRefundType(), PaywayEnum.WECHAT_MINIPROGRAM.getPayway())
                &&Objects.equals(refundDO.getStatus(), RefundStatus.REFUNDING.getCode())
                && Objects.equals(orderRefundQueryRsp.getStatus(), RefundStatus.REFUNDED.getKey())) {
            wechatV3NotifyService.handleNotify(CallbackOriginalTypeEnum.REFUND, orderRefundQueryRsp.getContent());
        }
        return orderRefundQueryRsp;
    }


    /**
     * 支付回调处理
     * 支付成功，payment的状态由 1-初始化 更新为 2-已支付
     *
     * @param dto
     * @param <T>
     */
    public <T extends BaseImPayCallbackDTO> void handlePayCallback(T dto) {
        PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(dto.getOutTradeNo());
        AssertUtils.trueWithBizExp(Objects.isNull(paymentDO), HeraBizErrorCodeEnum.ORDER_NON_EXIST);

        if(Objects.equals(paymentDO.getStatus(), PayStatus.PAID.getCode())){
            log.info("支付成功数据不符合更新逻辑，不处理，tradeNo: {}", paymentDO.getTradeNo());
            return;
        }
        // 支付成功，更新本地订单
        if (Boolean.TRUE.equals(dto.getPayStatus()) && Objects.equals(paymentDO.getStatus(), PayStatus.INIT.getCode())) {
            paymentDO.setOriStatus(paymentDO.getStatus());
            paymentDO.setPayTime(dto.getPayTime());
            paymentDO.setPayNo(dto.getPaymentTradeNo());
            paymentDO.setStatus(PayStatus.PAID.getCode());
            paymentPersistService.updateStatus(paymentDO);
        } else {
            paymentDO.setOriStatus(paymentDO.getStatus());
            paymentDO.setStatus(PayStatus.FAIL.getCode());
            paymentDO.setFailReason(dto.getErrorMsg());
            paymentPersistService.updateStatus(paymentDO);
        }
        bizMqProducer.sendPayMsg(paymentDO);

    }

    /**
     * 退款回调处理
     * 退款成功，更新refund的状态由 3-退款中 更新为 4-已退款，payment的状态由 3-退款中 更新为 4-已退款
     * 退款失败，更新refund的状态由 3-退款中 更新为 99-退款失败，payment的状态由 3-退款中 更新为 2-已支付
     *
     * @param dto
     * @param <T>
     */
    public <T extends BaseRefundCallbackDTO> void handleRefundCallback(T dto) {
        RefundDO refundDO = refundPersistService.getRefund(dto.getRefundOrderNo());
        AssertUtils.trueWithBizExp(Objects.isNull(refundDO), HeraBizErrorCodeEnum.ORDER_NON_EXIST);

        PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(refundDO.getTradeNo());
        if (Boolean.TRUE.equals(dto.getRefundStatus())) {
            if (Objects.equals(refundDO.getStatus(), RefundStatus.REFUNDING.getCode())) {
                refundDO.setStatus(RefundStatus.REFUNDED.getCode());
                refundDO.setRefundTime(dto.getRefundTime());
                refundDO.setOutTraceNo(dto.getRefundId());
                boolean updateResult = refundPersistService.update(refundDO);
                AssertUtils.notTrueWithBizExp(updateResult, HeraBizErrorCodeEnum.ILLEGAL_ERROR, "退款更新失败");

                // 更新支付单为已退款
                paymentDO.setOriStatus(paymentDO.getStatus());
                paymentDO.setStatus(PayStatus.REFUNDED.getCode());
                paymentDO.setRefundTime(dto.getRefundTime());
                paymentDO.setRefundAmount((paymentDO.getRefundAmount() == null ? 0 : paymentDO.getRefundAmount()) + refundDO.getAmount());
                paymentPersistService.updateStatus(paymentDO);

                // 这里用来转成消息的payment应该是订单的payment，而不是押金本身payment
                String tradeNoOfRefund = refundDO.getTradeNo();
                log.info("申请退款的tradeNo:{}", tradeNoOfRefund);
                PaymentDO paymentOfRequest = paymentPersistService.getPaymentByTradeNo(tradeNoOfRefund);
                ValidateUtil.requiredNotNull(paymentOfRequest, HeraBizErrorCodeEnum.ORDER_NON_EXIST);
                paymentOfRequest.setRefundAmount(refundDO.getAmount());
                log.info("processRefundedPayment {} -> {}", paymentOfRequest.getStatus(), PayStatus.REFUNDED.getCode());
                paymentOfRequest.setOriStatus(paymentOfRequest.getStatus());
                paymentOfRequest.setStatus(PayStatus.REFUNDED.getCode());
                paymentOfRequest.setRefundTime(new Date());
                paymentPersistService.updateStatus(paymentOfRequest);
                bizMqProducer.sendGatewayRefundMsg(paymentOfRequest, refundDO);
            } else {
                log.warn("refundDO status: {}", refundDO.getStatus());
            }
        } else {
            if (Objects.equals(refundDO.getStatus(), RefundStatus.REFUNDING.getCode())) {
                refundDO.setStatus(RefundStatus.FAIL.getCode());
                refundDO.setOutTraceNo(dto.getRefundId());
                refundDO.setErrorMsg(dto.getResponseMsg());
                boolean updateResult = refundPersistService.update(refundDO);
                AssertUtils.notTrueWithBizExp(updateResult, HeraBizErrorCodeEnum.ILLEGAL_ERROR, "退款更新失败");

                // 支付状态还原为已支付
                paymentDO.setOriStatus(paymentDO.getStatus());
                paymentDO.setStatus(PayStatus.PAID.getCode());
                paymentPersistService.updateStatus(paymentDO);
            } else {
                log.warn("refundDO status: {}", refundDO.getStatus());
            }
        }
    }

}
