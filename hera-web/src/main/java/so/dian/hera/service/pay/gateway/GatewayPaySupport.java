package so.dian.hera.service.pay.gateway;

import com.chargebolt.hera.client.dto.pay.req.OrderPayReq;
import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import so.dian.hera.utils.TradeNoGenerator;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.platform.common.constants.BusinessConstants;

import java.time.ZoneId;
import java.util.Objects;

@Component
public class GatewayPaySupport {

    public static PaymentDO convert(OrderPayReq req) {
        PaymentDO paymentDO = new PaymentDO();
        paymentDO.setOrderNo(req.getOrderNo());
        // zalopay 对单号生成规则有要求，必须是yymmdd(GMT+7)开头，所以时区也不做配置，固定为胡志明市时间
//        if(Objects.equals(req.getChannelId(), ChannelEnum.ZALOMINI_PAY.getChannelNo())){
//            paymentDO.setTradeNo(new DateBuild(ZoneId.of("Asia/Ho_Chi_Minh")).formatter(DateBuild.SIMPLE_SHORT_DATE)
//                    +"_"+TradeNoGenerator.generateTradeNo(PaymentBizTypeEnum.PAY_ORDER, req.getUserId()));
//        }else{
            paymentDO.setTradeNo(TradeNoGenerator.generateTradeNo(PaymentBizTypeEnum.PAY_ORDER, req.getUserId()));
//        }
        paymentDO.setUserId(req.getUserId());
        paymentDO.setBizType(req.getBizType());
        paymentDO.setPayType(req.getPayWay());
        paymentDO.setStatus(PayStatus.INIT.getCode());
        paymentDO.setPayAmount(req.getOrderAmount());
        paymentDO.setCurrency(StringUtils.isBlank(req.getCurrency()) ? BusinessConstants.CURRENCY_CNY : req.getCurrency());
        paymentDO.setReqSystem(req.getReqSystem());
        paymentDO.setRefundAmount(0);
        return paymentDO;
    }
}
