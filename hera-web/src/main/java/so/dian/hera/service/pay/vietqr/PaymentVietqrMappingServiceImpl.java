/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.service.pay.vietqr;

import com.chargebolt.hera.domain.PaymentVietqrMappingDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.hera.dao.rds.hera.PaymentVietqrMappingMapper;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: PaymentVietqrMappingServiceImpl.java, v 1.0 2024-07-08 下午3:31 Exp $
 */
@Slf4j
@Service
public class PaymentVietqrMappingServiceImpl implements PaymentVietqrMappingService {
    private final PaymentVietqrMappingMapper paymentVietqrMappingMapper;

    public PaymentVietqrMappingServiceImpl(ObjectProvider<PaymentVietqrMappingMapper> paymentVietqrMappingMapperProvider){
        this.paymentVietqrMappingMapper= paymentVietqrMappingMapperProvider.getIfUnique();
    }
    @Override
    public Integer saveRecord(PaymentVietqrMappingDO model) {
        Long stamp = System.currentTimeMillis();
        model.setDeleted(0);
        model.setGmtCreate(stamp);
        model.setGmtUpdate(stamp);
        return paymentVietqrMappingMapper.insert(model);
    }

    @Override
    public PaymentVietqrMappingDO getRecord(PaymentVietqrMappingDO model) {
        model.setDeleted(0);
        return paymentVietqrMappingMapper.getRecord(model);
    }
}