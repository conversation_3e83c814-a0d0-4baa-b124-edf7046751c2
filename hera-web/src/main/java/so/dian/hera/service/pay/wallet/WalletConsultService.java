package so.dian.hera.service.pay.wallet;

import java.util.Objects;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alipay.global.api.model.ResultStatusType;
import com.alipay.global.api.model.ams.CustomerBelongsTo;
import com.alipay.global.api.response.ams.pay.AlipayPayResponse;
import com.chargebolt.hera.client.dto.pay.apmconsult.ConsultFreezeApmMoneyRequest;
import com.chargebolt.hera.client.dto.pay.apmconsult.ConsultFreezeResponse;
import com.chargebolt.hera.client.enums.PayMethodEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.domain.notify.ChannelNotifyBody;
import com.chargebolt.hera.domain.sharding.PaymentDO;

import lombok.extern.slf4j.Slf4j;
import so.dian.hera.interceptor.utils.DateUtil;
import so.dian.hera.service.pay.bankcard.BankcardPayService;
import so.dian.hera.service.persist.db.PaymentQueryService;
import so.dian.platform.antom.common.enmus.AlipayResultStatusEnum;
import so.dian.platform.antom.processor.AntomCheckoutHostedProcessor;
import so.dian.platform.antom.processor.AntomProperty;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;

@Slf4j
@Service
public class WalletConsultService {

    @Resource
    private PaymentQueryService paymentQueryService;
    @Resource
    private AntomProperty antomProperty;
    @Resource
    private BankcardPayService bankcardPayService;
    @Resource
    private AntomCheckoutHostedProcessor antomCheckoutHostedProcessor;

    public ConsultFreezeResponse consultFreeze(ConsultFreezeApmMoneyRequest request) {

        // 根据 authState 查询 paymentDO
        PaymentDO paymentDO = paymentQueryService.getByTradeNo(request.getAuthState());
        log.info("paymentDO = {}", JSON.toJSONString(paymentDO));

        String accessToken = null;
        try {
            // 申请支付令牌
            accessToken = antomCheckoutHostedProcessor.applyToken(request.getAuthCode(),request.getCustomerBelongsTo());
        } catch (Exception e) {
            log.error("antom applyToken fail. errorMsg = {}", e.getMessage());
            throw HeraBizException.create(HeraBizErrorCodeEnum.PARAM_ERROR, e.getMessage());
        }

        // 发起钱包余额冻结操作
        AlipayPayResponse freezeResponse = antomCheckoutHostedProcessor.freezeApmBalance(paymentDO, accessToken,
                request.getCustomerBelongsTo());
        if (!Objects.equals(freezeResponse.getResult().getResultCode(), AlipayResultStatusEnum.SUCCESS.name())) {
            log.info("antom 预授权失败,result = {},channelNotifyBody = {}", JSON.toJSONString(freezeResponse.getResult()));
            throw HeraBizException.create(HeraBizErrorCodeEnum.PARAM_ERROR,
                    freezeResponse.getResult().getResultMessage());
        }

        // 业务处理逻辑
        ChannelNotifyBody channelNotifyBody = new ChannelNotifyBody();
        channelNotifyBody.setCurrentTradeNo(freezeResponse.getPaymentRequestId());
        channelNotifyBody.setOuterNo(freezeResponse.getPaymentId());
        channelNotifyBody.setPayway(PaywayEnum.ANTOM_CHECKOUT_CARD);
        if (StringUtils.isNotBlank(freezeResponse.getPaymentTime())) {
            channelNotifyBody.setDate(DateUtil.transferDate(freezeResponse.getPaymentTime()));
        }
        if (Objects.deepEquals(freezeResponse.getResult().getResultStatus(), ResultStatusType.S)) {
            channelNotifyBody.setStatus(PayStatus.CARD_AUTHORIZED);
            Integer toolId = 0;
            if (CustomerBelongsTo.ALIPAY_CN.name().equals(request.getCustomerBelongsTo())) {
                toolId = PayMethodEnum.Alipay.getId();
            } else if (CustomerBelongsTo.ALIPAY_HK.name().equals(request.getCustomerBelongsTo())) {
                toolId = PayMethodEnum.AlipayHK.getId();
            } else if (CustomerBelongsTo.GCASH.name().equals(request.getCustomerBelongsTo())) {
                toolId = PayMethodEnum.GCash.getId();
            }
            channelNotifyBody.setPayMethod(PayMethodEnum.explain(toolId));
            // 更新payment授权结果
            bankcardPayService.handlePreAuthCreationNotify(channelNotifyBody);
            // 将授权结果同步给上有业务 kronos
            bankcardPayService.handlePreAuthCreatedNotify(channelNotifyBody);
            return new ConsultFreezeResponse(paymentDO.getDeviceNo(), paymentDO.getTradeNo());
        } else if (Objects.equals(freezeResponse.getResult().getResultStatus(), ResultStatusType.F)) {
            // 更新支付单状态
            bankcardPayService.handlePreAuthFailedNotify(channelNotifyBody);
            throw HeraBizException.create(HeraBizErrorCodeEnum.PARAM_ERROR,
                    freezeResponse.getResult().getResultMessage());
        } else {
            // 等待通知回调，需要轮询
            return new ConsultFreezeResponse(paymentDO.getDeviceNo(), paymentDO.getTradeNo());

        }
    }

}
