package so.dian.hera.service.persist.db;

import com.chargebolt.hera.domain.ApmTokenDO;
import org.springframework.stereotype.Service;
import so.dian.hera.dao.rds.hera.ApmTokenMapper;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ApmTokenPersistService {

    @Resource
    private ApmTokenMapper apmTokenMapper;

    public void insert(ApmTokenDO apmTokenDO){
        apmTokenMapper.insert(apmTokenDO);
    }

    public void update(ApmTokenDO apmTokenDO){
        apmTokenMapper.update(apmTokenDO);
    }

    public List<ApmTokenDO> findUserToken(Long userId, Integer status){
        return apmTokenMapper.select(userId, status);
    }

    public ApmTokenDO getToken(String token){
        return apmTokenMapper.getByToken(token);
    }
}
