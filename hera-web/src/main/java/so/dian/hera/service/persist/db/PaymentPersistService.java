package so.dian.hera.service.persist.db;

import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import com.chargebolt.hera.domain.PaymentOrderMapPO;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import so.dian.hera.dao.sharding.PaymentMapper;
import so.dian.hera.dao.sharding.PaymentOrderMapMapper;
import javax.annotation.Resource;

@Service
@Slf4j
public class PaymentPersistService {
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentOrderMapMapper paymentOrderMapMapper;

    @Transactional(value = "shardingTransactionManager")
    public void insert(PaymentDO paymentDO) {
        log.info("持久化操作，paymentPO:{}", paymentDO);
        paymentOrderMapMapper.insert(new PaymentOrderMapPO(paymentDO.getOrderNo(), paymentDO.getTradeNo()));
        paymentMapper.insert(paymentDO);
    }

    public void updateStatus(PaymentDO paymentDO) {
        if (paymentDO.getOriStatus() == null) {
            paymentDO.setOriStatus(paymentDO.getStatus());
        }
        int count = paymentMapper.updateStatus(paymentDO);
        validateStatus(count, paymentDO);
//        statusFlowProcessor.statusProcess(paymentPO);
    }

    public boolean updateRefundAmount(PaymentDO paymentDO, Integer oldRefundAmount) {
        try {
            paymentMapper.updateRefundAmount(paymentDO, oldRefundAmount);
        } catch (Exception e) {
            log.error("update refund error.", e);
            return false;
        }
        return true;
    }

    public boolean updateRefundAmountByTradeNo(PaymentDO paymentDO) {
        try {
            paymentMapper.updateRefundAmountByTradeNo(paymentDO);
        } catch (Exception e) {
            log.error("update refund error.", e);
            return false;
        }
        return true;
    }

    public void updateNote(PaymentDO paymentDO) {
        // TODO update 有什么讲究吗
        paymentMapper.updateNote(paymentDO);
    }

    public void updateRefundStatus(PaymentDO paymentDO) {
        int count = paymentMapper.updateRefundStatus(paymentDO);
        validateStatus(count, paymentDO);
//        statusFlowProcessor.statusProcess(paymentPO);
    }

    public void reverseRefund(PaymentDO paymentDO) {
        int count = paymentMapper.reverseRefund(paymentDO);
        validateStatus(count, paymentDO);
//        statusFlowProcessor.statusProcess(paymentPO);
    }

    public void updateNotifyFlag(PaymentDO paymentDO) {
        paymentMapper.updateNotifyFlag(paymentDO);
    }

    public void updateNotifyTag(PaymentDO paymentDO) {
        paymentMapper.updateNotifyTag(paymentDO);
    }


    public PaymentDO getPaymentByTradeNo(String tradeNo) {
        return paymentMapper.selectByTradeNo(tradeNo);
    }

    public PaymentDO getPaymentByBizNo(String bizNo) {
        return byBizNo(bizNo);
    }

    public PaymentDO getPayment(String bizNo, String tradeNo) {
        if (!StringUtils.isEmpty(tradeNo)) {
            return getPaymentByTradeNo(tradeNo);
        } else if (!StringUtils.isEmpty(bizNo)) {
            return byBizNo(bizNo);
        } else {
            throw HeraBizException.create(HeraBizErrorCodeEnum.PARAM_ERROR, "bizNo和tradeNo不可同时为空");
        }
    }

    private PaymentDO byBizNo(String bizNo) {
        String tradeNo = paymentOrderMapMapper.selectTradeNoByOrderNo(bizNo);
        if (StringUtils.isEmpty(tradeNo)) {
            return null;
        }
        return paymentMapper.selectByTradeNo(tradeNo);
    }

    private void validateStatus(int count, PaymentDO paymentDO) {
        if (count <= 0) {
            log.error("订单状态异常，和原始状态不一致，不作处理，paymentPO：{}", paymentDO);
            throw HeraBizException.create(HeraBizErrorCodeEnum.ORDER_STATUS_ERROR);
        }
    }

    /**
     * 根据三方支付网关查询会员购买记录
     *
     * @param payNo
     * @return
     */
    public PaymentDO getPaymentPOByPayNo(String payNo) {
        return paymentMapper.selectByPayNo(payNo);
    }

}
