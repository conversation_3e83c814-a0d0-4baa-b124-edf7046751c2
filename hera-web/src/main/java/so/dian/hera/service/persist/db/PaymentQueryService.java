package so.dian.hera.service.persist.db;

import com.chargebolt.hera.client.dto.pay.PageDTO;
import com.chargebolt.hera.client.dto.pay.basic.req.PaymentQueryReq;
import com.chargebolt.hera.client.dto.pay.payment.PaymentExtInfoResponse;
import com.chargebolt.hera.client.dto.pay.payment.PaymentQueryDTO;
import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import so.dian.hera.dao.rds.drds.DrdsPaymentDAO;
import so.dian.hera.domain.context.PaymentExtCacheContext;
import so.dian.platform.common.configuration.redis.RedisClient;
import so.dian.platform.common.enums.CacheEnum;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * PaymentService
 *
 * <AUTHOR>
 * @desc 支付相关业务
 * @date 17/12/22
 */
@Slf4j
@Component
public class PaymentQueryService {

    @Resource
    private DrdsPaymentDAO drdsPaymentDAO;
    @Resource
    private RedisClient redisClient;
    /**
     * 通过订单号查询支付凭证payment
     * @param queryReq 查询条件
     * @return 支付凭证
     */
    public PaymentDO get(PaymentQueryReq queryReq){
        PaymentDO paymentDO = getByTradeNo(queryReq.getTradeNo());
        if (paymentDO != null) {
            return paymentDO;
        }
        paymentDO = getByOrderNo(queryReq.getBizNo());
        return paymentDO;
    }

    /**
     * 通过交易单号(商户单号)和数据源查询支付凭证payment
     * @param tradeNo 交易单号（商户单号）
     * @return 支付凭证
     */
    public PaymentDO getByTradeNo(String tradeNo){
        if(StringUtils.isEmpty(tradeNo)) {
            return null;
        } else {
            tradeNo = tradeNo.trim();
        }

        return drdsPaymentDAO.getByPaymentTradeNo(tradeNo);
    }

    /**
     * 通过订单号查询支付凭证payment
     * @param orderNo 订单号
     * @return 支付凭证
     */
    public PaymentDO getByOrderNo(String orderNo){
        if(StringUtils.isEmpty(orderNo)) {
            return null;
        } else {
            orderNo = orderNo.trim();
        }

        return drdsPaymentDAO.getByPaymentOrderNo(orderNo);
    }

    /**
     * 通过订单号查询支付凭证列表(所有支付方式、所有支付状态)
     * @param queryDTO 订单号列表
     * @return 支付凭证列表
     */
    public List<PaymentDO> getByQuery(PaymentQueryDTO queryDTO, PageDTO pageInfo) {
        Set<Integer> statusList = toStatusCode(queryDTO.getStatus());
        Integer bizType = getBizType(queryDTO);
        Long userId = queryDTO.getUserId();
        String tradeNo = queryDTO.getTradeNo();
        String orderNo = queryDTO.getOrderNo();

        int page = pageInfo.getPage();
        int pageSize = pageInfo.getPageSize();

        int offset = (page - 1) * pageSize;

        return drdsPaymentDAO.findByPage(statusList, bizType, 0, userId, tradeNo, orderNo, offset, pageSize);
    }

    public int getTotal(PaymentQueryDTO queryDTO) {
        Set<Integer> statusList = toStatusCode(queryDTO.getStatus());
        Integer bizType = getBizType(queryDTO);
        Long userId = queryDTO.getUserId();
        String tradeNo = queryDTO.getTradeNo();
        String orderNo = queryDTO.getOrderNo();

        return drdsPaymentDAO.findTotal(statusList, bizType, 0, userId, tradeNo, orderNo);
    }

//    public PaymentExtCacheContext getPaymentExtCacheContext(String tradeNo) {
//        return redisClient.get(CacheEnum.PAYMENT_EXT_CACHE_INFO.getNs(), tradeNo, PaymentExtCacheContext.class);
//    }

    private Integer getBizType(PaymentQueryDTO queryDTO) {
        PaymentBizTypeEnum bizType = queryDTO.getBizType();
        return bizType == null ? null : bizType.getCode();
    }

    private Set<Integer> toStatusCode(Set<PayStatus> status) {
        if (CollectionUtils.isEmpty(status)) {
            return null;
        }
        return status.stream().map(PayStatus::getCode).collect(Collectors.toSet());
    }

}
