package so.dian.hera.service.persist.db;

import com.chargebolt.hera.domain.PaymentRetryRecord;
import org.springframework.stereotype.Service;
import so.dian.hera.dao.rds.hera.PaymentRetryRecordMapper;

import javax.annotation.Resource;

@Service
public class PaymentRetryRecordService {

    @Resource
    private PaymentRetryRecordMapper paymentRetryRecordMapper;

    public void insert(PaymentRetryRecord model){
        model.setDeleted(0);
        model.setGmtCreate(System.currentTimeMillis());
        model.setGmtUpdate(System.currentTimeMillis());
        paymentRetryRecordMapper.insert(model);
    }

    public void update(PaymentRetryRecord model){
        model.setGmtUpdate(System.currentTimeMillis());
        paymentRetryRecordMapper.update(model);
    }

    public PaymentRetryRecord selectProcessingByTradeNo(String tradeNo){
        return paymentRetryRecordMapper.selectProcessingByTradeNo(tradeNo);
    }

}
