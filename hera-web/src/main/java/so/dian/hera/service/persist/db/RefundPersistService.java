package so.dian.hera.service.persist.db;

import com.chargebolt.hera.client.enums.status.RefundStatus;
import com.chargebolt.hera.domain.RefundDO;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.hera.dao.rds.hera.RefundMapper;
import so.dian.hera.interceptor.basic.RefundResult;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RefundPersistService {

    @Resource
    private RefundMapper refundMapper;

    @Resource
    private PaymentPersistService paymentPersistService;

    public boolean insert(RefundDO refundDO) {
        try {
            return refundMapper.insert(refundDO) == 1;
        } catch (Exception e) {
            log.error("refund insert error.", e);
            return false;
        }
    }

    public boolean update(RefundDO refundDO) {
        try {
            int count = refundMapper.update(refundDO);
            if (count <= 0) {
                return false;
            }
        } catch (Exception e) {
            log.error("refund update error.", e);
            return false;
        }
        return true;
    }

    public RefundDO getRefund(String refundNo) {
        return refundMapper.select(refundNo);
    }

    public RefundDO getRefundByTrade(String tradeNo) {
        return refundMapper.selectLastByTradeNo(tradeNo);
    }

    public RefundDO getRefundByOutTraceNo(String outTradeNo) {
        return refundMapper.selectByOutTraceNo(outTradeNo);
    }

    public RefundDO getRefundByOrder(String orderNo) {
        return refundMapper.selectLastByOrderNo(orderNo);
    }

    public List<RefundDO> getRefundListByOrder(String orderNo) {
        return refundMapper.selectByOrderNo(orderNo);
    }

    public List<RefundDO> getRefundListByTrade(String tradeNo) {
        return refundMapper.selectByTradeNo(tradeNo);
    }

    public boolean success(String tradeNo, Date refundTime) {
        RefundDO refundDO = getRefundByTrade(tradeNo);
        refundDO.setRefundTime(refundTime);
        refundDO.setStatus(RefundStatus.REFUNDED.getCode());
        return update(refundDO);
    }

    public boolean success(RefundDO refundDO, RefundResult result) {
        refundDO.setRefundTime(result.getRefundTime());
        refundDO.setStatus(result.getStatus().getCode());
        return update(refundDO);
    }

    public boolean failed(String tradeNo, String reason) {
        RefundDO refundDO = getRefundByTrade(tradeNo);
        PaymentDO paymentDO = paymentPersistService.getPayment(refundDO.getOrderNo(), refundDO.getTradeNo());
        if (!reverse(paymentDO, refundDO)) {
            return false;
        }
        refundDO.setErrorMsg(reason);
        refundDO.setStatus(RefundStatus.FAIL.getCode());
        return update(refundDO);
    }

    public boolean failed(String tradeNo, RefundDO refundDO, RefundResult result) {
        PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(tradeNo);
        return failed(paymentDO, refundDO, result);
    }

    public boolean failed(PaymentDO paymentDO, RefundDO refundDO, RefundResult result) {
        if (!reverse(paymentDO, refundDO)) {
            return false;
        }
        refundDO.setErrorMsg(result.getMessage());
        refundDO.setStatus(result.getStatus().getCode());
        return update(refundDO);
    }

    /**
     * 退款金额回滚
     *
     * @param paymentDO
     * @param refundDO
     * @return
     */
    private boolean reverse(PaymentDO paymentDO, RefundDO refundDO) {
        Integer oldRefundAmount = paymentDO.getRefundAmount();
        paymentDO.setRefundAmount(oldRefundAmount - refundDO.getAmount());
        paymentDO.setRefundTime(new Date());
        if (!paymentPersistService.updateRefundAmount(paymentDO, oldRefundAmount)) {
            return false;
        }
        return true;
    }
}
