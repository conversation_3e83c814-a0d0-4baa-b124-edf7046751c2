package so.dian.hera.service.persist.redis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.platform.common.configuration.redis.RedisClient;
import so.dian.platform.common.enums.CacheEnum;

import javax.annotation.Resource;
import java.util.Set;

@Component
@Slf4j
public class AutoCancelRedisPersistService {
    @Resource
    private RedisClient redisClient;

    public Set getCancelSet(){
        return redisClient.getSetAll(CacheEnum.PLAT_CANCELING_ORDERS.ns);
    }

    public void addMemberOfCancelSet(String tradeNo){
        if(!redisClient.isMember(CacheEnum.PLAT_CANCELING_ORDERS.ns,tradeNo)){
            redisClient.addOfSet(CacheEnum.PLAT_CANCELING_ORDERS.ns,tradeNo);
        }
    }

    public void removeMemberOfCancelSet(String tradeNo){
        if(redisClient.isMember(CacheEnum.PLAT_CANCELING_ORDERS.ns,tradeNo)){
            redisClient.removeOfSet(CacheEnum.PLAT_CANCELING_ORDERS.ns,tradeNo);
        }
    }

}
