/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.service.refund;

import com.chargebolt.hera.client.dto.pay.refund.req.RefundFeeRequest;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundFeeResponse;
import com.chargebolt.hera.domain.RefundChannelFeeRecord;

import java.util.List;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: RefundChannelFeeRecordService.java, v 0.1 2024-12-05 10:19:51 Exp $
 */

public interface RefundChannelFeeRecordService {
    /**
     * listRecord 查询列表
     *
     * @param model              实体model
     * @return List<RefundChannelFeeRecord>     返回结果
     */
    List<RefundChannelFeeRecord> listRecord(RefundChannelFeeRecord model);

    /**
     * getRecord 查询单条，确保条件查询结果最多返回一条
     *
     * @param model              实体model
     * @return RefundChannelFeeRecord     返回结果
     */
    RefundChannelFeeRecord getRecord(RefundChannelFeeRecord model);

    /**
     * saveRecord 记录保存
     *
     * @param model              实体model
     * @return                   insert条数（单条1）
     */
    int saveRecord(RefundChannelFeeRecord model);

    /**
     * removeRecord 删除记录，逻辑删除，使用update sql更新deleted字段
     * 默认使用model，可调整使用其他自定义字段
     *
     * @param model              实体model
     * @return                   逻辑删除数据条数
     */
    int removeRecord(RefundChannelFeeRecord model);

    /**
     * updateRecord 更新记录，默认以主键作为条件更新
     *
     * @param model              实体model
     * @return                   updateRecord更新数据条数
     */
    int updateRecord(RefundChannelFeeRecord model);


    List<RefundChannelFeeRecord> listRecordByTradeNo(String tradeNo);

    /**
     * 批量保存
     *
     * @param modelList
     * @return
     */
    int saveBatchRecord(List<RefundChannelFeeRecord> modelList);

    int saveRefundChannelFeeRecord(RefundFeeRequest request);

    List<RefundFeeResponse> getRefundChannelFeeRecord(String tradeNo);
}