/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.service.refund;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.chargebolt.hera.client.dto.pay.refund.req.RefundFeeRequest;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundFeeResponse;
import com.chargebolt.hera.domain.RefundChannelFeeRecord;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.hera.dao.rds.hera.RefundChannelFeeRecordMapper;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.mofa3.lang.enums.LogicDeleteEnum;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.DateUtil;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;


/**
 * 工具生成默认有五个方法实现
 * listRecord、getRecord、saveRecord、removeRecord、updateRecord
 *
 * <AUTHOR>
 * @version $Id: RefundChannelFeeRecordServiceImpl.java, v 0.1 2024-12-05 10:19:51 Exp $
 */
@Service
public class RefundChannelFeeRecordServiceImpl implements RefundChannelFeeRecordService {
    @Override
    public List<RefundChannelFeeRecord> listRecord(RefundChannelFeeRecord model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return refundChannelFeeRecordMapper.listRecord(model);
    }

    @Override
    public RefundChannelFeeRecord getRecord(RefundChannelFeeRecord model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        return refundChannelFeeRecordMapper.getRecord(model);
    }

    @Override
    public int saveRecord(RefundChannelFeeRecord model) {
        model.setDeleted(LogicDeleteEnum.FALSE.getDelete());
        Date date = new DateBuild().toDate();
        long timeStampMilli = DateUtil.timeStampMilli();
        model.setCreateTime(date);
        model.setUpdateTime(date);
        model.setGmtCreate(timeStampMilli);
        model.setGmtUpdate(timeStampMilli);
        return refundChannelFeeRecordMapper.saveRecord(model);
    }

    @Override
    public int removeRecord(RefundChannelFeeRecord model) {
        model.setDeleted(LogicDeleteEnum.TRUE.getDelete());
        return refundChannelFeeRecordMapper.removeRecord(model);
    }

    @Override
    public int updateRecord(RefundChannelFeeRecord model) {
        model.setUpdateTime(new DateBuild().toDate());
        model.setGmtUpdate(DateUtil.timeStampMilli());
        return refundChannelFeeRecordMapper.updateRecord(model);
    }

    @Override
    public List<RefundChannelFeeRecord> listRecordByTradeNo(final String tradeNo) {
        RefundChannelFeeRecord model = new RefundChannelFeeRecord();
        model.setPayTradeNo(tradeNo);
        model.setState(1);
        List<RefundChannelFeeRecord> list = listRecord(model);
        if (CollectionUtils.isNotEmpty(list)) {
            return list;
        }
        return Collections.emptyList();
    }

    @Override
    public int saveBatchRecord(List<RefundChannelFeeRecord> modelList) {
        Date date = new DateBuild().toDate();
        long timeStampMilli = DateUtil.timeStampMilli();
        List<RefundChannelFeeRecord> initializedList = modelList.stream()
                .peek(record -> {
                    record.setState(1);
                    record.setDeleted(LogicDeleteEnum.FALSE.getDelete());
                    record.setCreateTime(date);
                    record.setUpdateTime(date);
                    record.setGmtCreate(timeStampMilli);
                    record.setGmtUpdate(timeStampMilli);
                })
                .collect(Collectors.toList());
        return refundChannelFeeRecordMapper.saveBatchRecord(initializedList);
    }

    @Override
    public int saveRefundChannelFeeRecord(final RefundFeeRequest request) {
        PaymentDO paymentOfRequest = paymentPersistService.getPaymentByTradeNo(request.getTradeNo());
        if(Objects.isNull(paymentOfRequest)){
            throw new HeraBizException(HeraBizErrorCodeEnum.ORDER_NON_EXIST.getCode(), "order non exist");
        }
        RefundChannelFeeRecord refundChannelFeeRecord = new RefundChannelFeeRecord();
        refundChannelFeeRecord.setPayTradeNo(paymentOfRequest.getTradeNo());
        refundChannelFeeRecord.setCurrencyCode(paymentOfRequest.getCurrency());
        refundChannelFeeRecord.setAmount(request.getAmount());
        refundChannelFeeRecord.setSourceType(request.getSourceType());
        refundChannelFeeRecord.setNote(request.getNote());
        refundChannelFeeRecord.setUserId(paymentOfRequest.getUserId());
        refundChannelFeeRecord.setAgentId(request.getAgentId());
        refundChannelFeeRecord.setState(1);
        return saveRecord(refundChannelFeeRecord);
    }

    @Override
    public List<RefundFeeResponse> getRefundChannelFeeRecord(final String tradeNo) {
        RefundChannelFeeRecord query= new RefundChannelFeeRecord();
        query.setPayTradeNo(tradeNo);
        List<RefundChannelFeeRecord> list= listRecord(query);
        if(CollectionUtils.isNotEmpty(list)){
            List<RefundFeeResponse> responseList=new ArrayList<>();
            for (RefundChannelFeeRecord refundChannelFeeRecord : list) {
                RefundFeeResponse response = getRefundFeeResponse(refundChannelFeeRecord);
                responseList.add(response);
            }
            return responseList;
        }
        return null;
    }

    @NotNull
    private RefundFeeResponse getRefundFeeResponse(final RefundChannelFeeRecord refundChannelFeeRecord) {
        RefundFeeResponse response= new RefundFeeResponse();
        response.setPayTradeNo(refundChannelFeeRecord.getPayTradeNo());
        response.setAmount(refundChannelFeeRecord.getAmount());
        response.setSourceType(refundChannelFeeRecord.getSourceType());
        response.setNote(refundChannelFeeRecord.getNote());
        response.setUserId(refundChannelFeeRecord.getUserId());
        response.setAgentId(refundChannelFeeRecord.getAgentId());
        response.setGmtCreate(refundChannelFeeRecord.getGmtCreate());
        response.setGmtUpdate(refundChannelFeeRecord.getGmtUpdate());
        return response;
    }


    /**
     * 推荐使用构造器注入
     */
    private final RefundChannelFeeRecordMapper refundChannelFeeRecordMapper;
    private final PaymentPersistService paymentPersistService;
    public RefundChannelFeeRecordServiceImpl(ObjectProvider<RefundChannelFeeRecordMapper> refundChannelFeeRecordMapperProvider,
                                             ObjectProvider<PaymentPersistService> paymentPersistServiceProvider
                                             ) {
        this.refundChannelFeeRecordMapper= refundChannelFeeRecordMapperProvider.getIfUnique();
        this.paymentPersistService= paymentPersistServiceProvider.getIfUnique();
    }
}