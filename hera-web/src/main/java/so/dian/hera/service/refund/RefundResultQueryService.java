/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.service.refund;

import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.domain.PaymentVietqrMappingDO;
import com.chargebolt.hera.domain.RefundDO;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.hera.interceptor.QueryPayProcessor;
import so.dian.hera.interceptor.QueryRefundProcessor;
import so.dian.hera.interceptor.dto.req.RefundQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;
import so.dian.hera.interceptor.dto.rsp.RefundQueryResultDTO;
import so.dian.hera.service.pay.vietqr.PaymentVietqrMappingService;
import so.dian.hera.service.pay.vietqr.PaymentVietqrMappingServiceImpl;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.hera.service.persist.db.RefundPersistService;
import so.dian.hera.service.route.RouteService;
import so.dian.hera.utils.AssertUtils;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;

import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: PayResultQueryService.java, v 1.0 2024-04-17 3:38 PM Exp $
 */
@Slf4j
@Service
public class RefundResultQueryService {
    private final RouteService routeService;

    private final PaymentPersistService paymentPersistService;
    private final PaymentVietqrMappingService paymentVietqrMappingService;

    public RefundResultQueryService(ObjectProvider<RouteService> routeServiceProvider,
                                    ObjectProvider<PaymentPersistService> paymentPersistServiceProvider,
                                    ObjectProvider<PaymentVietqrMappingService> paymentVietqrMappingServiceProvider){
        this.routeService= routeServiceProvider.getIfUnique();
        this.paymentPersistService= paymentPersistServiceProvider.getIfUnique();
        this.paymentVietqrMappingService = paymentVietqrMappingServiceProvider.getIfUnique();
    }
    public RefundQueryResultDTO queryRefundResult(RefundDO refundDO) {
        //渠道解析
        PaywayEnum paywayEnum = routeService.channelAnalysis(refundDO.getRefundType());
        //获取渠道处理器
        QueryRefundProcessor processor = routeService.getQueryRefundProcessor(paywayEnum);
        RefundQueryResultRequest request= new RefundQueryResultRequest();
        request.setRefundNo(refundDO.getRefundNo());
        request.setTradeNo(refundDO.getTradeNo());
        request.setOutTraceNo(refundDO.getOutTraceNo());
        // vietqr
        if(paywayEnum.equals(PaywayEnum.VIETQR_STATIC)){
            // 获取额外需要的参数
            PaymentVietqrMappingDO paymentVietqrMappingDO= new PaymentVietqrMappingDO();
            paymentVietqrMappingDO.setTradeNo(refundDO.getTradeNo());
            paymentVietqrMappingDO= paymentVietqrMappingService.getRecord(paymentVietqrMappingDO);
            if(Objects.isNull(paymentVietqrMappingDO)){
                log.error("VietQR订单不存在：{}", refundDO.getTradeNo());
                throw new HeraBizException(HeraBizErrorCodeEnum.PAYMENT_NOT_EXIST.getCode(), "消息发送未知异常");
            }
            request.setBankAccount(paymentVietqrMappingDO.getBankAccount());
            request.setReferenceNumber(paymentVietqrMappingDO.getReferenceNumber());
        }
        // 印尼支付
        if(Objects.equals(refundDO.getRefundType(),PaywayEnum.MIDTRANS_CHECKOUT_APM.getPayway())){
            PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(refundDO.getTradeNo());
            request.setOutPayNo(paymentDO.getPayNo());
            request.setPayMethod(paymentDO.getPayMethod());
        }
        log.info("API退款结果查询，请求参数：{}", JsonUtil.beanToJson(request));
        return processor.orderRefundQuery(request);
    }
}