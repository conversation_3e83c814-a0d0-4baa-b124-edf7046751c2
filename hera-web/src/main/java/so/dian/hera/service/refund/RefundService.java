package so.dian.hera.service.refund;

import com.chargebolt.hera.client.dto.common.CurrencyExchangeInfo;
import com.chargebolt.hera.client.dto.pay.refund.req.RefundFeeRequest;
import com.chargebolt.hera.client.dto.pay.refund.req.RefundRequest;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundDTO;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundResultDTO;
import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.client.enums.status.RefundStatus;
import com.chargebolt.hera.domain.PaymentVietqrMappingDO;
import com.chargebolt.hera.domain.RefundChannelFeeRecord;
import com.chargebolt.hera.domain.RefundDO;
import com.chargebolt.hera.domain.notify.DelayQueryResultBody;
import com.chargebolt.hera.domain.notify.DelayRefundBody;
import com.chargebolt.hera.domain.notify.RefundNotifyBody;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import so.dian.commons.eden.constant.MqDelayLevelConst;
import so.dian.commons.eden.exception.BizException;
import so.dian.hera.common.OssRefundConverter;
import so.dian.hera.interceptor.RefundProcessor;
import so.dian.hera.interceptor.dto.req.ProcessorRefundRequest;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;
import so.dian.hera.service.pay.PayResultQueryService;
import so.dian.hera.service.pay.vietqr.PaymentVietqrMappingService;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.hera.service.persist.db.RefundPersistService;
import so.dian.hera.service.route.RouteService;
import so.dian.hera.utils.AssertUtils;
import so.dian.hera.utils.TradeNoGenerator;
import so.dian.mofa3.lang.money.CurrencyEnum;
import so.dian.mofa3.lang.money.MultiCurrencyMoney;
import so.dian.mofa3.lang.money.MultiCurrencyMoneyUtil;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.common.constants.BusinessConstants;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.mq.producer.BizMqProducer;
import so.dian.platform.common.mq.producer.SelfMqProducer;
import so.dian.platform.common.utils.ValidateUtil;
import so.dian.platform.zalopay.config.ZaloPayProperties;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.util.*;

import static com.chargebolt.hera.client.enums.PaywayEnum.PRE_AUTH_CAPTURE;

@Slf4j
@Service
public class RefundService {

    @Resource
    private RefundPersistService refundPersistService;
    @Resource
    private PaymentPersistService paymentPersistService;
    @Resource
    private RouteService routeService;
    @Resource
    private SelfMqProducer selfMqProducer;
    @Resource
    private BizMqProducer bizMqProducer;
    @Resource
    private ZaloPayProperties zaloPayProperties;
    @Resource
    private PayResultQueryService payResultQueryService;
    @Resource
    private PaymentVietqrMappingService paymentVietqrMappingService;
    @Resource
    private RefundChannelFeeRecordService refundChannelFeeRecordService;

    public RefundDO getRefund(String refundNo) {
        return refundPersistService.getRefund(refundNo);
    }

    public boolean update(RefundDO refundDO) {
        boolean updateResult = refundPersistService.update(refundDO);
        return updateResult;
    }

    public RefundDTO getLastRefund(String tradeNo, String orderNo) {
        RefundDO refundDO = null;
        if (StringUtils.isNotEmpty(tradeNo)) {
            refundDO = refundPersistService.getRefundByTrade(tradeNo);
        }
        if (refundDO == null && StringUtils.isNotEmpty(orderNo)) {
            refundDO = refundPersistService.getRefundByOrder(orderNo);
        }
        return refundDO == null ? null : OssRefundConverter.convert(refundDO);
    }

    public List<RefundDTO> getRecordList(String tradeNo, String orderNo) {
        List<RefundDO> refundDOS = null;
        if (StringUtils.isNotEmpty(tradeNo)) {
            refundDOS = refundPersistService.getRefundListByTrade(tradeNo);
        }
        if (CollectionUtils.isEmpty(refundDOS) && StringUtils.isNotEmpty(orderNo)) {
            refundDOS = refundPersistService.getRefundListByOrder(orderNo);
        }

        if (CollectionUtils.isEmpty(refundDOS)) {
            return Collections.emptyList();
        }
        return OssRefundConverter.convert(refundDOS);
    }

    /**
     * <a href=
     * "https://confluence.dian.so/pages/viewpage.action?pageId=80831377">看这里</a>
     */
    public RefundResultDTO refund(RefundRequest refundRequest) {
        // 路由真正用于退款的payment
        log.info("退款请求:{}", JsonUtil.beanToJson(refundRequest));
        PaymentDO paymentOfRequest = paymentPersistService.getPaymentByTradeNo(refundRequest.getTradeNo());
        ValidateUtil.requiredNotNull(paymentOfRequest, HeraBizErrorCodeEnum.ORDER_NON_EXIST);
        PaywayEnum paywayEnumOfRequestPayment = PaywayEnum.explain(paymentOfRequest.getPayType());

        // 路由真实退款的支付记录（银行卡预授权模式的预授权支付单）
        PaymentDO paymentOfRefund = routeRefundPayment(paymentOfRequest, paywayEnumOfRequestPayment);
        log.info("真正退款的tradeNo:{}", paymentOfRefund.getTradeNo());
        // 累计退款金额：多次退款时取已退金额+当前申请退款金额
        // todo 预授权和请款都更新退款金额
        String refundNo = TradeNoGenerator.generateRefundNo(paymentOfRefund.getPayType(), paymentOfRefund.getUserId());

        // 构建三方退款请求参数（包含一些前置校验）
        ProcessorRefundRequest processorRefundRequest = buildProcessorRefundRequest(refundRequest, paymentOfRequest,
                paymentOfRefund, refundNo, paywayEnumOfRequestPayment);
        // 先更新支付单&保存退款单（确保三方接口超时不会丢是单据）
        saveAndUpdateRefund(refundRequest, paymentOfRequest, paymentOfRefund, refundNo);
        // 发起三方退款
        PaywayEnum paywayEnumOfRefund = PaywayEnum.explain(paymentOfRefund.getPayType());
        ChannelEnum channelEnum = paywayEnumOfRefund.getChannel();
        RefundProcessor refundProcessor = routeService.getRefundProcessor(channelEnum);
        RefundResultDTO resultDTO = null;
        try {
            resultDTO = refundProcessor.refund(processorRefundRequest);
        } catch (Exception ex) {
            // 具体抛了什么异常就直接往上抛
            log.error("退款异常refund：{}", JsonUtil.beanToJson(processorRefundRequest), ex);
            throw ex;
        }
        if (!resultDTO.isSuccess()) {
            throw BizException.create(HeraBizErrorCodeEnum.REFUND_FAILED);
        }

        // 累计退款金额
        Integer refundAmount = Objects.nonNull(paymentOfRefund.getRefundAmount())
                ? paymentOfRefund.getRefundAmount() + refundRequest.getAmount()
                : refundRequest.getAmount();
        paymentOfRequest.setRefundAmount(refundAmount);
        // 将支付单状态改为退款中并更新退款金额
        processRefundingPayment(paymentOfRequest);
        // 银行卡预授权订单更新退款金额
        if (Objects.equals(paymentOfRequest.getPayType(), PRE_AUTH_CAPTURE.getPayway())) {
            paymentOfRefund.setRefundAmount(refundAmount);
            paymentPersistService.updateRefundAmountByTradeNo(paymentOfRefund);
        }

        // 更新三方退款单据
        updateRefundDO(refundNo, resultDTO);

        log.info("退款请求tradeNo:{} 处理完成", refundRequest.getTradeNo());
        DelayQueryResultBody delayQueryResultBody = new DelayQueryResultBody();
        delayQueryResultBody.setTransNo(refundNo);
        delayQueryResultBody.setType(BusinessConstants.TRANSACTION_REFUND);
        // 实现类 PayRefundQueryHandler
        selfMqProducer.sendSelfQueryDelayQuery(delayQueryResultBody, MqDelayLevelConst.DELAY_5M);
        return resultDTO;
    }

    /**
     * 更新支付单退款金额和状态 & 保存退款单
     *
     * @param refundRequest    退款请求体
     * @param paymentOfRequest 实际请款支付记录
     * @param paymentOfRefund  预授权支付记录
     * @param refundNo         退款申请单号
     */
    @Transactional
    public void saveAndUpdateRefund(RefundRequest refundRequest, PaymentDO paymentOfRequest, PaymentDO paymentOfRefund,
            String refundNo) {

        // 保存退款申请记录
        RefundDO refundDO = new RefundDO();
        refundDO.setOrderNo(paymentOfRequest.getOrderNo());
        refundDO.setTradeNo(paymentOfRequest.getTradeNo());
        refundDO.setRefundNo(refundNo);
        refundDO.setAmount(refundRequest.getAmount());
        refundDO.setCurrency(paymentOfRequest.getCurrency());
        refundDO.setBizType(paymentOfRequest.getBizType());
        // 此处调整为实际付款订单的支付方式（预授权模式中的 预授权支付记录）
        refundDO.setRefundType(paymentOfRefund.getPayType());
        refundDO.setReason(refundRequest.getRefundReason());
        refundDO.setSystem(refundRequest.getSystem());
        refundDO.setStatus(RefundStatus.REFUNDING.getCode());

        // 有收取服务费
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(refundRequest.getRefundFeeRequestList())) {
            List<RefundChannelFeeRecord> refundChannelFeeRecordList = getRefundChannelFeeRecords(refundRequest,
                    paymentOfRequest, refundNo);
            refundChannelFeeRecordService.saveBatchRecord(refundChannelFeeRecordList);
        }
        refundPersistService.insert(refundDO);
    }

    @NotNull
    private List<RefundChannelFeeRecord> getRefundChannelFeeRecords(final RefundRequest refundRequest,
            final PaymentDO paymentOfRequest, final String refundNo) {
        List<RefundChannelFeeRecord> refundChannelFeeRecordList = new ArrayList<>();
        for (RefundFeeRequest refundFeeRequest : refundRequest.getRefundFeeRequestList()) {
            RefundChannelFeeRecord refundChannelFeeRecord = new RefundChannelFeeRecord();
            refundChannelFeeRecord.setPayTradeNo(paymentOfRequest.getTradeNo());
            refundChannelFeeRecord.setRefundNo(refundNo);
            refundChannelFeeRecord.setCurrencyCode(paymentOfRequest.getCurrency());
            refundChannelFeeRecord.setAmount(refundFeeRequest.getAmount());
            refundChannelFeeRecord.setSourceType(refundFeeRequest.getSourceType());
            refundChannelFeeRecord.setNote(refundFeeRequest.getNote());
            refundChannelFeeRecord.setUserId(refundFeeRequest.getUserId());
            refundChannelFeeRecord.setAgentId(refundFeeRequest.getAgentId());
            refundChannelFeeRecordList.add(refundChannelFeeRecord);
        }
        return refundChannelFeeRecordList;
    }

    /**
     * 构建退款请求参数
     *
     * @param refundRequest
     * @param paymentOfRequest
     * @param paymentOfRefund
     * @param refundNo
     * @param paywayEnumOfRequestPayment
     * @return
     */
    private ProcessorRefundRequest buildProcessorRefundRequest(RefundRequest refundRequest, PaymentDO paymentOfRequest,
            PaymentDO paymentOfRefund, String refundNo, PaywayEnum paywayEnumOfRequestPayment) {
        ProcessorRefundRequest processorRefundRequest = new ProcessorRefundRequest();
        processorRefundRequest.setTradeNo(paymentOfRefund.getTradeNo());
        processorRefundRequest.setRefundNo(refundNo);
        // zalomini 退款，transId是支付时候返回的，没有直接存储，所以先查询支付订单信息
        if (PaywayEnum.ZALOPAY_MINI == paywayEnumOfRequestPayment) {
            PayQueryResultDTO payQueryResultDTO = payResultQueryService.queryPayResult(paymentOfRequest);
            processorRefundRequest.setPayNo(payQueryResultDTO.getThirdPayTradeNo());
        } else if (PaywayEnum.VIETQR_STATIC == paywayEnumOfRequestPayment) {
            PaymentVietqrMappingDO paymentVietqrMappingDO = new PaymentVietqrMappingDO();
            paymentVietqrMappingDO.setTradeNo(paymentOfRequest.getTradeNo());
            paymentVietqrMappingDO = paymentVietqrMappingService.getRecord(paymentVietqrMappingDO);
            if (Objects.isNull(paymentVietqrMappingDO)) {
                log.error("无法退款，缺少退款必须参数，tradeNo：{}", paymentOfRequest.getTradeNo());
                ValidateUtil.requiredNotNull(null, HeraBizErrorCodeEnum.PAYMENT_NOT_EXIST);
            }
            processorRefundRequest.setBankAccount(paymentVietqrMappingDO.getBankAccount());
            processorRefundRequest.setReferenceNumber(paymentVietqrMappingDO.getReferenceNumber());
        } else if (PaywayEnum.ZALOPAY == paywayEnumOfRequestPayment) {
            // ZALOPAY押金退款
            // zalopay单号有强制规则要求
            // Merchant must be generate merchant own transaction code when submit refund
            // requirement.
            // Format: yymmdd_appid_xxxxxxxxxx
            // fixme 时区从配置获取
            refundNo = TradeNoGenerator.generateRefundNoToZalopay(zaloPayProperties.getAppId(),
                    paymentOfRefund.getPayType(),
                    paymentOfRefund.getUserId(), ZoneId.of("Asia/Ho_Chi_Minh"));
            processorRefundRequest.setRefundNo(refundNo);
            processorRefundRequest.setPayNo(paymentOfRequest.getPayNo());

        } else if ((PaywayEnum.DEPOSIT.equals(paywayEnumOfRequestPayment)
                && Objects.equals(PaywayEnum.explain(paymentOfRefund.getPayType()).getPayway(),
                        PaywayEnum.ZALOPAY.getPayway()))) {
            // zalopay押金支付的退款，获取到原来支付单退款
            refundNo = TradeNoGenerator.generateRefundNoToZalopay(zaloPayProperties.getAppId(),
                    paymentOfRefund.getPayType(),
                    paymentOfRefund.getUserId(), ZoneId.of("Asia/Ho_Chi_Minh"));
            processorRefundRequest.setRefundNo(refundNo);
            processorRefundRequest.setPayNo(paymentOfRefund.getPayNo());
        } else {
            processorRefundRequest.setPayNo(paymentOfRefund.getPayNo());
        }
        processorRefundRequest.setRefundAmount(refundRequest.getAmount());
        processorRefundRequest.setCurrency(paymentOfRefund.getCurrency());

        // 处理退款原因，如果超过80个字符则截断
        String refundReason = refundRequest.getRefundReason();
        if (StringUtils.isNotEmpty(refundReason) && refundReason.length() > 80) {
            refundReason = refundReason.substring(0, 80);
        }
        processorRefundRequest.setReason(refundReason);

        processorRefundRequest.setPayOriginalAmount(paymentOfRefund.getPayAmount());

        /**
         * 这里有两个逻辑，如果是退押金，那paymentOfRequest和paymentOfRefund是同一个
         * 所以如果paymentOfRequest有currencyExchange，那直接按照里面的配置计算换算后的退款金额
         *
         * 但如果是退订单，那paymentOfRequest上没有，paymentOfRefund上有
         * 按照paymentOfRefund上的配置计算，但退款的换算信息要保存到paymentOfRequest上
         *
         */

        // 先判断paymentOfRefund有没有currencyExchange
        if (paymentOfRefund.getNoteObject().containsKey("currencyExchange")) {
            // 如果有，先计算换算后的退款金额，并替换processorRefundRequest里的对应参数值
            CurrencyExchangeInfo currencyExchangeInfo = paymentOfRefund.getNoteObject().getObject("currencyExchange",
                    CurrencyExchangeInfo.class);

            MultiCurrencyMoney sourceMoney = new MultiCurrencyMoney(0, paymentOfRefund.getCurrency());
            sourceMoney.setCent(refundRequest.getAmount());

            MultiCurrencyMoney targetMoney = MultiCurrencyMoneyUtil.exchangeRate(sourceMoney,
                    CurrencyEnum.getByCurrencyCode(currencyExchangeInfo.getTargetCurrency()),
                    currencyExchangeInfo.getExchangeRate().doubleValue());

            // int targetRefundAmount = (int) (refundRequest.getAmount() *
            // currencyExchangeInfo.getExchangeRate());
            processorRefundRequest.setCurrency(currencyExchangeInfo.getTargetCurrency());
            processorRefundRequest.setRefundAmount(Integer.valueOf(String.valueOf(targetMoney.getCent())));
            processorRefundRequest.setPayOriginalAmount(currencyExchangeInfo.getTargetAmount());
            // 判断换算后的退款金额回写到哪里
            if (paymentOfRefund.getId().equals(paymentOfRequest.getId())) {
                // 相同，退押金
                currencyExchangeInfo.setTargetRefundAmount(Integer.valueOf(String.valueOf(targetMoney.getCent())));
                paymentOfRequest.getNoteObject().put("currencyExchange", currencyExchangeInfo);
            } else {
                // 不相同，退订单，换算信息记到paymentOfRequest上
                CurrencyExchangeInfo orderCurrencyExchangeInfo = new CurrencyExchangeInfo();
                orderCurrencyExchangeInfo.setTargetCurrency(currencyExchangeInfo.getTargetCurrency());
                orderCurrencyExchangeInfo.setExchangeRate(currencyExchangeInfo.getExchangeRate());
                orderCurrencyExchangeInfo.setTargetRefundAmount(Integer.valueOf(String.valueOf(targetMoney.getCent())));
                paymentOfRequest.getNoteObject().put("currencyExchange", orderCurrencyExchangeInfo);
            }
        }
        processorRefundRequest.setPayWay(paymentOfRefund.getPayType());
        processorRefundRequest.setPayMethod(paymentOfRefund.getPayMethod());
        return processorRefundRequest;
    }

    /**
     * 路由真是退款的支付单
     *
     * @param paymentOfRequest
     * @param paywayEnumOfRequestPayment
     * @return
     */
    private PaymentDO routeRefundPayment(PaymentDO paymentOfRequest, PaywayEnum paywayEnumOfRequestPayment) {
        PaymentDO paymentOfRefund = null;
        PaymentBizTypeEnum paymentBizTypeEnum = PaymentBizTypeEnum.explain(paymentOfRequest.getBizType());
        if (PaymentBizTypeEnum.PAY_DEPOSIT == paymentBizTypeEnum) {
            // 退押金payment
            paymentOfRefund = paymentOfRequest;

        } else if (PaymentBizTypeEnum.PAY_ORDER == paymentBizTypeEnum) {
            // 退订单payment
            if (PaywayEnum.DEPOSIT.equals(paywayEnumOfRequestPayment)) {
                // 7 押金
                // 押金支付的订单
                // 押金订单支付订单的payment的payNo记的是支付押金的tradeNo，
                // 退款也是用支付押金的那条payment去退
                // 所以要找到支付押金的payment进行退款
                // 微信小程序的订单，本质上也是用押金支付，所以也是在这里
                String orderPaymentPayNo = paymentOfRequest.getPayNo();
                paymentOfRefund = paymentPersistService.getPaymentByTradeNo(orderPaymentPayNo);
            } else if (PRE_AUTH_CAPTURE.equals(paywayEnumOfRequestPayment)) {
                // 35 预授权
                // 预授权支付的订单
                // 预授权订单支付订单的payment的payNo记的是预授权的tradeNo，
                // 退款也是用预授权的那条payment去退
                // 所以要找到预授权的payment进行退款
                paymentOfRefund = paymentPersistService.getPaymentByTradeNo(paymentOfRequest.getPayNo());
            } else if (PaywayEnum.PINGPONG_ONE_CLICK.equals(paywayEnumOfRequestPayment)) {
                // 54 一键
                // 直接退payment
                paymentOfRefund = paymentOfRequest;
            } else if (PaywayEnum.PINGPONG_CHECKOUT_APM.equals(paywayEnumOfRequestPayment) ||
                    PaywayEnum.ANTOM_CHECKOUT_APM.equals(paywayEnumOfRequestPayment) ||
                    PaywayEnum.MIDTRANS_CHECKOUT_APM.equals(paywayEnumOfRequestPayment)) {
                // 54 一键
                // 直接退payment
                paymentOfRefund = paymentOfRequest;
            } else {
                log.error("没有可以退款的payment for refund. {}", paymentOfRequest.getTradeNo());
                ValidateUtil.requiredNotNull(null, HeraBizErrorCodeEnum.PAYMENT_NOT_EXIST);
            }
        }
        return paymentOfRefund;
    }

    private void updateRefundDO(String refundNo, RefundResultDTO resultDTO) {
        RefundDO refundDO = new RefundDO();
        refundDO.setRefundNo(refundNo);
        refundDO.setOutTraceNo(resultDTO.getRefundPayNo());
        refundPersistService.update(refundDO);
    }

    public boolean handleRefundNotify(RefundNotifyBody refundNotifyBody) {
        log.info("merchantTradeNo:{}", refundNotifyBody.getMerchantTradeNo());
        log.info("outerPayNo:{}", refundNotifyBody.getOuterPayNo());
        log.info("merchantRefundNo:{}", refundNotifyBody.getMerchantRefundNo());
        log.info("outerRefundNo:{}", refundNotifyBody.getOuterRefundNo());
        // 先找refundDO
        RefundDO refundDO = null;
        if (ChannelEnum.WOOSH_PAY.equals(refundNotifyBody.getChannel())
                || ChannelEnum.PINGPONG.equals(refundNotifyBody.getChannel())
                || ChannelEnum.ANTOM.equals(refundNotifyBody.getChannel())
                || ChannelEnum.MIDTRANS_PAY.equals(refundNotifyBody.getChannel())
                || ChannelEnum.AIRWALLEX.equals(refundNotifyBody.getChannel())) {
            refundDO = refundPersistService.getRefundByOutTraceNo(refundNotifyBody.getOuterRefundNo());
        }
        if (refundDO == null) {
            log.warn("没有该笔退款, outerPayNo: {}", refundNotifyBody.getOuterPayNo());
            return false;
        }
        // 退款单状态更新
        if (Objects.equals(refundDO.getStatus(), RefundStatus.REFUNDING.getCode())) {

            // 明确退款失败,目前仅有antom退款有可能会返回失败
            // 2024/11/13 airwallex 也有退款失败通知
            if (Objects.equals(refundNotifyBody.getRefundStatus(), false)) {
                refundDO.setStatus(RefundStatus.FAIL.getCode());
                boolean updateResult = refundPersistService.update(refundDO);
                AssertUtils.notTrueWithBizExp(updateResult, HeraBizErrorCodeEnum.ILLEGAL_ERROR, "退款更新失败");

                // 支付状态还原为已支付
                PaymentDO paymentDO = paymentPersistService.getPaymentByTradeNo(refundDO.getTradeNo());
                ValidateUtil.requiredNotNull(paymentDO, HeraBizErrorCodeEnum.ORDER_NON_EXIST);
                paymentDO.setOriStatus(paymentDO.getStatus());
                paymentDO.setStatus(PayStatus.PAID.getCode());
                paymentPersistService.updateStatus(paymentDO);

                // 发送退款失败消息 krons应用RefundMsgHandler
                bizMqProducer.sendRefundMsg(paymentDO, refundDO);
                return true;
            }

            refundDO.setStatus(RefundStatus.REFUNDED.getCode());
            refundDO.setRefundTime(refundNotifyBody.getDate());
            refundPersistService.update(refundDO);
        }
        String requestRefundTradeNo = refundDO.getTradeNo();
        log.info("申请退款的tradeNo:{}", requestRefundTradeNo);
        // paymentOfRefund 是真正用于退款的payment，
        // 预授权的payment
        // 押金的payment
        PaymentDO paymentOfRequest = paymentPersistService.getPaymentByTradeNo(requestRefundTradeNo);
        ValidateUtil.requiredNotNull(paymentOfRequest, HeraBizErrorCodeEnum.ORDER_NON_EXIST);
        processRefundedPayment(paymentOfRequest);
        // 发送退款成功消息 krons应用RefundMsgHandler
        bizMqProducer.sendRefundMsg(paymentOfRequest, refundDO);
        return true;
    }

    public void asyncRefundNotify(RefundNotifyBody refundNotifyBody) {
        selfMqProducer.sendSelfRefundMsg(refundNotifyBody);
    }

    public void handleRefundDelay(RefundRequest refundRequest) {
        DelayRefundBody body = new DelayRefundBody();
        body.setTradeNo(refundRequest.getTradeNo());
        body.setRefundAmount(refundRequest.getAmount().longValue());
        body.setRefundReason(refundRequest.getRefundReason());
        body.setSystem(refundRequest.getSystem());
        selfMqProducer.sendSelfRefundDelay(body);
    }

    private void processRefundingPayment(PaymentDO paymentDO) {
        log.info("processRefundingPayment {} -> {}", paymentDO.getStatus(), PayStatus.REFUNDING.getCode());
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.REFUNDING.getCode());
        paymentPersistService.updateStatus(paymentDO);
    }

    private void processRefundedPayment(PaymentDO paymentDO) {
        log.info("processRefundedPayment {} -> {}", paymentDO.getStatus(), PayStatus.REFUNDED.getCode());
        paymentDO.setOriStatus(paymentDO.getStatus());
        paymentDO.setStatus(PayStatus.REFUNDED.getCode());
        paymentDO.setRefundTime(new Date());
        paymentPersistService.updateStatus(paymentDO);
    }
}
