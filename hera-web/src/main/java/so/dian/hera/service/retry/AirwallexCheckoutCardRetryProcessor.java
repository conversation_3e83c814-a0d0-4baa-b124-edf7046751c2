package so.dian.hera.service.retry;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.pay.checkout.req.PreAuthCaptureRequest;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PreAuthCaptureResultDTO;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.client.enums.status.RetryStatus;
import com.chargebolt.hera.domain.PaymentRetryRecord;
import com.chargebolt.hera.domain.notify.ChannelNotifyBody;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.PaymentCaptureRetryProcessor;
import so.dian.hera.interceptor.dto.req.PayQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.hera.service.persist.db.PaymentRetryRecordService;
import so.dian.platform.airwallex.processor.AirwallexCheckoutProcessor;
import so.dian.platform.common.mq.producer.SelfMqProducer;
import so.dian.platform.pingpong.processor.PingpongCheckoutHostedProcessor;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@SuppressWarnings("all")
public class AirwallexCheckoutCardRetryProcessor implements PaymentCaptureRetryProcessor {

    private final AirwallexCheckoutProcessor airwallexCheckoutProcessor;
    private final PaymentRetryRecordService paymentRetryRecordService;
    private final SelfMqProducer selfMqProducer;
    private final PaymentPersistService paymentPersistService;

    public AirwallexCheckoutCardRetryProcessor(ObjectProvider<AirwallexCheckoutProcessor> AirwallexCheckoutProcessorProvider,
                                              ObjectProvider<PaymentRetryRecordService> paymentRetryRecordServiceProvider,
                                              ObjectProvider<PingpongCheckoutHostedProcessor> pingpongCheckoutHostedProcessorProvider,
                                              ObjectProvider<SelfMqProducer> selfMqProducerProvider,
                                              ObjectProvider<PaymentPersistService> paymentPersistServiceProvider){
        this.airwallexCheckoutProcessor= AirwallexCheckoutProcessorProvider.getIfUnique();
        this.paymentRetryRecordService= paymentRetryRecordServiceProvider.getIfUnique();
        this.selfMqProducer= selfMqProducerProvider.getIfUnique();
        this.paymentPersistService= paymentPersistServiceProvider.getIfUnique();
    }

    @Override
    public RetryStatus handleCaptureRetry(PaymentRetryRecord retryRecord, PaymentDO paymentDO) {
        log.info("空中云汇请款补偿任务开始");
        // 查询预授权的支付记录
        PaymentDO preAuthPaymentDO = paymentPersistService.getPaymentByTradeNo(paymentDO.getPayNo());
        if(Objects.isNull(preAuthPaymentDO)){
            log.error("空中云汇请款补偿任务#未查询到预授权订单 tradeNo: {},payNo: {}",paymentDO.getTradeNo(),paymentDO.getPayNo());
            return RetryStatus.FAILED;
        }

        // 先发起扣款，三方有api幂等
        retryCapture(preAuthPaymentDO, paymentDO);

        // 查询空中云汇的订单状态
        PayQueryResultRequest request = new PayQueryResultRequest();
        request.setTradeNo(retryRecord.getTradeNo());
        request.setPayNo(retryRecord.getPayNo());
        RetryStatus retryStatus= RetryStatus.PROCESSING;
        PayQueryResultDTO resultDTO = airwallexCheckoutProcessor.orderPayQuery(request);
        log.info("空中云汇请款补偿任务#查询订单请款结果 resultDTO: {}", JSON.toJSONString(resultDTO));
        if(Objects.equals(resultDTO.getStatus(), PayStatus.PAID.getCode())){
            retryStatus= RetryStatus.SUCCESSFUL;
        }else if(Objects.equals(resultDTO.getStatus(), PayStatus.FAIL.getCode())){
            retryStatus= RetryStatus.FAILED;
        }
        retryRecord.setState(retryStatus.getCode());
        if(Objects.equals(retryStatus, RetryStatus.SUCCESSFUL)){
            paymentRetryRecordService.update(retryRecord);
            send(resultDTO, preAuthPaymentDO);
        }else if(Objects.equals(retryStatus, RetryStatus.FAILED)){
            // todo 正常情况不会走到这个流程，可作为关注点，后续遇到此问题可增加告警机制
            log.error("空中云汇请款订单扣款失败，请及时核查 tradeNo: {},payNo: {}",retryRecord.getTradeNo(),retryRecord.getPayNo());
            retryRecord.setDescription(resultDTO.getResponseMsg());
            paymentRetryRecordService.update(retryRecord);
        }
        log.info("空中云汇请款补偿任务结束");
        return retryStatus;
    }

    private void retryCapture(PaymentDO preAuthPaymentDO, PaymentDO paymentDO){
        PreAuthCaptureRequest captureRequest= new PreAuthCaptureRequest();
        captureRequest.setCurrency(paymentDO.getCurrency());
        captureRequest.setOrderAmount(paymentDO.getPayAmount());
        try{
            PreAuthCaptureResultDTO resultDTO = airwallexCheckoutProcessor.doCapture(captureRequest, preAuthPaymentDO.getPayNo(), paymentDO);
            log.info("空中云汇补偿任务再次请款 resultDTO: {},payNo: {}",JSON.toJSONString(resultDTO),preAuthPaymentDO.getPayNo());
        }catch (Exception e){
            log.error("空中云汇补偿任务请款失败", e);
        }
    }

    private void send(PayQueryResultDTO resultDTO, PaymentDO preAuthPaymentDO){
        ChannelNotifyBody channelNotifyBody = new ChannelNotifyBody();
        channelNotifyBody.setPayway(PaywayEnum.AIRWALLEX_CHECKOUT_CARD);
        channelNotifyBody.setRefTradeNo(preAuthPaymentDO.getTradeNo());
        // AirwallexCaptureNotifyHandler
        selfMqProducer.sendSelfCaptureMsg(channelNotifyBody);
    }

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.AIRWALLEX_CHECKOUT_CARD);
    }

}
