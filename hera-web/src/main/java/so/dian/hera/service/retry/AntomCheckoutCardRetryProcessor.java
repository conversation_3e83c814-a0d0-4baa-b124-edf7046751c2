package so.dian.hera.service.retry;

import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.RetryStatus;
import com.chargebolt.hera.domain.PaymentRetryRecord;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.PaymentCaptureRetryProcessor;
import java.util.List;

/**
 * <AUTHOR>
 * 暂不实现
 */
@Slf4j
@Component
public class AntomCheckoutCardRetryProcessor implements PaymentCaptureRetryProcessor {

    @Override
    public RetryStatus handleCaptureRetry(PaymentRetryRecord retryRecord, PaymentDO paymentDO) {

        // 1、查询本地请款结果

        // 2、查询三方请款结果

        // 3、重新发起请款

        // 4、更新请款记录
        return RetryStatus.SUCCESSFUL;
    }

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.ANTOM_CHECKOUT_CARD);
    }

}
