/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.service.retry;

import com.chargebolt.hera.client.dto.pay.apmtoken.req.ApmTokenPayRequest;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.client.enums.status.RetryStatus;
import com.chargebolt.hera.domain.ApmTokenDO;
import com.chargebolt.hera.domain.PaymentRetryRecord;
import com.chargebolt.hera.domain.notify.ChannelNotifyBody;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import so.dian.hera.interceptor.PaymentCaptureRetryProcessor;
import so.dian.hera.interceptor.dto.req.PayQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;
import so.dian.hera.service.pay.apm.ApmPayService;
import so.dian.hera.service.persist.db.ApmTokenPersistService;
import so.dian.hera.service.persist.db.PaymentRetryRecordService;
import so.dian.platform.common.mq.producer.SelfMqProducer;
import so.dian.platform.common.utils.IPUtils;
import so.dian.platform.common.utils.MoneyUtil;
import so.dian.platform.pingpong.processor.PingPongApmPayResultProcessor;
import so.dian.platform.pingpong.processor.PingpongApmTokenProcessor;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * pingpong一键支付重试处理
 *
 * <AUTHOR>
 * @version: PingpongApmRetryProcessor.java, v 1.0 2024-04-30 10:03 AM Exp $
 */
@Slf4j
@Component
public class PingpongApmRetryProcessor implements PaymentCaptureRetryProcessor {


    private final PingPongApmPayResultProcessor pingPongApmPayResultProcessor;
    private final PaymentRetryRecordService paymentRetryRecordService;
    private final PingpongApmTokenProcessor pingpongApmTokenProcessor;
    private final ApmTokenPersistService apmTokenPersistService;
    private final SelfMqProducer selfMqProducer;
    public PingpongApmRetryProcessor(ObjectProvider<PingPongApmPayResultProcessor> pingPongApmPayResultProcessorProvider,
                                     ObjectProvider<PaymentRetryRecordService> paymentRetryRecordServiceProvider,
                                     ObjectProvider<PingpongApmTokenProcessor> pingpongApmTokenProcessorProvider,
                                     ObjectProvider<ApmTokenPersistService> apmTokenPersistServiceProvider,
                                     ObjectProvider<SelfMqProducer> selfMqProducerProvider){
        this.pingPongApmPayResultProcessor= pingPongApmPayResultProcessorProvider.getIfUnique();
        this.paymentRetryRecordService= paymentRetryRecordServiceProvider.getIfUnique();
        this.pingpongApmTokenProcessor= pingpongApmTokenProcessorProvider.getIfUnique();
        this.apmTokenPersistService= apmTokenPersistServiceProvider.getIfUnique();
        this.selfMqProducer= selfMqProducerProvider.getIfUnique();
    }
    @Override
    public RetryStatus handleCaptureRetry(PaymentRetryRecord retryRecord, PaymentDO paymentDO) {
        retryCapture(paymentDO);
        PayQueryResultRequest request= new PayQueryResultRequest();
        request.setTradeNo(retryRecord.getTradeNo());
        request.setPayNo(retryRecord.getPayNo());
        RetryStatus retryStatus= RetryStatus.PROCESSING;
        PayQueryResultDTO resultDTO= pingPongApmPayResultProcessor.orderPayQuery(request);
        if(Objects.equals(resultDTO.getStatus(), PayStatus.PAID.getCode())){
            retryStatus= RetryStatus.SUCCESSFUL;
        }else if(Objects.equals(resultDTO.getStatus(), PayStatus.FAIL.getCode())){
            retryStatus= RetryStatus.FAILED;
        }

        //
        retryRecord.setState(retryStatus.getCode());
        if(Objects.equals(retryStatus, RetryStatus.SUCCESSFUL)){
            paymentRetryRecordService.update(retryRecord);
            // 发送消息
            send(resultDTO);
        }else if(Objects.equals(retryStatus, RetryStatus.FAILED)){
            retryRecord.setDescription(resultDTO.getResponseMsg());
            paymentRetryRecordService.update(retryRecord);
            // 发送消息
            send(resultDTO);
        }
        return retryStatus;
    }
    private void send(PayQueryResultDTO resultDTO){
        // /notify/pingpong/ap
//        apmPayService.asyncPaidNotify(bankcardNotifyBody);
        ChannelNotifyBody channelNotifyBody = new ChannelNotifyBody();
        channelNotifyBody.setPayway(PaywayEnum.PINGPONG_CHECKOUT_APM);
        channelNotifyBody.setCurrentTradeNo(resultDTO.getTradeNo());
        channelNotifyBody.setOuterNo(resultDTO.getPayNo());
        if(Objects.nonNull(resultDTO.getPayTime())){
            channelNotifyBody.setDate(resultDTO.getPayTime());
        }else{
            channelNotifyBody.setDate(new Date());
        }
        channelNotifyBody.setStatus(PayStatus.explain(resultDTO.getStatus()));
        selfMqProducer.sendSelfApmPaidMsg(channelNotifyBody);
    }

    private void retryCapture(PaymentDO paymentDO){
        List<ApmTokenDO> apmTokenDOList = apmTokenPersistService.findUserToken(paymentDO.getUserId(), ApmTokenDO.STATUS_SUCCESS);
        if(CollectionUtils.isEmpty(apmTokenDOList)){
            return;
        }
        ApmTokenDO apmTokenDO = apmTokenDOList.get(0);
        ApmTokenPayRequest request= new ApmTokenPayRequest();
        request.setUserId(paymentDO.getUserId());
        request.setCurrency(paymentDO.getCurrency());
        request.setIp(IPUtils.getLocalIP());

        pingpongApmTokenProcessor.pay(request, apmTokenDO, paymentDO);

    }
    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.PINGPONG_ONE_CLICK);
    }
}