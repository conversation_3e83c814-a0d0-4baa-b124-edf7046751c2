/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.service.retry;

import com.chargebolt.hera.client.dto.pay.checkout.req.PreAuthCaptureRequest;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.client.enums.status.RetryStatus;
import com.chargebolt.hera.domain.PaymentRetryRecord;
import com.chargebolt.hera.domain.notify.ChannelNotifyBody;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.PaymentCaptureRetryProcessor;
import so.dian.hera.interceptor.dto.req.PayQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;
import so.dian.hera.service.persist.db.PaymentPersistService;
import so.dian.hera.service.persist.db.PaymentRetryRecordService;
import so.dian.platform.common.mq.producer.SelfMqProducer;
import so.dian.platform.pingpong.processor.PingPongCheckoutPayResultProcessor;
import so.dian.platform.pingpong.processor.PingpongCheckoutHostedProcessor;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * pingpong预授权重试处理
 *
 * <AUTHOR>
 * @version: PingpongCheckoutCardRetryProcessor.java, v 1.0 2024-04-30 10:03 AM Exp $
 */
@Slf4j
@Component
public class PingpongCheckoutCardRetryProcessor implements PaymentCaptureRetryProcessor {


    private final PingPongCheckoutPayResultProcessor pingPongCheckoutPayResultProcessor;
    private final PaymentRetryRecordService paymentRetryRecordService;
    private final PingpongCheckoutHostedProcessor pingpongCheckoutHostedProcessor;
    private final SelfMqProducer selfMqProducer;
    private final PaymentPersistService paymentPersistService;
    public PingpongCheckoutCardRetryProcessor(ObjectProvider<PingPongCheckoutPayResultProcessor> pingpongCheckoutPayResultProcessorProvider,
                                              ObjectProvider<PaymentRetryRecordService> paymentRetryRecordServiceProvider,
                                              ObjectProvider<PingpongCheckoutHostedProcessor> pingpongCheckoutHostedProcessorProvider,
                                              ObjectProvider<SelfMqProducer> selfMqProducerProvider,
                                              ObjectProvider<PaymentPersistService> paymentPersistServiceProvider){
        this.pingPongCheckoutPayResultProcessor= pingpongCheckoutPayResultProcessorProvider.getIfUnique();
        this.paymentRetryRecordService= paymentRetryRecordServiceProvider.getIfUnique();
        this.pingpongCheckoutHostedProcessor= pingpongCheckoutHostedProcessorProvider.getIfUnique();
        this.selfMqProducer= selfMqProducerProvider.getIfUnique();
        this.paymentPersistService= paymentPersistServiceProvider.getIfUnique();
    }
    @Override
    public RetryStatus handleCaptureRetry(PaymentRetryRecord retryRecord,PaymentDO paymentDO) {
        // 先发起扣款，api幂等
        retryCapture(retryRecord, paymentDO);

        PayQueryResultRequest request= new PayQueryResultRequest();
        request.setTradeNo(retryRecord.getTradeNo());
        request.setPayNo(retryRecord.getPayNo());
        RetryStatus retryStatus= RetryStatus.PROCESSING;
        PayQueryResultDTO resultDTO= pingPongCheckoutPayResultProcessor.orderPayQuery(request);
        if(Objects.equals(resultDTO.getStatus(), PayStatus.PAID.getCode())){
            retryStatus= RetryStatus.SUCCESSFUL;
        }else if(Objects.equals(resultDTO.getStatus(), PayStatus.FAIL.getCode())){
            retryStatus= RetryStatus.FAILED;
        }
        //
        retryRecord.setState(retryStatus.getCode());
        if(Objects.equals(retryStatus, RetryStatus.SUCCESSFUL)){
            paymentRetryRecordService.update(retryRecord);
            // 发送消息
            send(resultDTO, retryRecord);
        }else if(Objects.equals(retryStatus, RetryStatus.FAILED)){
            retryRecord.setDescription(resultDTO.getResponseMsg());
            paymentRetryRecordService.update(retryRecord);
            // 发送消息
            send(resultDTO, retryRecord);
        }
        return retryStatus;
    }

    private void send(PayQueryResultDTO resultDTO, PaymentRetryRecord retryRecord){
        ChannelNotifyBody channelNotifyBody = new ChannelNotifyBody();
        channelNotifyBody.setPayway(PaywayEnum.PINGPONG_CHECKOUT_CARD);
        channelNotifyBody.setStatus(PayStatus.explain(resultDTO.getStatus()));
        channelNotifyBody.setOuterNo(resultDTO.getPayNo());
        if(Objects.nonNull(resultDTO.getPayTime())){
            channelNotifyBody.setDate(resultDTO.getPayTime());
        }else{
            channelNotifyBody.setDate(new Date());
        }
        channelNotifyBody.setRefTradeNo(retryRecord.getPayNo());
        channelNotifyBody.setCurrentTradeNo(retryRecord.getTradeNo());
        selfMqProducer.sendSelfCaptureMsg(channelNotifyBody);
    }
    private void retryCapture(PaymentRetryRecord paymentRetryRecord, PaymentDO capturePaymentDO){
        PreAuthCaptureRequest preAuthCaptureRequest= new PreAuthCaptureRequest();
        preAuthCaptureRequest.setOrderAmount(capturePaymentDO.getPayAmount());
        preAuthCaptureRequest.setCurrency(capturePaymentDO.getCurrency());
        preAuthCaptureRequest.setPreAuthTradeNo(paymentRetryRecord.getPayNo());
        pingpongCheckoutHostedProcessor.doCapture(preAuthCaptureRequest, null, capturePaymentDO);
    }
    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.PINGPONG_CHECKOUT_CARD);
    }
}