/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hera.service.retry;

import com.chargebolt.hera.client.dto.pay.checkout.req.PreAuthCaptureRequest;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.client.enums.status.RetryStatus;
import com.chargebolt.hera.domain.PaymentRetryRecord;
import com.chargebolt.hera.domain.notify.ChannelNotifyBody;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.PaymentCaptureRetryProcessor;
import so.dian.hera.interceptor.dto.req.PayQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;
import so.dian.hera.service.persist.db.PaymentRetryRecordService;
import so.dian.platform.common.configuration.redis.RedisClient;
import so.dian.platform.common.enums.CacheEnum;
import so.dian.platform.common.mq.producer.SelfMqProducer;
import so.dian.platform.wooshpay.common.enums.WooshPayEventEnum;
import so.dian.platform.wooshpay.common.enums.WooshPayStatusEnum;
import so.dian.platform.wooshpay.processor.WooshPayPayResultProcessor;
import so.dian.platform.wooshpay.processor.WooshPayProcessor;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * wooshpay预授权重试处理
 *
 * <AUTHOR>
 * @version: WooshpayCheckoutCardRetryProcessor.java, v 1.0 2024-04-30 10:03 AM Exp $
 */
@Slf4j
@Component
public class WooshpayCheckoutCardRetryProcessor implements PaymentCaptureRetryProcessor {


    private final WooshPayPayResultProcessor wooshPayPayResultProcessor;
    private final PaymentRetryRecordService paymentRetryRecordService;
    private final WooshPayProcessor wooshPayProcessor;
    private final SelfMqProducer selfMqProducer;
    private final RedisClient redisClient;
    public WooshpayCheckoutCardRetryProcessor(ObjectProvider<WooshPayPayResultProcessor> wooshPayPayResultProcessorProvider,
                                              ObjectProvider<PaymentRetryRecordService> paymentRetryRecordServiceProvider,
                                              ObjectProvider<WooshPayProcessor> wooshPayProcessorProvider,
                                              ObjectProvider<RedisClient> redisClientProvider,
                                              ObjectProvider<SelfMqProducer> selfMqProducerProvider){
        this.wooshPayPayResultProcessor= wooshPayPayResultProcessorProvider.getIfUnique();
        this.paymentRetryRecordService= paymentRetryRecordServiceProvider.getIfUnique();
        this.wooshPayProcessor= wooshPayProcessorProvider.getIfUnique();
        this.redisClient= redisClientProvider.getIfUnique();
        this.selfMqProducer= selfMqProducerProvider.getIfUnique();
    }
    @Override
    public RetryStatus handleCaptureRetry(PaymentRetryRecord retryRecord, PaymentDO paymentDO) {
        retryCapture(retryRecord, paymentDO);
        PayQueryResultRequest request= new PayQueryResultRequest();
        request.setTradeNo(retryRecord.getTradeNo());
        request.setPayNo(retryRecord.getPayNo());
        RetryStatus retryStatus= RetryStatus.PROCESSING;
        PayQueryResultDTO resultDTO= wooshPayPayResultProcessor.orderPayQuery(request);
        if(Objects.equals(resultDTO.getStatus(), PayStatus.PAID.getCode())){
            retryStatus= RetryStatus.SUCCESSFUL;
        }else if(Objects.equals(resultDTO.getStatus(), PayStatus.FAIL.getCode())){
            retryStatus= RetryStatus.FAILED;
        }
        //
        retryRecord.setState(retryStatus.getCode());
        if(Objects.equals(retryStatus, RetryStatus.SUCCESSFUL)){
            paymentRetryRecordService.update(retryRecord);
            // 发送消息
            send(resultDTO, paymentDO,retryRecord);
        }else if(Objects.equals(retryStatus, RetryStatus.FAILED)){
            retryRecord.setDescription(resultDTO.getResponseMsg());
            paymentRetryRecordService.update(retryRecord);
            // 发送消息
            send(resultDTO, paymentDO,retryRecord);
        }
        return retryStatus;
    }

    private void send(PayQueryResultDTO resultDTO, PaymentDO paymentDO,PaymentRetryRecord retryRecord){
        ChannelNotifyBody channelNotifyBody = new ChannelNotifyBody();
        // 授权时的tradeNo
        channelNotifyBody.setCurrentTradeNo(paymentDO.getPayNo());
        // 扣款发起的tradeNo
        channelNotifyBody.setOuterNo(retryRecord.getTradeNo());
        channelNotifyBody.setPayway(PaywayEnum.WOOSHPAY_CARD);
        channelNotifyBody.setStatus(PayStatus.explain(resultDTO.getStatus()));
        if(Objects.nonNull(resultDTO.getPayTime())){
            channelNotifyBody.setDate(resultDTO.getPayTime());
        }else{
            channelNotifyBody.setDate(new Date());
        }
        selfMqProducer.sendSelfCaptureMsg(channelNotifyBody);
        redisClient.set(CacheEnum.BANKCARD_NOTIFY.getNs(), "capture_trade_no-" + retryRecord.getTradeNo(), paymentDO.getPayNo(), 86400);

    }

    private void retryCapture(PaymentRetryRecord paymentRetryRecord, PaymentDO paymentDO){
        PreAuthCaptureRequest captureRequest= new PreAuthCaptureRequest();
        captureRequest.setCurrency(paymentDO.getCurrency());
        captureRequest.setOrderAmount(paymentDO.getPayAmount());
        try{
            wooshPayProcessor.doCapture(captureRequest, paymentRetryRecord.getPayNo(), paymentDO);
        }catch (Exception e){
            log.error("wooshpay capture error", e);
        }
    }
    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.WOOSHPAY_CARD);
    }
}