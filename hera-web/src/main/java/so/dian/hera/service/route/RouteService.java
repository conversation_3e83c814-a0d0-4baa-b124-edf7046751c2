package so.dian.hera.service.route;

import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import so.dian.hera.common.enums.AccountPayTypeEnum;
import so.dian.hera.interceptor.*;
import so.dian.hera.interceptor.basic.Refund0000000Processor;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class RouteService {

    @Autowired
    private ApplicationContext applicationContext;

    private Map<PaywayEnum, CheckoutPayProcessor> bankcardPayProcessorMap = Maps.newHashMap();
    private Map<PaywayEnum, PayOrderProcessor> payOrderProcessorMap = Maps.newHashMap();
    private Map<PaywayEnum, TokenPayProcessor> authorizationPayProcessorMap = Maps.newHashMap();

    private Map<ChannelEnum, RefundProcessor> refundProcessorMap = Maps.newHashMap();
    private Map<PaywayEnum, QueryPayProcessor> queryPayProcessorMap = Maps.newHashMap();
    private Map<PaywayEnum, QueryRefundProcessor> queryRefundProcessorMap = Maps.newHashMap();
    private Map<PaywayEnum, PaymentCaptureRetryProcessor> captureRetryProcessorMap = Maps.newHashMap();
    private Map<PaywayEnum, CancelCheckProcessor> cancelCheckProcessorMap = Maps.newHashMap();

    @PostConstruct
    public void init() {
        Map<String, CheckoutPayProcessor> map0 = applicationContext.getBeansOfType(CheckoutPayProcessor.class);
        for (Map.Entry<String, CheckoutPayProcessor> entry : map0.entrySet()) {
            List<PaywayEnum> paywayEnumList = entry.getValue().getPayway();
            for (PaywayEnum paywayEnum : paywayEnumList) {
                bankcardPayProcessorMap.put(paywayEnum, entry.getValue());
            }
        }

        Map<String, PayOrderProcessor> map1 = applicationContext.getBeansOfType(PayOrderProcessor.class);
        for (Map.Entry<String, PayOrderProcessor> entry : map1.entrySet()) {
            List<PaywayEnum> paywayEnumList = entry.getValue().getPayway();
            for (PaywayEnum paywayEnum : paywayEnumList) {
                payOrderProcessorMap.put(paywayEnum, entry.getValue());
            }
        }

        Map<String, TokenPayProcessor> map2 = applicationContext.getBeansOfType(TokenPayProcessor.class);
        for (Map.Entry<String, TokenPayProcessor> entry : map2.entrySet()) {
            List<PaywayEnum> paywayEnumList = entry.getValue().getPayway();
            for (PaywayEnum paywayEnum : paywayEnumList) {
                authorizationPayProcessorMap.put(paywayEnum, entry.getValue());
            }
        }

        Map<String, RefundProcessor> map3 = applicationContext.getBeansOfType(RefundProcessor.class);
        for (Map.Entry<String, RefundProcessor> entry : map3.entrySet()) {
            ChannelEnum channelEnum = entry.getValue().getRefundChannel();
            refundProcessorMap.put(channelEnum, entry.getValue());
        }

        Map<String, QueryPayProcessor> map4 = applicationContext.getBeansOfType(QueryPayProcessor.class);
        for (Map.Entry<String, QueryPayProcessor> entry : map4.entrySet()) {
            List<PaywayEnum> paywayEnumList = entry.getValue().getPayway();
            for (PaywayEnum paywayEnum : paywayEnumList) {
                queryPayProcessorMap.put(paywayEnum, entry.getValue());
            }
        }

        Map<String, QueryRefundProcessor> map5 = applicationContext.getBeansOfType(QueryRefundProcessor.class);
        for (Map.Entry<String, QueryRefundProcessor> entry : map5.entrySet()) {
            List<PaywayEnum> paywayEnumList = entry.getValue().getPayway();
            for (PaywayEnum paywayEnum : paywayEnumList) {
                queryRefundProcessorMap.put(paywayEnum, entry.getValue());
            }
        }

        Map<String, PaymentCaptureRetryProcessor> map6 = applicationContext.getBeansOfType(PaymentCaptureRetryProcessor.class);
        for (Map.Entry<String, PaymentCaptureRetryProcessor> entry : map6.entrySet()) {
            List<PaywayEnum> paywayEnumList = entry.getValue().getPayway();
            for (PaywayEnum paywayEnum : paywayEnumList) {
                captureRetryProcessorMap.put(paywayEnum, entry.getValue());
            }
        }

        Map<String, CancelCheckProcessor> map7 = applicationContext.getBeansOfType(CancelCheckProcessor.class);
        for (Map.Entry<String, CancelCheckProcessor> entry : map7.entrySet()) {
            List<PaywayEnum> paywayEnumList = entry.getValue().getPayway();
            for (PaywayEnum paywayEnum : paywayEnumList) {
                cancelCheckProcessorMap.put(paywayEnum, entry.getValue());
            }
        }

    }


    public PaywayEnum channelAnalysis(Integer payway) {
        PaywayEnum paywayEnum = PaywayEnum.explain(payway);
        if (paywayEnum == null) {
            log.error("路由渠道不存在,payway:{}", payway);
            throw new HeraBizException(HeraBizErrorCodeEnum.ROUTE_EMPTY);
        }
        return paywayEnum;
    }

    public AccountPayTypeEnum accountAnalysis(Integer channel) {
        PaywayEnum paywayEnum = PaywayEnum.explain(channel);
        if (paywayEnum == null) {
            log.error("路由渠道不存在,payway:{}", channel);
            throw new HeraBizException(HeraBizErrorCodeEnum.ROUTE_EMPTY);
        }
        AccountPayTypeEnum accountPayTypeEnum = AccountPayTypeEnum.explain(paywayEnum);
        if (accountPayTypeEnum == null) {
            log.error("路由账户不存在,payway:{}", channel);
            throw new HeraBizException(HeraBizErrorCodeEnum.ROUTE_EMPTY);
        }
        return accountPayTypeEnum;
    }

    public CheckoutPayProcessor getCheckoutPayProcessor(PaywayEnum paywayEnum) {
        CheckoutPayProcessor checkoutPayProcessor = bankcardPayProcessorMap.get(paywayEnum);
        if (checkoutPayProcessor == null) {
            throw new HeraBizException(HeraBizErrorCodeEnum.ROUTE_NOT_SUPPORT);
        }
        return checkoutPayProcessor;
    }

    public TokenPayProcessor getAuthorizationPayProcessor(PaywayEnum payway) {
        TokenPayProcessor tokenPayProcessor = authorizationPayProcessorMap.get(payway);
        if (tokenPayProcessor == null) {
            throw new HeraBizException(HeraBizErrorCodeEnum.ROUTE_NOT_SUPPORT);
        }
        return tokenPayProcessor;
    }

    public PayOrderProcessor getPayOrderProcessor(PaywayEnum paywayEnum) {
        PayOrderProcessor payOrderProcessor = payOrderProcessorMap.get(paywayEnum);
        if (payOrderProcessor == null) {
            throw new HeraBizException(HeraBizErrorCodeEnum.ROUTE_NOT_SUPPORT);
        }
        return payOrderProcessor;
    }

    public RefundProcessor getRefundProcessor(ChannelEnum channelEnum) {
        RefundProcessor refundProcessor = refundProcessorMap.get(channelEnum);
        if (refundProcessor == null) {
            throw new HeraBizException(HeraBizErrorCodeEnum.ROUTE_NOT_SUPPORT);
        }
        return refundProcessor;
    }

    public Refund0000000Processor getRefundProcessor(PaywayEnum paywayEnum) {
        if (applicationContext.containsBean(paywayEnum.getRouteKey() + "RefundProcessor")) {
            return applicationContext.getBean(paywayEnum.getRouteKey() + "RefundProcessor", Refund0000000Processor.class);
        }
        throw new HeraBizException(HeraBizErrorCodeEnum.ROUTE_NOT_SUPPORT);
    }

    /**
     * 支付订单查询路由器
     *
     * @param paywayEnum
     * @return
     */
    public QueryPayProcessor getQueryPayProcessor(PaywayEnum paywayEnum) {
        QueryPayProcessor queryPayProcessor = queryPayProcessorMap.get(paywayEnum);
        if (Objects.isNull(queryPayProcessor)) {
            throw new HeraBizException(HeraBizErrorCodeEnum.ROUTE_NOT_SUPPORT);
        }
        return queryPayProcessor;
    }

    /**
     * 退款订单查询路由器
     *
     * @param paywayEnum
     * @return
     */
    public QueryRefundProcessor getQueryRefundProcessor(PaywayEnum paywayEnum) {
        QueryRefundProcessor queryRefundProcessor = queryRefundProcessorMap.get(paywayEnum);
        if (Objects.isNull(queryRefundProcessor)) {
            throw new HeraBizException(HeraBizErrorCodeEnum.ROUTE_NOT_SUPPORT);
        }
        return queryRefundProcessor;
    }

    /**
     * 预授权、一键扣款重试路由器
     *
     * @param paywayEnum
     * @return
     */
    public PaymentCaptureRetryProcessor getCaptureRetryProcessor(PaywayEnum paywayEnum) {
        PaymentCaptureRetryProcessor captureRetryProcessor = captureRetryProcessorMap.get(paywayEnum);
        if (Objects.isNull(captureRetryProcessor)) {
            throw new HeraBizException(HeraBizErrorCodeEnum.ROUTE_NOT_SUPPORT);
        }
        return captureRetryProcessor;
    }

    public CancelCheckProcessor getCancelCheckProcessor(PaywayEnum paywayEnum) {
        CancelCheckProcessor cancelCheckProcessor = cancelCheckProcessorMap.get(paywayEnum);
        if (cancelCheckProcessor == null) {
            throw new HeraBizException(HeraBizErrorCodeEnum.ROUTE_NOT_SUPPORT);
        }
        return cancelCheckProcessor;
    }
}