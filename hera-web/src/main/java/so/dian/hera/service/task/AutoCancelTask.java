package so.dian.hera.service.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import so.dian.hera.service.component.AutoCancelProcessor;

import java.util.Set;

@Slf4j
@Component
public class AutoCancelTask extends BaseTask{

    private static final String AUTO_CANCEL_TASK_CRON = "0 1/3 * * * ?";

    @Autowired
    private AutoCancelProcessor autoCancelProcessor;

    @Scheduled(cron = AUTO_CANCEL_TASK_CRON)
    public void refundOrdersProcess() {
        boolean isTarget = isTarget();
        log.info("is target:{}", isTarget);
        if(!isTarget)return;

        Set waitingCancellationOrders = autoCancelProcessor.getElements();
        if (!CollectionUtils.isEmpty(waitingCancellationOrders)) {
            for (Object orderNo : waitingCancellationOrders) {
                try {
                    autoCancelProcessor.doHandle(orderNo.toString());
                } catch (Exception e) {
                    log.error("auto cancel订单处理失败，e:{}", e);
                }
            }
        }
    }

}
