package so.dian.hera.service.task;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.PostConstruct;
import java.net.InterfaceAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Collections;
import java.util.Enumeration;
import java.util.List;

/**
 * BaseTask
 *
 * <AUTHOR>
 * @date 2018/1/30
 */
@Slf4j
@Data
public abstract class BaseTask {

    @Value("${hera.job.targetIp}")
    private String targetIP;
    private volatile boolean isTarget = false;
    private volatile boolean isInvoiceCheckTarget = false;

    public boolean isTarget() {
        return isTarget;
    }

    @PostConstruct
    public void init() {
        try {
            log.info("job target ip:{}", targetIP);
            if (isTarget) {
                return;
            }
            // 如果没有特别制定，所有机器都需要执行
            if (targetIP == null) {
                isTarget = false;
                return;
            }

            Enumeration<NetworkInterface> nets = NetworkInterface.getNetworkInterfaces();
            for (NetworkInterface networkInterface : Collections.list(nets)) {
                if (null != networkInterface.getHardwareAddress()) {
                    List<InterfaceAddress> list = networkInterface.getInterfaceAddresses();
                    for (InterfaceAddress interfaceAddress : list) {
                        String localIp = interfaceAddress.getAddress().toString();
//                        log.info("local ip:{}", localIp);
                        if (localIp.contains(this.targetIP)) {
                            isTarget = true;
                        }
                    }
                }
            }
        } catch (SocketException e) {
            log.error("job init failed!", e);
        }
    }
}
