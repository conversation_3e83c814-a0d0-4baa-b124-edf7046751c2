package so.dian.hera.service.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import so.dian.hera.service.listener.RefundListener;


/**
 * <AUTHOR>
 * @date 2020-01-10 15:54
 */
@Slf4j
@Component
public class RefundTask extends BaseTask {

    @Autowired
    private RefundListener refundListener;

    @Scheduled(cron = "0 0/15 * * * ?")
    public void start() {
        refundListener.executeJob();
    }
}
