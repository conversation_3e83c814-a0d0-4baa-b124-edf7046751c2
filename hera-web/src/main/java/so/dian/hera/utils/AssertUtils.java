package so.dian.hera.utils;

import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.Objects;

/**
 * AssertUtils
 *
 * <AUTHOR>
 * @date 2017/11/4
 */
public class AssertUtils {

    /**
     * 断言，若对象为null，则抛出业务异常code
     */
    public static void notNullWithBizExp(Object obj, HeraBizErrorCodeEnum codeEnum) {
        if (Objects.isNull(obj)) {
            throw HeraBizException.create(codeEnum);
        }
    }

    /**
     * 断言，若对象为null或对象值为空，则抛出业务异常code
     */
    public static void notEmptyWithBizExp(Object obj, HeraBizErrorCodeEnum codeEnum) {
        if (ObjectUtils.isEmpty(obj)) {
            throw HeraBizException.create(codeEnum);
        }
    }

    /**
     * 断言，若字符串为null或字符串为空白，则抛出业务异常code
     */
    public static void notBlankWithBizExp(CharSequence str, HeraBizErrorCodeEnum codeEnum) {
        if (StringUtils.isBlank(str)) {
            throw HeraBizException.create(codeEnum);
        }
    }

    /**
     * 断言，若字符串为null或字符串为空白，则抛出业务异常code
     */
    public static void notBlankWithBizExp(CharSequence str, HeraBizErrorCodeEnum codeEnum, @NonNull String msg) {
        if (StringUtils.isBlank(str)) {
            throw HeraBizException.create(codeEnum, msg);
        }
    }

    /**
     * 断言，若为假，则抛出业务异常code
     */
    public static void notTrueWithBizExp(boolean bool, HeraBizErrorCodeEnum codeEnum) {
        if (!bool) {
            throw HeraBizException.create(codeEnum);
        }
    }

    /**
     * 断言，若为真，则抛出业务异常code
     */
    public static void trueWithBizExp(boolean bool, HeraBizErrorCodeEnum codeEnum) {
        if (bool) {
            throw HeraBizException.create(codeEnum);
        }
    }

    /**
     * 断言，若为真，则抛出业务异常code
     */
    public static void trueWithBizExp(boolean bool, HeraBizErrorCodeEnum codeEnum, @NonNull String msg) {
        if (bool) {
            throw HeraBizException.create(codeEnum,msg);
        }
    }

    /**
     * 断言，若为假，则抛出业务异常code
     */
    public static void notTrueWithBizExp(boolean bool, HeraBizErrorCodeEnum codeEnum, @NonNull String msg) {
        if (!bool) {
            throw HeraBizException.create(codeEnum, msg);
        }
    }
}
