package so.dian.hera.utils;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
@Data
public class EnvUtil {

    private static String env;

    public static boolean isDev() {
        if (StringUtils.isEmpty(env)) {
            throw new RuntimeException("EnvUtil is not set!");
        } else {
            return env.trim().contains("dev") || env.trim().contains("local") || env.trim().contains("stable");
        }
    }

    public static boolean isReal() {
        if (StringUtils.isEmpty(env)) {
            throw new RuntimeException("EnvUtil is not set!");
        } else {
            return env.trim().equals("real");
        }
    }

    @Value("${hera.profile}")
    public void setEnv(String env) {
        if (env == null) {
            env = "local";
        }
        EnvUtil.env = env;
    }
}
