package so.dian.hera.utils;

import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.DateUtil;

import javax.annotation.PostConstruct;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.Date;
import java.util.Random;

@Slf4j
@Component
public class TradeNoGenerator {

    private static String orderPrefixMark;

    /**
     * 订单前缀标识
     */
    @Value("${pay.order.mark}")
    private String orderMark;

    @PostConstruct
    public void init() {
        TradeNoGenerator.orderPrefixMark = orderMark;
    }

    private static String generateNo(String tns, Long userId) {
        Assert.notNull(userId, "userId should not null");
        DateFormat dateFormat = new SimpleDateFormat("YYMMddHHmmss");
        String date = dateFormat.format(new Date());
        Long buyer = userId % (999);
        DecimalFormat df = new DecimalFormat("000");
        DecimalFormat df2 = new DecimalFormat("0000");
        Random r1 = new Random();
        String random1 = df.format(r1.nextInt(1000));
        Random r2 = new Random();
        String random2 = df2.format(r2.nextInt(10000));
        return tns + date + df.format(buyer) + random1 + random2;
    }

    public static String generateTradeNo(PaymentBizTypeEnum tns, Long userId) {
        return orderPrefixMark + "T" + generateNo(String.valueOf(tns.getCode()), userId);
    }

    public static String generateTradeNo(Integer code, Long userId) {
        return orderPrefixMark + "T" + generateNo(String.valueOf(code), userId);
    }

    public static String generateRefundNo(Integer code, Long userId) {
        return orderPrefixMark + "R" + generateNo(String.valueOf(code), userId);
    }

    /**
     * zalopay退款订单号生成
     * • Merchant must be generate merchant own transaction code when submit refund requirement.
     * • Format: yymmdd_appid_xxxxxxxxxx
     *
     * @param appId
     * @param code
     * @param userId
     * @param zoneId
     * @return
     */
    public static String generateRefundNoToZalopay(Integer appId,Integer code, Long userId, ZoneId zoneId) {
        return new DateBuild(zoneId).formatter(DateBuild.SIMPLE_SHORT_DATE) + "_"+appId+"_" + generateNo(String.valueOf(code), userId);
    }
}
