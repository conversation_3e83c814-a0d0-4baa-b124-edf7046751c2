channel:
  enable:
    wechat: true
    wooshpay: true
    pingpong: true
    zalopay: true
    zalomini: true
    vietqr: true
    antom: true
    midtrans: true
    airwallex: true

spring:
  mail:
    host: smtp.mxhichina.com
    port: 80
    username: <EMAIL>
    password: Biz123456
    properties:
      mail.transport.protocol: smtp
      mail.smtp.auth: true
      mail.smtp.starttls.enable: true
  rocketmq:
    name-server: mq101.dian-stable.com:9876;mq102.dian-stable.com:9876
    producer:
      retry-times-when-send-async-failed: 0
      send-msg-timeout: 3000
      compress-msg-body-over-howmuch: 4096
      max-message-size: 4194304
      retry-another-broker-when-not-store-ok: false
      retry-times-when-send-failed: 2
      retry-count: 3

  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
  datasource:
    rds:
      hera:
        driver-class-name: com.mysql.cj.jdbc.Driver
        initial-size: 5
        max-active: 20
        min-idle: 5
        password: 5X0nPTEAQWSkRbJ
        url: ****************************************************************
        username: chargebolt
      lhc:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        initial-size: 5
        max-active: 20
        min-idle: 5
        password: 5X0nPTEAQWSkRbJ
        url: ***************************************************************
        username: chargebolt
    sharding:
      urls: ********************************************************,********************************************************,********************************************************,********************************************************
      driver-class-name: com.mysql.cj.jdbc.Driver
      initial-size: 2
      max-active: 10
      min-idle: 2
      password: 5X0nPTEAQWSkRbJ
      showsql: false
      username: hermes
    #pg-PostgreSQL备用库，用于OSS查询
#    pg:
#      url: ***************************************************************/${spring.datasource.pg.name}
#      name: lhc
#      schema: drds
#      username: lhc
#      password: 5X0nPTEAQWSkRbJ
#      driver-class-name: org.postgresql.Driver
#      max-active: 20
#      min-idle: 5
#      initial-size: 2
    druid:
      filter:
        config:
          enabled: false
      stat-view-servlet:
        enabled: false
      web-stat-filter:
        enabled: false
  redis:
    host: oversea-dev.redis.diancs.com
    password: 4dd7650E368672ad
    pool:
      max-active: 30
      max-idle: 5
      max-wait: 3000
    port: 6379
    timeout: 3000
#rocketmq topic和业务tag配置
notify:
  rocketmq:
    self:
      topic: 'chargebolt-hera-self-topic_${hera.alter}'
      producerGroup: 'chargebolt-hera-self-group_${hera.alter}'
      tag:
        # 即时支付-支付消息-hera自消费
        wechatV3ImPay: 'wechatV3ImPay_${hera.alter}'
        # 即时支付-退款消息-hera自消费
        wechatV3ImRefund: 'wechatV3ImRefund_${hera.alter}'
        # 延时查询消息tag
        pay-refund-delay-query: 'payAndRefundDelayQueryTag_${hera.alter}'
        # 预授权、一键支付重新发起扣款tag
        payment-retry-delay: 'paymentRetryDelayTag_${hera.alter}'
        # 延迟发起退款（刚完成支付，立刻发起退款场景）
        refund-delay-initiate: 'refundDelayInitiateTag_${hera.alter}'
        # wooshpay-扣款
        wooshpay:
          cardCaptureNotify: 'wooshpayCaptureNotify_${hera.alter}'
        pingpong:
          cardCaptureNotify: 'pingpongCaptureNotify_${hera.alter}'
          apmPaidNotify: 'pingpongPaidNotify_${hera.alter}'
          authorizationTokenCreatedNotify: 'pingpongAuthorizationTokenCreatedNotify_${hera.alter}'
          apmTokenPaidNotify: 'pingpongApmTokenPaidNotify_${hera.alter}'
        zalopay:
          pay-notify: 'zalopayPayNotify_${hera.alter}'
          refund-notify: 'zalopayRefundNotify_${hera.alter}'
        zalo-mini:
          pay-notify: 'zaloMiniPayNotify_${hera.alter}'
          refund-notify: 'zaloMiniRefundNotify_${hera.alter}'
        # antom-支付通知-hera自消费
        antom:
          cardCaptureNotify: 'antomCaptureNotify_${hera.alter}'
          apmPaidNotify: 'antomApmPaidNotify_${hera.profile}'
        midtrans:
          apmPaidNotify: 'midtransApmPaidNotify_${hera.profile}'
        refund: 'selfRefundTag_${hera.alter}'
        airwallex:
          cardCaptureNotify: 'airwallexCaptureNotify_${hera.alter}'
    biz:
      topic: 'chargebolt-hera-biz-topic_${hera.alter}'
      tag:
        # 即时支付-支付消息-业务方lhc消费
        # 即时支付-退款消息-业务方lhc消费
        gateway:
          pay: 'gatewayPayTag_${hera.alter}'
          refund: 'gatewayRefundTag_${hera.alter}'
        account:
          pay: 'accountPayTag_${hera.alter}'
        bankcard:
          preAuthCreate: 'bankcardPreAuthCreateTag_${hera.alter}'
          capture: 'bankcardCaptureTag_${hera.alter}'
          preAuthCancel: 'bankcardPreAuthCancelTag_${hera.alter}'
        refund: 'refundTag_${hera.alter}'
        apm:
          paid: 'apmPaidTag_${hera.alter}'
        apmToken:
          tokenApplied: 'apmTokenAppliedTag_${hera.alter}'
          tokenPaid: 'apmTokenPaidTag_${hera.alter}'
hera:
  domain: 'https://chargebolt-hera-${hera.alter}.seven.dian-dev.com'
  profile: 'dev'
  alter: 'alter-1072768272912355328'
  feignUrl:
    internal:
      kronos: 'chargebolt-kronos-${hera.alter}.six.dian-dev.com'
  appScheme: 'Chargebolt://chargebolt.com/webview/redirect?url='
pay:
  # 支付订单标识（用于区分哪个国家区域下的交易），大写字母，私有化服务部署时，一定要修改这个配置值
  order:
    mark: 'CN'

hera.job.targetIp: 0.0.0.0
logging:
  level:
    so.dian.platform.wooshpay.client.WooshPayClient: DEBUG
    so.dian.platform.pingpong.client.PingpongClient: DEBUG


message:
  client:
    # 可选配置（已提供默认值）
    connect-timeout: 3000        # 连接超时时间(ms)
    read-timeout: 5000          # 读取超时时间(ms)
    max-retries: 3              # 最大重试次数
    retry-interval: 1000        # 重试间隔(ms)
    log-enabled: true           # 是否启用日志
    app-name: hera            # 应用名称
    app-key: hera-1745835188842 # 应用密钥
    # 线程池配置（可选）
    thread-pool:
      core-pool-size: 5         # 核心线程数
      max-pool-size: 10         # 最大线程数
      queue-capacity: 100       # 队列容量
      thread-name-prefix: message-client-  # 线程名前缀