###################本地公共配置#################
server:
  port: 8080
# 应用基本信息
info:
  component: "@project.description@"
  name: "@project.name@"
  os: ${os.name}
  version: "@project.version@"
  instance-id: ${eureka.instance.instance-id}
# eureka注册中心配置
eureka:
  client:
    fetch-registry: true
    register-with-eureka: true
    healthcheck.enabled: true
    service-url:
      defaultZone: http://eureka:8761/eureka/
  instance:
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ipAddress}:${server.port}
    health-check-url-path: ${management.context-path}/health
    status-page-url-path: ${management.context-path}/info
    metadata-map:
      instance-id: ${eureka.instance.instance-id}
      user.name: ${security.user.name}
      user.password: ${security.user.password}
management:
  server:
    port: 8888
  endpoint:
    health:
      show-details: always
  health:
    db:
      enabled: false
    mail:
      enabled: false
# 应用鉴权配置
security:
  basic:
    enabled: true
    path: ${management.context-path}
  # 微服务统一鉴权用户名密码
  user:
    name: helloEureka
    password: zhimakaimen
logging:
  file:
    name: ${logging.file.path}/${spring.application.name}.log
    path: ${user.home}/logs/${spring.application.name}
  level:
    so.dian.hera: INFO
hystrix:
  command:
    default.execution.isolation.thread.timeoutInMilliseconds: 2000
spring:
  output:
    ansi:
      enabled: always

###################本地公共配置#################