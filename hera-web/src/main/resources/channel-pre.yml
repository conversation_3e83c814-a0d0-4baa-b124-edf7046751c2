channel:
  wechat:
    mchId: "656355975"
    payTypeRoute: 0
    mchKey: "1cdb2059e695a924762bc08720b48576"
    apiV3Key: "efb25d9aaa0641c181ab4354dc9c07d5"
    certSerialNo: "2C86BA5D1C4F555B9F113675902CDB3B0FCE5730"
    serviceId: ""
    keyPath: "classpath:cert/wechat/656355975/apiclient_cert.p12"
    privateKeyPath: "classpath:cert/wechat/656355975/apiclient_key.pem"
    privateCertPath: "classpath:cert/wechat/656355975/apiclient_cert.pem"
    appId: "wxf5d10e78c68a01b1"
    platPath: ""
    notifyUrl: "${hera.domain}/trade/wechatV3/callback/immediate/{applicationId}/{channelId}"
    payScoreNotifyUrl: "${hera.domain}/trade/wechatV3/callback/immediate/{applicationId}/{channelId}"
    supportedCurrencies:
      - 'CNY'
      - 'HKD'
  wooshpay:
    url: 'https://api.wooshpay.com'
    skKey: '***************************************************************************************'
    notifyUrl: "${hera.domain}/notify/wooshpay"
    returnUrl: "https://h5-pre.chargebolt.com/pages/pingpong/index"
  pingpong:
    regionCode: 'HK'
    url: 'https://acquirer-payment.pingpongx.com'
    cardNotificationUrl: "${hera.domain}/notify/pingpong/normal"
    apmNotificationUrl: "${hera.domain}/notify/pingpong/apm"
    apmTokenNotificationUrl: "${hera.domain}/notify/pingpong/apmToken"
    apmTokenPaidNotificationUrl: "${hera.domain}/notify/pingpong/apmToken/paid"
    refundNotificationUrl: "${hera.domain}/notify/pingpong/normal"
    clientId: "2024031805505590159"
    accId: "2024031805505590159628"
    salt: "55D2204C92EAD72951E0D921"
    apmTokenApplyUrl: 'https://kronos-pre.chargebolt.com/1.0/payment/pingpong/authorization/auth/{userId}/{clientType}'
    payResultUrl:
      h5: 'https://h5-pre.chargebolt.com/pages/pingpong/index'
      app: 'https://h5-pre.chargebolt.com/pages/pingpong/index'
    payCancelUrl:
      h5: 'https://h5-pre.chargebolt.com/'
      app: 'chargebolt://home'
    paymentMethodsOfCard:
      - 'VISA'
      - 'MASTERCARD'
#      - 'American Express'
#      - 'JCB'
    paymentMethodsOfApm:
      - 'ALIPAY'
      - 'ALIPAY_HK'
#      - 'G_CASH'
#      - 'PROMPT_PAY'
#      - 'RABBIT_LINE_PAY'
      - 'KAKAO_PAY'
  zalopay:
    url: 'https://sb-openapi.zalopay.vn'
    app-id: 2554
    app-key: '********************************'
    app-key2: 'trMrHtvjo6myautxDUiAcYsVtaeQ8nhf'
    notify-url: '${hera.domain}/notify/zalopay'
  zalo-mini:
    url: 'https://payment-mini.zalo.me'
    app-id: '1105212442057600120'
    private-key: 'cd4a3bf10a73ba9de71e7027c46dbcb4'
    method: '{"id": "ZALOPAY_SANDBOX","isCustom": false}'
    notify-url: '${hera.domain}/notify/zalomini'
  vietqr:
    url: 'https://api.vietqr.org'
    secret-key: 'NjU5YTZiMTJhZTEwNGE4M2JkZDkzYWQzMGQwZDk4YzI='
    # 我方认证账号
    chargebolt-auth:
      user-name: 'vietqr-dev-erx01'
      password: 'dmlldHFyLWFwaS1hY3ZpMHNxZw=='
    # vietQR 认证账号
    vietqr-auth:
      user-name: 'customer-vso17978pin-user2488'
      password: 'Y3VzdG9tZXItdnNvMTc5NzhwaW4tdXNlcjI0ODg='
  antom:
    mid: '2188120222965604'
    url: 'https://open-sea-global.alipay.com'
    cardNotificationUrl: '${hera.domain}/notify/antom'
    apmNotificationUrl: "${hera.domain}/notify/antom/apm"
    refundNotificationUrl: "${hera.domain}/notify/antom/normal"
    clientId: '5YC4502ZQ5SE03983'
    mchPrivateKey: 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDDFlF+H95guy7mcr49Sd6gOIU3/dvp3+sAGL46zilpbZuRe8qy0zOjIXvhrTVRdSfRWC0w/4yHzQ0WvgsJgZfcd5iDDyRlr16mI+YFzPATSC3qOIUKX33EIRvNrf8V8hANTw8jxe/1M4I8MvML8z6u1ZisaxP+o7u/PqdNb7AVK+AR8T+FYF2KIDDdVMMcfHTiDtj7VflOHgLftlYt3NNL+m2YwmMYLuz0sehWybgfpG/acAhX0AQUUxF20H1zV5YUHeLZ64WRBCobwiti69tKjERN7z5i6FdYuCJyVAlTJt17xVkRQ9v+YHomJpGadqjtuya8kQuCeDpgRU79cvbtAgMBAAECggEBAJoqcu/mo0dx5Jg8KNYqIAIhaDVdKYJib0U+SGYUMWmdMoj/eKlkebP6a/lldXMP0A2BTwUfjaBrjGJigDpc7PNOl3lKaArrUTKdv9Lppva13YLwc0JFQA/T0jLxw1lGVyUNSvuMGhp9P/fuk17oNKrZJLNsUvqxzVZOXsu07UNsa7ISgY8W5pGWr5LXvJEGXBf99qzcDmAyoTNttIlgg6ED9asnjsU0iEkf+ZLVfOZ4xDnzWHGZWZ8Rqx5iESJs9VE7K9HqU+5u7P5gxkCamYlm+GG37pMOBC/WcND0qQs0YPf8i5ePBCRSNM7iQGDzS1/Ty1/5SdQegYbp2l8JxIECgYEA94cgQOGli5uKKbWlLX8HA+9xKUH5DR4c7+Uu+cz1lkg0nsk/vcTwF/g1N+4qB5s0bb+8P6KVZ7uiyFDicOwQdK1Gpdvka2M8bzxIDL1jqf7ofeHm/4RZiMJVpskhnnhhsVbZm553fHu+KJFX5bbBnwec0RnapmS5g0bejx3zFZECgYEAycOzK5LkOYGJUY9azembTKJo8N6H8NKlHecHZ8+2DNUSqKoiiKHLNPjO420+xxFTX+tt3iLi58FgNSNz1vGF6Ik7ZhfehUptAh2QEE4TVqKa5gEukozWKx8x7eXFgypEWyGFv1iWW2alFMlRIbxIemCNQqA1QQ4BM2tmSdQwbZ0CgYAznn9QaH4gbhBTAGlw4nOxE2by5qosXSJFuPKh72crruUiU++yAHbdWMqoXkdvjKNTW8/DKYGkv1GEGrIOQHxyCIyaHBA3gNtiixyvY3N8SQds8QY+NPiC95vrCziyloIw/F6nAFYEpL2UMjM1WXZpVqoSRST+3DR/tvDEwccmsQKBgCWAN0wRCFRb7D75iyIcml3bDfA/HDs5m7D8INc9txOZgtdQkAyfLdrRp/qDcpijcsCwTIJuL4ht28jkpwI7b+0xnJW0OArEM5CiGAW/zT5cCLhZXeOlQ6VOLgSOoXBoqb3adDaFa6TfZi1/4ZGIolwEmBzbFCrcRILfImr0gm/NAoGAMKci1TkSgichnjsjdKxzxSBYy9SP+Cvx4faojJBYzBV5qs4iepM1JlzNiS8XQgJ4plLQN4NS7d0mZUoidWJ1Ss5iHQyPqQaoDZgGrEuMZ2mBNj0dNguwckB1Vmbb4ISAuL5DEErcfxaD94U5G25QtDi7w0suAnDjhMhtbne3R60='
    antomPublicKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlJ23zCLgJ25A7eBIZw//BdClBtlV1H3mpq+vyw/+92XdNNQ/qsemfanfekOn8qa572rHeGUxJkcMYuvWc8eek4jgGZfICOhDR7mkSPXimyO545adsEgZaQMhkKwEyfPpPMD5XluXI0imHnc0B0+66WYj4lv79XhD/3195ZUq0zz/q8ngUnVVmJ7fLA+hrfni8YinIVR9D9jOn4npAwRhQ58oAhRVtKXmeOSf+cnYY/eRpmRetOa3VFJEtrbCyfjmS0QSx5S0lIcrMOhHf7PJv2efVQkrr1hiYSkT0nWTV7ZwTsNxE+0FisU9w2rxx0pTWOQUzZb1SYpqzFHDzLY6SQIDAQAB'
    payResultUrl:
      h5: 'https://h5-pre.chargebolt.com/pages/pingpong/index'
    paymentMethodsOfCard:
      - 'VISA'
      - 'MASTERCARD'
    paymentMethodsOfApm:
      - 'WECHAT'
      - 'ALIPAY'
      - 'ALIPAY_HK'
      - 'RABBIT_LINE_PAY'
      - 'KAKAO_PAY'
    supportCancelMethods:
      - 'ALIPAY'
      - 'ALIPAY_HK'
      - 'G_CASH'
      - 'KAKAO_PAY'
    # 商户经营业务所在的国家或地区
    regionCode: 'HK'
  midtrans:
    url: 'https://api.midtrans.com/v2'
    mchId: 'G574981076'
    clientKey: 'Mid-client-BmSSmuzvmh4GPK44'
    serverKey: 'Mid-server-cF-Pfl848aNvsfxhxJ_OofAG'
    isProduction: true
    payResultUrl:
      h5: 'https://h5-pre.chargebolt.com/pages/pingpong/index'
      app: 'https://h5-pre.chargebolt.com/pages/pingpong/index'
  # 支付渠道：空中云汇
  airwallex:
    url: 'https://api.airwallex.com'
    clientId: 'naSNdYMPTqalLkXB7kLtzg'
    apiKey: '96b07c6e6e176be3b723d1918c21c44c5d427a33eec26365b5e69a01e196ccf779217a15a7787f2697c4fbb1a182873b'
    payResultUrl:
      h5: 'https://h5-pre.chargebolt.com/pages/pingpong/index'
    paymentMethodsOfCard:
      - 'VISA'
      - 'MASTERCARD'
      - 'GOOGLE_PAY'
      - 'APPLE_PAY'
    regionCode: 'HK'
    merchantId: '4996-2462-2477'
    merchantName: 'chargebolt'
    buttonType: 'buy'
    buttonColor: 'white-with-line'
common:
  currency-exchange:
    - sourceCurrency: 'PHP'
      targetCurrency: 'HKD'
      exchangeRate: 0.133