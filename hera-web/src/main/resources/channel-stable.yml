channel:
  wechat:
    mchId: "656355975"
    payTypeRoute: 0
    mchKey: "1cdb2059e695a924762bc08720b48576"
    apiV3Key: "efb25d9aaa0641c181ab4354dc9c07d5"
    certSerialNo: "2C86BA5D1C4F555B9F113675902CDB3B0FCE5730"
    serviceId: ""
    keyPath: "classpath:cert/wechat/656355975/apiclient_cert.p12"
    privateKeyPath: "classpath:cert/wechat/656355975/apiclient_key.pem"
    privateCertPath: "classpath:cert/wechat/656355975/apiclient_cert.pem"
    appId: "wxf5d10e78c68a01b1"
    platPath: ""
    notifyUrl: "${hera.domain}/trade/wechatV3/callback/immediate/{applicationId}/{channelId}"
    payScoreNotifyUrl: "${hera.domain}/trade/wechatV3/callback/immediate/{applicationId}/{channelId}"
    supportedCurrencies:
      - 'CNY'
      - 'HKD'
  wooshpay:
    url: 'https://apitest.wooshpay.com'
    skKey: 'sk_test_NTE2NjEzNTMwMjIxMjQ1OTMxNTMxOmlod201R3BiaHlBY3dmRk9sSHg1WkN1TjE2ODQ5MzI0MDIyODU'
    notifyUrl: "${hera.domain}/notify/wooshpay"
    returnUrl: "https://h5.xpower.dian-dev.com/pages/pingpong/index"
  pingpong:
    regionCode: 'HK'
    url: 'https://sandbox-acquirer-payment.pingpongx.com'
    cardNotificationUrl: "${hera.domain}/notify/pingpong/normal"
    apmNotificationUrl: "${hera.domain}/notify/pingpong/apm"
    apmTokenNotificationUrl: "${hera.domain}/notify/pingpong/apmToken"
    apmTokenPaidNotificationUrl: "${hera.domain}/notify/pingpong/apmToken/paid"
    refundNotificationUrl: "${hera.domain}/notify/pingpong/normal"
    clientId: '2023121216311410287'
    accId: '2023121216311410287522'
    salt: '0FD8981AFE9A8B65E4F656A3'
    apmTokenApplyUrl: 'https://kronos.xpower.dian-dev.com/1.0/payment/pingpong/authorization/auth/{userId}/{clientType}'
    payResultUrl:
      h5: 'https://h5.xpower.dian-dev.com/pages/pingpong/index'
      app: 'https://h5.xpower.dian-dev.com/pages/pingpong/index'
    payCancelUrl:
      h5: 'https://h5.xpower.dian-dev.com/'
      app: 'chargebolt://home'
    paymentMethodsOfCard:
      - 'VISA'
      - 'MASTERCARD'
#      - 'American Express'
#      - 'JCB'
    paymentMethodsOfApm:
      - 'ALIPAY'
      - 'ALIPAY_HK'
      - 'WECHAT'
  zalopay:
    url: 'https://sb-openapi.zalopay.vn'
    app-id: 2554
    app-key: '********************************'
    app-key2: 'trMrHtvjo6myautxDUiAcYsVtaeQ8nhf'
    notify-url: '${hera.domain}/notify/zalopay'
  zalo-mini:
    url: 'https://payment-mini.zalo.me'
    app-id: '1105212442057600120'
    private-key: 'cd4a3bf10a73ba9de71e7027c46dbcb4'
    method: '{"id": "ZALOPAY_SANDBOX","isCustom": false}'
    notify-url: '${hera.domain}/notify/zalomini'
  vietqr:
    url: 'https://api.vietqr.org'
    secret-key: 'NjU5YTZiMTJhZTEwNGE4M2JkZDkzYWQzMGQwZDk4YzI='
    # 我方认证账号
    chargebolt-auth:
      user-name: 'vietqr-dev-erx01'
      password: 'dmlldHFyLWFwaS1hY3ZpMHNxZw=='
    # vietQR 认证账号
    vietqr-auth:
      user-name: 'customer-vso17978pin-user2488'
      password: 'Y3VzdG9tZXItdnNvMTc5NzhwaW4tdXNlcjI0ODg='
  antom:
    mid: ''
    url: 'https://open-sea-global.alipay.com'
    cardNotificationUrl: '${hera.domain}/notify/antom'
    apmNotificationUrl: "${hera.domain}/notify/antom/apm"
    refundNotificationUrl: "${hera.domain}/notify/antom/refund"
    clientId: 'SANDBOX_5YC02N2ZK53D02152'
    mchPrivateKey: 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDVZkk32UK7/zCCV8/T/ZSruwbSbfX1qiy4qqQdSW2EURyp8FLWGCXQjSJaRPWNhfLQNjW8F0IJ9edi6agSVyqO8w1iakOBr1RsSjyw60/PzvfLsHk4SUoBsZ4XJ7vUXxVWEiP1irZMOQLs2VFlTQl5vjuH3dUbEeppHQngfxZzVmDTPFzVCkdhbnRq/mYThMuXsPKZr/ULVQ7yoinmtsdA06ulvM9WKyocQwY2OnWQrgC/cPdE6uvK4QQV8vDDWIT8EQBvK0AlSsPzr22SCVG5kaj+m692Ia3u5cHHsWK8KOdMgqohJBpmgTA8ofRn9jRb86qhbfzygzqOaxio4A0PAgMBAAECggEAE1ug/rD1rcobY2eF0cwqBV4E42fYLP3P6ryIAKQdjCgzenLzgLpWC9IRsLCaucjR2vNf8n0Fn5Ov9rgNaASCNs9zgl7zWrtF5EDjuoTOC6LRtad/h4yWawrqtV0EW+J4NAOXQXngyY9OZZ/dE1xmpKWODugfocIBSqG4uKWuqq2bwWiG9ETFHcv6ez5lnn920yom+1l/obXkSlRrutZB4PLTPm9eVr6N20pBJdYAtZlg9pvmTTSJHvnBp9TvakYNI2KFfgka4J0th5eihK6ensxCY7W3/1DSDde6pNikIEh/hLgSDFWTNBGO8I5CjJhZbrUBGkrTa5Aeq2XjCE23CQKBgQD5urrr/vynpk7EEc6aali1byvTSWVNXOnhb3uOueXIPC3RdHkrBXEsJ6WkNJpCmErtPUbfzpwGnpFnJNG9DZ8C5BxaQZWK1lQgkyk5g8Xz6gTjxQN+3fPeIEc3vINKargFfrUAcO2ChIi7T/zQwDhgyWxltbvja35cNZROUNJL4wKBgQDawgazPuBklU/RzCp2O0akO9sGA9ZCx4tCLBfVYgpzyrMxbHKAnNIUnM7MaDwO0NpVAV+p89bTc+1n6HLsAO7M6l5ItfpCpebqIwaFg6R/+8d0fHbUcQtCtZwmX/Sm+9nf0FGR5qi51Ja0/q3gUD7AR0gFLVMIVET6GW2+wJMZ5QKBgQDCWuZxFwERhShWBjnSCaRCiGQGS1/w5rgLQFVcDifcP6G1Gr3WkqSypaSPeHT6EUZ2/rKzKjxaFny5Vnl5dilj+Z5rTBGTfRz6QS3bWkfsfhwSHFmNGjZakKp8oVVYzFetxyAdWMYwRu+XQDhVStRV101qlxbEdvtfzj8++D3C2wKBgEHruCMprmsBB5WcJauNP0Nceir82wMyqIdfTCtUKPwtuAQLvxF8sF3fxv2X54k7bJ+zE0Xeof0EcSUfygnOWifL9G+KzwhogAZMWbnAKsEaeUdj/lcUXaVTnuN9hPcQ693jI8GItY2SP3v5RyJSbCZ7FHvi5aOb6VFIembCF1H1AoGAZjwl1+ot84MkErZr7ST0faP6EWnXOllwyAOjM1eKk9s6b08mWvBPeL54Cwu5fnd5xIRKR4e+R6U+MwRYIqCyLuV1F2s/rO8neqtmAJcSSbJOYiITRSETXxzu9eh1OfOlL93yv00+zx7BldgQCJdYZx0Wak44nO720wGZRNq+a30='
    antomPublicKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjN/WJJHIfiIyczfYd4RyWxQF1DwKVvZqUIKaZQjkOye5eaCIGvGHDmqzGLpsfLkLPqZYZlmwGr3zCas4wlU2dMSbxWAHAeXeODQ28e2FKykd83VSIeaBK/ZXS52rjELgeqUCiMzAIpi7o2BnwdSoC0zIdF1WUMJD2jrPq45UR7LJK72f+CPf+hlRUU+djXkYErs16Vu2RQJpVAHNEaOWTpczN8z/PGfSRLU2kkbVDjQ+5YhPflEmueXIZydUSX4hzO2U2EF48uyefDnWlp2fZUnS6R6OL11pT3kcBduVRYBhTVRtRqA52SWeRD0Y87i0eve8YUS5UvGaqk17dvAoGQIDAQAB'
    payResultUrl:
      h5: 'https://chargebolt-mobile-${hera.alter}.seven.dian-dev.com/pages/pingpong/index'
    paymentMethodsOfCard:
      - 'VISA'
      - 'MASTERCARD'
    paymentMethodsOfApm:
      - 'ALIPAY'
      - 'ALIPAY_HK'
      - 'G_CASH'
      - 'KAKAO_PAY'
      - 'GRABPAY'
      - 'PAYNOW'
      - 'SHOPEEPAY'
    supportCancelMethods:
      - 'ALIPAY'
      - 'ALIPAY_HK'
      - 'G_CASH'
      - 'KAKAO_PAY'
      - 'GRABPAY'
      - 'PAYNOW'
      - 'SHOPEEPAY'
    # 商户经营业务所在的国家或地区
    regionCode: 'HK'
  midtrans:
    url: 'https://api.sandbox.midtrans.com/v2'
    mchId: 'G574981076'
    clientKey: 'SB-Mid-client-k9E4WUcUgXBcTwQ2'
    serverKey: 'SB-Mid-server-qZ53IeTm6yhebJPT2jOZxisC'
    isProduction: false
    payResultUrl:
      h5: 'https://h5.xpower.dian-dev.com/pages/pingpong/index'
      app: 'https://h5.xpower.dian-dev.com/pages/pingpong/index'
  # 支付渠道：空中云汇
  airwallex:
    url: 'https://api.airwallex.com'
    clientId: 'naSNdYMPTqalLkXB7kLtzg'
    apiKey: '96b07c6e6e176be3b723d1918c21c44c5d427a33eec26365b5e69a01e196ccf779217a15a7787f2697c4fbb1a182873b'
    payResultUrl:
      h5: 'https://h5.xpower.dian-dev.com/pages/pingpong/index'
    paymentMethodsOfCard:
      - 'VISA'
      - 'MASTERCARD'
      - 'GOOGLE_PAY'
      - 'APPLE_PAY'
    regionCode: 'HK'
    merchantId: '4996-2462-2477'
    merchantName: 'chargebolt'
    buttonType: 'buy'
    buttonColor: 'white-with-line'
common:
  currency-exchange:
