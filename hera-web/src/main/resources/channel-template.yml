channel:
  wechat:
    mchId: "656355975"
    payTypeRoute: 0
    mchKey: "1cdb2059e695a924762bc08720b48576"
    apiV3Key: "efb25d9aaa0641c181ab4354dc9c07d5"
    certSerialNo: "2C86BA5D1C4F555B9F113675902CDB3B0FCE5730"
    serviceId: ""
    keyPath: "classpath:cert/wechat/656355975/apiclient_cert.p12"
    privateKeyPath: "classpath:cert/wechat/656355975/apiclient_key.pem"
    privateCertPath: "classpath:cert/wechat/656355975/apiclient_cert.pem"
    appId: "wxf5d10e78c68a01b1"
    platPath: ""
    notifyUrl: "${hera.domain}/trade/wechatV3/callback/immediate/{applicationId}/{channelId}"
    payScoreNotifyUrl: "${hera.domain}/trade/wechatV3/callback/immediate/{applicationId}/{channelId}"
    supportedCurrencies:
      - 'CNY'
      - 'HKD'
  wooshpay:
    url: 'https://api.wooshpay.com'
    skKey: '***************************************************************************************'
    notifyUrl: "${hera.domain}/notify/wooshpay"
    returnUrl: "https://h5-sg.chargebolt.com/pages/pingpong/index"
  pingpong:
    regionCode: 'SG'
    url: 'https://acquirer-payment.pingpongx.com'
    cardNotificationUrl: "${hera.domain}/notify/pingpong/normal"
    apmNotificationUrl: "${hera.domain}/notify/pingpong/apm"
    apmTokenNotificationUrl: "${hera.domain}/notify/pingpong/apmToken"
    apmTokenPaidNotificationUrl: "${hera.domain}/notify/pingpong/apmToken/paid"
    refundNotificationUrl: "${hera.domain}/notify/pingpong/normal"
    clientId: "2024031805505590159"
    accId: "2024031805505590159628"
    salt: "55D2204C92EAD72951E0D921"
    apmTokenApplyUrl: 'https://kronos-sg.chargebolt.com/1.0/payment/pingpong/authorization/auth/{userId}/{clientType}'
    payResultUrl:
      h5: 'https://h5-sg.chargebolt.com/pages/pingpong/index'
      app: 'https://h5-sg.chargebolt.com/pages/pingpong/index'
    payCancelUrl:
      h5: 'https://h5-sg.chargebolt.com/'
      app: 'chargebolt://home'
    paymentMethodsOfCard:
      - 'VISA'
#      - 'MASTERCARD'
#      - 'American Express'
#      - 'JCB'
    paymentMethodsOfApm:
      - 'WECHAT'
#      - 'ALIPAY'
#      - 'ALIPAY_HK'
#      - 'G_CASH'
#      - 'PROMPT_PAY'
#      - 'RABBIT_LINE_PAY'
#      - 'KAKAO_PAY'
  zalopay:
    url: 'https://sb-openapi.zalopay.vn'
    app-id: 2554
    app-key: '********************************'
    app-key2: 'trMrHtvjo6myautxDUiAcYsVtaeQ8nhf'
    notify-url: '${hera.domain}/notify/zalopay'
  zalo-mini:
    url: 'https://payment-mini.zalo.me'
    app-id: '1105212442057600120'
    private-key: 'cd4a3bf10a73ba9de71e7027c46dbcb4'
    method: '{"id": "ZALOPAY_SANDBOX","isCustom": false}'
    notify-url: '${hera.domain}/notify/zalomini'
  vietqr:
    url: 'https://api.vietqr.org'
    secret-key: 'NjU5YTZiMTJhZTEwNGE4M2JkZDkzYWQzMGQwZDk4YzI='
    # 我方认证账号
    chargebolt-auth:
      user-name: 'vietqr-dev-erx01'
      password: 'dmlldHFyLWFwaS1hY3ZpMHNxZw=='
    # vietQR 认证账号
    vietqr-auth:
      user-name: 'customer-vso17978pin-user2488'
      password: 'Y3VzdG9tZXItdnNvMTc5NzhwaW4tdXNlcjI0ODg='
  antom:
    mid: ''
    url: 'https://open-sea-global.alipay.com'
    cardNotificationUrl: '${hera.domain}/notify/antom'
    apmNotificationUrl: "${hera.domain}/notify/antom/apm"
    refundNotificationUrl: "${hera.domain}/notify/antom/normal"
    clientId: SANDBOX_5YC4502ZQ5SE03983
    mchPrivateKey: 'MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQDkZNu70fmf5QDMpRzHZvK6whspazUdA+bzYT9uNKevWF6cAmIouZvh5pd9lbbN/Jb09wApEwA3Q8ezLuNJOVofMrCcKi7HPdlDpxVSUvfO9XTSfV2GIf0DGlI2qrMZCh4pFSkhxgKoCU/ILe9QqCl0jTyZsnAVx44QPbVC9B315mFZHK6jjSNJH1TzeZhOxwNCxrAQenpZtEr10PW0DVtAovzqCXtxjQnHvK0UzZI4SPA5p2ar/Zbrk5pH4fX+QJySDjfU/vYm7mU6bCsRv3tBwDJcj2bXYznjKEwPYbMCu5mI1RMPbFdd3JiTqnNCB0+ixRyEW8/mij3BqrGiaytpAgMBAAECggEBAL0A4f4KA4NYZVL1CF3TSedXHlcmSuvaANNDlNREasfmX1THfCergd5tjsv0izTnZJ0PkmhrOvCA/M/Ba4F7HqNWtWRmXBOxkQw1RqW71yrDYYuXB0Aq/FAHBiYLGO8NumvRARrb2/Lm0ckcikC7CKNVBeNDERJU87Fo/4mKrwh/ry6NIm4Y5K+ERfv/QDB+rfHH3UC/Q7jLBI/F9tXTgR9rs3uwohnXGR6wwfEK6rfz62oA8wZc1xRub1RsWecdaZqsK7SRHSO47QuR5/7L3twOsDaRd4Ol40Twg/+xSQ9dnp3ZPBZ0sE8oIrDF1oQYeXykLDqVh0ZOwabE6a7lAqECgYEA/pnQhGnVpo7sgboPpV2FFU2R90+zkS0chuZDT1uXchabHezxfM/dQBAFXrg1NIjl/GjcFfYmcQJwe+qOtRmkF8Vhr4mCASNJjHbINBv140BJ/qf33wnZJ+TMsIyNnZrqrXsidfbc//nEuaisd9QkubRa2f0yMY22+3Yry4bEoB0CgYEA5aYstrlDFzhqGhy8rQRtX3qB+nwfafsok+3R1g8nJy8zRkfkDbmOZGIvXkn0l4+nXJ2iT10SRIBn4Bv/ee97EUGFXwH9SL+HpyH+mKLTCTU8Z8+8LashmyRuxHQuGI48bAXhsa/x/eK6G5QqKG7JZnMMGt1T7CNYIDKi0W8e7r0CgYEA8p246rW/9vxzvBuGFVtNGsPUuQumXk3SxWpf8huh8gJAxv+oD/w1Jnsd4uk/KlTadPGn6PWsJDzi2/LlEwbgLBbXw+MRk+wAhm4a0i1i36+/04BU4KIN4cpSlFscCHg78h1I9YjNiNCG+zUVRQtQA/cuLFTvo/Eb0f0GNuiyLrECgYAJ9lJ8Pr8tU7Iunc6FhuqjuLHIxvep5k5VVHzE13VwWrjTtKys9764lhXN9lFY/c9Z0jDHVpdDR3q6GxzTElN5ZSk+/Nwa+swMsQnVX0LeZGPRcQ6NwJ29mDAB77RMNmdinEK1LZfG1r4SEjRd+qhb/GOrCqzoUoQxKWEsyrBB1QKBgQDMnnmWChSmvUuHXvGRpPkoqBO8Dy84DlMugT6wjKJFnmlKwnO+dl6pvOr4ZiH2XqetvMf4G4CU7IUeABWGP4Jhq5h5sGtFFaKTVO96rZRgu1ST4k1mqo+R61NleX5hl3ZdyYCpDshsN5IwuIMcRl2eKBGun8sOagabufbcH7JEcw=='
    antomPublicKey: 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkuaJJgHPPSbuSVfRIa/89b3lmsyxCUvjoNYJQGGyWgkR5+eevCZGH4IIWLhWufFo7G2oQL3Q+TZav93mOQhFnvNY16nGd2y/F2jXfZgBK9yi5XtMy4svG+iYFUPrCYetezQUoUGyaLOmyrgP4qj7P5DdnKutlBYkBhxlztd4lcGsjJa+5F7gW9TD3FxWsV5yiEeoo3/8A52Ae/QhzG6WFsipLSz6ffT65TeU9UGHkHqepMKchpO/lrC1RWYrs0wV6yD5gGX3k5mfy9Rkp1Gmt4eJun8wfl62fSGsWKtT3THPkAZ8AfhrUoqAMzNINWOhS42zNMqHPoLTSaI0s5vsZQIDAQAB'
    payResultUrl:
      h5: 'https://h5-sg.chargebolt.com/pages/pingpong/index'
    paymentMethodsOfCard:
      - 'VISA'
#      - 'MASTERCARD'
    paymentMethodsOfApm:
      - 'WECHAT'
#      - 'ALIPAY'
#      - 'ALIPAY_HK'
#      - 'RABBIT_LINE_PAY'
#      - 'KAKAO_PAY'
    supportCancelMethods:
      - 'ALIPAY'
#      - 'ALIPAY_HK'
#      - 'G_CASH'
#      - 'KAKAO_PAY'
    # 商户经营业务所在的国家或地区
    regionCode: 'HK'
  midtrans:
    url: 'https://api.midtrans.com/v2'
    mchId: 'G574981076'
    clientKey: 'Mid-client-BmSSmuzvmh4GPK44'
    serverKey: 'Mid-server-cF-Pfl848aNvsfxhxJ_OofAG'
    isProduction: true
    payResultUrl:
      h5: 'https://h5-sg.chargebolt.com/pages/pingpong/index'
      app: 'https://h5-sg.chargebolt.com/pages/pingpong/index'
  # 支付渠道：空中云汇
  airwallex:
    url: 'https://api.airwallex.com'
    clientId: 'naSNdYMPTqalLkXB7kLtzg'
    apiKey: '96b07c6e6e176be3b723d1918c21c44c5d427a33eec26365b5e69a01e196ccf779217a15a7787f2697c4fbb1a182873b'
    payResultUrl:
      h5: 'https://h5-sg.chargebolt.com/pages/pingpong/index'
    paymentMethodsOfCard:
      - 'VISA'
#      - 'MASTERCARD'
#      - 'GOOGLE_PAY'
#      - 'APPLE_PAY'
    regionCode: 'HK'
    merchantId: '4996-2462-2477'
    merchantName: 'chargebolt'
    buttonType: 'buy'
    buttonColor: 'white-with-line'