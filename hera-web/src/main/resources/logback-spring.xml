<?xml version="1.0" encoding="UTF-8"?>
<!-- scan 配置文件如果发生改变，将会被重新加载 scanPeriod 检测间隔时间-->
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <!--引入默认配置-->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>

    <springProperty scope="context" name="bizLevel" source="logging.biz.level" defaultValue="INFO"/>
    <springProperty scope="context" name="callbackLevel" source="logging.callback.level" defaultValue="INFO"/>
    <springProperty scope="context" name="springLevel" source="logging.spring.level" defaultValue="INFO"/>


    <!--自定义环境变量-->
    <property name="LOG_PATH" value="${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}"/>
    <property name="LOG_FILE" value="${LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}/spring.log}"/>
    <property name="ERROR_FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS}|{%m} %n"/>

    <!-- 项目全量日志，建议每个logger中都加入此appender，用于链路调用跟踪 -->
    <appender name="SPRING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE}</file>
        <!-- 循环政策：基于时间创建日志文件 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志命名:单个文件大于128MB 按照时间+自增i 生成log文件 -->
            <fileNamePattern>${LOG_FILE}.%d{yyyy-MM-dd}.gz</fileNamePattern>
            <maxHistory>${MAX_HISTORY:-7}</maxHistory>
        </rollingPolicy>
        <append>true</append>
        <!-- 日志格式 -->
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
        <!-- 日志临界值过滤器 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${springLevel}</level>
        </filter>
    </appender>

    <!-- 错误日志，只打印ERROR级别未处理异常堆栈跟踪日志：用于报警通知-->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/error.log.%d{yyyy-MM-dd}.gz</fileNamePattern>
            <maxHistory>${MAX_HISTORY:-7}</maxHistory>
        </rollingPolicy>
        <append>true</append>
        <encoder>
            <pattern>${ERROR_FILE_LOG_PATTERN}</pattern>
        </encoder>
        <!-- 日志级别过滤器 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 过滤的级别 -->
            <level>ERROR</level>
            <!-- 匹配时的操作：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配时的操作：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 其他自定义业务划分日志 -->
    <appender name="BIZ_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/biz.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/biz.log.%d{yyyy-MM-dd}.gz</fileNamePattern>
            <maxHistory>${MAX_HISTORY:-7}</maxHistory>
        </rollingPolicy>
        <append>true</append>
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
        <!-- 日志临界值过滤器 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${bizLevel}</level>
        </filter>
    </appender>

    <!-- 其他自定义业务划分日志 -->
    <appender name="CALLBACK_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/callback.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/callback.log.%d{yyyy-MM-dd}.gz</fileNamePattern>
            <maxHistory>${MAX_HISTORY:-7}</maxHistory>
        </rollingPolicy>
        <append>true</append>
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
        <!-- 日志临界值过滤器 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${callbackLevel}</level>
        </filter>
    </appender>


    <!--日志topic为error，即 @Slf4j(topic = "error") 或 LoggerFactory.getLogger("error") 方式打印的日志-->
    <logger name="error" level="ERROR" additivity="false">
        <appender-ref ref="ERROR_FILE"/>
        <appender-ref ref="SPRING_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <!--日志topic为biz，即 @Slf4j(topic = "biz") 或 LoggerFactory.getLogger("biz") 方式打印的日志-->
    <logger name="biz" level="${bizLevel}" additivity="false">
        <appender-ref ref="BIZ_FILE"/>
        <appender-ref ref="SPRING_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <!--日志topic为biz，即 @Slf4j(topic = "callback") 或 LoggerFactory.getLogger("callback") 方式打印的日志-->
    <logger name="callback" level="${callbackLevel}" additivity="false">
        <appender-ref ref="CALLBACK_FILE"/>
        <!--<appender-ref ref="SPRING_FILE"/>-->
        <appender-ref ref="CONSOLE"/>
    </logger>

    <!--默认日志topic，即 @Slf4j 或 LoggerFactory.getLogger(ClassName.class}) 方式打印的日志-->
    <root level="${springLevel}">
        <appender-ref ref="SPRING_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </root>

    <!--JMX管理 Spring Boot Admin-->
    <jmxConfigurator/>
</configuration>