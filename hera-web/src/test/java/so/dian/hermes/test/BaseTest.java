/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hermes.test;

import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestExecutionListeners;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.testng.AbstractTransactionalTestNGSpringContextTests;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import so.dian.hera.HeraApplication;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: BaseTest.java, v 1.0 2024-04-29 9:39 AM Exp $
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = HeraApplication.class)
@ActiveProfiles("local")
@Slf4j
@Transactional
@Rollback
@TestExecutionListeners(MockitoTestExecutionListener.class)
public class BaseTest extends AbstractTransactionalTestNGSpringContextTests {

    @Autowired
    public org.springframework.web.context.ConfigurableWebApplicationContext context;
    public MockMvc mvc;

    @Before
    public void setUp() throws Exception {
        mvc = MockMvcBuilders.webAppContextSetup(context).build();//建议使用这种
    }

    @Before
    public void before() throws Exception {
        log.info("before ---- ");
    }

    @After
    public void after() throws Exception {
        log.info("after ---- ");
    }


}