/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hermes.test.pingpong;


import com.chargebolt.hera.client.dto.pay.checkout.req.CaptureQueryRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import so.dian.hera.interceptor.dto.req.PayQueryResultRequest;
import so.dian.hermes.test.BaseTest;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.pingpong.processor.PingPongApmPayResultProcessor;
import so.dian.platform.pingpong.processor.PingPongCheckoutPayResultProcessor;
import so.dian.platform.pingpong.processor.PingpongCheckoutHostedProcessor;
import so.dian.platform.wooshpay.processor.WooshPayPayResultProcessor;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: PingpongTest.java, v 1.0 2024-04-29 9:41 AM Exp $
 */
@Slf4j
public class PingpongTest extends BaseTest {
    @Autowired
    private PingPongCheckoutPayResultProcessor pingPongPayResultProcessor;
    @Autowired
    private WooshPayPayResultProcessor wooshPayPayResultProcessor;
    @Autowired
    private PingPongApmPayResultProcessor pingPongApmPayResultProcessor;
//    @Autowired
//    private PingpongApmTokenProcessor pingpongApmTokenProcessor;

//    卡授权结果查询
//52 CNT232405071103266603689701 2024050750046472
//            pingPongApmPayResultProcessor.orderPayQuery(request);
//
//    卡授权支付结果查询
//52 CNT112405071108426609087451 CNT232405071103266603689701
//pingPongPayResultProcessor.orderPayQuery(request);

    @Test
    public void getPayResult() {
        //
        String tradeNo= "CNT112405071428336601137131";
        String payNo="";
        PayQueryResultRequest request= new PayQueryResultRequest();
        request.setTradeNo(tradeNo);
        request.setPayNo(payNo);
        Object resultDTO= pingPongPayResultProcessor.orderPayQuery(request);
        log.info("==>{}", JsonUtil.beanToJson(resultDTO));
    }

    // 卡授权结果查询
    // 一键支付结果查询
    @Test
    public void getPayResultApm() {
        String tradeNo= "CNT112405071705576607433244";
        String payNo="2024050750046622";
        PayQueryResultRequest request= new PayQueryResultRequest();
        request.setTradeNo(tradeNo);
        request.setPayNo(payNo);

        Object resultDTO= pingPongApmPayResultProcessor.orderPayQuery(request);
        log.info("==>{}", JsonUtil.beanToJson(resultDTO));
    }
    @Test
    public void getWooshPayResult() {
//        String tradeNo= "pi_1784841056999702528";
        String tradeNo= "pi_1784841056999702528";
        PayQueryResultRequest request= new PayQueryResultRequest();
        request.setTradeNo(tradeNo);
        Object resultDTO= wooshPayPayResultProcessor.orderPayQuery(request);
        log.info("==>{}", JsonUtil.beanToJson(resultDTO));
    }

//    @Test
//    private void apmTokenPre(){
//
//    }
}