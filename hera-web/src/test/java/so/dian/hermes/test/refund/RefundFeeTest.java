/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hermes.test.refund;

import com.chargebolt.hera.client.dto.pay.refund.req.RefundFeeRequest;
import com.chargebolt.hera.domain.RefundDO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import so.dian.hera.controller.RefundController;
import so.dian.hera.interceptor.dto.req.RefundQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.RefundQueryResultDTO;
import so.dian.hera.service.persist.db.RefundPersistService;
import so.dian.hera.service.refund.RefundResultQueryService;
import so.dian.hermes.test.BaseTest;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.wechat.processor.WechatRefundResultProcessor;

import javax.annotation.Resource;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: RefundFeeTest.java, v 1.0 2024-12-05 15:19 Exp $
 */
@Slf4j
public class RefundFeeTest extends BaseTest {
    @Resource
    RefundController refundController;
    @Resource
    private RefundResultQueryService refundResultQueryService;
    @Resource
    private RefundPersistService refundPersistService;
    @Resource
    private WechatRefundResultProcessor wechatRefundResultProcessor;
    @Test
    public void saveRefundFee(){
        RefundFeeRequest request= new RefundFeeRequest();
        request.setTradeNo("312202211523157149711234");
        request.setAmount(120L);
        request.setUserId(1234L);
        request.setAgentId(1150L);
        request.setSourceType(1);
        refundController.saveRefundFee(request);
    }

    @Test
    public void wehcatRefundQuery(){
//        HKR52503080037323085454724
//                HKR52503080021443313578531

//        String json= "{\"aborted\":false,\"allHeaders\":[{\"elements\":[{\"name\":\"application/json\",\"parameterCount\":0,\"parameters\":[]}],\"name\":\"Accept\",\"value\":\"application/json\"}],\"method\":\"GET\",\"params\":{\"names\":[]},\"protocolVersion\":{\"major\":1,\"minor\":1,\"protocol\":\"HTTP\"},\"requestLine\":{\"method\":\"GET\",\"protocolVersion\":{\"$ref\":\"$.protocolVersion\"},\"uri\":\"https://api.mch.weixin.qq.com/v3/refund/domestic/refunds/HKR52503080021443313578531\"},\"uRI\":\"https://api.mch.weixin.qq.com/v3/refund/domestic/refunds/HKR52503080021443313578531\"}";
        RefundDO refundDO = refundPersistService.getRefund("HKT112503102313295938720537");
        RefundQueryResultDTO resultDTO = refundResultQueryService.queryRefundResult(refundDO);
        log.info("resultDTO: {}", JsonUtil.beanToJson(resultDTO));
    }

    @Test
    public void wehcatRefundAPIQuery(){
//        2025-03-08 09:07:36.126  INFO 19128 --- [           main] s.d.p.w.p.WechatRefundResultProcessor
//        : 微信v3【订单退款查询】#响应参数 response: {"id":"50201202672025030835816195046","out_refund_no":"HKR52503080037323085454724","transaction_id":"4200002636202503077839069874","out_trade_no":"HKT112503072313593081744697","channel":"ORIGINAL","status":"PROCESSING","recv_account":"建设银行借记卡2796","fund_source":"REFUND_SOURCE_UNSETTLED_FUNDS","create_time":"2025-03-08T00:37:33+08:00","amount":{"refund":16800,"currency":"HKD","payer_refund":15667,"payer_currency":"CNY","exchange_rate":{"type":"USERPAYMENT_RATE","rate":********},"settlement_refund":16800,"settlement_currency":"HKD"}}
//        2025-03-08 09:09:54.731  INFO 19206 --- [           main] s.d.p.w.p.WechatRefundResultProcessor    :
//        微信v3【订单退款查询】#响应参数 response: {"id":"50200802822025030835816771825","out_refund_no":"HKR52503080021443313578531","transaction_id":"4200002610202503078805149062","out_trade_no":"HKT112503072325213315292682","channel":"ORIGINAL","status":"PROCESSING","recv_account":"支付用户的零钱","fund_source":"REFUND_SOURCE_UNSETTLED_FUNDS","create_time":"2025-03-08T00:21:45+08:00","amount":{"refund":17400,"currency":"HKD","payer_refund":16227,"payer_currency":"CNY","exchange_rate":{"type":"USERPAYMENT_RATE","rate":********},"settlement_refund":17400,"settlement_currency":"HKD"}}

        RefundQueryResultRequest request= new RefundQueryResultRequest();
        request.setRefundNo("*****************************");
        RefundQueryResultDTO resultDTO= wechatRefundResultProcessor.orderRefundQuery(request);
        log.info("resultDTO: {}", JsonUtil.beanToJson(resultDTO));
    }
}