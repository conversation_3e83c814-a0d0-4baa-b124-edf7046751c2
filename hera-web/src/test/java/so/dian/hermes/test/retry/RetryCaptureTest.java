/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hermes.test.retry;

import com.chargebolt.hera.domain.notify.PaymentRetryBody;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import so.dian.hera.mq.consumer.self.PaymentRetryHandler;
import so.dian.hermes.test.BaseTest;
import so.dian.mofa3.lang.util.JsonUtil;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: RetryCaptureTest.java, v 1.0 2024-05-08 下午5:22 Exp $
 */
@Slf4j
public class RetryCaptureTest extends BaseTest {
    @Autowired
    private PaymentRetryHandler paymentRetryHandler;
    @Test
    public void retry(){
        String tradeNo="CNT112405071507586605631921";
        PaymentRetryBody paymentRetryBody=new PaymentRetryBody();
        paymentRetryBody.setTradeNo(tradeNo);
        paymentRetryHandler.handle(JsonUtil.beanToJson(paymentRetryBody));
    }
}