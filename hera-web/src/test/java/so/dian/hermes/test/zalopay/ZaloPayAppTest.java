/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hermes.test.zalopay;

import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PrepayCreateResultDTO;
import com.chargebolt.hera.client.dto.pay.refund.req.RefundRequest;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundResultDTO;
import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import so.dian.hera.controller.ApmPayController;
import so.dian.hera.interceptor.dto.req.PayQueryResultRequest;
import so.dian.hera.service.refund.RefundService;
import so.dian.hera.utils.TradeNoGenerator;
import so.dian.hermes.test.BaseTest;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.mofa3.lang.util.RandomCodeUtil;
import so.dian.platform.common.utils.BizUtil;
import so.dian.platform.vietqr.request.GenerateCodeRequest;
import so.dian.platform.vietqr.request.QueryTransactionResultRequest;
import so.dian.platform.vietqr.response.GenerateCodeResponse;
import so.dian.platform.vietqr.service.VietqrApiService;
import so.dian.platform.zalopay.dto.request.OrderCreationRequest;
import so.dian.platform.zalopay.dto.response.OrderCreationResponse;
import so.dian.platform.zalopay.processor.ZaloPayApiService;
import so.dian.platform.zalopay.processor.ZaloPayResultProcessor;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.time.ZoneId;
import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloPayTest.java, v 1.0 2024-05-14 下午3:30 Exp $
 */
@Slf4j
public class ZaloPayAppTest extends BaseTest {
    private String payResultUrlH5= "https://chargebolt-mobile-alter-924136924334002176.seven.dian-dev.com/#/pages/pingpong/index";
    @Autowired
    private ApmPayController apmPayController;
    @Test
    public void zalopayCreate(){
        PrepayCreateRequest prePayCreateRequest = new PrepayCreateRequest();
        prePayCreateRequest.setCurrency("VND");
        prePayCreateRequest.setPayAmount(10000);
        prePayCreateRequest.setDelayTime(10 * 60 * 60 * 24L);
        prePayCreateRequest.setIsAutoCancel(true);
        prePayCreateRequest.setUserId(1234L);
        prePayCreateRequest.setCooperatorId(1L);
        prePayCreateRequest.setSystem("zeus");
        prePayCreateRequest.setIsNotify(true);
        prePayCreateRequest.setReqDate(new Date());
        prePayCreateRequest.setIp("127.0.0.1");
        prePayCreateRequest.setPayway(PaywayEnum.ZALOPAY);
        prePayCreateRequest.setRemark("Zalopay create order");
        prePayCreateRequest.getExtInfo().setRedirectUrl(payResultUrlH5
                +"?deviceNo={0}&cbPrepayTradeNo={1}&merchantTransactionId={2}&z="+ RandomCodeUtil.generateTextCode(RandomCodeUtil.TYPE_NUM_UPPER,6,null));
        prePayCreateRequest.getExtInfo().setDeviceNo("862990060014882");
        prePayCreateRequest.setBizNo((BizUtil.generateTradeNo(PaymentBizTypeEnum.PAY_DEPOSIT, prePayCreateRequest.getUserId())));
        prePayCreateRequest.setBizType(PaymentBizTypeEnum.PAY_DEPOSIT.getCode());
        log.info("请求参数：{}", JsonUtil.beanToJson(prePayCreateRequest));
//        PrepayCreateResultDTO resultDTO= apmPayController.prepay(prePayCreateRequest);
//        log.info("{}", JsonUtil.beanToJson(resultDTO));
    }


}