/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.hermes.test.zalopay;

import com.chargebolt.hera.client.dto.pay.refund.req.RefundRequest;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundResultDTO;
import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import so.dian.hera.interceptor.dto.req.PayQueryResultRequest;
import so.dian.hera.service.refund.RefundService;
import so.dian.hera.utils.TradeNoGenerator;
import so.dian.hermes.test.BaseTest;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.vietqr.request.GenerateCodeRequest;
import so.dian.platform.vietqr.request.QueryTransactionResultRequest;
import so.dian.platform.vietqr.response.GenerateCodeResponse;
import so.dian.platform.vietqr.service.VietqrApiService;
import so.dian.platform.zalomini.dto.request.ZaloMiniRefundQueryRequest;
import so.dian.platform.zalomini.dto.response.ZaloMiniBaseResponse;
import so.dian.platform.zalopay.dto.request.OrderCreationRequest;
import so.dian.platform.zalopay.dto.request.RefundQueryRequest;
import so.dian.platform.zalopay.dto.response.OrderCreationResponse;
import so.dian.platform.zalopay.dto.response.ZaloBaseResponse;
import so.dian.platform.zalopay.processor.ZaloPayResultProcessor;
import so.dian.platform.zalopay.processor.ZaloPayApiService;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.time.ZoneId;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloPayTest.java, v 1.0 2024-05-14 下午3:30 Exp $
 */
@Slf4j
public class ZaloPayTest extends BaseTest {
    @Autowired
    private ZaloPayApiService zaloPayApiService;
    @Autowired
    private RefundService refundService;
    @Autowired
    private ZaloPayResultProcessor zaloPayResultProcessor;
    @Autowired
    private VietqrApiService vietqrApiService;
    @Test
    public void payCreate(){
        OrderCreationRequest request= new OrderCreationRequest();
        String tradeNo= new DateBuild(ZoneId.of("Asia/Ho_Chi_Minh")).formatter(DateBuild.SIMPLE_SHORT_DATE)
                +"_"+ TradeNoGenerator.generateTradeNo(PaymentBizTypeEnum.PAY_ORDER,1234L);
        request.setAppTransId(tradeNo);
        request.setAppUser("1234");
        request.setAmount(20000L);
        request.setAppTime(System.currentTimeMillis());
//        request.setItem("[{\"itemid\":\"knb\",\"itename\":\"kim nguyen bao\",\"itemprice\":20000,\"itemquantity\":1}]");
        request.setDescription("Chargebolt-Payment for the order ");
        OrderCreationResponse response=zaloPayApiService.createOrder(request);
        log.info("{}", response.getOrderUrl());
    }


    @Test
    public void queryOrder(){
        PayQueryResultRequest request= new PayQueryResultRequest();
        request.setPayNo("371593858655910014835483428_1717575124696");
        request.setTradeNo("CNT122406051612026600174904");
        zaloPayResultProcessor.orderPayQuery(request);
    }

    @Test
    public void apiRefund(){
//        RefundRequest refundRequest= new RefundRequest();
//        refundRequest.setTradeNo("CNT122406041630286608460019");
//        refundRequest.setRefundReason("押金退还");
//        refundRequest.setAmount(5000);
//        refundRequest.setSystem("testCase");
//        refundService.refund(refundRequest);

//        ZaloMiniRefundRequest req=new ZaloMiniRefundRequest();
//        req.setTransId("240604_1530307607983877887840211818228");
//        req.setAmount(8899L);
//        req.setDescription("refund");
//        RefundResponse response= zaloPayApiService.refundOrder(req);
//        log.info("{}", JsonUtil.beanToJson(response));
    }


    @Test
    public void controllerRefund(){

        RefundRequest req=new RefundRequest();
        req.setTradeNo("CNT122407091844141489451769");
        req.setAmount(2840);
        req.setSystem("testCase");
        req.setRefundReason("refund");
//        req.setTransId("240604_1530307607983877887840211818228");
//        req.setAmount(8899L);
//        req.setDescription("refund");
        RefundResultDTO response= refundService.refund(req);
        log.info("{}", JsonUtil.beanToJson(response));
    }

//
    @Test
    public void apiRefundQuery(){
        // RefundQueryRequest req=new RefundQueryRequest();
        // req.setMRefundId("240612_2554_100148351718176402713");
        // ZaloBaseResponse response= zaloPayApiService.refundQuery(req);
        // log.info("{}", JsonUtil.beanToJson(response));
    }
    @Test
    public void getVietQrPayResult(){
        // ***************************** ****************

        QueryTransactionResultRequest request= new QueryTransactionResultRequest();
        request.setBankAccount("**********");
        request.setValue("****************");
        vietqrApiService.getPayResult(request);
    }

    @Test
    public void getVietQrRefundResult(){
        // ****************
        // ****************
        QueryTransactionResultRequest request= new QueryTransactionResultRequest();
        request.setBankAccount("**********");
//        request.setValue("****************");
        request.setValue("****************");

        vietqrApiService.getRefundResult(request);
    }

    @Test
    public void vietQrRefund(){
        // 6
        so.dian.platform.vietqr.request.RefundRequest request= new so.dian.platform.vietqr.request.RefundRequest();
        request.setBankAccount("**********");
        request.setReferenceNumber("****************");
        request.setAmount("249000");
        request.setContent("vietqr refund202411051735");

        vietqrApiService.refund(request);
    }
    @Test
    public void genCode(){
        GenerateCodeRequest request= new GenerateCodeRequest();
        request.setAmount("20000");
        request.setContent("***************");
        request.setBankAccount("**********");
        request.setBankCode("MB");
        request.setUserBankName("Cong Ty Tnhh Kh Kt 100 Phan Tram Hn");
        request.setTransType("C");
        request.setOrderId("");
        request.setServiceCode("Chargebolt-8260");
        request.setQrType(3);
        request.setTerminalCode("***************");
        GenerateCodeResponse response= vietqrApiService.generateCode(request);
        log.info("{}", JsonUtil.beanToJson(response));
    }
    @Test
    public void genBatchCode(){
        File csv = new File("/Users/<USER>/workspaces/hera/hera-web/src/test/resources/DeviceNo-********-111.csv");  // CSV文件路径
        String outPath = "/Users/<USER>/workspaces/hera/hera-web/src/test/resources/DeviceNo-out-0719.csv";  // CSV文件路径
        BufferedReader br = null;
        try {
            br = new BufferedReader(new FileReader(csv));
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        String line = "";
        String everyLine = "";
        try {
            while ((line = br.readLine()) != null)  //读取到的内容给line变量
            {
                everyLine = line;
                String[] aaa= everyLine.split(",");
                if(aaa.length<2){continue;}
                String deviceUrl= aaa[0];
                String deviceNo= aaa[1];
                GenerateCodeRequest request= new GenerateCodeRequest();
                request.setAmount("249000");
                request.setContent(deviceNo);
                request.setBankAccount("**********");
                request.setBankCode("MB");
                request.setUserBankName("Cong Ty Tnhh Kh Kt 100 Phan Tram Hn");
                request.setTransType("C");
                request.setOrderId("");
                request.setServiceCode(request.getContent().substring(3));
                request.setQrType(3);
                request.setTerminalCode(deviceNo);
                GenerateCodeResponse response= vietqrApiService.generateCode(request);
                String vietqrCode= response.getQrCode();

                outFile(outPath, deviceUrl+","+deviceNo+","+vietqrCode);

            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void outFile(String filePath, String content){
        BufferedWriter out= null;
        try{
            out= new BufferedWriter(new OutputStreamWriter(new FileOutputStream(filePath, true)));
            out.write(content+"\n");
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            try {
                out.close();
            }catch (IOException e){
                e.printStackTrace();
            }
        }
    }
}