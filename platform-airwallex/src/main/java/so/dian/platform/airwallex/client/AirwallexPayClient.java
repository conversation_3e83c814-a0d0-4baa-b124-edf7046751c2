package so.dian.platform.airwallex.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import so.dian.platform.airwallex.dto.request.*;
import so.dian.platform.airwallex.dto.response.*;
import java.util.Map;

@SuppressWarnings("all")
@FeignClient(name = "airwallexPayClient", url = "${channel.airwallex.url}", fallbackFactory = AirwallexPayClient.AirwallexClientFallbackFactory.class)
public interface AirwallexPayClient {

    /**
     * 获取接口访问令牌
     * https://www.airwallex.com/docs/api#/Authentication/API_Access/_api_v1_authentication_login/post
     * @param header
     * @return
     */
    @PostMapping("/api/v1/authentication/login")
    AuthenticationResponse getToken(@RequestBody TokenRequest request,@RequestHeader Map header);

    /**
     * 创建支付单
     * https://www.airwallex.com/docs/api#/Payment_Acceptance/Payment_Intents/_api_v1_pa_payment_intents_create/post
     * @param request
     * @param header
     * @return
     */
    @PostMapping("/api/v1/pa/payment_intents/create")
    CreateResponse create(@RequestBody CreateRequest request, @RequestHeader Map header);

    /**
     * 查询支付单
     * https://www.airwallex.com/docs/api#/Payment_Acceptance/Payment_Intents/_api_v1_pa_payment_intents__id_/get
     * @param request
     * @param header
     * @return
     */
    @GetMapping("/api/v1/pa/payment_intents/{id}")
    RetrieveResponse orderQuery(@PathVariable("id") String id, @RequestHeader Map header);


    /**
     * 支付请款
     * https://www.airwallex.com/docs/api#/Payment_Acceptance/Payment_Intents/_api_v1_pa_payment_intents__id__capture/post
     * @param id create接口返回的ID
     * @param request
     * @param header
     * @return
     */
    @PostMapping("/api/v1/pa/payment_intents/{id}/capture")
    CaptureResponse capture(@PathVariable("id") String id,@RequestBody CaptureRequest request, @RequestHeader Map header);

    /**
     * 取消支付订单
     * https://www.airwallex.com/docs/api#/Payment_Acceptance/Payment_Intents/_api_v1_pa_payment_intents__id__cancel/post
     * @param id create接口返回的ID
     * @param request
     * @param header
     * @return
     */
    @PostMapping("/api/v1/pa/payment_intents/{id}/cancel")
    CancelResponse cancel(@PathVariable("id") String id, @RequestBody CancelRequest request, @RequestHeader Map header);

    /**
     * 退款申请
     * https://www.airwallex.com/docs/api#/Payment_Acceptance/Refunds/_api_v1_pa_refunds_create/post
     * @param request
     * @param header
     * @return
     */
    @PostMapping("/api/v1/pa/refunds/create")
    RefundResponse refund(@RequestBody RefundRequest request, @RequestHeader Map header);

    /**
     * 退款查询
     * https://www.airwallex.com/docs/api#/Payment_Acceptance/Refunds/_api_v1_pa_refunds__id_/get
     * @param request
     * @param header
     * @return
     */
    @GetMapping("/api/v1/pa/refunds/{id}")
    RefundQueryResponse refundQuery(@PathVariable("id") String id, @RequestHeader Map header);


    @Slf4j
    @Component
    public class AirwallexClientFallbackFactory implements FallbackFactory<AirwallexPayClient> {

        @Override
        public AirwallexPayClient create(Throwable cause) {
            return new AirwallexPayClient() {
                @Override
                public AuthenticationResponse getToken(TokenRequest request,Map header) {
                    log.warn("AirwallexPayClient getToken error. {}",cause);
                    return null;
                }

                @Override
                public CreateResponse create(CreateRequest request, Map header) {
                    log.warn("AirwallexPayClient create error. {}",cause);
                    return null;
                }

                @Override
                public RetrieveResponse orderQuery(String id, Map header) {
                    log.warn("AirwallexPayClient orderQuery error. {}",cause);
                    return null;
                }

                @Override
                public CaptureResponse capture(String id, CaptureRequest request, Map header) {
                    log.warn("AirwallexPayClient capture error. {}",cause);
                    return null;
                }

                @Override
                public CancelResponse cancel(String id, CancelRequest request, Map header) {
                    log.warn("AirwallexPayClient cancel error. {}",cause);
                    return null;
                }

                @Override
                public RefundResponse refund(RefundRequest request, Map header) {
                    log.warn("AirwallexPayClient refund error. {}",cause);
                    return null;
                }

                @Override
                public RefundQueryResponse refundQuery(String id, Map header) {
                    log.warn("AirwallexPayClient refund error. {}",cause);
                    return null;
                }

            };
        }
    }

}
