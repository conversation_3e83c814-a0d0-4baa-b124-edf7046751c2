package so.dian.platform.airwallex.common.enmus;

import com.chargebolt.hera.client.enums.PayMethodEnum;
import lombok.Getter;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;

/**
 * <AUTHOR>
 */

@Getter
public enum AirwallexPayMethodEnum {


    MASTERCARD("Mastercard", PayMethodEnum.MasterCard),
    VISA("VISA", PayMethodEnum.VISA),

    GOOGLE_PAY("GooglePay", PayMethodEnum.GooglePay),
    APPLE_PAY("ApplePay", PayMethodEnum.ApplePay),

    ;
    private String method;
    private PayMethodEnum payMethodEnum;

    AirwallexPayMethodEnum(String method, PayMethodEnum payMethodEnum) {
        this.method = method;
        this.payMethodEnum = payMethodEnum;
    }

    public static AirwallexPayMethodEnum explainName(String name) {
        for (AirwallexPayMethodEnum pingpongPayMethodEnum : AirwallexPayMethodEnum.values()) {
            if (pingpongPayMethodEnum.name().equals(name)) {
                return pingpongPayMethodEnum;
            }
        }
        throw HeraBizException.create(HeraBizErrorCodeEnum.ILLEGAL_ERROR);
    }

    public static AirwallexPayMethodEnum explainMethod(String method) {
        for (AirwallexPayMethodEnum pingpongPayMethodEnum : AirwallexPayMethodEnum.values()) {
            if (pingpongPayMethodEnum.getMethod().equals(method)) {
                return pingpongPayMethodEnum;
            }
        }
        throw HeraBizException.create(HeraBizErrorCodeEnum.ILLEGAL_ERROR);
    }

    public static AirwallexPayMethodEnum explain(PayMethodEnum payMethodEnum) {
        for (AirwallexPayMethodEnum pingpongPayMethodEnum : AirwallexPayMethodEnum.values()) {
            if (pingpongPayMethodEnum.getPayMethodEnum().equals(payMethodEnum)) {
                return pingpongPayMethodEnum;
            }
        }
        throw HeraBizException.create(HeraBizErrorCodeEnum.ILLEGAL_ERROR);
    }
}
