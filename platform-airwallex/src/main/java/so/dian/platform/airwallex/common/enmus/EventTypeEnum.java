package so.dian.platform.airwallex.common.enmus;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EventTypeEnum {


    /**
     * 交易订单-已创建
     */
    PAYMENT_INTENT_CREATED("payment_intent.created"),
    /**
     * 交易订单-已取消
     */
    PAYMENT_INTENT_CANCELLED("payment_intent.cancelled"),
    /**
     * 交易请求-交易验证失败
     */
    PAYMENT_ATTEMPT_AUTHENTICATION_FAILED("payment_attempt.authentication_failed"),
    /**
     * 交易请求-预授权失败
     */
    PAYMENT_ATTEMPT_AUTHORIZATION_FAILED("payment_attempt.authorization_failed"),
    /**
     * 交易请求-预授权成功
     */
    PAYMENT_ATTEMPT_AUTHORIZED("payment_attempt.authorized"),
    /**
     * 交易请求-已取消
     */
    PAYMENT_ATTEMPT_CANCELLED("payment_attempt.cancelled"),

    /**
     * 请款支付-请款请求
     */
    PAYMENT_ATTEMPT_CAPTURE_REQUESTED("payment_attempt.capture_requested"),

    /**
     * 请款支付-已支付
     */
    PAYMENT_ATTEMPT_PAID("payment_attempt.paid"),

    /**
     *  TODO 跟空中云汇开发沟通该事件需要怎么处理
     * 交易请求-请款失败
     */
    PAYMENT_ATTEMPT_CAPTURE_FAILED("payment_attempt.capture_failed"),

    /**
     * 交易订单-已支付(请款成功)
     */
    PAYMENT_INTENT_SUCCEEDED("payment_intent.succeeded"),

    /**
     * 退款-已退款
     */
    REFUND_SUCCEEDED("refund.succeeded"),
    /**
     * 退款-退款失败
     */
    REFUND_FAILED("refund.failed"),
    ;

    private String eventType;
}
