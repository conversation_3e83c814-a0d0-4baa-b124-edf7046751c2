package so.dian.platform.airwallex.common.enmus;

import lombok.Getter;

@Getter
public enum PayStatusEnum {

    /**
     * PaymentIntent 正在等待确认请求。
     * 在创建 PaymentIntent 或之前的 PaymentAttempt 失败或过期后，会立即返回此状态。
     */
    REQUIRES_PAYMENT_METHOD,

    /**
     * PaymentIntent 正在等待客户执行进一步的身份验证、e.g. 3DS验证和 QR 码扫描操作。请查看 next_action。
     */
    REQUIRES_CUSTOMER_ACTION,

    /**
     * PaymentIntent 正在等待您的捕获完成付款。
     */
    REQUIRES_CAPTURE,

    /**
     * 待定
     * PaymentIntent 正在等待提供程序的最终结果。无需进一步操作。
     */
    PENDING,

    /**
     * PaymentIntent 已成功。付款已完成。
     */
    SUCCEEDED,

    /**
     * 取消
     * 您的请求已取消 PaymentIntent。付款已关闭。
     */
    CANCELLED

}
