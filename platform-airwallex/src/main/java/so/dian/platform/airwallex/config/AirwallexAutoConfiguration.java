package so.dian.platform.airwallex.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import so.dian.commons.eden.util.LogColorUtils;

/**
 * <AUTHOR>
 */
@Configuration
@ComponentScan("so.dian.platform.airwallex.*")
@ConditionalOnProperty(prefix = "channel.enable", name = "airwallex", havingValue = "true")
@Slf4j
public class AirwallexAutoConfiguration {

    static {
        log.info(LogColorUtils.blueMessage("Airwallex enable"));
    }
}
