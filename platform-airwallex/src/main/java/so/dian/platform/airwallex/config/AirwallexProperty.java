package so.dian.platform.airwallex.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import so.dian.platform.airwallex.common.enmus.AirwallexPayMethodEnum;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 开发者平台：https://www.airwallex.com/docs/api#/Payment_Acceptance/Payment_Intents/
 */
@Data
@Component
@SuppressWarnings("all")
@ConfigurationProperties(prefix = "channel.airwallex")
public class AirwallexProperty {

    /**
     * 网关端点（地址），取自开发者平台
     */
    private String url;
    /**
     * Client ID，取自开发者平台
     */
    private String clientId;
    /**
     * 商户私钥，取自开发者平台
     */
    private String apiKey;

    /**
     * 银行卡支付支持的支付方式
     */
    private List<AirwallexPayMethodEnum> paymentMethodsOfCard;

    /**
     * 支付结果url
     */
    private PayUrl payResultUrl;

    /**
     * 国家地区
     */
    private String regionCode;

    /**
     * 商户ID
     */
    private String merchantId;

    /**
     * 商户名称
     */
    private String merchantName;

    /**
     * 按钮文案
     */
    private String buttonType;

    /**
     * 按钮颜色
     */
    private String buttonColor;



    @Data
    public static class PayUrl {
        private String h5;
    }

    public void setPaymentMethodsOfCard(List<String> paymentMethodsOfCard) {
        this.paymentMethodsOfCard = paymentMethodsOfCard.stream().map(s -> AirwallexPayMethodEnum.explainName(s)).collect(Collectors.toList());
    }

}
