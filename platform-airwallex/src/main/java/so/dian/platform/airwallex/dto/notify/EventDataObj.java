package so.dian.platform.airwallex.dto.notify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class EventDataObj {

    private String id;
    private String status;
    private String amount;
    private String currency;
    @JsonProperty("created_at")
    private String createdAt;
    @JsonProperty("updated_at")
    private String updatedAt;
    @JsonProperty("payment_intent_id")
    private String paymentIntentId;

}
