package so.dian.platform.airwallex.dto.notify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class PaymentAttemptEventDataObject extends EventDataObj {

    @JsonProperty("payment_intent_id")
    private String paymentIntentId;
    @JsonProperty("payment_consent_id")
    private String paymentConsentId;
    @JsonProperty("captured_amount")
    private String capturedAmount;
    @JsonProperty("refunded_amount")
    private String refundedAmount;
    @JsonProperty("merchant_order_id")
    private String merchantOrderId;

}
