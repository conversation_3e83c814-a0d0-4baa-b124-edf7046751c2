package so.dian.platform.airwallex.dto.notify;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class PaymentIntentEventDataObject extends EventDataObj{

    @JsonProperty("merchant_order_id")
    private String merchantOrderId;
    @JsonProperty("base_amount")
    private Long baseAmount;
    @JsonProperty("captured_amount")
    private Long capturedAmount;
    @JsonProperty("request_id")
    private String requestId;
    @JsonProperty("payment_method")
    private PaymentMethod paymentMethod;

}
