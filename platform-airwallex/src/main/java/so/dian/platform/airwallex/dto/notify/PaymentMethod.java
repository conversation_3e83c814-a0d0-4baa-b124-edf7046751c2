package so.dian.platform.airwallex.dto.notify;

import lombok.Data;

@Data
public class PaymentMethod {

    private String type;
}

/**
 * {
 *           "card": {
 *             "billing": {
 *               "address": {
 *                 "city": "Shanghai",
 *                 "country_code": "CN",
 *                 "postcode": "201304",
 *                 "state": "Shanghai",
 *                 "street": "Pudong District"
 *               },
 *               "date_of_birth": "2011-10-12",
 *               "email": "<EMAIL>",
 *               "first_name": "john",
 *               "last_name": "<PERSON>",
 *               "phone_number": "*************"
 *             },
 *             "bin": "********",
 *             "brand": "mastercard",
 *             "card_type": "CREDIT",
 *             "cvc_check": "pass",
 *             "expiry_month": "08",
 *             "expiry_year": "2025",
 *             "fingerprint": "4fcJ7fIZiTB+0KkLUbxlw2BbON8=",
 *             "is_commercial": false,
 *             "issuer_country_code": "IE",
 *             "issuer_name": "ALLIED IRISH BANKS, PLC",
 *             "last4": "9903",
 *             "name": "Auto Testing",
 *             "number_type": "PAN"
 *           },
 *           "type": "card"
 *         }
 */