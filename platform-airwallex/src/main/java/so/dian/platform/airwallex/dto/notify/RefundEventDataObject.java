package so.dian.platform.airwallex.dto.notify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class RefundEventDataObject extends EventDataObj{

    @JsonProperty("request_id")
    private String requestId;
    @JsonProperty("payment_attempt_id")
    private String paymentAttemptId;
    @JsonProperty("payment_intent_id")
    private String paymentIntentId;
    private String reason;


}
