package so.dian.platform.airwallex.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CreateRequest {

    @JsonProperty("request_id")
    private String requestId;

    @JsonProperty("amount")
    private String amount;

    @JsonProperty("currency")
    private String currency;

    @JsonProperty("payment_method_options")
    private PaymentMethodOption paymentMethodOption;

    /**
     * 将向客户显示的描述符。最大长度为 32。
     */
    @JsonProperty("descriptor")
    private String descriptor;

    /**
     * 在商户订单系统中创建的与此 PaymentIntent 对应的订单 ID。最大长度为 64。
     */
    @JsonProperty("merchant_order_id")
    private String merchantOrderId;

    @JsonProperty("return_url")
    private String returnUrl;

    @NoArgsConstructor
    @Data
    public static class PaymentMethodOption {

        @JsonProperty("card")
        private Card card;

        @NoArgsConstructor
        @Data
        public static class Card {
            /**
             * 卡付款的授权类型。final_auth之一，pre_auth。
             * 仅在提供 payment_method 时适用。
             * 默认为 final_auth。目前仅在品牌为 visa 或 mastercard 时可用。
             */
            @JsonProperty("authorization_type")
            private String authorizationType;

            /**
             * 指定在付款授权后是否应自动申请资金。仅在提供 payment_method 时适用。默认为 true。
             */
            @JsonProperty("auto_capture")
            private boolean autoCapture;

        }

    }


}
