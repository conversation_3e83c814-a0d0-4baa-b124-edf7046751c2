package so.dian.platform.airwallex.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class RefundRequest {

    @JsonProperty("payment_intent_id")
    private String paymentIntentId;

    @JsonProperty("request_id")
    private String requestId;

    @JsonProperty("amount")
    private String amount;

    /**
     * 非必填
     */
    @JsonProperty("reason")
    private String reason;
}
