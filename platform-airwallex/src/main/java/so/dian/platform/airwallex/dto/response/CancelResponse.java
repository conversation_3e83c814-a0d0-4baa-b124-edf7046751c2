package so.dian.platform.airwallex.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CancelResponse {

    @JsonProperty("id")
    private String id;

    @JsonProperty("request_id")
    private String requestId;

    /**
     * SUCCEEDED
     */
    @JsonProperty("status")
    private String status;

    @JsonProperty("updated_at")
    private String updatedAt;

}
