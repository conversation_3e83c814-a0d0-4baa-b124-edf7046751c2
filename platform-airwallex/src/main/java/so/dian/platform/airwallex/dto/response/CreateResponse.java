package so.dian.platform.airwallex.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CreateResponse {

    @JsonProperty("id")
    private String id;

    @JsonProperty("request_id")
    private String requestId;

    @JsonProperty("amount")
    private Integer amount;

    @JsonProperty("currency")
    private String currency;

    /**
     * 将向客户显示的描述符。最大长度为 32。
     */
    @JsonProperty("descriptor")
    private String descriptor;

    /**
     * 在商户订单系统中创建的与此 PaymentIntent 对应的订单 ID。最大长度为 64。
     */
    @JsonProperty("merchant_order_id")
    private String merchantOrderId;

    /**
     * 订单状态
     */
    @JsonProperty("status")
    private String status;

    @JsonProperty("captured_amount")
    private Integer capturedAmount;

    @JsonProperty("created_at")
    private String createdAt;

    @JsonProperty("updated_at")
    private String updatedAt;

    @JsonProperty("available_payment_method_types")
    private List<String> available_payment_method_types;

    /**
     * 前端拉起sdk参数
     */
    @JsonProperty("client_secret")
    private String clientSecret;

    @JsonProperty("base_amount")
    private Integer baseAmount;

    @JsonProperty("base_currency")
    private String baseCurrency;

    @JsonProperty("return_url")
    private String returnUrl;

}
