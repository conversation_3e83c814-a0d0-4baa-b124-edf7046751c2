package so.dian.platform.airwallex.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import so.dian.platform.airwallex.common.enmus.RefundStatusEnum;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class RefundQueryResponse {

    /**
     * 退款的唯一标识符
     */
    @JsonProperty("id")
    private String id;

    /**
     * 一个唯一编号，用于标记信用卡或借记卡交易从商家的银行到持卡人的银行。
     */
    @JsonProperty("acquirer_reference_number")
    private String acquirerReferenceNumber;

    /**
     * 商户在上次操作中指定的唯一请求 ID
     */
    @JsonProperty("request_id")
    private String requestId;

    @JsonProperty("amount")
    private Integer amount;

    @JsonProperty("currency")
    private String currency;

    /**
     * 创建 Refund 的 PaymentIntent 的 ID
     */
    @JsonProperty("payment_intent_id")
    private String paymentIntentId;

    /**
     * 退款原因
     */
    @JsonProperty("reason")
    private String reason;

    /**
     * 退款状态
     * @see RefundStatusEnum
     */
    @JsonProperty("status")
    private String status;

    /**
     * 上次更新退款的时间
     */
    @JsonProperty("updated_at")
    private String updatedAt;



}
