package so.dian.platform.airwallex.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import so.dian.platform.airwallex.common.enmus.PayStatusEnum;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class RetrieveResponse {

    /**
     * 退款的唯一标识符
     */
    @JsonProperty("id")
    private String id;

    /**
     * 商户在上次操作中指定的唯一请求 ID
     */
    @JsonProperty("request_id")
    private String requestId;

    @JsonProperty("amount")
    private Integer amount;

    @JsonProperty("captured_amount")
    private Integer capturedAmount;

    @JsonProperty("currency")
    private String currency;

    /**
     * 创建此 PaymentIntent 的时间
     */
    @JsonProperty("created_at")
    private String createdAt;

    /**
     * 上次更新或操作此 PaymentIntent 的时间
     */
    @JsonProperty("updated_at")
    private String updatedAt;

    /**
     * 为此 PaymentIntent 付款的 Customer 的 ID
     */
    @JsonProperty("customer_id")
    private String customerId;

    /**
     * 在商户订单系统中创建的与此 PaymentIntent 对应的订单 ID
     */
    @JsonProperty("merchant_order_id")
    private String merchantOrderId;

    /**
     * 支付状态
     * @see PayStatusEnum
     */
    @JsonProperty("status")
    private String status;
}
