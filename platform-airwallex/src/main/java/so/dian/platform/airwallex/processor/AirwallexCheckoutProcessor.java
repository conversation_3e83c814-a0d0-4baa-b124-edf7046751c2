package so.dian.platform.airwallex.processor;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.checkout.req.PreAuthCaptureRequest;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PreAuthCancelResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PreAuthCaptureResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PrepayCreateResultDTO;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundResultDTO;
import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.client.enums.PayMethodEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.hera.interceptor.CheckoutPayProcessor;
import so.dian.hera.interceptor.QueryPayProcessor;
import so.dian.hera.interceptor.RefundProcessor;
import so.dian.hera.interceptor.dto.req.PayQueryResultRequest;
import so.dian.hera.interceptor.dto.req.ProcessorRefundRequest;
import so.dian.hera.interceptor.dto.req.RefundQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;
import so.dian.hera.interceptor.dto.rsp.RefundQueryResultDTO;
import so.dian.hera.interceptor.utils.DateUtil;
import so.dian.platform.airwallex.client.AirwallexPayClient;
import so.dian.platform.airwallex.common.enmus.PayStatusEnum;
import so.dian.platform.airwallex.common.enmus.RefundStatusEnum;
import so.dian.platform.airwallex.config.AirwallexProperty;
import so.dian.platform.airwallex.dto.request.CancelRequest;
import so.dian.platform.airwallex.dto.request.CaptureRequest;
import so.dian.platform.airwallex.dto.request.CreateRequest;
import so.dian.platform.airwallex.dto.request.RefundRequest;
import so.dian.platform.airwallex.dto.response.*;
import so.dian.platform.airwallex.service.TokenService;
import so.dian.platform.common.configuration.redis.RedisClient;
import so.dian.platform.common.utils.MoneyUtil;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@SuppressWarnings("all")
public class AirwallexCheckoutProcessor implements CheckoutPayProcessor, RefundProcessor,QueryPayProcessor {

    @Resource
    private RedisClient redisClient;

    @Resource
    private TokenService tokenService;

    @Resource
    private AirwallexProperty airwallexProperty;

    @Resource
    private AirwallexPayClient airwallexPayClient;

    private HashMap builderHeader(){
        HashMap header = new HashMap<>(2);
        String authorization = "Bearer " + tokenService.getToken();
        header.put("Authorization",authorization);
        header.put("Content-Type","application/json");
        return header;
    }

    @Override
    public PrepayCreateResultDTO doPrePay(PrepayCreateRequest prePayReq, PaymentDO paymentDO) {
        PrepayCreateRequest.PrepayCreateExtInfo extInfo = prePayReq.getExtInfo();
        CreateRequest request = new CreateRequest();
        request.setAmount(MoneyUtil.fen2Yuan(prePayReq.getPayAmount(), prePayReq.getCurrency()));
        request.setCurrency(prePayReq.getCurrency());
        request.setDescriptor(prePayReq.getRemark());
        CreateRequest.PaymentMethodOption paymentMethodOption = new CreateRequest.PaymentMethodOption();
        CreateRequest.PaymentMethodOption.Card card = new CreateRequest.PaymentMethodOption.Card();
        card.setAutoCapture(false);
        paymentMethodOption.setCard(card);
        request.setPaymentMethodOption(paymentMethodOption);
        request.setRequestId(paymentDO.getTradeNo());
        request.setMerchantOrderId(paymentDO.getTradeNo());
        // 用户完成支付后被重定向到的商户页面链接
        String payResultUrlFromRequest = extInfo.getPayResult();
        String paymentRedirectUrl = StringUtils.isBlank(payResultUrlFromRequest) ? airwallexProperty.getPayResultUrl().getH5() : payResultUrlFromRequest;
        request.setReturnUrl(paymentRedirectUrl + "?deviceNo=" + extInfo.getDeviceNo() + "&merchantTransactionId=" + paymentDO.getTradeNo());
        log.info("空中云汇订单创建请求 request: {}", JSON.toJSONString(request));
        CreateResponse response = null;
        try {
            response = airwallexPayClient.create(request,builderHeader());
            log.info("空中云汇订单创建应答 response: {},tradeNo: {}", JSON.toJSONString(response),paymentDO.getTradeNo());
        }catch (Exception ex){
            log.error("空中云汇订单创建应答异常 tradeNo: {},errMsg: {}",paymentDO.getTradeNo(),ex.getMessage());
            throw BizException.create(ErrorCodeEnum.FALLBACK, "调用airwallex接口失败");
        }
        if(Objects.isNull(response)){
            log.error("空中云汇订单创建应答为空 tradeNo: {}",paymentDO.getTradeNo());
            throw BizException.create(ErrorCodeEnum.FALLBACK, "调用airwallex接口失败");
        }

        PrepayCreateResultDTO prepayCreateResultDTO = new PrepayCreateResultDTO();
        HashMap resultMap = Maps.newHashMap();
        // 前端sdk拉起收银台公共参数
        resultMap.put("intentId", response.getId());
        resultMap.put("clientSecret", response.getClientSecret());
        resultMap.put("currency", prePayReq.getCurrency());
        resultMap.put("amount", prePayReq.getPayAmount());
        PayMethodEnum payMethodEnum = PayMethodEnum.explain(extInfo.getPayMethod());
        // 银行卡支付前端sdk拉起收银台参数
        if(Objects.equals(payMethodEnum, PayMethodEnum.VISA) ||
                Objects.equals(payMethodEnum, PayMethodEnum.MasterCard)){
            resultMap.put("methods",Lists.newArrayList("card"));
        }
        // GooglePay&ApplePay支付前端sdk拉起收银台参数
        if(Objects.equals(payMethodEnum, PayMethodEnum.GooglePay)){
            HashMap googlePayRequestOptions = Maps.newHashMap();
            googlePayRequestOptions.put("countryCode",airwallexProperty.getRegionCode());
            HashMap merchantInfo = Maps.newHashMap();
            merchantInfo.put("merchantName",airwallexProperty.getMerchantName());
            merchantInfo.put("merchantId",airwallexProperty.getMerchantId());
            googlePayRequestOptions.put("merchantInfo",merchantInfo);
            resultMap.put("googlePayRequestOptions",googlePayRequestOptions);
            resultMap.put("methods",Lists.newArrayList("googlepay"));
        }
        if(Objects.equals(payMethodEnum, PayMethodEnum.ApplePay)){
            HashMap applePayRequestOptions = Maps.newHashMap();
            applePayRequestOptions.put("countryCode", airwallexProperty.getRegionCode());
            applePayRequestOptions.put("buttonType", airwallexProperty.getButtonType());
            applePayRequestOptions.put("buttonColor", airwallexProperty.getButtonColor());
            resultMap.put("applePayRequestOptions",applePayRequestOptions);
            resultMap.put("methods",Lists.newArrayList("applepay"));
        }
        prepayCreateResultDTO.setPrepayResult(resultMap);
        prepayCreateResultDTO.setTradeNo(paymentDO.getTradeNo());
        prepayCreateResultDTO.setPayNo(response.getId());
        prepayCreateResultDTO.setStatus(TransStatusEnum.SUCCESS.getKey());
        return prepayCreateResultDTO;
    }

    @Override
    public PreAuthCancelResultDTO doCancel(PaymentDO paymentDO) {
        CancelRequest request = new CancelRequest();
        request.setRequestId(UUID.randomUUID().toString());
        log.info("空中云汇订单取消请求 request: {}", JSON.toJSONString(request));
        CancelResponse response = null;
        try {
            response = airwallexPayClient.cancel(paymentDO.getPayNo(),request,builderHeader());
            log.info("空中云汇订单取消应答 response: {},tradeNo: {}", JSON.toJSONString(response),paymentDO.getTradeNo());
        }catch (Exception ex){
            log.error("空中云汇订单取消应答异常 tradeNo: {},errMsg: {}",paymentDO.getTradeNo(),ex.getMessage());
            throw BizException.create(ErrorCodeEnum.FALLBACK, "调用airwallex接口失败");
        }
        if(Objects.isNull(response)){
            log.error("空中云汇订单取消应答为空 tradeNo: {}",paymentDO.getTradeNo());
            throw BizException.create(ErrorCodeEnum.FALLBACK, "调用airwallex接口失败");
        }
        PreAuthCancelResultDTO resultDTO = new PreAuthCancelResultDTO();
        resultDTO.setSuccess(true);
        resultDTO.setStatus(TransStatusEnum.SUCCESS.getKey());
        return resultDTO;
    }

    @Override
    public PreAuthCaptureResultDTO doCapture(PreAuthCaptureRequest preauthCaptureRequest, String captureId, PaymentDO capturePaymentDO) {
        CaptureRequest request = new CaptureRequest();
        request.setRequestId(UUID.randomUUID().toString());
        request.setAmount(MoneyUtil.fen2Yuan(capturePaymentDO.getPayAmount(), capturePaymentDO.getCurrency()));
        request.setCurrency(capturePaymentDO.getCurrency());
        log.info("空中云汇订单请款请求 request: {}", JSON.toJSONString(request));
        CaptureResponse response = null;
        try {
            response = airwallexPayClient.capture(captureId, request, builderHeader());
            log.info("空中云汇订单请款应答 response: {},tradeNo: {}", JSON.toJSONString(response),capturePaymentDO.getTradeNo());
        }catch (Exception ex){
            log.error("空中云汇请款应答异常 captureId: {},tradeNo: {},errMsg: {}",captureId,capturePaymentDO.getTradeNo(),ex.getMessage());
            throw BizException.create(ErrorCodeEnum.FALLBACK, "调用airwallex接口失败");
        }
        if(Objects.isNull(response)){
            log.error("空中云汇请款应答为空 captureId: {},tradeNo: {}",captureId,capturePaymentDO.getTradeNo());
            throw BizException.create(ErrorCodeEnum.FALLBACK, "调用airwallex接口失败");
        }
        PreAuthCaptureResultDTO resultDTO = new PreAuthCaptureResultDTO();
        resultDTO.setSuccess(true);
        resultDTO.setStatus(TransStatusEnum.SUCCESS.getKey());
        return resultDTO;
    }

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.AIRWALLEX_CHECKOUT_CARD);
    }

    @Override
    public RefundResultDTO refund(ProcessorRefundRequest refundRequest) {
        RefundRequest request = new RefundRequest();
        request.setReason(refundRequest.getReason());
        request.setAmount(MoneyUtil.fen2Yuan(refundRequest.getRefundAmount(), refundRequest.getCurrency()));
        request.setRequestId(refundRequest.getRefundNo());
        request.setPaymentIntentId(refundRequest.getPayNo());
        log.info("空中云汇订单退款请求 request: {}", JSON.toJSONString(request));
        RefundResponse response = null;
        try {
            response = airwallexPayClient.refund(request, builderHeader());
            log.info("空中云汇订单退款应答 response: {},refundNo: {},paymentIntentId: {}",
                    JSON.toJSONString(response),refundRequest.getRefundNo(),refundRequest.getPayNo());
        }catch (Exception ex){
            log.error("空中云汇订单退款应答异常 refundNo: {},payNo: {},errMsg: {}",refundRequest.getRefundNo(),refundRequest.getPayNo(),ex.getMessage());
            throw BizException.create(ErrorCodeEnum.FALLBACK, "调用airwallex接口失败");
        }
        if(Objects.isNull(response)){
            log.error("空中云汇订单退款应答为空 refundNo: {},payNo: {}",refundRequest.getRefundNo(),refundRequest.getPayNo());
            throw BizException.create(ErrorCodeEnum.FALLBACK, "调用airwallex接口失败");
        }
        RefundResultDTO resultDTO = new RefundResultDTO();
        resultDTO.setSuccess(true);
        resultDTO.setStatus(TransStatusEnum.SUCCESS.getKey());
        resultDTO.setRefundPayNo(response.getId());
        return resultDTO;
    }

    public RefundQueryResultDTO refundQuery(RefundQueryResultRequest queryResultRequest) {
        RefundQueryResponse refundQueryResponse = null;
        try {
            refundQueryResponse = airwallexPayClient.refundQuery(queryResultRequest.getOutTraceNo(), builderHeader());
            log.info("空中云汇退款订单退款查询应答 response: {},refundNo: {},outTraceNo: {}",
                    JSON.toJSONString(refundQueryResponse),queryResultRequest.getRefundNo(),queryResultRequest.getOutTraceNo());
        }catch (Exception ex){
            log.error("空中云汇退款订单退款查询应答异常 refundNo: {},outTraceNo: {},errMsg: {}", queryResultRequest.getRefundNo(),queryResultRequest.getOutTraceNo(),ex.getMessage());
            throw BizException.create(ErrorCodeEnum.FALLBACK, "调用airwallex接口失败");
        }
        if (Objects.isNull(refundQueryResponse)) {
            log.error("空中云汇退款订单退款查询应答为空 refundNo: {},outTraceNo: {}", queryResultRequest.getRefundNo(),queryResultRequest.getOutTraceNo());
            throw BizException.create(ErrorCodeEnum.FALLBACK, "调用airwallex接口失败");
        }
        RefundQueryResultDTO refundQueryResultDTO = new RefundQueryResultDTO();
        refundQueryResultDTO.setRefundNo(queryResultRequest.getRefundNo());
        refundQueryResultDTO.setOutTraceNo(refundQueryResponse.getId());
        if (Objects.equals(refundQueryResponse.getStatus(),RefundStatusEnum.SUCCEEDED.name())) {
            refundQueryResultDTO.setStatus(PayStatus.REFUNDED.getCode());
            refundQueryResultDTO.setRefundTime(DateUtil.transferDateV2(refundQueryResponse.getUpdatedAt()));
        } else if (Objects.equals(refundQueryResponse.getStatus(),RefundStatusEnum.FAILED.name())) {
            refundQueryResultDTO.setStatus(PayStatus.FAIL.getCode());
        } else if (Objects.equals(refundQueryResponse.getStatus(),RefundStatusEnum.RECEIVED.name())
                || Objects.equals(refundQueryResponse.getStatus(),RefundStatusEnum.ACCEPTED.name())) {
            refundQueryResultDTO.setStatus(PayStatus.REFUNDING.getCode());
        } else {
            log.warn("未知退款状态 refundNo: {},status: {}", queryResultRequest.getRefundNo(), refundQueryResponse.getStatus());
        }
        return refundQueryResultDTO;
    }

    @Override
    public PayQueryResultDTO orderPayQuery(PayQueryResultRequest request) {
        RetrieveResponse response = null;
        try {
            response = airwallexPayClient.orderQuery(request.getPayNo(),builderHeader());
            log.info("空中云汇订单查询应答 response: {},paymentIntentId: {}", JSON.toJSONString(response),request.getPayNo());
        }catch (Exception ex){
            log.error("空中云汇退款订单查询应答异常 payNo: {},errMsg: {}", request.getPayNo(),ex.getMessage());
            throw BizException.create(ErrorCodeEnum.FALLBACK, "调用airwallex接口失败");
        }
        if(Objects.isNull(response)){
            log.error("空中云汇退款订单查询应答为空 payNo: {}",request.getPayNo());
            throw BizException.create(ErrorCodeEnum.FALLBACK, "调用airwallex接口失败");
        }
        PayQueryResultDTO resultDTO = new PayQueryResultDTO();
        resultDTO.setPayNo(request.getPayNo());
        resultDTO.setTradeNo(request.getTradeNo());
        if(Objects.equals(response.getStatus(), PayStatusEnum.SUCCEEDED.name())){
            resultDTO.setStatus(PayStatus.PAID.getCode());
            if(StringUtils.isNotBlank(response.getUpdatedAt())){
                resultDTO.setPayTime(DateUtil.transferDateV2(response.getUpdatedAt()));
            }
        // 取消当作失败处理，正常情况下 不会出现取消的情况，只有请款一直请款不到才能出现（订单授权成功后30天内未请款成功，三方订单会取消）
        } else if (Objects.equals(response.getStatus(), PayStatusEnum.CANCELLED.name())) {
            resultDTO.setStatus(PayStatus.FAIL.getCode());
            resultDTO.setResponseMsg("订单已取消");
        } else{
            resultDTO.setStatus(PayStatus.INIT.getCode());
        }
        return resultDTO;
    }

    @Override
    public ChannelEnum getRefundChannel() {
        return ChannelEnum.AIRWALLEX;
    }

}
