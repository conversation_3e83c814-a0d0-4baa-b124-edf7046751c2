package so.dian.platform.airwallex.processor;

import com.chargebolt.hera.client.enums.PaywayEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.QueryRefundProcessor;
import so.dian.hera.interceptor.dto.req.RefundQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.RefundQueryResultDTO;
import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class AirwallexRefundProcessor implements QueryRefundProcessor {

    @Resource
    private AirwallexCheckoutProcessor airwallexCheckoutProcessor;

    @Override
    public RefundQueryResultDTO orderRefundQuery(RefundQueryResultRequest queryResultRequest) {
        return airwallexCheckoutProcessor.refundQuery(queryResultRequest);
    }

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.AIRWALLEX_CHECKOUT_CARD);
    }
}
