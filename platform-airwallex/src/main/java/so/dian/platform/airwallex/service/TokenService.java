package so.dian.platform.airwallex.service;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.platform.airwallex.client.AirwallexPayClient;
import so.dian.platform.airwallex.config.AirwallexProperty;
import so.dian.platform.airwallex.dto.request.TokenRequest;
import so.dian.platform.airwallex.dto.response.AuthenticationResponse;
import so.dian.platform.common.configuration.redis.RedisClient;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import static so.dian.platform.common.enums.CacheEnum.AIRWALLEX_TOKEN;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class TokenService {

    @Resource
    private RedisClient redisClient;

    @Resource
    private AirwallexProperty airwallexProperty;

    @Resource
    private AirwallexPayClient airwallexPayClient;

    public String getToken(){
        String token = redisClient.get(AIRWALLEX_TOKEN.ns,airwallexProperty.getRegionCode(), String.class);
        if(StringUtils.isNotBlank(token)){
            return token;
        }
        Map header = new HashMap<>();
        header.put("x-client-id",airwallexProperty.getClientId());
        header.put("x-api-key",airwallexProperty.getApiKey());
        header.put("Content-Type","application/json");
        AuthenticationResponse response = null;
        try{
            // FeignClient post请求方式一定需要加请求体（无任何意义），否则会报错
            TokenRequest request = new TokenRequest();
            request.setBody("body");
            response = airwallexPayClient.getToken(request,header);
        }catch (Exception ex){
            log.error("调用airwallex接口获取token异常",ex);
            throw BizException.create(ErrorCodeEnum.FALLBACK, "调用airwallex接口失败");
        }
        log.info("获取空中云汇接口令牌 token response: {}", JSON.toJSONString(response));
        token = response.getToken();
        redisClient.set(AIRWALLEX_TOKEN.ns,airwallexProperty.getRegionCode(),token,AIRWALLEX_TOKEN.expiredTime);
        return token;
    }

}
