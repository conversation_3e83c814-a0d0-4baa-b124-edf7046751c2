<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>chargebolt-hera</artifactId>
        <groupId>com.chargebolt.hera</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>platform-antom</artifactId>
    <version>${platform.antom.version}</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.chargebolt</groupId>
            <artifactId>hera-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chargebolt.hera</groupId>
            <artifactId>hera-interceptor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chargebolt.hera</groupId>
            <artifactId>platform-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
            <version>${springboot.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <version>${springboot.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
            <version>${springboot.version}</version>
        </dependency>
        <!-- antom APM支付|银行卡支付sdk,更新版本： https://github.com/alipay/global-open-sdk-java -->
        <dependency>
            <groupId>com.alipay.global.sdk</groupId>
            <artifactId>global-open-sdk-java</artifactId>
            <version>2.0.44</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>22.0</version>
        </dependency>
    </dependencies>
</project>
