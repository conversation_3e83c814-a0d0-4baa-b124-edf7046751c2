package so.dian.platform.antom.common.enmus;

import com.alipay.global.api.model.Result;
import com.alipay.global.api.model.ResultStatusType;
import lombok.Getter;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Getter
public enum AlipayResultStatusEnum {

    /**
     * 成功
     */
    SUCCESS,

    /**
     * 授权处理中
     */
    PAYMENT_IN_PROCESS,

    /**
     * 失败
     */
    ERROR,

    /**
     * 请款处理中
     */
    CAPTURE_IN_PROCESS,

    /**
     * 退款处理中
     */
    REFUND_IN_PROCESS,

    ;

    public static boolean isSuccess(Result result){
        return Objects.equals(result.getResultCode(),SUCCESS.name()) && Objects.equals(result.getResultStatus(),ResultStatusType.S);
    }

    public static boolean isProcessing(Result result) {
        return Objects.equals(result.getResultCode(), PAYMENT_IN_PROCESS.name())
                && Objects.equals(result.getResultStatus(), ResultStatusType.U);
    }

    /**
     * 请款申请成功
     * @param result
     * @return
     */
    public static boolean isCaptureSuccess(Result result){
        // 成功或者处理中 返回给业务方为 申请成功（具体结果以 请款通知接口 结果为准）
        return (Objects.equals(result.getResultCode(),SUCCESS.name()) || Objects.equals(result.getResultCode(),CAPTURE_IN_PROCESS.name()))
                && Objects.equals(result.getResultStatus(),ResultStatusType.S);
    }

    /**
     * 预授权成功
     * resultStatus = U & resultCode = PAYMENT_IN_PROCESS 一定会返回normalUrl
     * @param result
     * @return
     */
    public static boolean isAuthProcess(Result result){
        return Objects.equals(result.getResultCode(),PAYMENT_IN_PROCESS.name()) && Objects.equals(result.getResultStatus(), ResultStatusType.U);
    }

    /**
     * 退款处理中
     * @param result
     * @return
     */
    public static boolean isRefundProcess(Result result){
        return Objects.equals(result.getResultCode(),REFUND_IN_PROCESS.name()) && Objects.equals(result.getResultStatus(), ResultStatusType.U);
    }
}
