package so.dian.platform.antom.common.enmus;

import com.chargebolt.hera.client.enums.PayMethodEnum;
import lombok.Getter;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;

/**
 * <AUTHOR>
 */

@Getter
public enum AntomPayMethodEnum {


    MASTERCARD("Mastercard", PayMethodEnum.MasterCard),
    VISA("VISA", PayMethodEnum.VISA),
    UNION_PAY("CHINA UNION PAY", PayMethodEnum.UnionPay),
    AMERICAN_EXPRESS("American Express", PayMethodEnum.AmericanExpress),
    JCB("JCB", PayMethodEnum.JCB),

    ALIPAY("Alipay", PayMethodEnum.Alipay),
    ALIPAY_HK("AlipayHK", PayMethodEnum.AlipayHK),
    WECHAT("WeChat Pay", PayMethodEnum.Wechat),
    G_CASH("GCash", PayMethodEnum.GCash),
    PROMPT_PAY("PromptPay", PayMethodEnum.PromptPay),
    RABBIT_LINE_PAY("Rabbit LINE Pay", PayMethodEnum.RabbitLinePay),
    KAKAO_PAY("Kakaopay", PayMethodEnum.KakaoPay),

    GRABPAY("Grabpay", PayMethodEnum.Grabpay),
    PAYNOW("PayNow", PayMethodEnum.PayNow),
    SHOPEEPAY("ShopeePay", PayMethodEnum.ShopeePay),

    ;
    private String method;
    private PayMethodEnum payMethodEnum;

    AntomPayMethodEnum(String method, PayMethodEnum payMethodEnum) {
        this.method = method;
        this.payMethodEnum = payMethodEnum;
    }

    public static AntomPayMethodEnum explainName(String name) {
        for (AntomPayMethodEnum pingpongPayMethodEnum : AntomPayMethodEnum.values()) {
            if (pingpongPayMethodEnum.name().equals(name)) {
                return pingpongPayMethodEnum;
            }
        }
        throw HeraBizException.create(HeraBizErrorCodeEnum.ILLEGAL_ERROR);
    }

    public static AntomPayMethodEnum explainMethod(String method) {
        for (AntomPayMethodEnum pingpongPayMethodEnum : AntomPayMethodEnum.values()) {
            if (pingpongPayMethodEnum.getMethod().equals(method)) {
                return pingpongPayMethodEnum;
            }
        }
        throw HeraBizException.create(HeraBizErrorCodeEnum.ILLEGAL_ERROR);
    }

    public static AntomPayMethodEnum explain(PayMethodEnum payMethodEnum) {
        for (AntomPayMethodEnum pingpongPayMethodEnum : AntomPayMethodEnum.values()) {
            if (pingpongPayMethodEnum.getPayMethodEnum().equals(payMethodEnum)) {
                return pingpongPayMethodEnum;
            }
        }
        throw HeraBizException.create(HeraBizErrorCodeEnum.ILLEGAL_ERROR);
    }
}
