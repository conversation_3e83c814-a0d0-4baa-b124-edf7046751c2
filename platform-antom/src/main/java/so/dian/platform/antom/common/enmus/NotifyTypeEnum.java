package so.dian.platform.antom.common.enmus;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum NotifyTypeEnum {
    /**
     * 支付结果
     */
    PAYMENT_RESULT,

    /**
     * 用户已完成支付。商家需要等待最终的支付结果
     */
    PAYMENT_PENDING,

    /**
     * 请款通知
     */
    CAPTURE_RESULT,

    /**
     * 退款通知
     */
    REFUND_RESULT,


    /**
     * 授权码创建通知
     */
    AUTHCODE_CREATED,

    ;

}
