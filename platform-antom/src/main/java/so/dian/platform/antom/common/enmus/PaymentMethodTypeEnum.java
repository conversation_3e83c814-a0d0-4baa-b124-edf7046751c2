package so.dian.platform.antom.common.enmus;

import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum PaymentMethodTypeEnum {
    /**
     * 银行卡
     */
    CARD,

    /**
     * 钱包-中国大陆alipyCN
     */
    ALIPAY_CN,

    /**
     * 钱包-中国香港alipyHK
     */
    ALIPAY_HK,

    /**
     * 钱包-菲律宾 GCash
     */
    GCASH,
    /**
     * 钱包-韩国Kakao Pay
     */
    KAKAOPAY,

    /**
     * 谷歌支付
     */
    GOOGLEPAY,

    /**
     * 苹果支付
     */
    APPLEPAY,

    /**
     * 新加坡支付方式
     */
    GRABPAY,

    PAYNOW,

    SHOPEEPAY,

    ;
}
