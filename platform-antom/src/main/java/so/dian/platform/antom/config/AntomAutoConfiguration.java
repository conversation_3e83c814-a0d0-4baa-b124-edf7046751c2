package so.dian.platform.antom.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import so.dian.commons.eden.util.LogColorUtils;

/**
 * <AUTHOR>
 */
@Configuration
@ComponentScan("so.dian.platform.antom.*")
@ConditionalOnProperty(prefix = "channel.enable", name = "antom", havingValue = "true")
@Slf4j
public class AntomAutoConfiguration {

    static {
        log.info(LogColorUtils.blueMessage("Antom enable"));
    }
}
