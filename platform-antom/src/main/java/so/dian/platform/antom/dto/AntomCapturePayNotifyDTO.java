package so.dian.platform.antom.dto;

import com.alipay.global.api.model.ams.Amount;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AntomCapturePayNotifyDTO extends AntomNotifyBaseDTO {

    /**
     * 商户为识别请款请求而分配的专属 ID
     */
    private String captureRequestId;

    /**
     * Antom 为识别请款而分配的请款 ID
     */
    private String captureId;

    /**
     * 商家在交易货币中请求收取的请款金额
     */
    private Amount captureAmount;

    /**
     * Antom 完成请款的时间
     */
    private String captureTime;

}
