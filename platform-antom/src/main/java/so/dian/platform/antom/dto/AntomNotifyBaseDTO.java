package so.dian.platform.antom.dto;

import com.alipay.global.api.model.Result;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AntomNotifyBaseDTO {

    /**
     * 支付状态通知的类型
     * PAYMENT_RESULT: 表示通知是关于支付结果的。
     * PAYMENT_PENDING: 表示用户已完成支付。商家需要等待最终的支付结果。
     */
    private String notifyType;

    /**
     * 支付结果的详细信息，如支付状态、结果代码和结果消息。
     */
    private Result result;

    /**
     * Antom 为识别支付而分配的支付 ID。paymentId 与 paymentRequestId 之间存在一对一对应关系。
     * 预授权时返回的支付antom支付ID
     */
    private String paymentId;

    /**
     * 非 Antom 收单机构为交易分配的专属 ID
     */
    private String acquirerReferenceNo;

}
