package so.dian.platform.antom.dto;

import com.alipay.global.api.model.ams.*;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AntomPayNotifyDTO extends AntomNotifyBaseDTO {

    /**
     * 商户为识别支付请求而分配的专属 ID
     */
    private String paymentRequestId;

    /**
     * 商家请求在订单货币中接收的支付金额
     */
    private Amount paymentAmount;

    /**
     * 支付创建的日期和时间。
     * 值遵循 ISO 8601 标准格式。例如，“2019-11-27T12:01:01+08:00”。
     */
    private String paymentCreateTime;

    /**
     * 支付成功达到最终状态的日期和时间
     * 注意：此字段仅在支付达到最终成功状态（result.resultStatus 的值为 S）时返回。
     * 值遵循 ISO 8601 标准格式。例如，“2019-11-27T12:01:01+08:00”。
     */
    private String paymentTime;

    /**
     * 关于 Alipay+ 支付方式的客户信息。
     */
    private PspCustomerInfo pspCustomerInfo;

    /**
     * 用于海关报关的总额。
     */
    private Amount customsDeclarationAmount;

    /**
     * 此字段的值等于交易金额乘以 settlementQuote 的值。当货币兑换预先确定且交易时汇率被锁定时，会返回此字段。
     */
    private Amount grossSettlementAmount;

    /**
     * 结算货币与交易货币之间的汇率。
     */
    private Quote settlementQuote;

    /**
     * 支付结果信息。
     * 注意：当 paymentMethodType 在 支付（收银台）接口中的值为 CARD 时，返回此参数。
     */
    private PaymentResultInfo paymentResultInfo;

    /**
     * 优惠结果
     */
    private List<PromotionResult> promotionResult;
}
