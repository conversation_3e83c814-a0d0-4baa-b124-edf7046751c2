package so.dian.platform.antom.dto;

import com.alipay.global.api.model.ams.Amount;
import com.alipay.global.api.model.ams.Quote;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AntomRefundNotifyDTO extends AntomNotifyBaseDTO{

    /**
     * 退款结果的指示
     * SUCCESS: 退款成功。
     * FAIL: 退款失败。
     */
    private String refundStatus;

    /**
     * 商家为识别退款请求而分配的专属 ID
     */
    private String refundRequestId;

    /**
     * Antom 分配的用于识别退款的专属 ID。与 paymentId 和 paymentRequestId 之间存在一一对应关系
     */
    private String refundId;

    /**
     * 商家发起的退款金额
     */
    private Amount refundAmount;

    /**
     * 退款成功或失败的最终状态的日期和时间
     */
    private String refundTime;

    /**
     * 退款结算金额，等于退款金额乘以 settlementQuote 的值。
     */
    private Amount grossSettlementAmount;

    /**
     * 结算货币与交易货币之间的汇率。当返回 grossSettlementAmount时，此字段会被返回。
     */
    private Quote settlementQuote;

}
