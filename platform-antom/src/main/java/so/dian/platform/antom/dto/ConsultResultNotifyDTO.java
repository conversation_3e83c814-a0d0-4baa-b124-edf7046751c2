package so.dian.platform.antom.dto;

import com.alipay.global.api.model.Result;
import lombok.Data;

@Data
public class ConsultResultNotifyDTO {

    /**
     * 有效值：
     * AUTHCODE_CREATED（仅限代扣）：表示用户同意授权代扣。用户同意授权后，可以从此通知中获取支付方式生成的授权码，并在 申请支付令牌
     * 接口中使用该授权码以获取访问令牌。
     * TOKEN_CREATED（仅限快捷支付）：表示用户在商户客户端成功发起快捷支付的授权。
     * TOKEN_CANCELED：表示由商户或支付方式客户端发起的授权取消操作成功。
     */
    private String authorizationNotifyType;
    private Result result;
    private String authClientId;
    private String accessToken;
    private String authState;
    private String authCode;
    private String reason;
    private String userLoginId;
    private String userId;
}
