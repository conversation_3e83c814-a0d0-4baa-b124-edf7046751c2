package so.dian.platform.antom.processor;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.enums.PayMethodEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import so.dian.hera.interceptor.CancelCheckProcessor;
import so.dian.platform.antom.common.enmus.AntomPayMethodEnum;
import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@SuppressWarnings("all")
public class AntomCancelCheckProcessor implements CancelCheckProcessor {

    @Resource
    private AntomProperty antomProperty;

    /**
     * 接口文档：https://global.alipay.com/docs/ac/cashierpay_zh-cn/cancel
     * 支付方式文档：https://global.alipay.com/docs/ac/pm_zh-cn/enumeration_values
     *
     *  印尼本地支付方式及 JKOPay
     *  Mercado Pago（巴西、智利、墨西哥和秘鲁）支付成功后七天内可以取消交易
     *  卡支付：支付成功后七天内可以取消交易；请款成功的交易无法取消
     *  泰国网上银行、移动银行 APP 和银行转账类支付方式：下单时间至支付成功前可以取消交易；支付成功后无法取消交易
     *
     * @param paymentDO
     * @return
     */
    @Override
    public boolean cancelCheck(PaymentDO paymentDO) {
        // 校验支付方式和支付方法
        PaywayEnum paywayEnum = PaywayEnum.explain(paymentDO.getPayType());
        // 不是antom钱包支付的不支持取消
        if(!Objects.equals(paywayEnum,PaywayEnum.ANTOM_CHECKOUT_APM)){
            return false;
        }
        PayMethodEnum payMethodEnum = PayMethodEnum.explain(paymentDO.getPayMethod());
        if(Objects.isNull(payMethodEnum)){
            return false;
        }

        // 获取配置可以取消的支付方法
        List<AntomPayMethodEnum> supportCancelMethods = antomProperty.getSupportCancelMethods();
        List<AntomPayMethodEnum> matchCancelMethods = supportCancelMethods.stream().filter(item->
                Objects.equals(payMethodEnum.getId(),item.getPayMethodEnum().getId())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(matchCancelMethods)){
            return false;
        }

        // 校验订单时间是否在取消范围内
        Date currentTime = new Date();
        Date startTime = paymentDO.getCreateTime();
        Date endTime = getEndTime(paymentDO.getCreateTime());
        if(!currentTime.before(startTime) && !currentTime.after(endTime)){
            return true;
        }
        return false;
    }

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.ANTOM_CHECKOUT_APM);
    }

    /**
     * 计算当前订单时间的算T+1日的00:15
     * @param orderCreateTime
     * @return
     */
    private static Date getEndTime(Date orderCreateTime){
        // 将java.util.Date转换为LocalDateTime
        Instant instant = orderCreateTime.toInstant();
        ZoneId zoneId = ZoneId.of("Asia/Shanghai");
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zoneId);
        // 设置东八区时区 (Asia/Shanghai)
        ZonedDateTime zonedDateTime = ZonedDateTime.of(localDateTime, zoneId);
        // 计算T+1日的00:15
        ZonedDateTime nextDay = zonedDateTime.plusDays(1).withHour(0).withMinute(15).withSecond(0);
        return  Date.from(nextDay.toInstant());
    }

    public static void main(String[] args) {
        String inputTime = "2024-10-15 16:00:00";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime localDateTime = LocalDateTime.parse(inputTime, formatter);
        ZoneId zoneId = ZoneId.of("Asia/Shanghai");
        ZonedDateTime zonedDateTime = ZonedDateTime.of(localDateTime, zoneId);
        Date currentTime = new Date();
        Date startTime = Date.from(zonedDateTime.toInstant());
        Date endTime = getEndTime(startTime);
        System.out.println(JSON.toJSON(startTime));
        System.out.println(JSON.toJSON(endTime));
        System.out.println(!currentTime.before(startTime) && !currentTime.after(endTime));
    }

}
