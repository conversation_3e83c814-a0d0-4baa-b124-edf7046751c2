package so.dian.platform.antom.processor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alipay.global.api.AlipayClient;
import com.alipay.global.api.DefaultAlipayClient;
import com.alipay.global.api.exception.AlipayApiException;
import com.alipay.global.api.model.Result;
import com.alipay.global.api.model.ams.*;
import com.alipay.global.api.request.ams.auth.AlipayAuthApplyTokenRequest;
import com.alipay.global.api.request.ams.auth.AlipayAuthConsultRequest;
import com.alipay.global.api.request.ams.pay.*;
import com.alipay.global.api.response.ams.auth.AlipayAuthApplyTokenResponse;
import com.alipay.global.api.response.ams.auth.AlipayAuthConsultResponse;
import com.alipay.global.api.response.ams.pay.*;
import com.chargebolt.eden.enums.ClientTypeEnum;
import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.checkout.req.CaptureQueryRequest;
import com.chargebolt.hera.client.dto.pay.checkout.req.PreAuthCaptureRequest;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.CaptureQueryResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PreAuthCancelResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PreAuthCaptureResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PrepayCreateResultDTO;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundResultDTO;
import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.client.enums.OsTypeEnum;
import com.chargebolt.hera.client.enums.PayMethodEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.chargebolt.hera.domain.sharding.PaymentNote;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.CheckoutPayProcessor;
import so.dian.hera.interceptor.RefundProcessor;
import so.dian.hera.interceptor.dto.req.PaymentQueryRequest;
import so.dian.hera.interceptor.dto.req.ProcessorRefundRequest;
import so.dian.hera.interceptor.dto.req.RefundQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.PaymentQueryResultDTO;
import so.dian.hera.interceptor.dto.rsp.RefundQueryResultDTO;
import so.dian.platform.antom.common.enmus.AlipayResultStatusEnum;
import so.dian.platform.antom.common.enmus.AntomNotifyTypeEnum;
import so.dian.platform.antom.common.enmus.CaptureModeEnum;
import so.dian.platform.antom.common.enmus.PaymentMethodTypeEnum;
import so.dian.platform.common.configuration.redis.RedisClient;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;

import javax.annotation.Resource;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 *         antom sdks集成 - 银行卡支付 wap
 */
@Slf4j
@Component
@SuppressWarnings("all")
public class AntomCheckoutHostedProcessor implements CheckoutPayProcessor, RefundProcessor {

    @Resource
    private RedisClient redisClient;

    @Resource
    private AntomProperty antomProperty;

    private ArrayList<Integer> capturePayMethods = Lists.newArrayList(PayMethodEnum.GooglePay.getId(),
            PayMethodEnum.ApplePay.getId());

    private List<Integer> supportsApmPreAuthMethods = Lists.newArrayList(PayMethodEnum.Alipay.getId(),PayMethodEnum.GCash.getId(),
            PayMethodEnum.AlipayHK.getId());

    /**
     * 构建AlipayClient
     *
     * @return
     */
    public AlipayClient getAlipayClient() {
        // 网关端点有3个，具体请参考 EndPointConstants
        return new DefaultAlipayClient(antomProperty.getUrl(), antomProperty.getMchPrivateKey(),
                antomProperty.getAntomPublicKey(), antomProperty.getClientId());
    }

    @Override
    public ChannelEnum getRefundChannel() {
        return ChannelEnum.ANTOM;
    }

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.ANTOM_CHECKOUT_CARD, PaywayEnum.ANTOM_CHECKOUT_APM);
    }

    /**
     * 申请支付令牌：
     * /v1/authorizations/applyToken
     */
    public String applyToken(String authCode, String customerBelongsTo) {
        AlipayAuthApplyTokenRequest applyTokenRequest = new AlipayAuthApplyTokenRequest();
        applyTokenRequest.setGrantType(GrantType.AUTHORIZATION_CODE);
        applyTokenRequest.setCustomerBelongsTo(CustomerBelongsTo.valueOf(customerBelongsTo));
        applyTokenRequest.setAuthCode(authCode);
        try {
            log.info("antom applyToken req = {}", JSON.toJSONString(applyTokenRequest));
            AlipayAuthApplyTokenResponse response = getAlipayClient().execute(applyTokenRequest);
            log.info("antom applyToken rsp = {}", JSON.toJSONString(response));
            if (AlipayResultStatusEnum.isSuccess(response.getResult())) {
                return response.getAccessToken();
            }
            log.error("antom applyToken fail. errorMsg = {}", response.getResult());
        } catch (Exception e) {
            log.error("antom applyToken fail. errorMsg = {}", e.getMessage());
            throw new HeraBizException(HeraBizErrorCodeEnum.AliRSP_ERROR);
        }
        throw new HeraBizException(HeraBizErrorCodeEnum.AliRSP_ERROR);
    }

    /**
     * 发起代扣（冻结Apm余额）
     */
    public AlipayPayResponse freezeApmBalance(PaymentDO paymentDO, String accessToken, String customerBelongsTo) {
        AlipayPayRequest alipayPayRequest = new AlipayPayRequest();
        // 订单信息：包括买家、商户、商品、金额、配送信息和购买环境的
        Amount orderAmount = Amount.builder()
                .currency("HKD")
                .value(paymentDO.getPayAmount().toString())
                .build();
        // 包括买家的 ID、姓名、电话号码和电子邮件的买家信息。paymentMethodType 的值为 CARD 必传
        Buyer buyer = Buyer.builder()
                .referenceBuyerId(paymentDO.getUserId().toString())
                .buyerPhoneNo("")
                .build();
        Order order = Order.builder()
                .orderAmount(orderAmount)
                .referenceOrderId(paymentDO.getOrderNo())
                .orderDescription("charge order fee")
                .buyer(buyer)
                .build();
        alipayPayRequest.setOrder(order);
        // 商户为识别支付请求所分配的专属 ID
        alipayPayRequest.setPaymentRequestId(paymentDO.getTradeNo());
        // 商家在订单货币中请求接收的支付金额
        alipayPayRequest.setPaymentAmount(orderAmount);
        // 支付请求的结算策略,与收单币种保持一致
        alipayPayRequest.setSettlementStrategy(SettlementStrategy.builder().settlementCurrency("HKD").build());
        alipayPayRequest.setProductCode(ProductCodeType.AGREEMENT_PAYMENT);
        PaymentMethodTypeEnum typeEnum = null;
        Integer toolId = 0;
        if (CustomerBelongsTo.ALIPAY_CN.name().equals(customerBelongsTo)) {
            typeEnum = PaymentMethodTypeEnum.ALIPAY_CN;
            toolId = PayMethodEnum.Alipay.getId();
        } else if (CustomerBelongsTo.ALIPAY_HK.name().equals(customerBelongsTo)) {
            typeEnum = PaymentMethodTypeEnum.ALIPAY_HK;
            toolId = PayMethodEnum.AlipayHK.getId();
        } else if (CustomerBelongsTo.GCASH.name().equals(customerBelongsTo)) {
            typeEnum = PaymentMethodTypeEnum.GCASH;
            toolId = PayMethodEnum.GCash.getId();
        }
        PaymentMethod paymentMethod = PaymentMethod.builder()
                .paymentMethodType(typeEnum.name())
                .paymentMethodId(accessToken)
                .build();
        alipayPayRequest.setPaymentMethod(paymentMethod);
        alipayPayRequest.setPaymentFactor(PaymentFactor.builder().isAuthorization(true).build());
        alipayPayRequest.setPaymentRedirectUrl(antomProperty.getPayResultUrl().getH5() + "?deviceNo="
                + paymentDO.getDeviceNo() + "&merchantTransactionId=" + paymentDO.getTradeNo());
        alipayPayRequest.setPaymentNotifyUrl(
                antomProperty.getCardNotificationUrl() + "/" + AntomNotifyTypeEnum.BANK.getCode() + "/" + toolId);
        AlipayPayResponse payResponse = null;
        try {
            log.info("antom freezeApmBalance req = {}",
                    JSONArray.toJSONString(alipayPayRequest, SerializerFeature.DisableCircularReferenceDetect));
            payResponse = getAlipayClient().execute(alipayPayRequest);
            log.info("antom freezeApmBalance rsp = {}", JSONArray.toJSONString(payResponse));
        } catch (Exception e) {
            String errorMsg = e.getMessage();
            log.error("antom freezeApmBalance fail. errorMsg = {}", errorMsg);
            throw new HeraBizException(HeraBizErrorCodeEnum.AliRSP_ERROR);
        }
        return payResponse;
    }

    /**
     * 处理预支付请求
     * @param prepayCreateRequest 预支付创建请求
     * @param paymentDO 支付信息
     * @return 预支付创建结果
     */
    public PrepayCreateResultDTO doPrePay(PrepayCreateRequest prepayCreateRequest, PaymentDO paymentDO) {
        PrepayCreateRequest.PrepayCreateExtInfo extInfo = prepayCreateRequest.getExtInfo();
        String merchantRegion = getMerchantRegion(extInfo);

        validateClientType(extInfo);

        // 处理电子钱包-预授权支付
        if (isWalletPreAuthPayment(prepayCreateRequest, extInfo)) {
            return doConsult(paymentDO, prepayCreateRequest, merchantRegion);
        }

        // 支付宝小程序特殊处理
        if (PaywayEnum.ANTOM_CHECKOUT_APM.equals(prepayCreateRequest.getPayway())
                && Objects.equals(extInfo.getClientType(), ClientTypeEnum.ALIPAY_MINI.getType())) {
            log.info("AlipayMiniPayment,payway={},clientType={}", prepayCreateRequest.getPayway(),
                    extInfo.getClientType());
            return doAlipayMiniPayment(prepayCreateRequest, paymentDO, merchantRegion);
        }

        AlipayPaymentSessionRequest alipayPayRequest = buildBasePaymentRequest(prepayCreateRequest, paymentDO, merchantRegion);

        // 根据支付方式设置特定参数
        if (isCardPayment(prepayCreateRequest)) {
            setupCardPaymentParams(alipayPayRequest, extInfo, paymentDO);
        } else {
            setupWalletPaymentParams(alipayPayRequest, extInfo);
        }

        return executePaymentRequest(alipayPayRequest, paymentDO, prepayCreateRequest);
    }

    /**
     * 预授权确认请款
     * 接口文档：https://global.alipay.com/docs/ac/ams_zh-cn/capture
     *
     * @param preauthCaptureRequest 请款请求信息
     * @param captureId             预授权支付宝返回的支付宝单号
     * @param capturePaymentDO      请款支付信息
     * @return
     */
    @Override
    public PreAuthCaptureResultDTO doCapture(PreAuthCaptureRequest preauthCaptureRequest, String captureId,
            PaymentDO capturePaymentDO) {

        AlipayCaptureRequest captureRequest = new AlipayCaptureRequest();
        captureRequest.setCaptureRequestId(capturePaymentDO.getTradeNo());
        captureRequest.setPaymentId(captureId);
        Amount captureAmount = Amount.builder()
                .currency("HKD")
                .value(capturePaymentDO.getPayAmount().toString())
                .build();
        captureRequest.setCaptureAmount(captureAmount);
        AlipayCaptureResponse captureResponse;
        try {
            log.info("antom请款 req = {}", JSONArray.toJSONString(captureRequest));
            captureResponse = getAlipayClient().execute(captureRequest);
            log.info("antom请款 rsp = {}", JSONArray.toJSONString(captureResponse));
        } catch (AlipayApiException e) {
            String errorMsg = e.getMessage();
            log.error("antom doCapture fail. errorMsg = {}", errorMsg);
            throw new HeraBizException(HeraBizErrorCodeEnum.AliRSP_ERROR);
        }
        Result result = captureResponse.getResult();
        PreAuthCaptureResultDTO resultDTO = new PreAuthCaptureResultDTO();
        if (AlipayResultStatusEnum.isCaptureSuccess(result)) {
            resultDTO.setSuccess(true);
            resultDTO.setStatus(TransStatusEnum.SUCCESS.getKey());
            return resultDTO;
        } else {
            resultDTO.setSuccess(false);
            resultDTO.setStatus(TransStatusEnum.FAIL.getKey());
            return resultDTO;
        }
    }


    /**
     * 取消订单
     * 接口文档：https://global.alipay.com/docs/ac/ams_zh-cn/paymentc_online
     *
     * @param paymentDO
     * @return
     */
    @Override
    public PreAuthCancelResultDTO doCancel(PaymentDO paymentDO) {
        AlipayPayCancelRequest cancelRequest = new AlipayPayCancelRequest();
        cancelRequest.setPaymentRequestId(paymentDO.getTradeNo());
        AlipayPayCancelResponse cancelResponse;
        try {
            log.info("antom doCancel req = {}", JSONArray.toJSONString(cancelRequest));
            cancelResponse = getAlipayClient().execute(cancelRequest);
            log.info("antom doCancel rsp = {}", JSONArray.toJSONString(cancelResponse));
        } catch (AlipayApiException e) {
            String errorMsg = e.getMessage();
            log.error("antom doCancel fail. errorMsg = {}", errorMsg);
            throw new HeraBizException(HeraBizErrorCodeEnum.AliRSP_ERROR);
        }
        Result result = cancelResponse.getResult();
        /**
         * 如果 result.resultStatus 的值为 S，取消操作成功。
         * 如果 result.resultStatus 的值为F，取消操作失败。查看返回的错误代码并确定进一步的操作。
         * 如果 result.resultStatus 的值为 U，取消结果未知。使用相同的请求参数重试取消请求。
         * 如果没有返回响应，使用相同的请求参数重试取消请求。
         */
        PreAuthCancelResultDTO resultDTO = new PreAuthCancelResultDTO();
        if (AlipayResultStatusEnum.isSuccess(result)) {
            resultDTO.setSuccess(true);
            resultDTO.setStatus(TransStatusEnum.SUCCESS.getKey());
            return resultDTO;
        } else {
            resultDTO.setSuccess(false);
            resultDTO.setStatus(TransStatusEnum.FAIL.getKey());
            return resultDTO;
        }
    }


    

    /**
     * 支付结果查询：https://global.alipay.com/docs/ac/ams_zh-cn/paymentri_online
     *
     * @param request
     * @return
     */
    public PaymentQueryResultDTO paymentQuery(PaymentQueryRequest request) {
        AlipayPayQueryRequest payQueryRequest = new AlipayPayQueryRequest();
        payQueryRequest.setPaymentRequestId(request.getTradeNo());
        AlipayPayQueryResponse payQueryResponse;
        try {
            log.info("antom paymentQuery req = {}", JSONArray.toJSONString(payQueryRequest));
            payQueryResponse = getAlipayClient().execute(payQueryRequest);
            log.info("antom paymentQuery rsp = {}", JSONArray.toJSONString(payQueryResponse));
        } catch (AlipayApiException e) {
            String errorMsg = e.getMessage();
            log.error("antom paymentQuery fail. errorMsg = {}", errorMsg);
            throw new HeraBizException(HeraBizErrorCodeEnum.AliRSP_ERROR);
        }
        /**
         * 如果 result.resultStatus 的值为 S， 支付结果查询 接口调用成功。您可以从接口响应的 paymentStatus 字段获取支付结果。
         * 如果 result.resultStatus 的值为 F， 支付结果查询 接口调用失败。您无法从接口响应中获取支付结果。
         * 如果 result.resultStatus 的值为 U， 支付结果查询 接口调用的状态未知。使用相同的请求参数重试 支付结果查询 接口。
         */
        PaymentQueryResultDTO resultDTO = new PaymentQueryResultDTO();
        // 成功
        if (AlipayResultStatusEnum.isSuccess(payQueryResponse.getResult())) {
            resultDTO.setSuccess(true);
            resultDTO.setStatus(TransStatusEnum.SUCCESS.getKey());
            return resultDTO;
            // 失败
        } else {
            resultDTO.setSuccess(false);
            resultDTO.setStatus(TransStatusEnum.FAIL.getKey());
            return resultDTO;
        }
    }

    /**
     * 请款查询与
     * 支付结果查询：https://global.alipay.com/docs/ac/ams_zh-cn/paymentri_online
     *
     * @param captureQueryRequest
     * @return
     */
    public CaptureQueryResultDTO captureQuery(CaptureQueryRequest captureQueryRequest) {
        AlipayPayQueryRequest payQueryRequest = new AlipayPayQueryRequest();
        payQueryRequest.setPaymentId(captureQueryRequest.getPayNo());
        AlipayPayQueryResponse payQueryResponse;
        try {
            log.info("antom captureQuery req = {}", JSONArray.toJSONString(payQueryRequest));
            payQueryResponse = getAlipayClient().execute(payQueryRequest);
            log.info("antom captureQuery rsp = {}", JSONArray.toJSONString(payQueryResponse));
        } catch (AlipayApiException e) {
            String errorMsg = e.getMessage();
            log.error("antom captureQuery fail. errorMsg = {}", errorMsg);
            throw new HeraBizException(HeraBizErrorCodeEnum.AliRSP_ERROR);
        }
        /**
         * 如果 result.resultStatus 的值为 S， 支付结果查询 接口调用成功。您可以从接口响应的 paymentStatus 字段获取支付结果。
         * 如果 result.resultStatus 的值为 F， 支付结果查询 接口调用失败。您无法从接口响应中获取支付结果。
         * 如果 result.resultStatus 的值为 U， 支付结果查询 接口调用的状态未知。使用相同的请求参数重试 支付结果查询 接口。
         */
        CaptureQueryResultDTO resultDTO = new CaptureQueryResultDTO();
        // 成功
        if (AlipayResultStatusEnum.isSuccess(payQueryResponse.getResult())) {
            resultDTO.setSuccess(true);
            resultDTO.setStatus(TransStatusEnum.SUCCESS.getKey());
            return resultDTO;
            // 失败
        } else {
            resultDTO.setSuccess(false);
            resultDTO.setStatus(TransStatusEnum.FAIL.getKey());
            return resultDTO;
        }
    }

    /**
     * 退款申请
     * 接口文档：https://global.alipay.com/docs/ac/ams_zh-cn/refund_online
     *
     * @param request
     * @return
     */
    @Override
    public RefundResultDTO refund(ProcessorRefundRequest request) {
        AlipayRefundRequest refundRequest = new AlipayRefundRequest();
        refundRequest.setRefundRequestId(request.getRefundNo());
        refundRequest.setPaymentId(request.getPayNo());
        Amount refundAmount = Amount.builder().currency("HKD")
                .value(request.getRefundAmount().toString()).build();
        refundRequest.setRefundAmount(refundAmount);
        refundRequest.setRefundReason(request.getReason());
        // 钱包支付退款是同步的，不会有退款通知（退款补偿延时消息处理退款结果）
        refundRequest.setRefundNotifyUrl(antomProperty.getRefundNotificationUrl());
        AlipayRefundResponse refundResponse;
        try {
            log.info("antom refund req = {}", JSONArray.toJSONString(refundRequest));
            refundResponse = getAlipayClient().execute(refundRequest);
            log.info("antom refund rsp = {}", JSONArray.toJSONString(refundResponse));
        } catch (AlipayApiException e) {
            String errorMsg = e.getMessage();
            log.error("antom refund fail. errorMsg = {}", errorMsg);
            throw new HeraBizException(HeraBizErrorCodeEnum.AliRSP_ERROR);
        }
        /**
         * 如果 result.resultStatus 的值为S，退款成功。
         * 如果 result.resultStatus 的值为F，退款失败。通常，失败原因是退款窗口已过期(result.resultCode =
         * REFUND_WINDOW_EXCEED)。如果仍需处理退款，请联系相关人员。
         * 如果 result.resultStatus 的值为U，结果未知。可能由于系统或网络问题导致处理失败。使用相同的 refundRequestId
         * 重试，或使用 退款查询 接口查询退款结果。
         */
        // 成功
        RefundResultDTO resultDTO = new RefundResultDTO();
        if (AlipayResultStatusEnum.isSuccess(refundResponse.getResult()) ||
                (Objects.equals(request.getPayWay(), PaywayEnum.ANTOM_CHECKOUT_CARD.getPayway())
                        && AlipayResultStatusEnum.isRefundProcess(refundResponse.getResult()))) {
            resultDTO.setSuccess(true);
            resultDTO.setStatus(TransStatusEnum.SUCCESS.getKey());
            resultDTO.setRefundNo(refundResponse.getRefundRequestId());
            resultDTO.setRefundPayNo(refundResponse.getRefundId());
            return resultDTO;
            // 失败
        } else {
            resultDTO.setSuccess(false);
            resultDTO.setStatus(TransStatusEnum.FAIL.getKey());
            return resultDTO;
        }
    }

    

    /**
     * 接口文档：https://global.alipay.com/docs/ac/ams_zh-cn/ir_online
     *
     * @param queryResultRequest
     * @return
     */
    public RefundQueryResultDTO refundQuery(RefundQueryResultRequest queryResultRequest) {
        AlipayInquiryRefundRequest request = new AlipayInquiryRefundRequest();
        request.setRefundRequestId(queryResultRequest.getRefundNo());
        AlipayInquiryRefundResponse refundResponse;
        try {
            log.info("antom refundQuery req = {}", JSONArray.toJSONString(request));
            refundResponse = getAlipayClient().execute(request);
            log.info("antom refundQuery rsp = {}", JSONArray.toJSONString(refundResponse));
        } catch (AlipayApiException e) {
            String errorMsg = e.getMessage();
            log.error("antom refundQuery fail. errorMsg = {}", errorMsg);
            throw new HeraBizException(HeraBizErrorCodeEnum.AliRSP_ERROR);
        }
        RefundQueryResultDTO resultDTO = new RefundQueryResultDTO();
        if (AlipayResultStatusEnum.isSuccess(refundResponse.getResult())) {
            if (Objects.equals(refundResponse.getRefundStatus(), TransactionStatusType.SUCCESS)) {
                resultDTO.setStatus(PayStatus.REFUNDED.getCode());
                OffsetDateTime offsetDateTime = OffsetDateTime.parse(refundResponse.getRefundTime(),
                        DateTimeFormatter.ISO_OFFSET_DATE_TIME);
                resultDTO.setRefundTime(Date.from(offsetDateTime.toInstant()));
            } else if (Objects.equals(refundResponse.getRefundStatus(), TransactionStatusType.PROCESSING)) {
                resultDTO.setStatus(PayStatus.REFUNDING.getCode());
            } else if (Objects.equals(refundResponse.getRefundStatus(), TransactionStatusType.FAIL)) {
                resultDTO.setStatus(PayStatus.FAIL.getCode());
            }
        }
        return resultDTO;
    }

    private PrepayCreateResultDTO doAlipayMiniPayment(PrepayCreateRequest prepayCreateRequest, PaymentDO paymentDO,
            String merchantRegion) {
        AlipayPayRequest request = new AlipayPayRequest();
        request.setMerchantRegion(merchantRegion);
        request.setPaymentRequestId(paymentDO.getTradeNo());
        request.setEnv(buildEnv(prepayCreateRequest));
        request.setOrder(buildOrder(prepayCreateRequest, paymentDO));
        request.setPaymentAmount(buildAmount(prepayCreateRequest));
        request.setSettlementStrategy(SettlementStrategy.builder().settlementCurrency("HKD").build());
        // 支付宝小程序跳转连接，这里是写死的
        request.setPaymentRedirectUrl("");
        request.setProductCode(ProductCodeType.CASHIER_PAYMENT);
        PaymentMethod paymentMethod = PaymentMethod.builder()
                .paymentMethodType(getPaymentMethodType(prepayCreateRequest.getExtInfo().getPayMethod()))
                .build();
        request.setPaymentMethod(paymentMethod);
        request.setPaymentNotifyUrl(buildWalletNotifyUrl(prepayCreateRequest.getExtInfo()));

        try {
            log.info("antom doApmPay req = {}", JSONArray.toJSONString(request));
            AlipayPayResponse response = getAlipayClient().execute(request);
            log.info("antom doApmPay rsp = {}", JSONArray.toJSONString(response));

            if (AlipayResultStatusEnum.isSuccess(response.getResult()) || AlipayResultStatusEnum.isProcessing(response.getResult())) {
                log.info("antom doApmPay success, response={}",JSON.toJSONString(response));
                PrepayCreateResultDTO result = new PrepayCreateResultDTO();
                result.setSuccess(true);
                result.setTradeNo(paymentDO.getTradeNo());
                result.getPrepayResult().put("normalUrl", response.getNormalUrl());
                result.getPrepayResult().put("paymentId", response.getPaymentId());

                if (isCardPayment(prepayCreateRequest)) {
                    result.getPrepayResult().put("currency", "HKD");
                    result.getPrepayResult().put("amount", prepayCreateRequest.getPayAmount());
                }
                log.info("antom doApmPay success, result={}", JSON.toJSONString(result));
                return result;
            }
        } catch (AlipayApiException e) {
            log.error("antom doApmPay fail. errorMsg = {}", e.getMessage());
        }

        throw new HeraBizException(HeraBizErrorCodeEnum.AliRSP_ERROR);
    }

    /**
     * 获取商户区域代码
     */
    private String getMerchantRegion(PrepayCreateRequest.PrepayCreateExtInfo extInfo) {
        String requestRegionCode = extInfo.getRegionCode();
        return StringUtils.isEmpty(requestRegionCode) ? antomProperty.getRegionCode() : requestRegionCode;
    }

    /**
     * 验证客户端类型
     */
    private void validateClientType(PrepayCreateRequest.PrepayCreateExtInfo extInfo) {
        boolean isValidClient = Objects.equals(extInfo.getClientType(), ClientTypeEnum.H5.getType()) ||
                Objects.equals(extInfo.getClientType(), ClientTypeEnum.IOS_H5.getType()) ||
                Objects.equals(extInfo.getClientType(), ClientTypeEnum.ANDROID_H5.getType())||
                Objects.equals(extInfo.getClientType(), ClientTypeEnum.ALIPAY_MINI.getType());
        if (!isValidClient) {
            throw new HeraBizException(HeraBizErrorCodeEnum.ROUTE_NOT_SUPPORT);
        }
    }

    /**
     * 判断是否为钱包-预授权支付
     */
    private boolean isWalletPreAuthPayment(PrepayCreateRequest prepayCreateRequest, PrepayCreateRequest.PrepayCreateExtInfo extInfo) {
        return PaywayEnum.ANTOM_CHECKOUT_CARD.equals(prepayCreateRequest.getPayway())
                && supportsApmPreAuthMethods.contains(extInfo.getPayMethod());
    }

    /**
     * 判断是否为银行卡支付
     */
    private boolean isCardPayment(PrepayCreateRequest prepayCreateRequest) {
        return Objects.equals(prepayCreateRequest.getPayway(), PaywayEnum.ANTOM_CHECKOUT_CARD);
    }

    /**
     * 构建基础支付请求
     */
    private AlipayPaymentSessionRequest buildBasePaymentRequest(PrepayCreateRequest prepayCreateRequest,
            PaymentDO paymentDO, String merchantRegion) {
        AlipayPaymentSessionRequest request = new AlipayPaymentSessionRequest();
        request.setMerchantRegion(merchantRegion);
        request.setPaymentRequestId(paymentDO.getTradeNo());
        request.setEnv(buildEnv(prepayCreateRequest));
        request.setOrder(buildOrder(prepayCreateRequest, paymentDO));
        request.setPaymentAmount(buildAmount(prepayCreateRequest));
        request.setSettlementStrategy(SettlementStrategy.builder().settlementCurrency("HKD").build());
        request.setPaymentRedirectUrl(buildRedirectUrl(prepayCreateRequest.getExtInfo(), paymentDO));
        request.setProductCode(ProductCodeType.CASHIER_PAYMENT);
        return request;
    }

    /**
     * 构建环境信息
     */
    private Env buildEnv(PrepayCreateRequest prepayCreateRequest) {
        if (ClientTypeEnum.ALIPAY_MINI.getType().equals(prepayCreateRequest.getExtInfo().getClientType())) {
            if (OsTypeEnum.ANDROID.name().equals(prepayCreateRequest.getExtInfo().getOsType())) {
                return Env.builder()
                        .clientIp(prepayCreateRequest.getIp())
                        .osType(OsType.ANDROID)
                        .terminalType(TerminalType.MINI_APP)
                        .build();
            } else if (OsTypeEnum.IOS.name().equals(prepayCreateRequest.getExtInfo().getOsType())) {
                return Env.builder()
                        .clientIp(prepayCreateRequest.getIp())
                        .osType(OsType.IOS)
                        .terminalType(TerminalType.MINI_APP)
                        .build();
            } else {
                log.error("invalid osType={}", prepayCreateRequest.getExtInfo().getOsType());
                throw new HeraBizException(HeraBizErrorCodeEnum.ILLEGAL_ERROR);
            }
        }
        return Env.builder()
                .clientIp(prepayCreateRequest.getIp())
                .build();
    }

    /**
     * 构建订单信息
     */
    private Order buildOrder(PrepayCreateRequest prepayCreateRequest, PaymentDO paymentDO) {
        Amount orderAmount = buildAmount(prepayCreateRequest);
        List<Goods> goods = buildGoods(prepayCreateRequest.getExtInfo());
        Buyer buyer = buildBuyer(prepayCreateRequest);

        return Order.builder()
                .orderAmount(orderAmount)
                .referenceOrderId(prepayCreateRequest.getBizNo())
                .orderDescription(prepayCreateRequest.getRemark())
                .goods(goods)
                .buyer(buyer)
                .build();
    }

    /**
     * 构建金额信息
     */
    private Amount buildAmount(PrepayCreateRequest prepayCreateRequest) {
        return Amount.builder()
                .currency("HKD")
                .value(prepayCreateRequest.getPayAmount().toString())
                .build();
    }

    /**
     * 构建商品信息
     */
    private List<Goods> buildGoods(PrepayCreateRequest.PrepayCreateExtInfo extInfo) {
        JSONObject goodInfo = JSONObject.parseObject(extInfo.getGoodsJSON(), JSONObject.class);
        return Lists.newArrayList(Goods.builder()
                .referenceGoodsId(goodInfo.getString("number"))
                .goodsName(goodInfo.getString("name"))
                .build());
    }

    /**
     * 构建买家信息
     */
    private Buyer buildBuyer(PrepayCreateRequest prepayCreateRequest) {
        return Buyer.builder()
                .referenceBuyerId(prepayCreateRequest.getUserId().toString())
                .buyerPhoneNo(prepayCreateRequest.getExtInfo().getMobile())
                .build();
    }

    /**
     * 设置银行卡支付相关参数
     * 不指定 3dsAuthentication, 默认收单机构会根据模型判断是否需要 3DS 认证
     */
    private void setupCardPaymentParams(AlipayPaymentSessionRequest request,
            PrepayCreateRequest.PrepayCreateExtInfo extInfo, PaymentDO paymentDO) {
        Map<String, Object> paymentMethodMetaData = new HashMap<>();
        paymentMethodMetaData.put("tokenizeMode", "ENABLED");

        PayMethodEnum payMethodEnum = PayMethodEnum.explain(extInfo.getPayMethod());
        String cardBrand = Objects.nonNull(payMethodEnum) ?
                payMethodEnum.name().toUpperCase() : PayMethodEnum.VISA.name();
        paymentMethodMetaData.put("selectedCardBrand", cardBrand);

        PaymentMethod paymentMethod = PaymentMethod.builder()
                .paymentMethodType(PaymentMethodTypeEnum.CARD.name())
                .paymentMethodMetaData(paymentMethodMetaData)
                .build();

        request.setPaymentMethod(paymentMethod);
        request.setPaymentFactor(PaymentFactor.builder()
                .isAuthorization(true)
                .captureMode(CaptureModeEnum.MANUAL.name())
                .build());
        request.setPaymentNotifyUrl(buildCardNotifyUrl(extInfo));
    }

    /**
     * 设置钱包支付相关参数
     */
    private void setupWalletPaymentParams(AlipayPaymentSessionRequest request,
            PrepayCreateRequest.PrepayCreateExtInfo extInfo) {
        PaymentMethod paymentMethod = PaymentMethod.builder()
                .paymentMethodType(getPaymentMethodType(extInfo.getPayMethod()))
                .build();
        request.setPaymentMethod(paymentMethod);
        request.setPaymentNotifyUrl(buildWalletNotifyUrl(extInfo));
    }

    /**
     * 构建银行卡支付通知URL
     */
    private String buildCardNotifyUrl(PrepayCreateRequest.PrepayCreateExtInfo extInfo) {
        return antomProperty.getCardNotificationUrl() + "/" +
                AntomNotifyTypeEnum.BANK.getCode() + "/" + extInfo.getPayMethod();
    }

    /**
     * 构建钱包支付通知URL
     */
    private String buildWalletNotifyUrl(PrepayCreateRequest.PrepayCreateExtInfo extInfo) {
        return antomProperty.getApmNotificationUrl() + "/" + extInfo.getPayMethod();
    }

    /**
     * 构建支付重定向URL
     */
    private String buildRedirectUrl(PrepayCreateRequest.PrepayCreateExtInfo extInfo, PaymentDO paymentDO) {
        String baseUrl = StringUtils.isBlank(extInfo.getPayResult()) ?
                antomProperty.getPayResultUrl().getH5() : extInfo.getPayResult();
        return baseUrl + "?deviceNo=" + extInfo.getDeviceNo() +
                "&merchantTransactionId=" + paymentDO.getTradeNo();
    }

    /**
     * 执行支付请求
     */
    private PrepayCreateResultDTO executePaymentRequest(AlipayPaymentSessionRequest alipayPayRequest,
            PaymentDO paymentDO, PrepayCreateRequest prepayCreateRequest) {
        try {
            log.info("antom doPrePay req = {}", JSONArray.toJSONString(alipayPayRequest));
            AlipayPaymentSessionResponse response = getAlipayClient().execute(alipayPayRequest);
            log.info("antom doPrePay rsp = {}", JSONArray.toJSONString(response));

            if (AlipayResultStatusEnum.isSuccess(response.getResult())) {
                return buildSuccessResult(response, paymentDO, prepayCreateRequest);
            }
        } catch (AlipayApiException e) {
            log.error("antom doPrePay fail. errorMsg = {}", e.getMessage());
        }

        throw new HeraBizException(HeraBizErrorCodeEnum.AliRSP_ERROR);
    }

    /**
     * 构建成功结果
     */
    private PrepayCreateResultDTO buildSuccessResult(AlipayPaymentSessionResponse response,
            PaymentDO paymentDO, PrepayCreateRequest prepayCreateRequest) {
        PrepayCreateResultDTO result = new PrepayCreateResultDTO();
        result.setSuccess(true);
        result.setTradeNo(paymentDO.getTradeNo());
        result.getPrepayResult().put("paymentSessionData", response.getPaymentSessionData());

        if (isCardPayment(prepayCreateRequest)) {
            result.getPrepayResult().put("currency", "HKD");
            result.getPrepayResult().put("amount", prepayCreateRequest.getPayAmount());
        }

        return result;
    }

    /**
     * 处理支付宝预授权请求
     */
    private PrepayCreateResultDTO doConsult(PaymentDO paymentDO, PrepayCreateRequest prepayCreateRequest, String merchantRegion) {
        AlipayAuthConsultRequest consultRequest = buildConsultRequest(paymentDO, prepayCreateRequest, merchantRegion);
        AlipayAuthConsultResponse consultResponse = executeConsultRequest(consultRequest);
        return buildConsultResult(consultResponse, paymentDO, prepayCreateRequest);
    }

    /**
     * 构建预授权请求
     */
    private AlipayAuthConsultRequest buildConsultRequest(PaymentDO paymentDO, PrepayCreateRequest prepayCreateRequest, String merchantRegion) {
        PrepayCreateRequest.PrepayCreateExtInfo extInfo = prepayCreateRequest.getExtInfo();
        
        AlipayAuthConsultRequest request = new AlipayAuthConsultRequest();
        request.setCustomerBelongsTo(getCustomerBelongsTo(extInfo.getPayMethod()));
        request.setAuthClientId(antomProperty.getClientId());
        request.setAuthRedirectUrl(buildConsultRedirectUrl(extInfo));
        request.setScopes(new ScopeType[] { ScopeType.AGREEMENT_PAY });
        request.setAuthState(paymentDO.getTradeNo());
        if (Objects.equals(extInfo.getClientType(), ClientTypeEnum.ALIPAY_MINI.getType())) {
            request.setTerminalType(TerminalType.MINI_APP);
        } else {
            request.setTerminalType(TerminalType.WAP);
        }
        request.setOsType(getOsType(extInfo.getClientType()));
        request.setMerchantRegion(merchantRegion);
        request.setRecurringPayment(false);
        return request;
    }

    /**
     * 获取客户归属
     */
    private CustomerBelongsTo getCustomerBelongsTo(Integer payMethod) {
        if (PayMethodEnum.Alipay.getId().equals(payMethod)) {
            return CustomerBelongsTo.ALIPAY_CN;
        } else if (PayMethodEnum.AlipayHK.getId().equals(payMethod)) {
            return CustomerBelongsTo.ALIPAY_HK;
        } else if (PayMethodEnum.GCash.getId().equals(payMethod)) {
            return CustomerBelongsTo.GCASH;
        }
        return null;
    }

    /**
     * 构建预授权回调地址
     */
    private String buildConsultRedirectUrl(PrepayCreateRequest.PrepayCreateExtInfo extInfo) {
        return antomProperty.getCardNotificationUrl() + 
               "/consult/" + 
               extInfo.getPayMethod() + 
               "/" + 
               extInfo.getDeviceNo();
    }

    /**
     * 获取操作系统类型
     */
    private OsType getOsType(Integer clientType) {
        if (Objects.equals(clientType, ClientTypeEnum.IOS_H5.getType())) {
            return OsType.IOS;
        } else if (Objects.equals(clientType, ClientTypeEnum.ANDROID_H5.getType())) {
            return OsType.ANDROID;
        }
        return null;
    }

    /**
     * 执行预授权请求
     */
    private AlipayAuthConsultResponse executeConsultRequest(AlipayAuthConsultRequest request) {
        try {
            log.info("antom consult req = {}", JSONArray.toJSONString(request));
            AlipayAuthConsultResponse response = getAlipayClient().execute(request);
            log.info("antom consult rsp = {}", JSONArray.toJSONString(response));
            return response;
        } catch (Exception e) {
            log.error("antom consult fail. errorMsg = {}", e.getMessage());
            throw new HeraBizException(HeraBizErrorCodeEnum.AliRSP_ERROR);
        }
    }

    /**
     * 构建预授权结果
     */
    private PrepayCreateResultDTO buildConsultResult(AlipayAuthConsultResponse consultResponse, 
            PaymentDO paymentDO, PrepayCreateRequest prepayCreateRequest) {
        if (!AlipayResultStatusEnum.isSuccess(consultResponse.getResult())) {
            throw new HeraBizException(HeraBizErrorCodeEnum.AliRSP_ERROR);
        }

        PrepayCreateResultDTO resultDTO = new PrepayCreateResultDTO();
        resultDTO.setSuccess(true);
        resultDTO.setTradeNo(paymentDO.getTradeNo());

        AlipayPaymentSessionResponse sessionResponse = new AlipayPaymentSessionResponse();
        sessionResponse.setNormalUrl(consultResponse.getAuthUrl());
        sessionResponse.setResult(consultResponse.getResult());
        resultDTO.getPrepayResult().put("paymentSessionData", sessionResponse);

        if (Objects.equals(prepayCreateRequest.getPayway(), PaywayEnum.ANTOM_CHECKOUT_CARD)) {
            resultDTO.getPrepayResult().put("currency", "HKD");
            resultDTO.getPrepayResult().put("amount", prepayCreateRequest.getPayAmount());
        }

        return resultDTO;
    }

    /**
     * 支付工具映射为antom的支付方式
     * PayMethodEnum - > PaymentMethodTypeEnum
     *
     * @param payMethod
     * @return
     */
    private String getPaymentMethodType(Integer payMethod) {
        switch (payMethod) {
            case 201:
                return PaymentMethodTypeEnum.ALIPAY_CN.name();
            case 202:
                return PaymentMethodTypeEnum.ALIPAY_HK.name();
            case 204:
                return PaymentMethodTypeEnum.GCASH.name();
            case 207:
                return PaymentMethodTypeEnum.KAKAOPAY.name();
            case 212:
                return PaymentMethodTypeEnum.GOOGLEPAY.name();
            case 213:
                return PaymentMethodTypeEnum.APPLEPAY.name();
            case 214:
                return PaymentMethodTypeEnum.GRABPAY.name() + "_SG";
            case 215:
                return PaymentMethodTypeEnum.PAYNOW.name();
            case 210:
                return PaymentMethodTypeEnum.SHOPEEPAY.name() + "_SG";
            default:
                throw new HeraBizException(HeraBizErrorCodeEnum.ROUTE_NOT_SUPPORT);
        }

    }

}
