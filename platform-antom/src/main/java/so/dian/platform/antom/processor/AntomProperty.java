package so.dian.platform.antom.processor;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import so.dian.platform.antom.common.enmus.AntomPayMethodEnum;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 开发者平台：https://dashboard.alipay.com/global-payments/developers/quickStart
 */
@Data
@Component
@ConfigurationProperties(prefix = "channel.antom")
public class AntomProperty {

    /**
     * 银行卡授权回调地址
     */
    private String cardNotificationUrl;
    /**
     * 钱包支付回调地址
     */
    private String apmNotificationUrl;
    /**
     * 退款回调地址
     */
    private String refundNotificationUrl;
    /**
     * 网关端点（地址），取自开发者平台
     */
    private String url;
    /**
     * Client ID，取自开发者平台
     */
    private String clientId;
    /**
     * 商户私钥，取自开发者平台
     */
    private String mchPrivateKey;
    /**
     * antom公钥，取自开发者平台
     */
    private String antomPublicKey;

    /**
     * 银行卡支付支持的支付方式
     */
    private List<AntomPayMethodEnum> paymentMethodsOfCard;
    /**
     * 钱包支付支持的支付方式
     */
    private List<AntomPayMethodEnum> paymentMethodsOfApm;

    /**
     * 钱包支付支持取消的支付方式
     */
    private List<AntomPayMethodEnum> supportCancelMethods;
    /**
     * 支付结果url
     */
    private PayUrl payResultUrl;

    /**
     * 商户经营业务所在的国家或地区.该参数遵循 ISO 3166 国家代码标准，是一个二位字母的国家或地区代码。
     * 可能的值包括 US, SG, HK, PK, JP, CN, BR, AU, 和 MY。
     */
    private String regionCode;

    /**
     * 商户ID
     * googlePay必填配置参数
     */
    private String merchantId;

    /**
     * 结算币种
     */
    private String settlementCurrency;

    @Data
    public static class PayUrl {
        private String h5;
    }

    public void setPaymentMethodsOfCard(List<String> paymentMethodsOfCard) {
        this.paymentMethodsOfCard = paymentMethodsOfCard.stream().map(s -> AntomPayMethodEnum.explainName(s)).collect(Collectors.toList());
    }

    public void setPaymentMethodsOfApm(List<String> paymentMethodsOfApm) {
        this.paymentMethodsOfApm = paymentMethodsOfApm.stream().map(s -> AntomPayMethodEnum.explainName(s)).collect(Collectors.toList());
    }
}
