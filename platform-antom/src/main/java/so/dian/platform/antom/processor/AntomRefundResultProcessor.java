package so.dian.platform.antom.processor;

import com.chargebolt.hera.client.enums.PaywayEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.QueryRefundProcessor;
import so.dian.hera.interceptor.dto.req.RefundQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.RefundQueryResultDTO;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AntomRefundResultProcessor implements QueryRefundProcessor {

    @Resource
    private AntomCheckoutHostedProcessor antomCheckoutHostedProcessor;

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.ANTOM_CHECKOUT_CARD, PaywayEnum.ANTOM_CHECKOUT_APM);
    }

    @Override
    public RefundQueryResultDTO orderRefundQuery(RefundQueryResultRequest queryResultRequest) {
       return antomCheckoutHostedProcessor.refundQuery(queryResultRequest);
    }
}
