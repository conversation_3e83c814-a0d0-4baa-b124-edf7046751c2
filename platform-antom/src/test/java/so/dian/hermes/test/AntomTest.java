package so.dian.hermes.test;

import com.alibaba.fastjson.JSONArray;
import com.alipay.global.api.AlipayClient;
import com.alipay.global.api.DefaultAlipayClient;
import com.alipay.global.api.exception.AlipayApiException;
import com.alipay.global.api.request.ams.pay.AlipayPayCancelRequest;
import com.alipay.global.api.response.ams.pay.AlipayPayCancelResponse;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;

/**
 * <AUTHOR>
 */
public class AntomTest {

    public static void main(String[] args) {
        String url = "https://open-sea-global.alipay.com";
        String privateKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDVZkk32UK7/zCCV8/T/ZSruwbSbfX1qiy4qqQdSW2EURyp8FLWGCXQjSJaRPWNhfLQNjW8F0IJ9edi6agSVyqO8w1iakOBr1RsSjyw60/PzvfLsHk4SUoBsZ4XJ7vUXxVWEiP1irZMOQLs2VFlTQl5vjuH3dUbEeppHQngfxZzVmDTPFzVCkdhbnRq/mYThMuXsPKZr/ULVQ7yoinmtsdA06ulvM9WKyocQwY2OnWQrgC/cPdE6uvK4QQV8vDDWIT8EQBvK0AlSsPzr22SCVG5kaj+m692Ia3u5cHHsWK8KOdMgqohJBpmgTA8ofRn9jRb86qhbfzygzqOaxio4A0PAgMBAAECggEAE1ug/rD1rcobY2eF0cwqBV4E42fYLP3P6ryIAKQdjCgzenLzgLpWC9IRsLCaucjR2vNf8n0Fn5Ov9rgNaASCNs9zgl7zWrtF5EDjuoTOC6LRtad/h4yWawrqtV0EW+J4NAOXQXngyY9OZZ/dE1xmpKWODugfocIBSqG4uKWuqq2bwWiG9ETFHcv6ez5lnn920yom+1l/obXkSlRrutZB4PLTPm9eVr6N20pBJdYAtZlg9pvmTTSJHvnBp9TvakYNI2KFfgka4J0th5eihK6ensxCY7W3/1DSDde6pNikIEh/hLgSDFWTNBGO8I5CjJhZbrUBGkrTa5Aeq2XjCE23CQKBgQD5urrr/vynpk7EEc6aali1byvTSWVNXOnhb3uOueXIPC3RdHkrBXEsJ6WkNJpCmErtPUbfzpwGnpFnJNG9DZ8C5BxaQZWK1lQgkyk5g8Xz6gTjxQN+3fPeIEc3vINKargFfrUAcO2ChIi7T/zQwDhgyWxltbvja35cNZROUNJL4wKBgQDawgazPuBklU/RzCp2O0akO9sGA9ZCx4tCLBfVYgpzyrMxbHKAnNIUnM7MaDwO0NpVAV+p89bTc+1n6HLsAO7M6l5ItfpCpebqIwaFg6R/+8d0fHbUcQtCtZwmX/Sm+9nf0FGR5qi51Ja0/q3gUD7AR0gFLVMIVET6GW2+wJMZ5QKBgQDCWuZxFwERhShWBjnSCaRCiGQGS1/w5rgLQFVcDifcP6G1Gr3WkqSypaSPeHT6EUZ2/rKzKjxaFny5Vnl5dilj+Z5rTBGTfRz6QS3bWkfsfhwSHFmNGjZakKp8oVVYzFetxyAdWMYwRu+XQDhVStRV101qlxbEdvtfzj8++D3C2wKBgEHruCMprmsBB5WcJauNP0Nceir82wMyqIdfTCtUKPwtuAQLvxF8sF3fxv2X54k7bJ+zE0Xeof0EcSUfygnOWifL9G+KzwhogAZMWbnAKsEaeUdj/lcUXaVTnuN9hPcQ693jI8GItY2SP3v5RyJSbCZ7FHvi5aOb6VFIembCF1H1AoGAZjwl1+ot84MkErZr7ST0faP6EWnXOllwyAOjM1eKk9s6b08mWvBPeL54Cwu5fnd5xIRKR4e+R6U+MwRYIqCyLuV1F2s/rO8neqtmAJcSSbJOYiITRSETXxzu9eh1OfOlL93yv00+zx7BldgQCJdYZx0Wak44nO720wGZRNq+a30=";
        String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjN/WJJHIfiIyczfYd4RyWxQF1DwKVvZqUIKaZQjkOye5eaCIGvGHDmqzGLpsfLkLPqZYZlmwGr3zCas4wlU2dMSbxWAHAeXeODQ28e2FKykd83VSIeaBK/ZXS52rjELgeqUCiMzAIpi7o2BnwdSoC0zIdF1WUMJD2jrPq45UR7LJK72f+CPf+hlRUU+djXkYErs16Vu2RQJpVAHNEaOWTpczN8z/PGfSRLU2kkbVDjQ+5YhPflEmueXIZydUSX4hzO2U2EF48uyefDnWlp2fZUnS6R6OL11pT3kcBduVRYBhTVRtRqA52SWeRD0Y87i0eve8YUS5UvGaqk17dvAoGQIDAQAB";
        String clientId = "SANDBOX_5YC02N2ZK53D02152";
        AlipayClient alipayClient = new DefaultAlipayClient(url, privateKey, publicKey,clientId);

//        AlipayInquiryRefundRequest request = new AlipayInquiryRefundRequest();
//        request.setRefundRequestId("CNR622410091429565620156414");
//        AlipayInquiryRefundResponse refundResponse;
//        try {
//            log.info("antom refundQuery req = {}", JSONArray.toJSONString(request));
//            refundResponse = alipayClient.execute(request);
//            log.info("antom refundQuery rsp = {}", JSONArray.toJSONString(refundResponse));
//        } catch (AlipayApiException e) {
//            String errorMsg = e.getMessage();
//            log.error("antom refundQuery fail. errorMsg = {}",errorMsg);
//            throw new HeraBizException(HeraBizErrorCodeEnum.AliRSP_ERROR);
//        }
//        System.out.println(refundResponse);
//        RefundQueryResultDTO resultDTO = new RefundQueryResultDTO();
//        if(AlipayResultStatusEnum.isSuccess(refundResponse.getResult())) {
//            if(Objects.equals(refundResponse.getRefundStatus(),TransactionStatusType.SUCCESS)){
//                resultDTO.setStatus(PayStatus.REFUNDED.getCode());
//                OffsetDateTime offsetDateTime = OffsetDateTime.parse(refundResponse.getRefundTime(), DateTimeFormatter.ISO_OFFSET_DATE_TIME);
//                resultDTO.setRefundTime(Date.from(offsetDateTime.toInstant()));
////                resultDTO.setRefundTime(DateUtil.parseDate(refundResponse.getRefundTime(),DateUtil.TIMESTAMP_PATTERN_01));
//            } else if (Objects.equals(refundResponse.getRefundStatus(),TransactionStatusType.PROCESSING)){
//                resultDTO.setStatus(PayStatus.REFUNDING.getCode());
//            } else if (Objects.equals(refundResponse.getRefundStatus(),TransactionStatusType.FAIL)){
//                resultDTO.setStatus(PayStatus.FAIL.getCode());
//            }
//        }
//        System.out.println(resultDTO);

        AlipayPayCancelRequest cancelRequest = new AlipayPayCancelRequest();
        cancelRequest.setPaymentRequestId("CNT122410141007575621855401");
        AlipayPayCancelResponse cancelResponse;
        try {
            System.out.println("antom doCancel req = " + JSONArray.toJSONString(cancelRequest));
            cancelResponse =alipayClient.execute(cancelRequest);
            System.out.println("antom doCancel rsp = " + JSONArray.toJSONString(cancelResponse));
        } catch (AlipayApiException e) {
            String errorMsg = e.getMessage();
            System.out.println("antom doCancel fail = " + errorMsg);
            throw new HeraBizException(HeraBizErrorCodeEnum.AliRSP_ERROR);
        }
    }
}
