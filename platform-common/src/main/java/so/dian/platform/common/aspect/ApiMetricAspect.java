package so.dian.platform.common.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.util.StopWatch;
import so.dian.platform.common.annotation.ApiMonitor;

/**
 * <AUTHOR>
 * @date 2021/10/20
 */
@Slf4j
@Aspect
public class ApiMetricAspect {

    @Around("@annotation(apiMonitor)")
    public Object doAround(ProceedingJoinPoint pjp, ApiMonitor apiMonitor) throws Throwable {
        StopWatch watch = new StopWatch();

        try {
            watch.start();

            return pjp.proceed();
        } finally {
            watch.stop();
            log.info("apiEnum=[{}] elapsedTime={}", apiMonitor.value().toString(), watch.getTotalTimeMillis());
        }
    }


}
