package so.dian.platform.common.configuration;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import so.dian.platform.common.aspect.ApiMetricAspect;

/**
 * <AUTHOR>
 * @date 2021/10/20
 */
@Configuration
@ComponentScan("so.dian.platform.common.*")
public class PlatformCommonConfiguration implements InitializingBean {

    @Bean
    public ApiMetricAspect apiMetricAspect() {
        return new ApiMetricAspect();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
    }
}
