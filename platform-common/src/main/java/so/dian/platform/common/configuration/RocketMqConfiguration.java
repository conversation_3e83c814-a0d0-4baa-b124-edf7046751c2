package so.dian.platform.common.configuration;

import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @desc 自建RocketMQ配置
 * ## 通用参数
 * 参数名	默认值	说明
 * namesrvAddr		Name Server地址列表，多个NameServer地址用分号隔开
 * clientIP	本机IP	客户端本机IP地址，某些机器会发生无法识别客户端IP地址情况，需要应用在代码中强制指定
 * instanceName	DEFAULT	客户端实例名称，客户端创建的多个Producer、Consumer实际是共用一个内部实例（这个实例包含网络连接、线程资源等）
 * clientCallbackExecutorThreads	4	通信层异步回调线程数
 * pollNameServerInteval	30000	轮询Name Server间隔时间，单位毫秒
 * heartbeatBrokerInterval	30000	向Broker发送心跳间隔时间，单位毫秒
 * persistConsumerOffsetInterval	5000	持久化Consumer消费进度间隔时间，单位毫秒
 * <p>
 * Producer配置
 * 参数名	默认值	说明
 * producerGroup	DEFAULT_PRODUCER	Producer组名，多个Producer如果属于一个应用，发送同样的消息，则应该将它们归为同一组
 * createTopicKey	TBW102	在发送消息时，自动创建服务器不存在的topic，需要指定Key，该Key可用于配置发送消息所在topic的默认路由。
 * defaultTopicQueueNums	4	在发送消息，自动创建服务器不存在的topic时，默认创建的队列数
 * sendMsgTimeout	10000	发送消息超时时间，单位毫秒
 * compressMsgBodyOverHowmuch	4096	消息Body超过多大开始压缩（Consumer收到消息会自动解压缩），单位字节
 * retryAnotherBrokerWhenNotStoreOK	FALSE	如果发送消息返回sendResult，但是sendStatus!=SEND_OK，是否重试发送
 * retryTimesWhenSendFailed	2	如果消息发送失败，最大重试次数，该参数只对同步发送模式起作用
 * maxMessageSize	4MB	客户端限制的消息大小，超过报错，同时服务端也会限制，所以需要跟服务端配合使用。
 * transactionCheckListener		事务消息回查监听器，如果发送事务消息，必须设置
 * checkThreadPoolMinSize	1	Broker回查Producer事务状态时，线程池最小线程数
 * checkThreadPoolMaxSize	1	Broker回查Producer事务状态时，线程池最大线程数
 * checkRequestHoldMax	2000	Broker回查Producer事务状态时，Producer本地缓冲请求队列大小
 * RPCHook	null	该参数是在Producer创建时传入的，包含消息发送前的预处理和消息响应后的处理两个接口，用户可以在第一个接口中做一些安全控制或者其他操作。
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "spring.rocketmq.producer")
public class RocketMqConfiguration {

    /**
     * 分号分隔 "localhost:9876;localhost:9875"
     */
    @Value("${spring.rocketmq.name-server}")
    private String nameServerAddr;

    /**
     * Producer组名，多个Producer如果属于一个应用，发送同样的消息，则应该将它们归为同一组
     */
    @Value("${notify.rocketmq.self.producerGroup}")
    private String producerGroup;

    /**
     * 发送失败是否重试下一个broker
     */
    private Boolean retryAnotherBrokerWhenNotStoreOK;

    /**
     * 异步发送失败重试次数，异步重试不会选择其他 broker，仅在同一个 broker 上做重试，不保证消息不丢
     */
    private Integer retryTimesWhenSendAsyncFailed;

    /**
     * 消息发送超时时间
     */
    private Integer sendMsgTimeout;

    /**
     * 消息Body超过多大开始压缩（Consumer收到消息会自动解压缩），单位字节
     */
    private Integer compressMsgBodyOverHowmuch;

    /**
     * 如果消息发送失败，最大重试次数，该参数只对同步发送模式起作用
     */
    private Integer retryTimesWhenSendFailed;

    private Integer maxMessageSize;

    /**
     * 发送同步&异步消息失败重试次数
     */
    @Value("${spring.rocketmq.retryCount:3}")
    private Integer retryCount;


    @Bean("defaultProducer")
    public DefaultMQProducer defaultProducer() throws MQClientException {
        DefaultMQProducer producer = new DefaultMQProducer(producerGroup);
        producer.setNamesrvAddr(nameServerAddr);
        producer.setRetryTimesWhenSendFailed(retryTimesWhenSendFailed);
        producer.setSendMsgTimeout(sendMsgTimeout);
        producer.setCompressMsgBodyOverHowmuch(compressMsgBodyOverHowmuch);
        producer.setMaxMessageSize(maxMessageSize);
        producer.setRetryAnotherBrokerWhenNotStoreOK(retryAnotherBrokerWhenNotStoreOK);
        producer.setRetryTimesWhenSendAsyncFailed(retryTimesWhenSendAsyncFailed);
        producer.start();
        log.info("RocketMQ default producer start success... producer: {}", JSON.toJSONString(producer));
        return producer;
    }

}
