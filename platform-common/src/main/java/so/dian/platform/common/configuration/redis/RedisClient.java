package so.dian.platform.common.configuration.redis;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicInteger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class RedisClient {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    private final static String appName = "hera";

    public void set(String ns, String key, Object value, long expireTime) {
        try {
            String key0 = this.generateKey(ns, key);
            log.debug("set to redis, key = {}", key0);
            redisTemplate.opsForValue().set(key0, JSON.toJSONString(value));
            redisTemplate.expire(this.generateKey(ns, key), expireTime, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("ns:{},key:{},value:{}", ns, key, value, e);
        }
    }

    public Boolean setNX(String ns, String key, Object value, long expireTime){
        String redisKey = this.generateKey(ns, key);
        Boolean result = redisTemplate.opsForValue().setIfAbsent(redisKey, JSON.toJSONString(value));
        if(result){
            redisTemplate.expire(redisKey, expireTime, TimeUnit.SECONDS);
        }
        return result;
    }

    public void set(String ns, String key, Object value) {
        try {
            redisTemplate.opsForValue().set(this.generateKey(ns, key), JSON.toJSONString(value));
        } catch (Exception e) {
            log.error("ns:{},key:{},value:{}", ns, key, value, e);
        }
    }

    public <T> T get(String ns, String key, Class<T> clazz) {
        try {
            String key0 = this.generateKey(ns, key);
            log.debug("get from redis, key = {}", key0);
            Object object = redisTemplate.opsForValue().get(key0);
            T result = JSON.parseObject((String) object, clazz);
            log.debug("get from redis, result = {}", result);
            return result;
        } catch (Exception e) {
            log.error("ns:{},key:{}", ns, key, e);
            return null;
        }
    }

    public void hashSet(String ns, String key, Object hashKey, Object value) {
        redisTemplate.opsForHash().put(this.generateKey(ns, key), hashKey, JSON.toJSONString(value));
    }

    public <T> T hashGet(String ns, String key, Object hashKey, Class<T> clazz) {
        Object object = redisTemplate.opsForHash().get(this.generateKey(ns, key), hashKey);
        T result = JSON.parseObject((String) object, clazz);
        return result;
    }

    public void setMap(String ns, String key, Map<String, String> value, long expireTime) {

        redisTemplate.opsForHash().putAll(this.generateKey(ns, key), value);
        redisTemplate.expire(this.generateKey(ns, key), expireTime, TimeUnit.SECONDS);
    }

    public Map<Object, Object> getMap(String ns, String key) {
        return redisTemplate.opsForHash().entries(this.generateKey(ns, key));
    }

    public boolean exists(String ns, String key) {
        return redisTemplate.hasKey(this.generateKey(ns, key));
    }

    public void remove(String ns, String key) {
        if (exists(ns, key)) {
            redisTemplate.delete(this.generateKey(ns, key));
        }
    }

    private String generateKey(String ns, String key) {
        return appName + "$" + ns + "$" + key;
    }

    public Long incr(String ns, String key, int value) {

        RedisAtomicInteger redisAtomicInteger = new RedisAtomicInteger(this.generateKey(ns, key),
                redisTemplate.getConnectionFactory());
        return ((Integer) redisAtomicInteger.addAndGet(value)).longValue();
    }


    public Long decr(String ns, String key, int value) {

        RedisAtomicInteger redisAtomicInteger = new RedisAtomicInteger(this.generateKey(ns, key),
                redisTemplate.getConnectionFactory());
        return ((Integer) redisAtomicInteger.addAndGet(0 - value)).longValue();
    }

    public Long appendToList(String key, String value){
        return redisTemplate.opsForList().leftPush(key, value);
    }

    public Long firstAppendToList(String key, String value){
        return redisTemplate.opsForList().rightPush(key, value);
    }

    public String popOfList(String key){
        return redisTemplate.opsForList().rightPop(key);
    }

    public Set getSetAll(String key){
        return redisTemplate.opsForSet().members(key);
    }

    public void addOfSet(String key,String value){
        redisTemplate.opsForSet().add(key, value);
    }

    public Long removeOfSet(String key,Object value){
        return redisTemplate.opsForSet().remove(key, value);
    }

    public boolean isMember(String key,String member) {
        return redisTemplate.opsForSet().isMember(key, member);
    }
}
