package so.dian.platform.common.configuration.redis.lock;

import com.alibaba.fastjson.JSON;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.SimpleEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import so.dian.platform.common.configuration.redis.RedisClient;

import javax.annotation.Resource;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class DistributedLockAspect {

    private static final String KEY_PREFIX = "hera:lock:";

//    @Resource
//    private RedissonClient redissonClient;
    @Resource
    private RedisClient redisClient;

    private final SpelExpressionParser parser = new SpelExpressionParser();

    private final ParameterNameDiscoverer parameterNameDiscoverer = new DefaultParameterNameDiscoverer();

    @Around(value = "@annotation(distributedLock)")
    public Object distributedLockAround(ProceedingJoinPoint proceedingJoinPoint, DistributedLock distributedLock) throws Throwable {

        // 获取方法参数
        Object[] args = proceedingJoinPoint.getArgs();
        // 获取方法参数名称
        Method method = ((MethodSignature) proceedingJoinPoint.getSignature()).getMethod();
        String[] parameterNames = parameterNameDiscoverer.getParameterNames(method);

        // 构建spel上下文
        SimpleEvaluationContext context = SimpleEvaluationContext.forReadOnlyDataBinding().build();
        for (int i = 0; i < args.length; i++) {
            context.setVariable(parameterNames[i], args[i]);
        }

        // 解析spel参数
        Expression expression = parser.parseExpression(distributedLock.key());
        String lockKey = expression.getValue(context, String.class);
        Assert.hasLength(lockKey, "lockKey不能为空");

        // 加分布式锁
        boolean lock = redisClient.setNX(KEY_PREFIX, lockKey, 1, 1);
        if (!lock) {
            log.warn("交易锁定中，请稍后重试, req:{}", JSON.toJSONString(args));
            throw HeraBizException.create(HeraBizErrorCodeEnum.OPERATE_BUSY,"交易锁定中，请稍后重试");
        }

        try {
            return proceedingJoinPoint.proceed();
        } finally {
            redisClient.remove(KEY_PREFIX, lockKey);
        }

    }
}
