//package so.dian.platform.common.configuration.redis.lock;
//
//import lombok.Data;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//
///**
// * <AUTHOR>
// */
//@Data
//@ConfigurationProperties("spring.redis")
//public class RedisProperties {
//
//    /**
//     * ip
//     */
//    private String host;
//
//    /**
//     * 密码
//     */
//    private String password;
//
//    /**
//     * 端口
//     */
//    private int port;
//
//    /**
//     * 超时时间
//     */
//    private int timeout;
//
//}
