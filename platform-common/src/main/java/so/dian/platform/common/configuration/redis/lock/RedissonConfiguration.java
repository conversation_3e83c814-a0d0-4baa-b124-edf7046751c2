//package so.dian.platform.common.configuration.redis.lock;
//
//
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//import org.redisson.Redisson;
//import org.redisson.api.RedissonClient;
//import org.redisson.codec.JsonJacksonCodec;
//import org.redisson.config.Config;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import javax.annotation.Resource;
//
///**
// * <AUTHOR>
// */
//@Data
//@Slf4j
//@Configuration
//public class RedissonConfiguration {
//
//    @Resource
//    private RedisProperties redisProperties;
//
//    @Resource
//    private RedisPoolProperties redisPoolProperties;
//
//    public Config config() {
//        Config config = new Config();
//        config.useSingleServer().setPassword(redisProperties.getPassword());
//        config.useSingleServer().setRetryAttempts(3);
//        config.useSingleServer().setRetryInterval(1500);
//        config.useSingleServer().setTimeout(redisPoolProperties.getMaxWait());
//        config.useSingleServer().setConnectTimeout(redisProperties.getTimeout());
//        config.useSingleServer().setConnectionPoolSize(redisPoolProperties.getMaxActive());
//        config.useSingleServer().setConnectionMinimumIdleSize(redisPoolProperties.getMaxIdle());
//        config.setCodec(new JsonJacksonCodec());
//        return config;
//    }
//
//    @Bean
//    public RedissonClient redissonClient() {
//        log.info("RedissonSingleConfiguration init....");
//        return Redisson.create(this.config());
//    }
//}
