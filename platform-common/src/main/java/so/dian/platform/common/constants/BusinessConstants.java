package so.dian.platform.common.constants;

/**
 * <AUTHOR>
 */
public class BusinessConstants {

    /** ------------------- 字符 --------------------------------*/

    /**
     * 英文空串
     */
    public static final String EMPTY = "";

    /**
     * 英文冒号
     */
    public static final String COLON = ":";

    /**
     * 英文冒号
     */
    public static final String COMMA = ",";

    public static final String LINE = "-";

    /** ------------------- 请求头 --------------------------------*/

    public static final String ACCEPT = "Accept";

    public static final String ACCEPT_VALUE = "application/json";

    public static final String CONTENT_TYPE = "Content-type";

    public static final String CONTENT_TYPE_VALUE = "application/json; charset=utf-8";

    /** ------------------- 参数 --------------------------------*/

    public static final String PATH_PAY_PROD_TYPE = "{payProdType}";

    public static final String PATH_APPLICATION_ID = "{applicationId}";

    public static final String PATH_OUT_TRADE_NO = "{out_trade_no}";

    public static final String PATH_OUT_ORDER_NO = "{out_order_no}";

    public static final String PATH_TRANSACTION_ID = "{transaction_id}";

    public static final String PATH_CHANNEL_ID = "{channelId}";

    public static final String OUT_REFUND_NO = "{out_refund_no}";

    /**
     * 应用ID
     */
    public static final String APPID = "appid";
    /**
     * 商户号
     */
    public static final String MCHID = "mchid";

    /**
     * 商户号
     */
    public static final String MCH_ID = "mch_id";

    /**
     * 服务商-商户号
     */
    public static final String SP_MCHID = "sp_mchid";

    /**
     * 子商户号
     */
    public static final String SUB_MCHID = "sub_mchid";

    /**
     * 商户订单号
     */
    public static final String OUT_ORDER_NO = "out_order_no";

    /**
     * 服务ID
     */
    public static final String SERVICE_ID = "service_id";

    public static final String PACKAGE = "package";


    /**
     * 签名类型
     */
    public static final String SIGN_TYPE = "sign_type";

    /**
     * 随机字符串
     */
    public static final String NONCE_STR  = "nonce_str";

    /**
     * 时间戳
     */
    public static final String TIMESTAMP  = "timestamp";


    public static final String ERROR_CODE = "err_code";

    public static final String ERROR_CODE_DESC = "err_code_des";




    /** ------------------- 应答码 --------------------------------*/

    public static final String WECHAT_CODE_ORDER_CLOSED = "ORDER_CLOSED";

    public static final String WECHAT_CODE_RESOURCE_NOT_EXISTS = "RESOURCE_NOT_EXISTS";


    public static final String WECHAT_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'+08:00'";


    public static final String CURRENCY_CNY = "CNY";

    /** ------------------- 微信支付通知状态码 --------------------------------*/


    public static final String SUCCESS = "SUCCESS";

    public static final String REFUND = "REFUND";

    public static final String NOTPAY = "NOTPAY";

    public static final String CLOSED = "CLOSED";

    public static final String REVOKED = "REVOKED";

    public static final String USERPAYING = "USERPAYING";

    public static final String PAYERROR = "PAYERROR";

    public static final String FAIL = "FAIL";


    /**
     * 交易类型 1 支付
     */
    public static final Integer TRANSACTION_PAY = 1;
    /**
     * 交易类型 2 退款
     */
    public static final Integer TRANSACTION_REFUND = 2;
    /**
     * 银行卡标识
     */
    public static final String CARD_TOKEN = "cardToken";

    /**
     * 银行卡组织为识别交易而分配的交易ID
     */
    public static final String  NETWORK_TRANSACTION_ID= "networkTransactionId";

    /**
     * 请款类型
     */
    public static final String CAPTURE_MODE = "captureMode";


}
