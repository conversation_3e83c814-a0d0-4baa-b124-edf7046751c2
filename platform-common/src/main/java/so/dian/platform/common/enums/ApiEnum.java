package so.dian.platform.common.enums;

/**
 * <AUTHOR>
 * @date 2021/10/20
 */
public enum ApiEnum {

    //微信支付分创建
    WECHAT_PAYSCORE_CREATE,

    //微信支付分查询
    WECHAT_PAYSCORE_QUERY,

    //微信支付分取消
    WECHAT_PAYSCORE_CANCEL,

    //微信支付分完结
    WECHAT_PAYSCORE_COMPLETE,

    //宝付-微信支付分创建
    BAOFU_WECHAT_PAYSCORE_CREATE,

    //宝付-微信支付分查询
    BAOFU_WECHAT_PAYSCORE_QUERY,

    //宝付-微信支付分取消
    BAOFU_WECHAT_PAYSCORE_CANCEL,

    //宝付-微信支付分完结
    BAOFU_WECHAT_PAYSCORE_COMPLETE,

    //微美-微信支付分创建
    WEIMEI_WECHAT_PAYSCORE_CREATE,

    //微美-微信支付分查询
    WEIMEI_WECHAT_PAYSCORE_QUERY,

    //微美-微信支付分取消
    WEIMEI_WECHAT_PAYSCORE_CANCEL,

    //微美-微信支付分完结
    WEIMEI_WECHAT_PAYSCORE_COMPLETE,

    //芝麻借还创建
    ZHIMA_LOAN_CREATE,

    //芝麻借还查询
    ZHIMA_LOAN_QUERY,

    //芝麻借还取消
    ZHIMA_LOAN_CANCEL,

    //芝麻借还完结
    ZHIMA_LOAN_COMPLETE,

    //支付宝发起签约
    ALIPAY_SIGN,

    //支付宝签约查询
    ALIPAY_SIGN_QUERY,

    //支付宝发起解约
    ALIPAY_UNSIGN,

    //支付宝收单交易创建
    ALIPAY_TRADE_CREATE,

    //支付宝收单交易关闭
    ALIPAY_TRADE_CLOSE,

    //支付宝收单交易查询
    ALIPAY_TRADE_QUERY,

    //支付宝发起代扣
    ALIPAY_WITHHOLD_TRADE,

    //支付宝代扣查询
    ALIPAY_WITHHOLD_TRADE_QUERY,

    //支付宝收单交易退款
    ALIPAY_TRADE_REFUND,

    //支付宝收单交易退款查询
    ALIPAY_TRADE_REFUND_QUERY,

    //支付宝企业转账
    ALIPAY_FUND_TRANSFER,

    //支付宝企业转账查询
    ALIPAY_FUND_TRANSFER_QUERY,

    //小电运营活动-支付宝企业转账
    XDYYHD_ALIPAY_FUND_TRANSFER,

    //支付宝预授权冻结
    ALIPAY_PREAUTH_FREEZE,

    //支付宝预授权查询
    ALIPAY_PREAUTH_QUERY,

    //支付宝预授权解冻
    ALIPAY_PREAUTH_UNFREEZE,

    //支付宝预授权转支付
    ALIPAY_PREAUTH_PAY,

    //微信支付下单
    WECHAT_TRADE,

    //微信支付下单
    WECHAT_TRADE_QUERY,

    //微信支付关闭
    WECHAT_TRADE_CLOSE,

    //微信支付退款
    WECHAT_TRADE_REFUND,

    //微信支付退款查询
    WECHAT_TRADE_REFUND_QUERY,

    //微信支付并签约
    WECHAT_PAY_AND_SIGN,

    //微信发起签约
    WECHAT_SIGN,

    //微信签约查询
    WECHAT_SIGN_QUERY,

    //微信发起解约
    WECHAT_UNSIGN,

    //微信发起代扣
    WECHAT_WITHHOLD,

    //微信发起代扣查询
    WECHAT_WITHHOLD_QUERY,

    //微信企业打款
    WECHAT_TRANSFER,

    //微信企业打款查询
    WECHAT_TRANSFER_QUERY,

    //qq钱包发起支付
    QPAY_TRADE,

    //qq钱包支付查询
    QPAY_TRADE_QUERY,

    //qq钱包支付关闭
    QPAY_TRADE_CLOSE,

    //qq钱包发起退款
    QPAY_TRADE_REFUND,

    //qq钱包退款查询
    QPAY_TRADE_REFUND_QUERY,

    //苏宁支付发起支付
    SUNING_PAY_TRADE,

    //苏宁支付发起查询
    SUNING_PAY_TRADE_QUERY,

    //苏宁支付发起退款
    SUNING_PAY_TRADE_REFUND,

    //苏宁支付退款查询
    SUNING_PAY_TRADE_REFUND_QUERY,

    //银联支付发起支付
    UNION_PAY_TRADE,

    //银联支付发起查询
    UNION_PAY_TRADE_QUERY,

    //银联支付发起退款
    UNION_PAY_TRADE_REFUND;

    ApiEnum() {
    }
}
