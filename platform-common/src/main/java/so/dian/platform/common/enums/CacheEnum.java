package so.dian.platform.common.enums;

import lombok.Getter;

@Getter
public enum CacheEnum {

    PLAT_PENDING_ORDERS("PLAT_PENDING_ORDERS", -1),
    PLAT_CANCELING_ORDERS("PLAT_CANCELING_ORDERS", -1),

    USER_APPLEPAY_BIND_FLAG("USER_APPLEPAY_BIND_FLAG", 30),
    PAY_ORDER("PAY_ORDER", 1800),
    REFUND("REFUND", 10),
    REFUND_TASK("HERA_REFUND_TASK", 1800),


    //    PREAUTH_TRADE_NO("PREAUTH_TRADE_NO", 60),
//    CAPTURE_TRADE_NO("CAPTURE_TRADE_NO", 60),
    BANKCARD_NOTIFY("CAPTURE_TRADE_NO", 60),
    WOOSHPAY_NOTIFY("WOOSHPAY_NOTIFY", 60),
    PINGPONG_NOTIFY("PINGPONG_NOTIFY", 60),
    ANTOM_NOTIFY("ANTOM_NOTIFY", 60),
    MIDTRANS_NOTIFY("MIDTRANS_NOTIFY", 60),
    AIRWALLEX_TOKEN("AIRWALLEX_TOKEN",1200),
    // 支付单和设备绑定关系，缓存7天
    PAYMENT_DEVICE_MAPPING("PAYMENT_DEVICE_MAPPING", 604800),
    // 缓存90天
    PAYMENT_EXT_CACHE_INFO("PAYMENT_EXT_CACHE_INFO", 7776000),
    ;

    public final String ns;
    public final Integer expiredTime;//单位秒

    CacheEnum(String ns, Integer expiredTime) {
        this.ns = ns;
        this.expiredTime = expiredTime;
    }

}
