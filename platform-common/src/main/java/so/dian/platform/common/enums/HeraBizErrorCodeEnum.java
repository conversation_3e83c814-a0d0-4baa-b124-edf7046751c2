package so.dian.platform.common.enums;

import lombok.Getter;
import so.dian.commons.eden.enums.EnumInterface;

@Getter
public enum HeraBizErrorCodeEnum implements EnumInterface<HeraBizErrorCodeEnum> {

    REQUIRE_NOT_NULL(100, "must be not null"),

    DEFAULT_ERROR(1000, "Error", "Error"),
    PARAM_ERROR(1002, "param error", "param error"),
    CLIENT_EMPTY(1003, "client empty", "client empty"),
    CLIENT_IP_GET_FAIL(1004, "client ip get fail", "本机ip获取失败"),
    LVY_FALLBACK(1005, "lvy fallback", "资金账户中心请求失败"),
    UNSUPPORTED_CURRENCY(1006, "UNSUPPORTED_CURRENCY", "UNSUPPORTED_CURRENCY"),

    PAYMENT_NOT_EXIST(1101, "payment exception", "payment不存在"),
    PAYMENT_EXIST(1102, "payment exception", "payment已存在"),

    AMOUNT_INVALID(2000, "amount invalid", "amount invalid"),

    ROUTE_EMPTY(2050, "route empty", "路由信息不存在"),
    ROUTE_NOT_SUPPORT(2051, "route channel not support", "支付渠道不支持"),

    QQRSP_EMPTY(2100, "qq rsp empty", "qq钱包响应信息为空"),
    WXRSP_EMPTY(2150, "wx rsp empty", "微信响应信息为空"),
    AliRSP_EMPTY(2150, "alipay rsp empty", "支付宝响应信息为空"),

    AliRSP_ERROR(2150, "alipay rsp ERROR", "支付宝响应异常"),
    WXRSP_ERROR(2150, "wx rsp ERROR", "微信响应异常"),
    RSP_EMPTY(2150, "rsp ERROR", "响应为空"),

    ALIRSP_REFUND_EMPTY(2151, "alipay refund empty", "支付宝退款订单不存在"),
    GEN_SIGN_ERROR(2152, "generate sign error", "生成签名异常"),
    ILLEGAL_ERROR(2153, "illegal error", "非法验证错误"),


    AIYG_ERROR(2250, "aiyuangong error", "aiyg error"),

    PRIVATE_KEY_ERROR(2350, "私钥生成失败", "private key error"),
    PUBLIC_KEY_ERROR(2351, "公钥生成失败", "public key error"),
    SIGN_ERROR(2352, "签名生成失败", "sign error"),
    SIGN_VERIFY_ERROR(2353, "签名验证失败", "sign verify error"),
    ORDER_STATUS_ERROR(2354, "订单状态已被修改,不进行处理", "stats_error"),
    ORDER_NON_EXIST(2355, "订单不存在", "order non exist"),
    PLAT_PAY_ALREADY_EXIST(2356, "plat pay already exist", "三方支付订单已存在"),
    PLAT_PAY_DATA_TYPE_ERROR(2357, "plat pay data type error", "渠道附加参数类型不正确"),
    CARD_BIND_RECORD_NOT_EXIST(2358, "card bind record not exist", "绑卡记录不存在"),
    CARD_STATUS_ERROR(2359, "card status error", "卡状态不正确"),
    CARD_UPDATE_ERROR(2360, "card update fail", "卡状态已被修改,不进行处理"),
    CARD_RANK_MAP_IS_EMPTY(2360, "card rank map can not empty", "卡排序列表不能为空"),
    PAY_BIZ_TYPE_NOT_EXIST(2361, "payment biz type not exist", "支付业务类型不存在"),
    REFUND_AMOUNT_ERROR(2362, "refund amount error", "退款金额错误"),
    REFUND_FAILED(2363, "refund amount error", "退款金额错误"),

    BIZ_NOT_SUPPORT(2400, "biz type not support", "不支持的业务类型"),
    ACCOUNT_NOT_SUPPORT(2400, "account type not support", "不支持的账户类型"),
    ACCOUNT_TASK_ALREADY_SUCCESS(2401, "account task already success", "对账文件已拉取成功"),
    ACCOUNT_TASK_DOWNLOAD_FAIL(2402, "account task download fail", "对账文件下载失败"),
    REVERSE_DATA_NON_EXIST(2403, "reverse data non exist", "待冲账数据不存在或不匹配"),
    TASK_NON_EXIST(2404, "task non exist", "任务不存在"),
    TASK_PROCESSING(2405, "task is processing", "任务执行中"),
    TASK_ALREADY_SUCCESS(2406, "task already success", "任务已成功"),
    TASK_ALREADY_EXIST(2407, "task already exist", "任务已存在"),
    ACCOUNT_FETCH_TIME_ERROR(2408, "account fetch time error", "对账文件获取时间有误，请检查后重新填写"),
    FETCH_TASK_NONEXIST(2409, "fetch task not exist", "账单获取任务不存在"),
    FETCH_TASK_LOCKED(2410, "fetch task locked", "账单获取任务已被锁定"),

    STATUS_ERROR(2500,"status error","订单状态异常"),
    CALLBACK_STATUS_ERROR(2501,"callback status error","回调订单状态异常"),
    CONTRACT_RECORD_NOTEXSIT(2502,"contract is empty","签约记录不存在"),
    WXRSP_STATUS_NOTEXSIT(2503,"wx rsp status not exsit","微信响应状态不存在"),
    NO_RECORD_TO_TERMINATE(2504,"no record is waiting for terminating","不存在待解约订单"),
    REFUND_AMOUNT_INVALID(2505,"refund amount invalid","退款金额错误"),
    REFUND_EXIST(2506,"refund exist", "退款单已存在"),
    REFUND_NOT_EXIST(2506,"refund not exist", "退款单不存在"),
    NOT_SUPPORT_MULTIPLE_REFUND(2507,"not support multiple refund", "不支持多笔退款"),

    ACCOUNT_UPDATE_ERROR(2801, "account update error", "账户更新失败"),
    ACCOUNT_NOT_EXIST(2802, "account not exist", "账户不存在"),
    ACCOUNT_STATUS_ERROR(2803, "account status error", "账户状态异常"),
    ACCOUNT_BALANCE_NOT_ENOUGH(2803, "account balance not enough", "账户余额不足"),

    OPERATE_BUSY(4000, "操作繁忙，请稍后重试"),

    ORDER_NO_BLANK(13, "订单号不可为空"),
    TRADE_NO_BLANK(13, "商户单号不可为空"),

    ORDER_UPDATE_MEMBER_TYPE_ERROR(4001, "修改订单会员类型异常");

    private Integer code;

    private String desc;

    private String message;

    HeraBizErrorCodeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    HeraBizErrorCodeEnum(int code, String desc, String message) {
        this(code, message);
        this.desc = desc;
    }


    @Override
    public HeraBizErrorCodeEnum getDefault() {
        return DEFAULT_ERROR;
    }
}
