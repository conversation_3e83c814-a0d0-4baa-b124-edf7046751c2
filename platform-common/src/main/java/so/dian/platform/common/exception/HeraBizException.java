package so.dian.platform.common.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;

@Data
@EqualsAndHashCode(callSuper = true)
public class HeraBizException extends RuntimeException {

    private int code;

    private String desc;

    private String msg;

    private static String toMsg(int code, String msg) {
        return "[" + code + "] " + msg;
    }

    private static String toMsg(int code, String desc, String msg) {
        return desc + " [" + code + "] " + msg;
    }

    public HeraBizException(int code, String msg) {
        super(toMsg(code, msg));
        this.code = code;
        this.msg = msg;
    }

    public HeraBizException(int code, String desc, String msg) {
        super(toMsg(code, desc, msg));
        this.code = code;
        this.msg = msg;
        this.desc = desc;
    }

    public HeraBizException(HeraBizErrorCodeEnum error) {
        this(error.getCode(), error.getDesc(), error.getMessage());
    }

    public static HeraBizException create(HeraBizErrorCodeEnum error){
        return new HeraBizException(error);
    }

    public static HeraBizException create(HeraBizErrorCodeEnum error, String errorMsg){
        return new HeraBizException(error.getCode(),errorMsg);
    }
}
