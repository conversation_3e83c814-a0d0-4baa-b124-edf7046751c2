package so.dian.platform.common.mq.producer;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.notify.NotifyDTO;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import com.chargebolt.hera.domain.RefundDO;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.MQProducer;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Component
public class BizMqProducer {

    @Resource
    @Qualifier("defaultProducer")
    private MQProducer rocketProducer;
    @Value("${notify.rocketmq.biz.topic}")
    private String topic;
    @Value("${notify.rocketmq.biz.tag.bankcard.preAuthCreate}")
    private String preAuthCreateTag;
    @Value("${notify.rocketmq.biz.tag.bankcard.capture}")
    private String captureTag;
    @Value("${notify.rocketmq.biz.tag.bankcard.preAuthCancel}")
    private String preAuthCancelTag;
    @Value("${notify.rocketmq.biz.tag.apm.paid}")
    private String apmPaidTag;
    @Value("${notify.rocketmq.biz.tag.apmToken.tokenApplied}")
    private String tokenAppliedTag;
    @Value("${notify.rocketmq.biz.tag.apmToken.tokenPaid}")
    private String tokenPaidTag;
    @Value("${notify.rocketmq.biz.tag.gateway.pay}")
    private String gatewayPayTag;
    @Value("${notify.rocketmq.biz.tag.gateway.refund}")
    private String gatewayRefundTag;
    @Value("${notify.rocketmq.biz.tag.account.pay}")
    private String accountPayTag;
    @Value("${notify.rocketmq.biz.tag.refund}")
    private String refundTag;


    private void send(NotifyDTO notifyDTO, String tag) {
        try {
            Message message = new Message(topic, tag, notifyDTO.getPaymentOrderNo(), JSON.toJSONString(notifyDTO).getBytes(RemotingHelper.DEFAULT_CHARSET));
            log.info("业务消息 topic:{}, tag:{}, delay:{}, body: {}",
                    topic, tag,message.getDelayTimeLevel(),JSON.toJSONString(notifyDTO));
            org.apache.rocketmq.client.producer.SendResult sendResult = rocketProducer.send(message);
            log.info("业务消息 sendResult: {}", JSON.toJSONString(sendResult));
        } catch (Exception exception) {
            log.error("支付通知#消息发送异常 exception: {}", exception);
            throw new HeraBizException(HeraBizErrorCodeEnum.OPERATE_BUSY.getCode(), "消息发送未知异常");
        }
    }

    private NotifyDTO genNotifyDTO(PaymentDO paymentDO) {
        NotifyDTO notifyDTO = new NotifyDTO();
        notifyDTO.setPayway(paymentDO.getPayType());
        notifyDTO.setBizType(paymentDO.getBizType());
        notifyDTO.setUserId(paymentDO.getUserId());
        notifyDTO.setPaymentOrderNo(paymentDO.getOrderNo());
        notifyDTO.setTradeNo(paymentDO.getTradeNo());
        notifyDTO.setCurrency(paymentDO.getCurrency());
        notifyDTO.setAmount(paymentDO.getPayAmount());
        notifyDTO.setPayTime(paymentDO.getPayTime());
        notifyDTO.setRefundTime(paymentDO.getRefundTime());
        notifyDTO.setPaymentStatus(paymentDO.getStatus());
        notifyDTO.setDeviceNo(paymentDO.getDeviceNo());
        notifyDTO.setPayMethod(paymentDO.getPayMethod());
        return notifyDTO;
    }

    public void sendPayMsg(PaymentDO paymentDO) {
        NotifyDTO notifyDTO = genNotifyDTO(paymentDO);
        send(notifyDTO, gatewayPayTag);
    }

    public void sendAccountPayMsg(PaymentDO paymentDO) {
        NotifyDTO notifyDTO = genNotifyDTO(paymentDO);
        send(notifyDTO, accountPayTag);
    }

    public void sendBankcardPreAuthCreateMsg(PaymentDO paymentDO) {
        NotifyDTO notifyDTO = genNotifyDTO(paymentDO);
        send(notifyDTO, preAuthCreateTag);
    }

    public void sendBankcardPreAuthCaptureMsg(PaymentDO paymentDO) {
        NotifyDTO notifyDTO = genNotifyDTO(paymentDO);
        send(notifyDTO, captureTag);
    }

    public void sendBankcardPreAuthCancelMsg(PaymentDO paymentDO) {
        NotifyDTO notifyDTO = genNotifyDTO(paymentDO);
        send(notifyDTO, preAuthCancelTag);
    }

    public void sendApmPaidMsg(PaymentDO paymentDO) {
        NotifyDTO notifyDTO = genNotifyDTO(paymentDO);
        send(notifyDTO, apmPaidTag);
    }

    public void sendApmTokenAppliedMsg(PaymentDO paymentDO) {
        NotifyDTO notifyDTO = genNotifyDTO(paymentDO);
        send(notifyDTO, tokenAppliedTag);
    }

    public void sendApmTokenPaidMsg(PaymentDO paymentDO) {
        NotifyDTO notifyDTO = genNotifyDTO(paymentDO);
        send(notifyDTO, tokenPaidTag);
    }

    private NotifyDTO refundNotifyDTO(PaymentDO paymentDO, RefundDO refundDO) {
        NotifyDTO notifyDTO = genNotifyDTO(paymentDO);
        if (refundDO != null) {
            notifyDTO.setRefundStatus(refundDO.getStatus());
            notifyDTO.setRefundAmount(refundDO.getAmount());
            notifyDTO.setRefundTime(refundDO.getRefundTime());
            if (refundDO.getRefundTime() == null) {
                notifyDTO.setRefundTime(new Date());
            }
        }
        return notifyDTO;
    }

    public void sendRefundMsg(PaymentDO paymentDO, RefundDO refundDO) {
        NotifyDTO notifyDTO = refundNotifyDTO(paymentDO, refundDO);
        send(notifyDTO, refundTag);
    }

    public void sendGatewayRefundMsg(PaymentDO paymentDO, RefundDO refundDO) {
        NotifyDTO notifyDTO = refundNotifyDTO(paymentDO, refundDO);
        send(notifyDTO, gatewayRefundTag);
    }

}
