package so.dian.platform.common.mq.producer;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.callback.impay.WxImPayCallbackDTO;
import com.chargebolt.hera.client.dto.callback.refund.WxRefundCallbackDTO;
import com.chargebolt.hera.domain.notify.ChannelNotifyBody;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.domain.notify.DelayRefundBody;
import com.chargebolt.hera.domain.notify.PaymentRetryBody;
import com.chargebolt.hera.domain.notify.RefundNotifyBody;
import com.chargebolt.hera.domain.notify.DelayQueryResultBody;
import com.chargebolt.hera.domain.notify.ZaloMiniNotifyBody;
import com.chargebolt.hera.domain.notify.ZalopayNotifyBody;
import so.dian.platform.common.constants.BusinessConstants;
import org.apache.rocketmq.client.producer.SendResult;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.MQProducer;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.common.RemotingHelper;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.constant.MqDelayLevelConst;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class SelfMqProducer {

    @Resource
    @Qualifier("defaultProducer")
    private MQProducer rocketProducer;
    @Value("${notify.rocketmq.self.topic}")
    private String topic;
    @Value("${notify.rocketmq.self.tag.wechatV3ImPay}")
    private String wechatV3ImPayTag;
    @Value("${notify.rocketmq.self.tag.wechatV3ImRefund}")
    private String wechatV3ImRefundTag;
    @Value("${notify.rocketmq.self.tag.wooshpay.cardCaptureNotify}")
    private String wooshpayCaptureNotifyTag;
    @Value("${notify.rocketmq.self.tag.pingpong.cardCaptureNotify}")
    private String pingpongCaptureNotifyTag;
    @Value("${notify.rocketmq.self.tag.pingpong.apmPaidNotify}")
    private String pingpongPaidNotifyTag;
    @Value("${notify.rocketmq.self.tag.pingpong.apmTokenPaidNotify}")
    private String pingpongApmTokenPaidNotifyTag;
    @Value("${notify.rocketmq.self.tag.antom.apmPaidNotify}")
    private String antomApmPaidNotify;
    @Value("${notify.rocketmq.self.tag.antom.cardCaptureNotify}")
    private String antomCaptureNotifyTag;
    @Value("${notify.rocketmq.self.tag.airwallex.cardCaptureNotify}")
    private String airwallexCaptureNotifyTag;
    @Value("${notify.rocketmq.self.tag.refund}")
    private String refundTag;
    // zaloPay
    @Value("${notify.rocketmq.self.tag.zalopay.pay-notify}")
    private String zaloPayPayNotifyTag;
    @Value("${notify.rocketmq.self.tag.zalopay.refund-notify}")
    private String zaloPayRefundNotifyTag;

    // zalo小程序
    @Value("${notify.rocketmq.self.tag.zalo-mini.pay-notify}")
    private String zaloMiniPayNotifyTag;
    @Value("${notify.rocketmq.self.tag.zalo-mini.refund-notify}")
    private String zaloMiniRefundNotifyTag;

    @Value("${notify.rocketmq.self.tag.midtrans.apmPaidNotify}")
    private String midtransApmPaidNotifyTag;

    /**
     * 延迟查询消息队列
     */
    @Value("${notify.rocketmq.self.tag.pay-refund-delay-query}")
    private String payRefundDelayQueryTag;
    /**
     * 预授权扣款重试消息队列tag
     */
    @Value("${notify.rocketmq.self.tag.payment-retry-delay}")
    private String paymentRetryDelay;

    /**
     * 延迟发起退款（刚完成支付，立刻发起退款场景）
     */
    @Value("${notify.rocketmq.self.tag.refund-delay-initiate}")
    private String refundDelayInitiate;

    private static Map<PaywayEnum, String> TAG_MAP = Maps.newHashMap();

    @PostConstruct
    public void init() {
        TAG_MAP.put(PaywayEnum.WOOSHPAY_CARD, wooshpayCaptureNotifyTag);
        TAG_MAP.put(PaywayEnum.PINGPONG_CHECKOUT_CARD, pingpongCaptureNotifyTag);
        TAG_MAP.put(PaywayEnum.PINGPONG_CHECKOUT_APM, pingpongPaidNotifyTag);
        TAG_MAP.put(PaywayEnum.PINGPONG_ONE_CLICK, pingpongApmTokenPaidNotifyTag);
        TAG_MAP.put(PaywayEnum.ANTOM_CHECKOUT_CARD, antomCaptureNotifyTag);
        TAG_MAP.put(PaywayEnum.ANTOM_CHECKOUT_APM, antomApmPaidNotify);
        TAG_MAP.put(PaywayEnum.MIDTRANS_CHECKOUT_APM, midtransApmPaidNotifyTag);
        TAG_MAP.put(PaywayEnum.AIRWALLEX_CHECKOUT_CARD,airwallexCaptureNotifyTag);
    }


    public void sendSelfWechatPayMsg(WxImPayCallbackDTO dto) {
        try {
            Message message = new Message(topic, wechatV3ImPayTag, dto.getOutTradeNo(), JSON.toJSONString(dto).getBytes(RemotingHelper.DEFAULT_CHARSET));
            log.info("Hera:self:微信V3通知#即时支付#支付通知#发送消息 topic:{}, tag:{}, delay:{}, body: {}",
                    topic, wechatV3ImPayTag,message.getDelayTimeLevel(),JSON.toJSONString(dto));
            SendResult sendResult = rocketProducer.send(message);
            log.info("Hera:self:微信V3通知#即时支付#支付通知#发送消息 message: {},sendResult: {}", JSON.toJSONString(message), JSON.toJSONString(sendResult));
        } catch (MQBrokerException | RemotingException | UnsupportedEncodingException | InterruptedException |
                 MQClientException exception) {
            log.error("Hera:self:微信V3通知#即时支付#支付通知#消息发送异常 exception: {}", exception);
            throw new HeraBizException(HeraBizErrorCodeEnum.OPERATE_BUSY.getCode(), "消息发送未知异常");
        }
    }

    public void sendSelfWechatRefundMsg(WxRefundCallbackDTO wxRefundCallbackDTO) {
        try {
            Message message = new Message(topic, wechatV3ImRefundTag, wxRefundCallbackDTO.getRefundOrderNo(), JSON.toJSONString(wxRefundCallbackDTO).getBytes(RemotingHelper.DEFAULT_CHARSET));
            log.info("Hera:self:微信V3通知#即时支付#退款通知#发送消息 topic:{}, tag:{}, delay:{}, body: {}",
                    topic, wechatV3ImRefundTag,message.getDelayTimeLevel(),JSON.toJSONString(wxRefundCallbackDTO));
            SendResult sendResult = rocketProducer.send(message);
            log.info("Hera:self:微信V3通知#即时支付#退款通知#发送消息 message: {},sendResult: {}", JSON.toJSONString(message), JSON.toJSONString(sendResult));
        } catch (MQBrokerException | RemotingException | UnsupportedEncodingException | InterruptedException | MQClientException exception) {
            log.error("Hera:self:微信V3通知#即时支付#支付通知#消息发送异常 exception: {}", exception);
            throw new HeraBizException(HeraBizErrorCodeEnum.OPERATE_BUSY.getCode(), "消息发送未知异常");
        }
    }

    public void sendSelfCaptureMsg(ChannelNotifyBody channelNotifyBody) {
        try {
            String tag = TAG_MAP.get(channelNotifyBody.getPayway());
            if (StringUtils.isEmpty(tag)) {
                log.warn("no tag for payway {}", channelNotifyBody.getPayway());
                return;
            }
            Message message = new Message(topic, tag, channelNotifyBody.getCurrentTradeNo(), JSON.toJSONString(channelNotifyBody).getBytes(RemotingHelper.DEFAULT_CHARSET));
            message.setDelayTimeLevel(MqDelayLevelConst.DELAY_5S);
            log.info("Hera Self Capture#发送消息 topic:{}, tag:{}, delay:{}, body: {}",
                    topic, tag,message.getDelayTimeLevel(),JSON.toJSONString(channelNotifyBody));
            SendResult sendResult = rocketProducer.send(message);
            log.info("Hera Self Capture#发送消息 sendResult: {}", JSON.toJSONString(sendResult));
        } catch (MQBrokerException | RemotingException | UnsupportedEncodingException | InterruptedException | MQClientException exception) {
            log.error("Hera Self Capture#消息发送异常 exception: {}", exception);
            throw new HeraBizException(HeraBizErrorCodeEnum.OPERATE_BUSY.getCode(), "消息发送未知异常");
        }
    }

    public void sendSelfApmPaidMsg(ChannelNotifyBody channelNotifyBody) {
        try {
            String tag = TAG_MAP.get(channelNotifyBody.getPayway());
            if (StringUtils.isEmpty(tag)) {
                log.warn("no tag for payway {}", channelNotifyBody.getPayway());
                return;
            }
            Message message = new Message(topic, tag, channelNotifyBody.getCurrentTradeNo(), JSON.toJSONString(channelNotifyBody).getBytes(RemotingHelper.DEFAULT_CHARSET));
            message.setDelayTimeLevel(MqDelayLevelConst.DELAY_5S);
            log.info("Hera Self Apm Paid#发送消息 topic:{}, tag:{}, delay:{}, body: {}",
                    topic, tag,message.getDelayTimeLevel(),JSON.toJSONString(channelNotifyBody));
            SendResult sendResult = rocketProducer.send(message);
            log.info("Hera Self Apm Paid#发送消息 sendResult: {}", JSON.toJSONString(sendResult));
        } catch (MQBrokerException | RemotingException | UnsupportedEncodingException | InterruptedException | MQClientException exception) {
            log.error("Hera Self Apm Paid#消息发送异常 exception: {}", exception);
            throw new HeraBizException(HeraBizErrorCodeEnum.OPERATE_BUSY.getCode(), "消息发送未知异常");
        }
    }

    public void sendSelfApmTokenPaidMsg(ChannelNotifyBody channelNotifyBody) {
        try {
            String tag = TAG_MAP.get(PaywayEnum.PINGPONG_ONE_CLICK);
            if (StringUtils.isEmpty(tag)) {
                log.warn("no tag for payway {}", PaywayEnum.PINGPONG_ONE_CLICK);
                return;
            }
            Message message = new Message(topic, tag, channelNotifyBody.getCurrentTradeNo(), JSON.toJSONString(channelNotifyBody).getBytes(RemotingHelper.DEFAULT_CHARSET));
            message.setDelayTimeLevel(MqDelayLevelConst.DELAY_5S);
            log.info("Hera Self ApmToken#发送消息 topic:{}, tag:{}, delay:{}, body: {}",
                    topic, tag,message.getDelayTimeLevel(),JSON.toJSONString(channelNotifyBody));
            SendResult sendResult = rocketProducer.send(message);
            log.info("Hera Self ApmToken#发送消息 sendResult: {}", JSON.toJSONString(sendResult));
        } catch (MQBrokerException | RemotingException | UnsupportedEncodingException | InterruptedException | MQClientException exception) {
            log.error("Hera Self Capture#消息发送异常 exception: {}", exception);
            throw new HeraBizException(HeraBizErrorCodeEnum.OPERATE_BUSY.getCode(), "消息发送未知异常");
        }
    }

    public void sendSelfRefundMsg(RefundNotifyBody refundNotifyBody) {
        try {
            Message message = new Message(topic, refundTag, refundNotifyBody.getOuterRefundNo(), JSON.toJSONString(refundNotifyBody).getBytes(RemotingHelper.DEFAULT_CHARSET));
            message.setDelayTimeLevel(MqDelayLevelConst.DELAY_5S);
            log.info("Hera Self Refund#发送消息 topic:{}, tag:{}, delay:{}, body: {}",
                    topic, refundTag,message.getDelayTimeLevel(),JSON.toJSONString(refundNotifyBody));
            SendResult sendResult = rocketProducer.send(message);
            log.info("Hera Self Refund#发送消息 sendResult: {}", JSON.toJSONString(sendResult));
        } catch (MQBrokerException | RemotingException | UnsupportedEncodingException | InterruptedException | MQClientException exception) {
            log.error("Hera Self Refund#消息发送异常 exception: {}", exception);
            throw new HeraBizException(HeraBizErrorCodeEnum.OPERATE_BUSY.getCode(), "消息发送未知异常");
        }
    }

    /**
     * zaloPay支付，退款消息发送
     *
     * @param msg
     */
    public void sendSelfZaloPayMsg(ZalopayNotifyBody msg){
        try {
            Message message =null;
            if(Objects.equals(msg.getType(), BusinessConstants.TRANSACTION_PAY)){
                message = new Message(topic, zaloPayPayNotifyTag, msg.getTradeNo(), JSON.toJSONString(msg).getBytes(RemotingHelper.DEFAULT_CHARSET));
                log.info("Hera:self:zaloPay通知#即时支付#发送消息 topic:{}, tag:{}, delay:{}, body: {}",
                        topic, zaloPayPayNotifyTag,message.getDelayTimeLevel(),JSON.toJSONString(msg));
            }else{
                message = new Message(topic, zaloPayRefundNotifyTag, msg.getRefundNo(), JSON.toJSONString(msg).getBytes(RemotingHelper.DEFAULT_CHARSET));
                log.info("Hera:self:zaloPay通知#退款通知#发送消息 topic:{}, tag:{}, delay:{}, body: {}",
                        topic, zaloPayRefundNotifyTag,message.getDelayTimeLevel(),JSON.toJSONString(msg));
            }

            org.apache.rocketmq.client.producer.SendResult sendResult = rocketProducer.send(message);
            log.info("Hera:self:zaloPay通知#即时支付#退款通知#发送消息 sendResult: {}", JSON.toJSONString(sendResult));
        } catch (MQBrokerException | RemotingException | UnsupportedEncodingException | InterruptedException | MQClientException exception) {
            log.error("Hera:self:zaloPay通知#即时支付#支付通知#消息发送异常 exception: {}",exception);
            throw new HeraBizException(HeraBizErrorCodeEnum.OPERATE_BUSY.getCode(),"消息发送未知异常");
        }
    }

    /**
     * zaloMini支付，退款消息发送
     *
     * @param msg
     */
    public void sendSelfZaloMiniMsg(ZaloMiniNotifyBody msg){
        try {
            Message message = new Message(topic, zaloMiniPayNotifyTag, msg.getTradeNo(), JSON.toJSONString(msg).getBytes(RemotingHelper.DEFAULT_CHARSET));
            log.info("Hera:self:zaloMini通知#即时支付#退款通知#发送消息 topic:{}, tag:{}, delay:{}, body: {}",
                    topic, zaloMiniPayNotifyTag,message.getDelayTimeLevel(),JSON.toJSONString(msg));
            org.apache.rocketmq.client.producer.SendResult sendResult = rocketProducer.send(message);
            log.info("Hera:self:zaloMini通知#即时支付#退款通知#发送消息 sendResult: {}", JSON.toJSONString(sendResult));
        } catch (MQBrokerException | RemotingException | UnsupportedEncodingException | InterruptedException | MQClientException exception) {
            log.error("Hera:self:zaloMini通知#即时支付#支付通知#消息发送异常 exception: {}",exception);
            throw new HeraBizException(HeraBizErrorCodeEnum.OPERATE_BUSY.getCode(),"消息发送未知异常");
        }
    }

    /**
     * 支付结果、退款结果查询延迟消息发送
     *
     * @param body
     * @param delayTimeLevel
     */
    public void sendSelfQueryDelayQuery(DelayQueryResultBody body, Integer delayTimeLevel) {
        try {
            Message message = new Message(topic, payRefundDelayQueryTag, body.getTransNo(), JSON.toJSONString(body).getBytes(RemotingHelper.DEFAULT_CHARSET));
            message.setDelayTimeLevel(delayTimeLevel);
            log.info("Hera Self DelayQuery#发送消息 topic:{}, tag:{}, delay:{}, body: {}",
                    topic, payRefundDelayQueryTag,message.getDelayTimeLevel(),JSON.toJSONString(body));
            org.apache.rocketmq.client.producer.SendResult sendResult = rocketProducer.send(message);
            log.info("Hera Self DelayQuery#发送消息 sendResult: {}", JSON.toJSONString(sendResult));
        } catch (MQBrokerException | RemotingException | UnsupportedEncodingException | InterruptedException | MQClientException exception) {
            log.error("Hera Self DelayQuery#消息发送异常 exception: ", exception);
            throw new HeraBizException(HeraBizErrorCodeEnum.OPERATE_BUSY.getCode(), "消息发送未知异常");
        }
    }

    /**
     * 支付结果、退款结果查询延迟消息发送
     *
     * @param body
     */
    public void sendSelfPaymentRetryDelayQuery(PaymentRetryBody body) {
        try {
            Message message = new Message(topic, paymentRetryDelay, body.getTradeNo(), JSON.toJSONString(body).getBytes(RemotingHelper.DEFAULT_CHARSET));
            message.setDelayTimeLevel(MqDelayLevelConst.DELAY_1M);
            log.info("Hera Self PaymentRetry#发送消息 topic:{}, tag:{}, delay:{}, body: {}",
                    topic, paymentRetryDelay,message.getDelayTimeLevel(),JSON.toJSONString(body));
            org.apache.rocketmq.client.producer.SendResult sendResult = rocketProducer.send(message);
            log.info("Hera Self PaymentRetry#发送消息 sendResult: {}", JSON.toJSONString(sendResult));
        } catch (MQBrokerException | RemotingException | UnsupportedEncodingException | InterruptedException | MQClientException exception) {
            log.error("Hera Self PaymentRetry#消息发送异常 exception: ", exception);
            throw new HeraBizException(HeraBizErrorCodeEnum.OPERATE_BUSY.getCode(), "消息发送未知异常");
        }
    }

    /**
     * 延迟退款消息发送
     *
     * @param body
     */
    public void sendSelfRefundDelay(DelayRefundBody body) {
        try {
            Message message = new Message(topic, refundDelayInitiate, body.getTradeNo(), JSON.toJSONString(body).getBytes(RemotingHelper.DEFAULT_CHARSET));
            message.setDelayTimeLevel(body.getDelayLevel());
            log.info("Hera Self RefundDelay#发送消息 topic:{}, tag:{}, delay:{}, body: {}",
                    topic, refundDelayInitiate,message.getDelayTimeLevel(),JSON.toJSONString(body));
            org.apache.rocketmq.client.producer.SendResult sendResult = rocketProducer.send(message);
            log.info("Hera Self RefundDelay#发送消息 sendResult: {}", JSON.toJSONString(sendResult));
        } catch (MQBrokerException | RemotingException | UnsupportedEncodingException | InterruptedException | MQClientException exception) {
            log.error("Hera Self RefundDelay#消息发送异常 exception: ", exception);
            throw new HeraBizException(HeraBizErrorCodeEnum.OPERATE_BUSY.getCode(), "消息发送未知异常");
        }
    }
}
