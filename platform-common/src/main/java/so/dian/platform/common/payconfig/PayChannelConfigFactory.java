package so.dian.platform.common.payconfig;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class PayChannelConfigFactory {

    private static final Map<String, PayChannelConfig> payChannelConfigMap = new ConcurrentHashMap<>();

    public static Map<String, PayChannelConfig> getPayChannelConfigMap() {
        return payChannelConfigMap;
    }

    public static PayChannelConfig getPayChannelConfig(String key) {
        return payChannelConfigMap.get(key);
    }
}
