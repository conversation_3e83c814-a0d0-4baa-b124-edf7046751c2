package so.dian.platform.common.utils;

import org.apache.commons.codec.binary.Base64;

/**
 * Created by ailun on 2017/11/4.
 */
public class Base64Util {

    public static String encodeStr(String plainText) {
        byte[] b = plainText.getBytes();
        return Base64.encodeBase64URLSafeString(b);
    }

    public static String decodeStr(String encodeStr) {
        byte[] b = encodeStr.getBytes();
        Base64 base64 = new Base64();
        b = base64.decode(b);
        return new String(b);
    }

}
