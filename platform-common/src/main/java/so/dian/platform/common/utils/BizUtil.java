package so.dian.platform.common.utils;

import com.chargebolt.hera.client.enums.PaymentBizTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;

/**
 * Created with lhc
 *
 * @Author: bailong Date: 17/6/6 Time: 下午5:41 北京伊电园网络科技有限公司 2016-2017 © 版权所有 京ICP备17000101号
 */
@Slf4j
public class BizUtil {

    private static final Long SHOP_ID_PLUS = 50000L;
    private static final Long SHOP_ID_BASIC = 5000L;
    private static final int SENCONDS_OF_DAY = 60 * 60 * 24;
    private static final int SENCONDS_OF_HOUR = 60 * 60;
    private static final int SENCONDS_OF_MINUTE = 60;

    /**
     * 这里对shopId做一层伪造，算法时间复杂度不需要太复杂
     */
    public static Long shopIdForge(Long shopId) {
        if (shopId > SHOP_ID_BASIC) {
            shopId = shopId + SHOP_ID_PLUS;
        }
        return shopId;
    }

    /**
     * 暴露shopId
     */
    public static Long shopIdExpose(String forgeShopId) {
        Assert.notNull(forgeShopId, "forgeShopId should not be null");
        Long shopIdd = Long.valueOf(forgeShopId);
        Long shopId = null;
        if (shopIdd > SHOP_ID_PLUS) {
            shopId = shopIdd - SHOP_ID_PLUS;
        } else {
            shopId = shopIdd;
        }
        return shopId;
    }

    /**
     * @param secondTime 单位秒
     */
    public static String second2Day(Integer secondTime, boolean isSecond) {
        StringBuilder stringBuilder = new StringBuilder();
        int days = secondTime / SENCONDS_OF_DAY;
        if (days != 0) {
            stringBuilder.append(days).append("天");
        }
        int hours = (secondTime - days * SENCONDS_OF_DAY) / SENCONDS_OF_HOUR;
        if (hours != 0) {
            stringBuilder.append(hours).append("小时");
        }
        int minutes = (secondTime - days * SENCONDS_OF_DAY - hours * SENCONDS_OF_HOUR) / SENCONDS_OF_MINUTE;
        if (minutes != 0) {
            stringBuilder.append(minutes).append("分钟");
        }
        if (isSecond) {
            int seconds = (secondTime - days * SENCONDS_OF_DAY - hours * SENCONDS_OF_HOUR
                    - minutes * SENCONDS_OF_MINUTE);
            if (seconds != 0) {
                stringBuilder.append(seconds).append("秒");
            }
        }
        String result = stringBuilder.toString();
        if (StringUtils.isEmpty(result)) {
            result = "不到一分钟";
        }

        return result;
    }

    public static String forgeNick(String nick) {
        //防空指针,真实数据有一些没有nick
        if (StringUtils.isEmpty(nick)) {
            return "*";
        }
        if (nick.length() >= 1) {
            return nick.substring(0, 1) + "*";
        }
        return nick;
    }

    public static String filterEmoji(String source, String slipStr) {
        if (!StringUtils.isEmpty(source)) {
            return source.replaceAll("[\\ud800\\udc00-\\udbff\\udfff\\ud800-\\udfff]", slipStr);
        } else {
            return source;
        }
    }


    public static Date getBetDateOfStart(Date date, int inteval) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        instance.set(Calendar.DAY_OF_MONTH, instance.get(Calendar.DAY_OF_MONTH) + inteval);
        return instance.getTime();
    }

    public static Date getDayStart(Date date) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        return instance.getTime();
    }

    public static Date getDayEnd(Date date) {
        Calendar instance = Calendar.getInstance();
        instance.setTime(date);
        instance.set(Calendar.HOUR_OF_DAY, 24);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        return instance.getTime();
    }

    public static String generateNo(String tns, Long id) {
        Assert.notNull(id, "bizId should not null");
        DateFormat dateFormat = new SimpleDateFormat("YYMMddHHmmss");
        String date = dateFormat.format(new Date());
        Long buyer = id % (1000);
        DecimalFormat df = new DecimalFormat("0000");
        DecimalFormat df2 = new DecimalFormat("0000");
        Random r1 = new Random();
        String random1 = df.format(r1.nextInt(10000));
        Random r2 = new Random();
        String random2 = df2.format(r2.nextInt(10000));
        return tns + date + df.format(buyer) + random1 + random2;
    }

    public static String generateTradeNo(PaymentBizTypeEnum bizTypeEnum, Long bizId) {
        return generateNo(String.valueOf(bizTypeEnum.getCode()), Math.abs(bizId));
    }
}
