package so.dian.platform.common.utils;

import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.io.InputStream;

public class CertFileUtil {

    public enum CertProvider{
        Wechat("wechat"), QPay("qpay"), UnionPay("unionpay");

        private String provider;

        CertProvider(String provider) {
            this.provider = provider;
        }
    }

    private static ClassPathResource getCertFileClassPathResource(CertProvider provider, String mchId, String fileName){
        ClassPathResource resource = new ClassPathResource("cert/" + provider.provider + "/" + mchId + "/" + fileName);
        return resource;
    }

    public static InputStream getCertFileInputStream(CertProvider provider, String mchId, String fileName) throws IOException {
        return getCertFileClassPathResource(provider, mchId, fileName).getInputStream();
    }

//    public static File getCertFile(CertProvider provider, String mchId, String fileName) throws IOException {
//        return getCertFileClassPathResource(provider, mchId, fileName).getFile();
//    }

}
