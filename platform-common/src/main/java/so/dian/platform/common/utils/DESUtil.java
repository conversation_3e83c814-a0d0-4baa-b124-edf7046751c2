package so.dian.platform.common.utils;

import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.IvParameterSpec;
import java.security.Key;
import java.security.SecureRandom;


public class DESUtil {

    private static final String iv = "45062349";

    /**
     * 根据参数生成 KEY
     */
    public static Key getKey(String strKey) {
        try {
            KeyGenerator generator = KeyGenerator.getInstance("DES");
            //防止linux下 随机生成key   
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(strKey.getBytes());
            generator.init(56, secureRandom);
            return generator.generateKey();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 加密 String 明文输入 ,String 密文输出
     */
    public static String encryptStr(String keySecert, String strMing) {
        BASE64Encoder base64Encoder = new BASE64Encoder();
        byte[] byteMi;
        try {
            byteMi = encryptByte(keySecert, strMing.getBytes());
            return base64Encoder.encode(byteMi);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 解密 以 String 密文输入 ,String 明文输出
     */
    public static String decryptStr(String keySecert, String strMi) {
        BASE64Decoder base64De = new BASE64Decoder();
        byte[] byteMing;
        byte[] byteMi;
        try {
            byteMi = base64De.decodeBuffer(strMi);
            byteMing = decryptByte(keySecert, byteMi);
            return new String(byteMing, "UTF8");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 加密以 byte[] 明文输入 ,byte[] 密文输出
     */
    public static byte[] encryptByte(String keySecert, byte[] data) {
        try {
            Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
            IvParameterSpec zeroIv = new IvParameterSpec(iv.getBytes());
            cipher.init(Cipher.ENCRYPT_MODE, getKey(keySecert), zeroIv);
            return cipher.doFinal(data);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 解密以 byte[] 密文输入 , 以 byte[] 明文输出
     */
    public static byte[] decryptByte(String keySecert, byte[] data) {
        try {
            Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
            IvParameterSpec zeroIv = new IvParameterSpec(iv.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, getKey(keySecert), zeroIv);
            return cipher.doFinal(data);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) throws Exception {
//        System.out.println(DESUtil.encryptStr("CHANNEL4ZSD","6227002491000416729"));
        System.out.println(DESUtil.decryptStr("CHANNEL4ZSD", "/6xgvgwikE8PNHStAXT6MYShJJrdehTK"));
    }
}