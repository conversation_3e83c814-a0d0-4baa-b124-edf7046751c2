package so.dian.platform.common.utils;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 */
public class DateUtil {

    /**
     * 获取当前时间n小时以后的时间
     * @param hour
     * @return
     */
    public static String getAfterHourTime(Long hour){
        // 获取当前时间（带时区）
        ZonedDateTime currentTime = ZonedDateTime.now();

        // 加上48小时
        ZonedDateTime timeAfter48Hours = currentTime.plus(hour, ChronoUnit.HOURS);

        // 定义输出的时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");

        // 格式化时间并输出
        String formattedTime = timeAfter48Hours.format(formatter);
        return formattedTime;
    }

}
