package so.dian.platform.common.utils;

import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class HeraStreamUtils {

    public static <T> List<List<T>> listSplit(Collection<T> list, Integer size) {
        if (CollectionUtils.isEmpty(list) || size == null || size == 0) {
            return new ArrayList<>();
        }
        return Stream.iterate(0, n -> n + 1).limit((long) Math.ceil((double) list.size() / size))
                .map(a -> list.stream().skip(a * size).limit(size).collect(Collectors.toList()))
                .collect(Collectors.toList());
    }
}
