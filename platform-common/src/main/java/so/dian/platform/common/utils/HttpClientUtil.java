package so.dian.platform.common.utils;

import org.apache.http.Consts;
import org.apache.http.NameValuePair;
import org.apache.http.client.fluent.Content;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicNameValuePair;

import java.io.IOException;
import java.util.*;

/**
 * Created by dell on 2016/12/13.
 */
public class HttpClientUtil {

    public static String httpGet(String url, int timeout) throws IOException {
        String content = Request.Get(url).connectTimeout(timeout).socketTimeout(timeout).execute().returnContent()
                .asString(Consts.UTF_8);
        return content;
    }

    public static String httpPut(String url, int timeout, String postBody) throws IOException {
        String content = Request.Put(url).connectTimeout(timeout).socketTimeout(timeout)
                .body(new StringEntity(postBody)).execute().returnContent().asString();
        return content;
    }

    public static String httpPost(String url, int timeout, Map map) throws IOException {
        List<NameValuePair> list = new ArrayList<>();
        Iterator iterator = map.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> elem = (Map.Entry<String, String>) iterator.next();
            list.add(new BasicNameValuePair(elem.getKey(), elem.getValue()));
        }
        return Request.Post(url)
//                .addHeader(Header.CONTENT_TYPE.toString(), cn.hutool.http.ContentType.FORM_URLENCODED.toString())
                .connectTimeout(timeout).socketTimeout(timeout).bodyForm(list, Consts.UTF_8).execute()
                .returnContent().asString();
    }

    public static String httpPost(String url, int timeout, String postBody) throws IOException {
        Content content = Request.Post(url).bodyString(postBody, ContentType.APPLICATION_JSON).connectTimeout(timeout)
                .socketTimeout(timeout).execute().returnContent();
        if (content == null) {
            return null;
        }
        return content.asString();
    }

    public static String httpPost(String url, int timeout, Map<String, String> headers, Map map) throws IOException {
        Request request = Request.Post(url).connectTimeout(timeout).socketTimeout(timeout);
        List<NameValuePair> list = new ArrayList<>();
        Iterator iterator = map.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> elem = (Map.Entry<String, String>) iterator.next();
            list.add(new BasicNameValuePair(elem.getKey(), elem.getValue()));
        }
        if (headers != null) {
            Set<String> keys = headers.keySet();
            for (Iterator<String> i = keys.iterator(); i.hasNext(); ) {
                String key = (String) i.next();
                request.addHeader(key, headers.get(key));
            }
        }
        return request.bodyForm(list, Consts.UTF_8).execute().returnContent().asString();

    }

    public static String httpDelete(String url, int timeout, String postBody) throws IOException {
        return Request.Delete(url).connectTimeout(timeout).socketTimeout(timeout).body(new StringEntity(postBody))
                .execute().returnContent().asString();
    }
}
