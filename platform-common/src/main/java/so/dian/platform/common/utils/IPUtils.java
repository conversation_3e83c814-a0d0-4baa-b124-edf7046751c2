package so.dian.platform.common.utils;

import java.net.InetAddress;
import java.net.UnknownHostException;

public class IPUtils {

    public static String getLocalIP() {
        InetAddress addr;
        try {
            addr = InetAddress.getLocalHost();
        } catch (UnknownHostException e) {
            throw new RuntimeException("client ip get fail");
        }
        return addr.getHostAddress();
    }
}
