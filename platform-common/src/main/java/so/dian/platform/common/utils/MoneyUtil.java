package so.dian.platform.common.utils;

import so.dian.mofa3.lang.money.MultiCurrencyMoney;

/**
 * <AUTHOR>
 * @since 2016-07-30
 */
public class MoneyUtil {

    public static String fen2Yuan(Integer fen, String currency) {
        if (fen == null) {
            return "0";
        }
        MultiCurrencyMoney money= new MultiCurrencyMoney(0, currency);
        money.setCent(fen);
        return money.getAmount().toString();
    }

    public static long yuan2Fen(String yuan) {
        Double.valueOf(yuan);
        int indexPoint = yuan.indexOf(".");
        if (indexPoint == -1) {
            return Long.parseLong(yuan + "00");
        } else if (indexPoint == 0) {
            if (yuan.length() == 2) {
                return Long.parseLong(yuan.substring(1, 2) + "0");
            } else {
                return Long.parseLong(yuan.substring(1, 3));
            }
        } else {
            if (yuan.length() == indexPoint + 1) {
                return Long.parseLong(yuan.substring(0, indexPoint) + "00");
            } else {
                String decimalValue = yuan.substring(indexPoint + 1);
                if (decimalValue.length() == 1) {
                    return Long.parseLong(yuan.substring(0, indexPoint)
                            + yuan.substring(indexPoint + 1, indexPoint + 2) + "0");
                } else {
                    return Long.parseLong(yuan.substring(0, indexPoint)
                            + yuan.substring(indexPoint + 1, indexPoint + 3));
                }
            }
        }
    }

}
