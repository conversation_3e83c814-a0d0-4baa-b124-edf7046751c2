package so.dian.platform.common.utils;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by ailun on 2017/11/5.
 */
public class ReflectUtils {

    public static Class getListParamType(Field field) {
        if (field.getType().isAssignableFrom(List.class)) {
            Type clazz = field.getGenericType();
            if (clazz == null) {
                return null;
            }
            if (clazz instanceof ParameterizedType) {
                ParameterizedType pz = (ParameterizedType) clazz;
                return (Class) pz.getActualTypeArguments()[0];
            }
        }
        return null;
    }

    public static Map<String, Object> objectToMap(Object obj) {
        if (obj == null) {
            return null;
        }

        Map<String, Object> map = new HashMap<>();

        Field[] declaredFields = obj.getClass().getDeclaredFields();
        for (Field field : declaredFields) {
            field.setAccessible(true);
            try {
                map.put(field.getName(), field.get(obj));
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }

        return map;
    }

    public static Map<String, Object> objectToMapExcludeEmpty(Object obj) {
        if (obj == null) {
            return null;
        }

        Map<String, Object> map = new HashMap<>();

        Field[] declaredFields = obj.getClass().getDeclaredFields();
        for (Field field : declaredFields) {
            field.setAccessible(true);
            try {
                Object value = field.get(obj);
                if (ValidateUtil.isEmpty(value)) {
                    continue;
                }
                map.put(field.getName(), value);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }

        return map;
    }
}
