package so.dian.platform.common.utils;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class RequestConvertUtil {

    private RequestConvertUtil() {

    }

    public static Map<String, String> paramToMap(Map<String, String[]> parameterMap) {
        Map<String, String> map = new HashMap<>();
        if (MapUtils.isNotEmpty(parameterMap)) {
            for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
                map.put(entry.getKey(), StringUtils.join(entry.getValue(), ","));
            }
        }
        return map;
    }
}
