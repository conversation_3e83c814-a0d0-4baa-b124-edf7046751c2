package so.dian.platform.common.utils;

import lombok.extern.slf4j.Slf4j;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR>
 */
@Slf4j
public class SHA256Util {

    // 生成签名
    public static String generateSignature(String data, String secretKey) throws NoSuchAlgorithmException {
        String payload = data + secretKey;
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hash = digest.digest(payload.getBytes(StandardCharsets.UTF_8));
        return bytesToHex(hash);
    }

    // 将字节数组转换为十六进制字符串
    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    // 验证签名
    public static boolean verifySignature(String data, String secretKey, String expectedSignature) throws NoSuchAlgorithmException {
        String generatedSignature = generateSignature(data, secretKey);
        log.info("verifySignature expectedSignature: {},generatedSignature: {}",expectedSignature,generatedSignature);
        return generatedSignature.equals(expectedSignature);
    }


    public static void main(String[] args) {
        try {
            String data = "待签名的数据";
            String secretKey = "密钥";
            String signature = generateSignature(data, secretKey);
            System.out.println("生成的签名: " + signature);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
    }

}
