package so.dian.platform.common.utils;

import cn.hutool.core.util.ObjectUtil;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collection;

public class ValidateUtil {

    public  static void requiredNotNull(Object o, HeraBizErrorCodeEnum exceEnum) {
        if (o == null) {
            throw new HeraBizException(exceEnum);
        }
    }

    public  static void requiredNotEmpty(String o, HeraBizErrorCodeEnum exceEnum) {
        if (StringUtils.isEmpty(o)) {
            throw new HeraBizException(exceEnum);
        }
    }

    public  static void requiredNotEmpty(Collection o, HeraBizErrorCodeEnum exceEnum) {
        if (CollectionUtils.isEmpty(o)) {
            throw new HeraBizException(exceEnum);
        }
    }

    public  static void requiredNull(Object o, HeraBizErrorCodeEnum exceEnum) {
        if (o != null) {
            throw new HeraBizException(exceEnum);
        }
    }

    public  static void requiredTrue(boolean o, HeraBizErrorCodeEnum exceEnum) {
        if (!o) {
            throw new HeraBizException(exceEnum);
        }
    }

    public  static void  assertEquals(Object o, Object t, HeraBizErrorCodeEnum exceEnum) {
        if (!o.equals(t)) {
            throw new HeraBizException(exceEnum);
        }
    }

    public  static void  assertNotEquals(Object o, Object t, HeraBizErrorCodeEnum exceEnum) {
        if (o.equals(t)) {
            throw new HeraBizException(exceEnum);
        }
    }

    public static void assertNotInEquals(Object o, HeraBizErrorCodeEnum exceEnum, Object...t) {
        if (t == null || t.length == 0) {
            return;
        }
        for(Object key : t){
            if (o.equals(key)) {
                return;
            }
        }
        throw new HeraBizException(exceEnum);
    }

    public static boolean assertNotInEquals(Object o,Object...t) {
        if (t == null || t.length == 0) {
            return false;
        }
        for(Object key : t){
            if (o.equals(key)) {
                return true;
            }
        }
        return false;
    }

    public static void notEmpty(Object object, String msg) {
        if (ObjectUtil.isEmpty(object)) {
            throw new IllegalArgumentException(msg);
        }
    }

    public static boolean isEmpty(Object object) {
        return ObjectUtil.isEmpty(object);
    }
}
