package so.dian.platform.common.utils;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.Charset;

/**
 * Created with sax
 *
 * @Author: bailong Date: 2017/9/14 Time: 下午8:20 北京伊电园网络科技有限公司 2016-2017 © 版权所有 京ICP备17000101号
 */
public class Xml2JUtil {

    public static Object xmlToBean(String xmlStr, Class<?> load) throws JAXBException, IOException {
        JAXBContext context = JAXBContext.newInstance(load);
        Unmarshaller unmarshaller = context.createUnmarshaller();
        byte[] buf = xmlStr.getBytes(Charset.forName("GBK"));
        ByteArrayInputStream bin = new ByteArrayInputStream(buf);
        Object object = unmarshaller.unmarshal(bin);
        return object;
    }

    public static Object xmlToBean(String xmlStr, Class<?> load, String charset) throws JAXBException, IOException {
        JAXBContext context = JAXBContext.newInstance(load);
        Unmarshaller unmarshaller = context.createUnmarshaller();
        byte[] buf = xmlStr.getBytes(Charset.forName(charset));
        ByteArrayInputStream bin = new ByteArrayInputStream(buf);
        Object object = unmarshaller.unmarshal(bin);
        return object;
    }
}
