<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>chargebolt-hera</artifactId>
        <groupId>com.chargebolt.hera</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>platform-midtrans</artifactId>
    <version>${platform.midtrans.version}</version>
    <packaging>jar</packaging>


    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.chargebolt</groupId>
            <artifactId>hera-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chargebolt.hera</groupId>
            <artifactId>hera-interceptor</artifactId>
        </dependency>

        <dependency>
            <groupId>com.chargebolt.hera</groupId>
            <artifactId>platform-common</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
            <version>${springboot.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <version>${springboot.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
            <version>${springboot.version}</version>
        </dependency>

        <dependency>
            <groupId>so.dian.mofa3</groupId>
            <artifactId>common-lang</artifactId>
        </dependency>

        <dependency>
            <groupId>com.midtrans</groupId>
            <artifactId>java-library</artifactId>
            <version>3.1.4</version>
        </dependency>
    </dependencies>
</project>
