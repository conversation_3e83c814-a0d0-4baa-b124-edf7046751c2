package so.dian.platform.midtrans.client;

import com.midtrans.Config;
import com.midtrans.httpclient.APIHttpClient;
import com.midtrans.httpclient.CoreApi;
import com.midtrans.httpclient.error.MidtransError;
import org.json.JSONObject;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class MidtransClient extends CoreApi {

    /**
     * 申请退款
     * 接口文档：https://docs.midtrans.com/reference/direct-refund-transaction
     * @param requestBody
     * @param orderId
     * @return
     * @throws MidtransError
     */
    public static JSONObject refund(Map<String, Object> requestBody,String orderId) throws MidtransError {
        Config configOptions = Config.getGlobalConfig();
        return new JSONObject((String) APIHttpClient.request(
                APIHttpClient.POST,
                configOptions.getCoreApiURL() + API_VERSION2 + "/" + orderId + "/refund/online/direct",
                configOptions,
                requestBody
        ));
    }

    public static JSONObject snapRefund(Map<String, Object> requestBody,String orderId) throws MidtransError {
        Config configOptions = Config.getGlobalConfig();
        return new JSONObject((String) APIHttpClient.request(
                APIHttpClient.POST,
                configOptions.getCoreApiURL() + API_VERSION2 + "/" + orderId + "/refund",
                configOptions,
                requestBody
        ));
    }

    /**
     * 查询订单状态
     * 接口文档：https://docs.midtrans.com/reference/get-transaction-status
     * @param orderId
     * @return
     * @throws MidtransError
     */
    public static JSONObject orderQuery(String orderId) throws MidtransError {
        Config configOptions = Config.getGlobalConfig();
        return new JSONObject((String) APIHttpClient.request(
                APIHttpClient.GET,
                configOptions.getCoreApiURL() + API_VERSION2 + "/" + orderId + "/status",
                configOptions,
                null
        ));
    }
}
