package so.dian.platform.midtrans.common.enums;

import lombok.Getter;

/**
 * 接口文档：https://docs.midtrans.com/docs/https-notification-webhooks#b-status-definition-b
 */
@Getter
public enum TransactionStatusEnum {

    /**
     * 交易成功，卡余额已成功获取。如果您不采取任何行动，交易将在当天或第二天或您与合作银行约定的结算时间内成功结算。
     * 然后交易状态变为 结算。可以安全地假设付款成功。
     */
    capture,

    /**
     * 交易已成功结算。资金已存入您的账户。
     */
    settlement,

    /**
     * 交易已创建，正在等待客户通过银行转账、电子钱包等支付提供商付款。对于卡支付方式：等待客户完成（和发卡机构验证）3DS/OTP 流程。
     */
    pending,

    /**
     * 用于付款的凭证被付款提供商或 Midtrans 欺诈检测系统 (FDS) 拒绝。要了解交易被拒绝的原因和详细信息，请参阅响应中的。status_message
     */
    deny,

    /**
     * 交易已取消。可由商户触发。您可以在以下情况下触发取消状态：1. 如果您在捕获状态后取消交易。
     */
    cancel,

    /**
     * 由于付款延迟，交易无法处理。
     */
    expire,

    /**
     * 交易处理期间发生意外错误。
     */
    failure,

    /**
     * 交易被标记为退款。退款状态可由商家触发。
     */
    refund,

    /**
     * 交易被标记为部分退款（如果您选择退款的金额少于支付金额）。退款状态可由商家触发。
     */
    partial_refund,

    /**
     * 仅在您使用预授权功能进行卡交易时才可用（默认情况下您不会拥有的高级功能，因此在大多数情况下可以安全地忽略它）。交易成功，卡余额已成功保留（授权）。您稍后可以执行 API“捕获”将其更改为，或者如果不采取任何措施，将自动释放。根据您的业务用例，您可以将状态视为成功交易。captureauthorize
     */
    authorize
}
