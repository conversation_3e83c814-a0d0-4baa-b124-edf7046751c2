package so.dian.platform.midtrans.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import so.dian.commons.eden.util.LogColorUtils;

/**
 * <AUTHOR>
 */
@Configuration
@ComponentScan("so.dian.platform.midtrans.*")
@ConditionalOnProperty(prefix = "channel.enable", name = "midtrans", havingValue = "true")
@Slf4j
public class MidtransAutoConfiguration {

    static {
        log.info(LogColorUtils.blueMessage("Midtrans enable"));
    }

}
