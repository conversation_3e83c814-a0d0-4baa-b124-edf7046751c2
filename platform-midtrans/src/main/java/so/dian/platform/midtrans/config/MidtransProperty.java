package so.dian.platform.midtrans.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "channel.midtrans")
public class MidtransProperty {

    private String url;
    private String mchId;
    private String clientKey;
    private String serverKey;
    /**
     * 是否为生产环境 true-是，false-否
     */
    private Boolean isProduction;

    /**
     * 支付结果url
     */
    private PayUrl payResultUrl;

    @Data
    public static class PayUrl {
        private String h5;
        private String app;
    }

}
