package so.dian.platform.midtrans.dto;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ChargeResponse {

    private String status_message;
    private String transaction_id;
    private String fraud_status;
    private String transaction_status;
    private String status_code;
    private String merchant_id;
    private String gross_amount;
    private String payment_type;
    private Date transaction_time;
    private String currency;
    private Date expiry_time;
    private String order_id;
    private List<Action> actions;
    /**
     * dana支付返回
     */
    private String token;
    /**
     * 重定向url
     */
    private String redirect_url;

}
