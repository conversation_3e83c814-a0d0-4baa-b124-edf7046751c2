package so.dian.platform.midtrans.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class RefundResponse {

    private String status_code;
    private String status_message;
    private String transaction_id;
    private String order_id;
    private String gross_amount;
    private String payment_type;
    private Date transaction_time;
    private String transaction_status;
    private int refund_chargeback_id;
    private String refund_amount;
    private String refund_key;
    private String refund_chargeback_uuid;

}
