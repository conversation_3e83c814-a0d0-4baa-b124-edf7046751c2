package so.dian.platform.midtrans.dto.notify;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MidtransNotifyDTO{

    /**
     * 交易的订单ID(商户订单号)
     */
    private String order_id;

    /**
     * 交易状态代码
     */
    private String status_code;

    /**
     * 完成交易的总金额
     */
    private String gross_amount;

    /**
     * 签名密钥
     */
    private String signature_key;

    /**
     * 交易发起的时间。
     */
    private String transaction_time;

    /**
     * 交易状态
     * @see so.dian.platform.midtrans.common.enums.TransactionStatusEnum
     */
    private String transaction_status;

    /**
     * 具体交易的交易 ID(三方的)。
     */
    private String transaction_id;

    /**
     * 状态消息
     */
    private String status_message;

    /**
     * 交易状态被确认为 “结算 ”的时间
     */
    private String settlement_time;

    /**
     * 使用的付款方式类型
     */
    private String payment_type;

    /**
     * 发起交易的商家 ID。
     */
    private String merchant_id;


    /**
     * 欺诈状态，参考文档：https://docs.midtrans.com/docs/https-notification-webhooks#b-status-definition-b
     */
    private String fraud_status;

    /**
     * 货币单位
     */
    private String currency;

    /**
     * 退款通知返回
     */
    private List<RefundDetail> refunds;

    /***************************** 以下字段qris支付返回 *********************************/
    /**
     * 确定交易是如何获取的
     * on-us 或 off-us
     */
    private String transaction_type;

    /**
     * 通过二维码付款的提供商。
     */
    private String issuer;

    /**
     * 提供商创建二维码并接受付款。
     * 可能的值：airpay shopee、gopay
     */
    private String acquirer;

}
