package so.dian.platform.midtrans.dto.notify;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class RefundDetail {
    /**
     * 退款序列号
     */
    private Long refund_chargeback_id;
    private String refund_amount;
    private Date created_at;
    private String reason;
    /**
     * 商户退款单号
     */
    private String refund_key;
    private String refund_method;
    /**
     * 三方退款单号
     */
    private String refund_chargeback_uuid;
    /**
     * 收到收单银行退款申请确认的时间戳。
     */
    private String bank_confirmed_at;

}
