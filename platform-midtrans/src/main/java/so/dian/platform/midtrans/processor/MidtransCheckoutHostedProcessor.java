package so.dian.platform.midtrans.processor;

import com.alibaba.fastjson.JSON;
import com.chargebolt.eden.enums.ClientTypeEnum;
import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.checkout.req.PreAuthCaptureRequest;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PreAuthCancelResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PreAuthCaptureResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PrepayCreateResultDTO;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundResultDTO;
import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.client.enums.PayMethodEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.google.common.collect.Lists;
import com.midtrans.Midtrans;
import com.midtrans.httpclient.CoreApi;
import com.midtrans.httpclient.SnapApi;
import com.midtrans.httpclient.error.MidtransError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.CheckoutPayProcessor;
import so.dian.hera.interceptor.RefundProcessor;
import so.dian.hera.interceptor.dto.req.ProcessorRefundRequest;
import so.dian.hera.interceptor.utils.DateUtil;
import so.dian.mofa3.lang.exception.BizProcessException;
import so.dian.platform.midtrans.client.MidtransClient;
import so.dian.platform.midtrans.config.MidtransProperty;
import so.dian.platform.midtrans.dto.Action;
import so.dian.platform.midtrans.dto.ChargeResponse;
import so.dian.platform.midtrans.dto.RefundResponse;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MidtransCheckoutHostedProcessor  implements CheckoutPayProcessor, RefundProcessor {

    @Resource
    private MidtransProperty midtransProperty;

    /**
     * 接口文档：https://docs.midtrans.com/docs/coreapi-advanced-features#recommended-parameters
     * @param prePayReq
     * @param paymentDO
     * @return
     */
    @Override
    public PrepayCreateResultDTO doPrePay(PrepayCreateRequest prePayReq, PaymentDO paymentDO){
        // 支付扩展参数信息
        PrepayCreateRequest.PrepayCreateExtInfo extInfo = prePayReq.getExtInfo();

        // 设置全局变量：商户服务端密钥
        Midtrans.serverKey = midtransProperty.getServerKey();
        // 设置全局变量：如果您需要生产环境（接受真实交易），则将值设置为 true
        Midtrans.isProduction = midtransProperty.getIsProduction();
        Map<String, Object> chargeParams = new HashMap<>();
        Map<String, String> transactionDetails = new HashMap<>();
        transactionDetails.put("order_id", paymentDO.getTradeNo());
        transactionDetails.put("gross_amount", paymentDO.getPayAmount().toString());
        chargeParams.put("transaction_details", transactionDetails);

        PayMethodEnum payMethodEnum = PayMethodEnum.explain(extInfo.getPayMethod());

        // 付款方式
        chargeParams.put("payment_type", payMethodEnum.name().toLowerCase());

        // 默认15分支未支付则订单过期 格式为YYYY-MM-DD HH:MM:SS。时区：印度尼西亚西部时间 (GMT+7)
        Map<String, Object> customExpiry = new HashMap<>();
        customExpiry.put("order_time", DateUtil.getFormatDate(new Date(),DateUtil.DEFAULT_PATTERN_YN));
        customExpiry.put("expiry_duration",60);
        customExpiry.put("unit","minute");
        chargeParams.put("custom_expiry",customExpiry);

        // 用户完成支付后被重定向到的商户页面链接
        String payResultUrlFromRequest = extInfo.getPayResult();
        String paymentRedirectUrl;
        Integer clientType = extInfo.getClientType();
        if(Objects.equals(clientType,ClientTypeEnum.APP_ANDROID.getType()) || Objects.equals(clientType,ClientTypeEnum.APP_IOS.getType())){
            paymentRedirectUrl = StringUtils.isBlank(payResultUrlFromRequest) ? midtransProperty.getPayResultUrl().getApp() : payResultUrlFromRequest;
        }else{
            paymentRedirectUrl = StringUtils.isBlank(payResultUrlFromRequest) ? midtransProperty.getPayResultUrl().getH5() : payResultUrlFromRequest;
        }
        String paymentRedirectUrlNoParams = paymentRedirectUrl;
        paymentRedirectUrl = paymentRedirectUrl + "?deviceNo=" + extInfo.getDeviceNo() + "&merchantTransactionId=" + paymentDO.getTradeNo();

        // 接口类型：SnapApi（目前只有Dana支付方式） 和 CoreApi
        boolean isSnapApi = Objects.equals(payMethodEnum,PayMethodEnum.Dana);

        // 商品详情（暂时不传）接口文档：https://docs.midtrans.com/reference/item-details-object
        // 接口文档：https://docs.midtrans.com/reference/gopay-object
        if(Objects.equals(payMethodEnum,PayMethodEnum.GoPay)){
            Map<String, Object> gopay = new HashMap<>();
            gopay.put("enable_callback",true);
            gopay.put("callback_url",paymentRedirectUrl);
            chargeParams.put("gopay",gopay);
        // 接口文档：https://docs.midtrans.com/reference/shopeepay-object
        } else if (Objects.equals(payMethodEnum,PayMethodEnum.ShopeePay)) {
            Map<String, Object> shopeepay = new HashMap<>();
            shopeepay.put("callback_url",paymentRedirectUrl);
            chargeParams.put("shopeepay",shopeepay);
        // 接口文档：https://docs.midtrans.com/reference/qris-object
        // 默认支付方式：gopay
        } else if(Objects.equals(payMethodEnum,PayMethodEnum.QRIS)){
            Map<String, Object> qris = new HashMap<>();
            qris.put("acquirer","gopay");
            chargeParams.put("qris",qris);
        // 接口文档：https://docs.midtrans.com/reference/dana
        }else if(isSnapApi){
            Map<String, Object> dana = new HashMap<>();
            dana.put("callback_url",paymentRedirectUrlNoParams);
            chargeParams.put("dana",dana);
            List<String> enabledPayments = Lists.newArrayList((String) chargeParams.get("payment_type"));
            chargeParams.put("enabled_payments",enabledPayments);
        }

        JSONObject result = null;
        try {
            log.info("印尼创建支付单请求参数 request: {},production: {}", JSON.toJSONString(chargeParams),Midtrans.isProduction);
            result = isSnapApi ? SnapApi.createTransaction(chargeParams) : CoreApi.chargeTransaction(chargeParams);
            log.info("印尼创建支付单应答参数 result: {}", JSON.toJSONString(result));
        } catch (MidtransError e) {
            log.error("印尼创建支付单应答异常 error ",e);
            throw new BizProcessException(e.getMessage());
        }
        // 接口应答非正常
        if(Objects.nonNull(result) && result.keySet().contains("status_code") && !Objects.equals(result.get("status_code"),"201")){
            log.error("印尼创建支付单三方应答失败 errMsg: {}",result.get("status_message")+"-"+result.get("channel_response_message"));
            throw new BizProcessException(result.get("status_message")+"-"+result.get("channel_response_message"));
        }

        ChargeResponse chargeResponse = JSON.parseObject(String.valueOf(result),ChargeResponse.class);
        PrepayCreateResultDTO prepayCreateResultDTO = new PrepayCreateResultDTO();
        prepayCreateResultDTO.setSuccess(true);
        prepayCreateResultDTO.setTradeNo(chargeResponse.getOrder_id());
        prepayCreateResultDTO.setPayNo(chargeResponse.getTransaction_id());

        if(isSnapApi){
            prepayCreateResultDTO.getPrepayResult().put("paymentUrl", chargeResponse.getRedirect_url());
        }else {
            Action action;
            if (Objects.equals(payMethodEnum, PayMethodEnum.QRIS)) {
                action = chargeResponse.getActions().stream().filter(item -> Objects.equals(item.getName(), "generate-qr-code")).collect(Collectors.toList()).get(0);
            } else {
                action = chargeResponse.getActions().stream().filter(item -> Objects.equals(item.getName(), "deeplink-redirect")).collect(Collectors.toList()).get(0);
            }
            prepayCreateResultDTO.getPrepayResult().put("paymentUrl", action.getUrl());
        }
        return prepayCreateResultDTO;
    }

    @Override
    public PreAuthCancelResultDTO doCancel(PaymentDO paymentDO) {
        return null;
    }

    @Override
    public PreAuthCaptureResultDTO doCapture(PreAuthCaptureRequest preauthCaptureRequest, String captureId, PaymentDO capturePaymentDO) {
        return null;
    }

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.MIDTRANS_CHECKOUT_APM);
    }

    /**
     * 接口文档：https://docs.midtrans.com/reference/direct-refund-transaction
     * @param request
     * @return
     */
    @Override
    public RefundResultDTO refund(ProcessorRefundRequest request) {
        // 设置全局变量：商户服务端密钥
        Midtrans.serverKey = midtransProperty.getServerKey();
        // 设置全局变量：如果您需要生产环境（接受真实交易），则将值设置为 true
        Midtrans.isProduction = midtransProperty.getIsProduction();
        Map<String, Object> refundParams = new HashMap<>();
        refundParams.put("refund_key",request.getRefundNo());
        refundParams.put("amount",request.getRefundAmount());
        if(StringUtils.isNotBlank(request.getRemark())){
            refundParams.put("reason",request.getReason());
        }

        // 接口类型：SnapApi（目前只有Dana支付方式） 和 CoreApi
        boolean isSnapApi = Objects.equals(request.getPayMethod(),PayMethodEnum.Dana.getId());

        JSONObject result = null;
        try {
            log.info("印尼申请退款请求参数 request: {},production: {}", JSON.toJSONString(refundParams),Midtrans.isProduction);
            result = isSnapApi ? MidtransClient.snapRefund(refundParams,request.getPayNo()) : MidtransClient.refund(refundParams,request.getTradeNo());
            log.info("印尼申请退款应答参数 result: {}", JSON.toJSONString(result));
        } catch (MidtransError e) {
            log.error("印尼申请退款应答异常 error ",e);
            throw new BizProcessException(e.getMessage());
        }
        RefundResponse refundResponse = JSON.parseObject(String.valueOf(result), RefundResponse.class);
        RefundResultDTO resultDTO = new RefundResultDTO();
        if(Objects.equals(refundResponse.getStatus_code(),"200")){
            resultDTO.setRefundPayNo(isSnapApi ? refundResponse.getRefund_key() : refundResponse.getRefund_chargeback_uuid());
            resultDTO.setSuccess(true);
            resultDTO.setStatus(TransStatusEnum.SUCCESS.getKey());
        }else{
            resultDTO.setSuccess(false);
            resultDTO.setStatus(TransStatusEnum.FAIL.getKey());
        }
        return resultDTO;
    }

    @Override
    public ChannelEnum getRefundChannel() {
        return ChannelEnum.MIDTRANS_PAY;
    }

}
