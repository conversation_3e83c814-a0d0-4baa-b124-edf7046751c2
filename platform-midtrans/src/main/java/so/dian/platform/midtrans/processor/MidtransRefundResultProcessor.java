package so.dian.platform.midtrans.processor;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.enums.PayMethodEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.google.common.collect.Lists;
import com.midtrans.Midtrans;
import com.midtrans.httpclient.error.MidtransError;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.QueryRefundProcessor;
import so.dian.hera.interceptor.dto.req.RefundQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.RefundQueryResultDTO;
import so.dian.hera.interceptor.utils.DateUtil;
import so.dian.mofa3.lang.exception.BizProcessException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.utils.SHA512SignatureUtils;
import so.dian.platform.midtrans.client.MidtransClient;
import so.dian.platform.midtrans.common.enums.TransactionStatusEnum;
import so.dian.platform.midtrans.config.MidtransProperty;
import so.dian.platform.midtrans.dto.notify.MidtransNotifyDTO;
import javax.annotation.Resource;
import java.util.*;
import static so.dian.hera.interceptor.utils.DateUtil.DEFAULT_PATTERN;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MidtransRefundResultProcessor implements QueryRefundProcessor {

    @Resource
    private MidtransProperty midtransProperty;

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(
                PaywayEnum.MIDTRANS_CHECKOUT_APM
        );
    }

    /**
     * 接口文档：https://docs.midtrans.com/reference/get-transaction-status
     * @param queryResultRequest
     * @return
     */
    @Override
    public RefundQueryResultDTO orderRefundQuery(RefundQueryResultRequest queryResultRequest) {

        // 设置全局变量：商户服务端密钥
        Midtrans.serverKey = midtransProperty.getServerKey();
        // 设置全局变量：如果您需要生产环境（接受真实交易），则将值设置为 true
        Midtrans.isProduction = midtransProperty.getIsProduction();
        // 接口类型：SnapApi（目前只有Dana支付方式） 和 CoreApi
        boolean isSnapApi = Objects.equals(queryResultRequest.getPayMethod(), PayMethodEnum.Dana.getId());
        String tradeNo = isSnapApi ? queryResultRequest.getOutPayNo() : queryResultRequest.getTradeNo();
        JSONObject result = null;
        try {
            log.info("印尼订单查询请求参数 request: {},production : {}", queryResultRequest.getTradeNo(),Midtrans.isProduction);
            result = MidtransClient.orderQuery(tradeNo);
            log.info("印尼订单查询应答参数 result： {}", JSON.toJSONString(result));
        } catch (MidtransError e) {
            log.error("印尼订单查询应答异常 error ",e);
            throw new BizProcessException(e.getMessage());
        }

        // 订单查询应答报文与通知报文一样
        MidtransNotifyDTO notifyDTO = JSON.parseObject(result.toString(),MidtransNotifyDTO.class);
        String signString = notifyDTO.getOrder_id() + notifyDTO.getStatus_code() + notifyDTO.getGross_amount() + midtransProperty.getServerKey();
        String localSign = SHA512SignatureUtils.generateSHA512(signString);
        // 验签失败，可重试一次 GlobalExceptionHandler.serverErrorExceptionHandler
        if(!Objects.equals(localSign,notifyDTO.getSignature_key())){
            throw new BizProcessException(HeraBizErrorCodeEnum.SIGN_VERIFY_ERROR.getMessage());
        }
        // 明确退款状态的视为退款成功，其他状态均为失败
        RefundQueryResultDTO resultDTO = new RefundQueryResultDTO();
        resultDTO.setRefundNo(queryResultRequest.getRefundNo());
        if(Objects.equals(notifyDTO.getTransaction_status(), TransactionStatusEnum.refund.name()) ||
                Objects.equals(notifyDTO.getTransaction_status(), TransactionStatusEnum.partial_refund.name())){
            resultDTO.setStatus(PayStatus.REFUNDED.getCode());
            notifyDTO.getRefunds().forEach(item->{
                if(Objects.equals(queryResultRequest.getRefundNo(),item.getRefund_key())){
                    if(StringUtils.isNotBlank(item.getBank_confirmed_at())){
                        resultDTO.setRefundTime(DateUtil.parse(item.getBank_confirmed_at(),DEFAULT_PATTERN));
                    }else{
                        resultDTO.setRefundTime(new Date());
                    }
                }
            });
        }else{
            resultDTO.setStatus(PayStatus.FAIL.getCode());
        }
        return resultDTO;
    }
}