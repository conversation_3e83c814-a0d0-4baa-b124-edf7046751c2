import com.alibaba.fastjson.JSON;
import com.midtrans.Midtrans;
import com.midtrans.httpclient.SnapApi;
import com.midtrans.httpclient.error.MidtransError;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import so.dian.mofa3.lang.exception.BizProcessException;
import so.dian.platform.midtrans.client.MidtransClient;
import so.dian.platform.midtrans.dto.RefundResponse;
import so.dian.platform.midtrans.dto.notify.MidtransNotifyDTO;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class Test {

    public static void main(String[] args) throws MidtransError {
//        refund();
//        danaPay();
        orderQuery();
    }


    public static void danaPay() throws MidtransError {
        // 设置全局变量：商户服务端密钥
        Midtrans.serverKey = "SB-Mid-server-qZ53IeTm6yhebJPT2jOZxisC";
        // 设置全局变量：如果您需要生产环境（接受真实交易），则将值设置为 true
        Midtrans.isProduction = false;
        String json = " {\"payment_type\":\"dana\",\"custom_expiry\":{\"unit\":\"minute\",\"expiry_duration\":60,\"order_time\":\"2025-01-10 15:50:45 +0700\"},\"transaction_details\":{\"order_id\":\"CNT122501101550452012368612\",\"gross_amount\":\"6000\"},\"dana\":{\"callback_url\":\"https://chargebolt-mobile-alter-985472210925785088.seven.dian-dev.com/pages/pingpong/index?deviceNo=862990060014882&merchantTransactionId=CNT122501101550452012368612\",\"enabled_payments\":[\"dana\"]}}";
        Map<String, Object> requestBody = JSON.parseObject(json,Map.class);

        JSONObject jsonObject = SnapApi.createTransaction(requestBody);
        System.out.println(jsonObject);
    }

    public static void refund(){
        // 设置全局变量：商户服务端密钥
        Midtrans.serverKey = "SB-Mid-server-qZ53IeTm6yhebJPT2jOZxisC";
        // 设置全局变量：如果您需要生产环境（接受真实交易），则将值设置为 true
        Midtrans.isProduction = false;
        Map<String, Object> refundParams = new HashMap<>();
        refundParams.put("refund_key","CNR582501101935435621549513");
        refundParams.put("amount",1000);
        refundParams.put("reason","test");
        JSONObject result = null;
        try {
            log.info("midtrans refund req = {}", JSON.toJSONString(refundParams));
            result = MidtransClient.snapRefund(refundParams,"A1202501101128552CwelQZ84EID");
            log.info("midtrans refund rsp = {}", JSON.toJSONString(result));
        } catch (MidtransError e) {
            log.error("midtrans refund error ",e);
            throw new BizProcessException(e.getMessage());
        }
        RefundResponse refundResponse = JSON.parseObject(String.valueOf(result), RefundResponse.class);
        System.out.println(refundResponse);
    }

    public static void orderQuery(){
        // 设置全局变量：商户服务端密钥
        Midtrans.serverKey = "SB-Mid-server-qZ53IeTm6yhebJPT2jOZxisC";
        // 设置全局变量：如果您需要生产环境（接受真实交易），则将值设置为 true
        Midtrans.isProduction = false;
        JSONObject result = null;
        try {
            Map<String, Object> refundParams = new HashMap<>();
            log.info("midtrans refund req = {}", JSON.toJSONString(refundParams));
            result = MidtransClient.orderQuery("A120250110115746ecRznSydOUID");
            log.info("midtrans refund rsp = {}", JSON.toJSONString(result));
        } catch (MidtransError e) {
            log.error("midtrans refund error ",e);
            throw new BizProcessException(e.getMessage());
        }
        MidtransNotifyDTO refundResponse = JSON.parseObject(String.valueOf(result), MidtransNotifyDTO.class);

    }

}
