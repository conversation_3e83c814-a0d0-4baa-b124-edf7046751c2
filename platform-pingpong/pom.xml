<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>chargebolt-hera</artifactId>
        <groupId>com.chargebolt.hera</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>platform-pingpong</artifactId>
    <version>${platform.pingpong.version}</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.chargebolt</groupId>
            <artifactId>hera-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chargebolt.hera</groupId>
            <artifactId>hera-interceptor</artifactId>
        </dependency>
        <dependency>
            <groupId>com.chargebolt.hera</groupId>
            <artifactId>platform-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
            <version>${springcloud.version}</version>
        </dependency>

        <dependency>
            <groupId>so.dian.mofa3</groupId>
            <artifactId>common-lang</artifactId>
        </dependency>
    </dependencies>

</project>