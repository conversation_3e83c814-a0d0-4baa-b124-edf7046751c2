package so.dian.platform.pingpong.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import so.dian.platform.pingpong.dto.requerst.*;
import so.dian.platform.pingpong.dto.response.*;

/**
 * 不要直接依赖PingpongClient，因为bizContent暂时无法实现自动反序列化，需要手工处理一下
 * @see PingpongClientDelegate
 * */
@FeignClient(name = "pingpongClient", url = "${channel.pingpong.url}", fallbackFactory = PingpongClient.PingpongClientFallbackFactory.class)
public interface PingpongClient {

    @PostMapping("/v4/payment/prePay")
    PingpongResponse<PingpongPrePayResponse> prePay(@RequestBody PingpongRequest<PingpongPrePayRequest> request);

    @PostMapping("/v4/payment/capture")
    PingpongResponse<PingpongCaptureResponse> capture(@RequestBody PingpongRequest<PingpongCaptureRequest> request);

    @PostMapping("/v4/payment/refund")
    PingpongResponse<PingpongRefundResponse> refund(PingpongRequest<PingpongRefundRequest> request);

    @PostMapping("/v4/payment/void")
    PingpongResponse<PingpongVoidResponse> paymentVoid(@RequestBody PingpongRequest<PingpongVoidRequest> request);

    @PostMapping("/v4/payment/query")
    PingpongResponse<PingpongQueryResponse> paymentQuery(@RequestBody PingpongRequest<PingpongQueryRequest> request);

    @PostMapping("/v4/refund/query")
    PingpongResponse<PingpongRefundQueryResponse> refundQuery(@RequestBody PingpongRequest<PingpongRefundQueryRequest> request);

    @PostMapping("/v4/capture/query")
    PingpongResponse<PingpongCaptureQueryResponse> captureQuery(@RequestBody PingpongRequest<PingpongCaptureQueryRequest> request);

    @PostMapping("/v4/authorization/prepare")
    PingpongResponse<PingpongApmTokenPrepareResponse> tokenPrepare(@RequestBody PingpongRequest<PingpongApmTokenPrepareRequest> request);

    @PostMapping("/v4/authorization/applyToken")
    PingpongResponse<PingpongApmTokenApplyResponse> tokenApply(@RequestBody PingpongRequest<PingpongApmTokenApplyRequest> request);

    @PostMapping("/v4/payment/unifiedPay")
    PingpongResponse<PingpongUnifiedPayResponse> unifiedPay(@RequestBody PingpongRequest<PingpongUnifiedPayRequest> request);

    @PostMapping("/v4/authorization/list")
    PingpongResponse<PingpongApmTokenQueryResponse> tokenQuery(@RequestBody PingpongRequest<PingpongApmTokenQueryRequest> response);
    @PostMapping("/v4/authorization/cancelToken")
    PingpongResponse<PingpongApmTokenCancelResponse> cancelToken(@RequestBody PingpongRequest<PingpongApmTokenCancelRequest> response);


    @Slf4j
    @Component
    public class PingpongClientFallbackFactory implements FallbackFactory<PingpongClient> {
        @Override
        public PingpongClient create(Throwable cause) {
            return new PingpongClient() {
                @Override
                public PingpongResponse<PingpongPrePayResponse> prePay(PingpongRequest<PingpongPrePayRequest> request) {
                    log.warn("prePay, {}", cause);
                    return null;
                }

                @Override
                public PingpongResponse<PingpongCaptureResponse> capture(PingpongRequest<PingpongCaptureRequest> request) {
                    log.warn("capture, {}", cause);
                    return null;
                }

                @Override
                public PingpongResponse<PingpongRefundResponse> refund(PingpongRequest<PingpongRefundRequest> request) {
                    log.warn("refund, {}", cause);
                    return null;
                }

                @Override
                public PingpongResponse<PingpongVoidResponse> paymentVoid(PingpongRequest<PingpongVoidRequest> request) {
                    log.warn("paymentVoid, {}", cause);
                    return null;
                }

                @Override
                public PingpongResponse<PingpongQueryResponse> paymentQuery(PingpongRequest<PingpongQueryRequest> request) {
                    log.warn("paymentQuery, {}", cause);
                    return null;
                }

                @Override
                public PingpongResponse<PingpongRefundQueryResponse> refundQuery(PingpongRequest<PingpongRefundQueryRequest> request) {
                    log.warn("refundQuery, {}", cause);
                    return null;
                }

                @Override
                public PingpongResponse<PingpongCaptureQueryResponse> captureQuery(PingpongRequest<PingpongCaptureQueryRequest> request) {
                    log.warn("captureQuery, {}", cause);
                    return null;
                }

                @Override
                public PingpongResponse<PingpongApmTokenPrepareResponse> tokenPrepare(PingpongRequest<PingpongApmTokenPrepareRequest> request) {
                    log.warn("prepare, {}", cause);
                    return null;
                }

                @Override
                public PingpongResponse<PingpongApmTokenApplyResponse> tokenApply(PingpongRequest<PingpongApmTokenApplyRequest> request) {
                    log.warn("applyToken, {}", cause);
                    return null;
                }

                @Override
                public PingpongResponse<PingpongUnifiedPayResponse> unifiedPay(PingpongRequest<PingpongUnifiedPayRequest> request) {
                    return null;
                }

                @Override
                public PingpongResponse<PingpongApmTokenQueryResponse> tokenQuery(PingpongRequest<PingpongApmTokenQueryRequest> request) {
                    return null;
                }

                @Override
                public PingpongResponse<PingpongApmTokenCancelResponse> cancelToken(PingpongRequest<PingpongApmTokenCancelRequest> response) {
                    return null;
                }
            };
        }
    }
}
