package so.dian.platform.pingpong.client;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.platform.pingpong.dto.requerst.*;
import so.dian.platform.pingpong.dto.response.*;

import javax.annotation.Resource;
@Slf4j
@Component
public class PingpongClientDelegate implements PingpongClient {

    @Resource
    private PingpongClient pingpongClient;


    @Override
    public PingpongResponse<PingpongPrePayResponse> prePay(PingpongRequest<PingpongPrePayRequest> request) {
        PingpongResponse<PingpongPrePayResponse> response = pingpongClient.prePay(request);
        if (response == null) {
            return null;
        }
        response.setBizContentObject(JSON.parseObject(response.getBizContent(), PingpongPrePayResponse.class));
        return response;
    }

    @Override
    public PingpongResponse<PingpongCaptureResponse> capture(PingpongRequest<PingpongCaptureRequest> request) {
        PingpongResponse<PingpongCaptureResponse> response = pingpongClient.capture(request);
        if (response == null) {
            return null;
        }
        response.setBizContentObject(JSON.parseObject(response.getBizContent(), PingpongCaptureResponse.class));
        return response;
    }

    @Override
    public PingpongResponse<PingpongRefundResponse> refund(PingpongRequest<PingpongRefundRequest> request) {
        PingpongResponse<PingpongRefundResponse> response = pingpongClient.refund(request);
        if (response == null) {
            return null;
        }
        response.setBizContentObject(JSON.parseObject(response.getBizContent(), PingpongRefundResponse.class));
        return response;
    }

    @Override
    public PingpongResponse<PingpongVoidResponse> paymentVoid(PingpongRequest<PingpongVoidRequest> request) {
        PingpongResponse<PingpongVoidResponse> response = pingpongClient.paymentVoid(request);
        if (response == null) {
            return null;
        }
        response.setBizContentObject(JSON.parseObject(response.getBizContent(), PingpongVoidResponse.class));
        return response;
    }

    @Override
    public PingpongResponse<PingpongQueryResponse> paymentQuery(PingpongRequest<PingpongQueryRequest> request) {
        PingpongResponse<PingpongQueryResponse> response = pingpongClient.paymentQuery(request);
        if (response == null) {
            return null;
        }
        response.setBizContentObject(JSON.parseObject(response.getBizContent(), PingpongQueryResponse.class));
        return response;
    }

    @Override
    public PingpongResponse<PingpongRefundQueryResponse> refundQuery(PingpongRequest<PingpongRefundQueryRequest> request) {
        PingpongResponse<PingpongRefundQueryResponse> response = pingpongClient.refundQuery(request);
        if (response == null) {
            return null;
        }
        response.setBizContentObject(JSON.parseObject(response.getBizContent(), PingpongRefundQueryResponse.class));
        return response;
    }

    @Override
    public PingpongResponse<PingpongCaptureQueryResponse> captureQuery(PingpongRequest<PingpongCaptureQueryRequest> request) {
        log.info("captureQuery请求参数:{}", JSON.toJSONString(request));
        PingpongResponse<PingpongCaptureQueryResponse> response = pingpongClient.captureQuery(request);
        log.info("captureQuery返回结果:{}", JSON.toJSONString(response));
        if (response == null) {
            return null;
        }
        response.setBizContentObject(JSON.parseObject(response.getBizContent(), PingpongCaptureQueryResponse.class));
        return response;
    }

    @Override
    public PingpongResponse<PingpongApmTokenPrepareResponse> tokenPrepare(PingpongRequest<PingpongApmTokenPrepareRequest> request) {
        PingpongResponse<PingpongApmTokenPrepareResponse> response = pingpongClient.tokenPrepare(request);
        if (response == null) {
            return null;
        }
        response.setBizContentObject(JSON.parseObject(response.getBizContent(), PingpongApmTokenPrepareResponse.class));
        return response;
    }

    @Override
    public PingpongResponse<PingpongApmTokenApplyResponse> tokenApply(PingpongRequest<PingpongApmTokenApplyRequest> request) {
        PingpongResponse<PingpongApmTokenApplyResponse> response = pingpongClient.tokenApply(request);
        if (response == null) {
            return null;
        }
        response.setBizContentObject(JSON.parseObject(response.getBizContent(), PingpongApmTokenApplyResponse.class));
        return response;
    }

    @Override
    public PingpongResponse<PingpongUnifiedPayResponse> unifiedPay(PingpongRequest<PingpongUnifiedPayRequest> request) {
        PingpongResponse<PingpongUnifiedPayResponse> response = pingpongClient.unifiedPay(request);
        if (response == null) {
            return null;
        }
        response.setBizContentObject(JSON.parseObject(response.getBizContent(), PingpongUnifiedPayResponse.class));
        return response;
    }

    @Override
    public PingpongResponse<PingpongApmTokenQueryResponse> tokenQuery(PingpongRequest<PingpongApmTokenQueryRequest> request) {
        PingpongResponse<PingpongApmTokenQueryResponse> response = pingpongClient.tokenQuery(request);
        if (response == null) {
            return null;
        }
        response.setBizContentObject(JSON.parseObject(response.getBizContent(), PingpongApmTokenQueryResponse.class));
        return response;
    }

    @Override
    public PingpongResponse<PingpongApmTokenCancelResponse> cancelToken(PingpongRequest<PingpongApmTokenCancelRequest> request) {
        PingpongResponse<PingpongApmTokenCancelResponse> response = pingpongClient.cancelToken(request);
        if (response == null) {
            return null;
        }
        response.setBizContentObject(JSON.parseObject(response.getBizContent(), PingpongApmTokenCancelResponse.class));
        return response;
    }
}
