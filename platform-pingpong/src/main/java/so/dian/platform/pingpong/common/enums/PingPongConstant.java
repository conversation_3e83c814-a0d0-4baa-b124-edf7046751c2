package so.dian.platform.pingpong.common.enums;

public class PingPongConstant {

    public static final String NOTIFY_TYPE_CAPTURE = "CAPTURE";
    public static final String NOTIFY_TYPE_RECHARGE = "RECHARGE";
    public static final String NOTIFY_TYPE_VOID = "VOID";
    public static final String NOTIFY_TYPE_REFUND = "REFUND";
    /**
     * INIT-初始态
     */
    public static final String STATUS_INIT = "INIT";
    /**
     * PROCESSING-进行中
     */
    public static final String STATUS_PROCESSING = "PROCESSING";
    /**
     * SUCCESS-成功
     */
    public static final String STATUS_SUCCESS = "SUCCESS";
    /**
     * FAILED-失败
     */
    public static final String STATUS_FAILED = "FAILED";
    /**
     * CLOSED-订单关闭
     */
    public static final String STATUS_CLOSED = "CLOSED";
    /**
     * AUTH_SUCCESS-授权成功
     */
    public static final String STATUS_AUTH_SUCCESS = "AUTH_SUCCESS";

    public static final String ACCESS_TOKEN_CREATION = "ACCESS_TOKEN_CREATION";
    public static final String ACCESS_TOKEN_CANCEL_OF_MERCHANT = "ACCESS_TOKEN_CANCEL_OF_MERCHANT";
    public static final String ACCESS_TOKEN_CANCEL_OF_MERCHANT_USER = "ACCESS_TOKEN_CANCEL_OF_MERCHANT_USER";
}
