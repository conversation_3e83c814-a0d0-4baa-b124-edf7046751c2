package so.dian.platform.pingpong.common.enums;

import com.chargebolt.hera.client.enums.PayMethodEnum;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import lombok.Getter;

@Getter
public enum PingpongPayMethodEnum {


    MASTERCARD("Mastercard", PayMethodEnum.MasterCard),
    VISA("VISA", PayMethodEnum.VISA),
    UNION_PAY("CHINA UNION PAY", PayMethodEnum.UnionPay),
    AMERICAN_EXPRESS("American Express", PayMethodEnum.AmericanExpress),
    JCB("JCB", PayMethodEnum.JCB),

    ALIPAY("Alipay", PayMethodEnum.Alipay),
    ALIPAY_HK("AlipayHK", PayMethodEnum.AlipayHK),
    WECHAT("WeChat Pay", PayMethodEnum.Wechat),
    G_CASH("GCash", PayMethodEnum.GCash),
    PROMPT_PAY("PromptPay", PayMethodEnum.PromptPay),
    RABBIT_LINE_PAY("Rabbit LINE Pay", PayMethodEnum.RabbitLinePay),
    KAKAO_PAY("Kakaopay", PayMethodEnum.KakaoPay),
    ZALO_PAY("ZaloPay", PayMethodEnum.ZaloPay),

    ;
    private String method;
    private PayMethodEnum payMethodEnum;

    PingpongPayMethodEnum(String method, PayMethodEnum payMethodEnum) {
        this.method = method;
        this.payMethodEnum = payMethodEnum;
    }

    public static PingpongPayMethodEnum explainName(String name) {
        for (PingpongPayMethodEnum pingpongPayMethodEnum : PingpongPayMethodEnum.values()) {
            if (pingpongPayMethodEnum.name().equals(name)) {
                return pingpongPayMethodEnum;
            }
        }
        throw HeraBizException.create(HeraBizErrorCodeEnum.ILLEGAL_ERROR);
    }

    public static PingpongPayMethodEnum explainMethod(String method) {
        for (PingpongPayMethodEnum pingpongPayMethodEnum : PingpongPayMethodEnum.values()) {
            if (pingpongPayMethodEnum.getMethod().equals(method)) {
                return pingpongPayMethodEnum;
            }
        }
        throw HeraBizException.create(HeraBizErrorCodeEnum.ILLEGAL_ERROR);
    }

    public static PingpongPayMethodEnum explain(PayMethodEnum payMethodEnum) {
        for (PingpongPayMethodEnum pingpongPayMethodEnum : PingpongPayMethodEnum.values()) {
            if (pingpongPayMethodEnum.getPayMethodEnum().equals(payMethodEnum)) {
                return pingpongPayMethodEnum;
            }
        }
        throw HeraBizException.create(HeraBizErrorCodeEnum.ILLEGAL_ERROR);
    }
}
