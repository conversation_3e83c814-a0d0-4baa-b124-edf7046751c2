package so.dian.platform.pingpong.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import so.dian.commons.eden.util.LogColorUtils;

@Configuration
@ComponentScan("so.dian.platform.pingpong.*")
@ConditionalOnProperty(prefix = "channel.enable", name = "pingpong", havingValue = "true")
@Slf4j
public class PingpongAutoConfiguration {

    static {
        log.info(LogColorUtils.blueMessage("Pingpong enable"));
    }
}
