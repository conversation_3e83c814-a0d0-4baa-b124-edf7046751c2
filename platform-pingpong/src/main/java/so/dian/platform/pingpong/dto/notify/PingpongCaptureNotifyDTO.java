package so.dian.platform.pingpong.dto.notify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * https://acquirer-api-docs-v4.pingpongx.com/pages/ad0c0f/
 */
@Data
public class PingpongCaptureNotifyDTO extends PingpongNotifyType {
    @JsonProperty("amount")
    private String amount;
    @JsonProperty("captureTime")
    private String captureTime;
    @JsonProperty("captureEndingTime")
    private String captureEndingTime;
    @JsonProperty("merchantTransactionId")
    private String merchantTransactionId;
    @JsonProperty("transactionId")
    private String transactionId;
    @JsonProperty("merchantCaptureId")
    private String merchantCaptureId;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("status")
    private String status;

}
