package so.dian.platform.pingpong.dto.notify;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.StringUtils;

@NoArgsConstructor
@Data
public class PingpongNotifyBaseDTO {
    @JsonProperty("accId")
    private String accId;
    @JsonProperty("bizContent")
    private String bizContent;
    @JsonProperty("clientId")
    private String clientId;
    @JsonProperty("code")
    private String code;
    @JsonProperty("description")
    private String description;
    @JsonProperty("sign")
    private String sign;
    @JsonProperty("signType")
    private String signType;

    private JSONObject bizContentObject = new JSONObject();

    public void setBizContent(String bizContent) {
        this.bizContent = bizContent;
        if (!StringUtils.isEmpty(bizContent)) {
            this.bizContentObject = JSON.parseObject(bizContent);
        }
    }

    public String getNotifyType() {
        return bizContentObject.getString("notifyType");
    }

    public String getStatus() {
        return bizContentObject.getString("status");
    }
}
