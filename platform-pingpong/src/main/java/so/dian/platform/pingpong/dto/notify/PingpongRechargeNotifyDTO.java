package so.dian.platform.pingpong.dto.notify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * https://acquirer-api-docs-v4.pingpongx.com/pages/5121a4/
 */
@Data
public class PingpongRechargeNotifyDTO extends PingpongNotifyType {
    @JsonProperty("amount")
    private String amount;
    @JsonProperty("transactionTime")
    private String transactionTime;
    @JsonProperty("merchantTransactionId")
    private String merchantTransactionId;
    @JsonProperty("transactionId")
    private String transactionId;
    @JsonProperty("token")
    private String token;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("threeDSecure")
    private String threeDSecure;
    @JsonProperty("status")
    private String status;
    @JsonProperty("captureDelayHours")
    private Integer captureDelayHours;
    @JsonProperty("remark")
    private String remark;
    @JsonProperty("requestId")
    private String requestId;
    @JsonProperty("exchangedAmount")
    private String exchangedAmount;
    @JsonProperty("exchangedCurrency")
    private String exchangedCurrency;
    @JsonProperty("paymentMethod")
    private PaymentMethod paymentMethod;
    @JsonProperty("issuerInfo")
    private IssuerInfo issuerInfo;
    @JsonProperty("cardInfo")
    private CardInfo cardInfo;
    @JsonProperty("threeDSInfo")
    private ThreeDSInfo threeDSInfo;
    @JsonProperty("authenticationInfo")
    private AuthenticationInfo authenticationInfo;


    @Data
    public static class PaymentMethod {
        @JsonProperty("type")
        private String type;
    }

    @Data
    private static class IssuerInfo {
        @JsonProperty("issuerResultCode")
        private String issuerResultCode;
        @JsonProperty("issuerResultMsg")
        private String issuerResultMsg;
    }

    @Data
    private static class CardInfo {
        @JsonProperty("cardLevel")
        private String cardLevel;
        /**
         * 卡交易·`卡类型,枚举值：
         * DEBIT
         * CREDIT
         * CHARGE
         * CARD
         */
        @JsonProperty("cardType")
        private String cardType;
        @JsonProperty("firstName")
        private String firstName;
        @JsonProperty("lastName")
        private String lastName;
        @JsonProperty("firstSixDigits")
        private String firstSixDigits;
        @JsonProperty("lastFourDigits")
        private String lastFourDigits;
        @JsonProperty("ipCountry")
        private String ipCountry;
        @JsonProperty("isoCountry")
        private String isoCountry;
        @JsonProperty("issuringBank")
        private String issuringBank;
        @JsonProperty("isoCountryA2")
        private String isoCountryA2;
        /**
         * 卡交易·卡品牌：VISA / Mastercard / American Express/ JCB / Discover
         */
        @JsonProperty("paymentBrand")
        private String paymentBrand;
    }

    @Data
    private static class ThreeDSInfo {

        @JsonProperty("authenticationResult")
        private String authenticationResult;
        @JsonProperty("liabilityShift")
        private String liabilityShift;
        /**
         * 仅对 Visa、American Express、ICB、Diners Club 和Discover 的交易才会返回。当身份验证失败时，该字段为空。该字段包含以下值之一:
         * 01:尝试身份验证 (Mastercard) 01:
         * 02:成功身份验证 (Mastercard)
         * 05:成功身份验证 (Visa、American Express、JCB、Diners Club 和 Discover)
         * 06: 尝试身份验证 (Visa、American Express、JCB、Diners Club 和 Discover)
         */
        @JsonProperty("eci")
        private String eci;
        @JsonProperty("authentication3DSecure")
        private String authentication3DSecure;
        @JsonProperty("specificationVersion")
        private String specificationVersion;
        @JsonProperty("cavv")
        private String cavv;

    }

    @Data
    private static class AuthenticationInfo {
        @JsonProperty("avsResult")
        private String avsResult;
        @JsonProperty("cvvResult")
        private String cvvResult;
        @JsonProperty("threeDSecure")
        private String threeDSecure;
    }
}
