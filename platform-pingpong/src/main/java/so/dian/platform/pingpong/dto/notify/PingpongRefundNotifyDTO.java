package so.dian.platform.pingpong.dto.notify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * https://acquirer-api-docs-v4.pingpongx.com/pages/71c886/
 * */
@Data
public class PingpongRefundNotifyDTO extends PingpongNotifyType {
    @JsonProperty("amount")
    private String amount;
    @JsonProperty("refundTime")
    private String refundTime;
    @JsonProperty("refundEndingTime")
    private String refundEndingTime;
    @JsonProperty("transactionId")
    private String transactionId;
    @JsonProperty("merchantTransactionId")
    private String merchantTransactionId;
    @JsonProperty("merchantRefundId")
    private String merchantRefundId;
    @JsonProperty("refundId")
    private String refundId;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("status")
    private String status;
    @JsonProperty("remark")
    private String remark;
}
