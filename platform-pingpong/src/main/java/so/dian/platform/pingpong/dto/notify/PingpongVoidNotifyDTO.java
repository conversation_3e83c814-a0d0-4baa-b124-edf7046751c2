package so.dian.platform.pingpong.dto.notify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * https://acquirer-api-docs-v4.pingpongx.com/pages/f687d3/
 */
@Data
public class PingpongVoidNotifyDTO extends PingpongNotifyType {

    @JsonProperty("amount")
    private String amount;
    @JsonProperty("voidTime")
    private String voidTime;
    @JsonProperty("voidEndingTime")
    private String voidEndingTime;
    @JsonProperty("transactionId")
    private String transactionId;
    @JsonProperty("merchantTransactionId")
    private String merchantTransactionId;
    @JsonProperty("merchantVoidId")
    private String merchantVoidId;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("status")
    private String status;
}
