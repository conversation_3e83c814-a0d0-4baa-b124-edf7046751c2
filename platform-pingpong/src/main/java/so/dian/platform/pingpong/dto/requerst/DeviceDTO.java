package so.dian.platform.pingpong.dto.requerst;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;


@NoArgsConstructor
@Data
public class DeviceDTO {
    public static final String H5 = "01";
    public static final String PC = "02";
    public static final String IOS = "03";
    public static final String ANDROID = "04";

    @JsonProperty("orderTerminal")
    private String orderTerminal;
}
