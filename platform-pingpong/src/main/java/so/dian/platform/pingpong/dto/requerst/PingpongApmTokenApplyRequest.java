package so.dian.platform.pingpong.dto.requerst;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class PingpongApmTokenApplyRequest {

    @JsonProperty("requestId")
    private String requestId;
    @JsonProperty("merchantUserId")
    private String merchantUserId;
    @JsonProperty("authState")
    private String authState;
    @JsonProperty("authCode")
    private String authCode;
}
