package so.dian.platform.pingpong.dto.requerst;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class PingpongApmTokenCancelRequest {
    @JsonProperty("requestId")
    private String requestId;
    @JsonProperty("merchantUserId")
    private String merchantUserId;
    @JsonProperty("token")
    private String token;
}
