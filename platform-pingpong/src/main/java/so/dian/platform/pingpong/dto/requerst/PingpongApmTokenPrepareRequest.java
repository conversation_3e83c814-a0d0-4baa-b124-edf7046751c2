package so.dian.platform.pingpong.dto.requerst;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class PingpongApmTokenPrepareRequest {

    @JsonProperty("requestId")
    private String requestId;
    @JsonProperty("merchantUserId")
    private String merchantUserId;
    @JsonProperty("payMethod")
    private String payMethod;
    @JsonProperty("authRedirectUrl")
    private String authRedirectUrl;
    @JsonProperty("notifyUrl")
    private String notifyUrl;
    @JsonProperty("device")
    private DeviceDTO device;

}
