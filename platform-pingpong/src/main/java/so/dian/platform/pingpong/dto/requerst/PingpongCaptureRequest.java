package so.dian.platform.pingpong.dto.requerst;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class PingpongCaptureRequest {

    @JsonProperty("merchantTransactionId")
    private String merchantTransactionId;
    @JsonProperty("merchantCaptureId")
    private String merchantCaptureId;
    @JsonProperty("amount")
    private String amount;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("notificationUrl")
    private String notificationUrl;
}
