package so.dian.platform.pingpong.dto.requerst;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class PingpongPrePayRequest extends PingpongBaseRequest{

    /**
     * 0表示立即capture
     * -1表示手动capture，调用统一下单API成功之后还需要调用下预授权确认 ,本地支付不支持
     * */
    @JsonProperty("captureDelayHours")
    private Integer captureDelayHours = -1;
    /**
     * 关单时间。3分钟到10天（低于3分钟默认取3分钟，高于10天默认取10天）时间戳格式，单位：秒，示例值：1683530496
     * */
    @JsonProperty("timeExpire")
    private String timeExpire;
    @JsonProperty("amount")
    private String amount;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("payResultUrl")
    private String payResultUrl;
    @JsonProperty("payCancelUrl")
    private String payCancelUrl;
    @JsonProperty("notificationUrl")
    private String notificationUrl;
    @JsonProperty("shopperIP")
    private String shopperIP;
    @JsonProperty("merchantUserId")
    private String merchantUserId;
    @JsonProperty("language")
    private String language;
    @JsonProperty("tradeCountry")
    private String tradeCountry;


    @JsonProperty("customer")
    private CustomerDTO customer;
    /**
     * https://acquirer-api-docs-v4.pingpongx.com/pages/d590e1/
     * */
    @JsonProperty("paymentMethods")
    private List<String> paymentMethods;
    private List<GoodsDTO> goods;

    @NoArgsConstructor
    @Data
    public static class CustomerDTO {
        @JsonProperty("identificationId")
        private String identificationId;
        @JsonProperty("lastName")
        private String lastName;
        @JsonProperty("email")
        private String email;
        @JsonProperty("phone")
        private String phone;
    }

    @NoArgsConstructor
    @Data
    public static class GoodsDTO{

        @JsonProperty("averageUnitPrice")
        private String averageUnitPrice;
        @JsonProperty("description")
        private String description;
        @JsonProperty("name")
        private String name;
        @JsonProperty("number")
        private String number;
        @JsonProperty("sku")
        private String sku;
        @JsonProperty("virtualProduct")
        private String virtualProduct;
    }
}
