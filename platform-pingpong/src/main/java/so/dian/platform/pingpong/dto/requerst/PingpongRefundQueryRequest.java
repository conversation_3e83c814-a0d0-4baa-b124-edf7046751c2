package so.dian.platform.pingpong.dto.requerst;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class PingpongRefundQueryRequest {

    @JsonProperty("merchantTransactionId")
    private String merchantTransactionId;
    @JsonProperty("merchantRefundId")
    private String merchantRefundId;
    @JsonProperty("refundId")
    private String refundId;
}
