package so.dian.platform.pingpong.dto.requerst;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class PingpongRequest<T> {
    @JsonProperty("accId")
    private String accId;
    @JsonProperty("bizContent")
    private String bizContent;
    @JsonProperty("clientId")
    private String clientId;
    @JsonProperty("sign")
    private String sign;
    @JsonProperty("signType")
    private String signType;
    @JsonProperty("version")
    private String version;

    public void setBizRequest(T bizRequest) {
        this.bizContent = JSON.toJSONString(bizRequest);
    }
}
