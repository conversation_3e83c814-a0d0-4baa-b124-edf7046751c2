package so.dian.platform.pingpong.dto.requerst;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
public class PingpongUnifiedPayRequest {

    @JsonProperty("captureDelayHours")
    private Integer captureDelayHours;
    @JsonProperty("amount")
    private String amount;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("requestId")
    private String requestId;
    @JsonProperty("payResultUrl")
    private String payResultUrl;
    @JsonProperty("notificationUrl")
    private String notificationUrl;
    @JsonProperty("merchantTransactionId")
    private String merchantTransactionId;
    @JsonProperty("shopperIP")
    private String shopperIP;
    @JsonProperty("language")
    private String language;
    @JsonProperty("merchantUserId")
    private String merchantUserId;
    @JsonProperty("paymentMethod")
    private PaymentMethodDTO paymentMethod;
    @JsonProperty("customer")
    private CustomerDTO customer;
    @JsonProperty("device")
    private DeviceDTO device;

    @NoArgsConstructor
    @Data
    public static class PaymentMethodDTO {
        @JsonProperty("type")
        private String type;
    }

    @NoArgsConstructor
    @Data
    public static class CustomerDTO {
        @JsonProperty("email")
        private String email;
    }
}
