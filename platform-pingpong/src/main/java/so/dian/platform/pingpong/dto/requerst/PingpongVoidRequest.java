package so.dian.platform.pingpong.dto.requerst;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class PingpongVoidRequest {

    @JsonProperty("notificationUrl")
    private String notificationUrl;
    @JsonProperty("merchantVoidId")
    private String merchantVoidId;
    @JsonProperty("merchantTransactionId")
    private String merchantTransactionId;
    @JsonProperty("amount")
    private String amount;
    @JsonProperty("currency")
    private String currency;
}
