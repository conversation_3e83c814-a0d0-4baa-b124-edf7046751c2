package so.dian.platform.pingpong.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class PingpongApmTokenApplyResponse {
    @JsonProperty("userLoginId")
    private String userLoginId;
    @JsonProperty("expireTime")
    private String expireTime;
    @JsonProperty("token")
    private String token;
}
