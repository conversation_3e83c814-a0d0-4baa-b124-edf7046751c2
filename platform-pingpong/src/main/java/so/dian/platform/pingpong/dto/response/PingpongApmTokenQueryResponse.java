package so.dian.platform.pingpong.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class PingpongApmTokenQueryResponse {

    @JsonProperty("queryData")
    private List<QueryDataDTO> queryData;

    @NoArgsConstructor
    @Data
    public static class QueryDataDTO {
        @JsonProperty("userLoginId")
        private String userLoginId;
        @JsonProperty("clientId")
        private String clientId;
        @JsonProperty("expireTime")
        private String expireTime;
        @JsonProperty("payMethod")
        private String payMethod;
        @JsonProperty("accId")
        private String accId;
        @JsonProperty("merchantUserId")
        private String merchantUserId;
        @JsonProperty("token")
        private String token;
    }
}
