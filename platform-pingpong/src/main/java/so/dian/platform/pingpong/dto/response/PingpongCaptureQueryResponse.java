package so.dian.platform.pingpong.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class PingpongCaptureQueryResponse {
    @JsonProperty("amount")
    private String amount;
    @JsonProperty("captureTime")
    private String captureTime;
    private String captureEndingTime;
    @JsonProperty("resultCode")
    private String resultCode;
    @JsonProperty("transactionId")
    private String transactionId;
    @JsonProperty("merchantTransactionId")
    private String merchantTransactionId;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("resultDescription")
    private String resultDescription;
    @JsonProperty("status")
    private String status;
}
