package so.dian.platform.pingpong.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class PingpongCaptureResponse {

    @JsonProperty("transactionId")
    private String transactionId;
    @JsonProperty("merchantTransactionId")
    private String merchantTransactionId;
    @JsonProperty("merchantCaptureId")
    private String merchantCaptureId;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("amount")
    private String amount;
    @JsonProperty("captureTime")
    private String captureTime;
    @JsonProperty("captureEndingTime")
    private String captureEndingTime;
    @JsonProperty("status")
    private String status;

}
