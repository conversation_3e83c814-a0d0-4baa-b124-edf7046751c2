package so.dian.platform.pingpong.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class PingpongPrePayResponse {
    @JsonProperty("amount")
    private String amount;
    @JsonProperty("paymentUrl")
    private String paymentUrl;
    @JsonProperty("transactionId")
    private String transactionId;
    @JsonProperty("token")
    private String token;
    @JsonProperty("merchantTransactionId")
    private String merchantTransactionId;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("innerJsUrl")
    private String innerJsUrl;
}
