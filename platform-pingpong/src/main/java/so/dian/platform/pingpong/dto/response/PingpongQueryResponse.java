package so.dian.platform.pingpong.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class PingpongQueryResponse {
    @JsonProperty("issuerInfo")
    private IssuerInfoDTO issuerInfo;
    @JsonProperty("threeDSecure")
    private String threeDSecure;
    @JsonProperty("resultCode")
    private String resultCode;
    @JsonProperty("transactionTime")
    private String transactionTime;
    private String transactionEndingTime;
    @JsonProperty("requestId")
    private String requestId;
    @JsonProperty("merchantTransactionId")
    private String merchantTransactionId;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("resultDescription")
    private String resultDescription;
    @JsonProperty("amount")
    private String amount;
    @JsonProperty("authenticationInfo")
    private AuthenticationInfoDTO authenticationInfo;
    @JsonProperty("cardInfo")
    private CardInfoDTO cardInfo;
    @JsonProperty("transactionId")
    private String transactionId;
    @JsonProperty("token")
    private String token;
    @JsonProperty("captureDelayHours")
    private Integer captureDelayHours;
    @JsonProperty("status")
    private String status;

    @NoArgsConstructor
    @Data
    public static class IssuerInfoDTO {
        @JsonProperty("issuerResultMsg")
        private String issuerResultMsg;
        @JsonProperty("issuerResultCode")
        private String issuerResultCode;
    }

    @NoArgsConstructor
    @Data
    public static class AuthenticationInfoDTO {
        @JsonProperty("avsResult")
        private String avsResult;
        @JsonProperty("cvvResult")
        private String cvvResult;
        @JsonProperty("threeDSecure")
        private String threeDSecure;
    }

    @NoArgsConstructor
    @Data
    public static class CardInfoDTO {
        @JsonProperty("firstName")
        private String firstName;
        @JsonProperty("isoCountryA2")
        private String isoCountryA2;
        @JsonProperty("lastName")
        private String lastName;
        @JsonProperty("lastFourDigits")
        private String lastFourDigits;
        @JsonProperty("cardLevel")
        private String cardLevel;
        @JsonProperty("paymentBrand")
        private String paymentBrand;
        @JsonProperty("cardType")
        private String cardType;
        @JsonProperty("issuringBank")
        private String issuringBank;
        @JsonProperty("ipCountry")
        private String ipCountry;
        @JsonProperty("firstSixDigits")
        private String firstSixDigits;
        @JsonProperty("isoCountry")
        private String isoCountry;
    }
}
