package so.dian.platform.pingpong.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class PingpongRefundQueryResponse {
    @JsonProperty("amount")
    private String amount;
    @JsonProperty("cardInfo")
    private CardInfoDTO cardInfo;
    @JsonProperty("threeDSecure")
    private String threeDSecure;
    @JsonProperty("resultCode")
    private String resultCode;
    @JsonProperty("transactionTime")
    private String transactionTime;
    @JsonProperty("transactionId")
    private String transactionId;
    @JsonProperty("merchantTransactionId")
    private String merchantTransactionId;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("resultDescription")
    private String resultDescription;
    @JsonProperty("captureDelayHours")
    private Integer captureDelayHours;
    @JsonProperty("status")
    private String status;
    private String refundEndingTime;

    @NoArgsConstructor
    @Data
    public static class CardInfoDTO {
        @JsonProperty("firstName")
        private String firstName;
        @JsonProperty("lastName")
        private String lastName;
        @JsonProperty("lastFourDigits")
        private String lastFourDigits;
        @JsonProperty("ipCountry")
        private String ipCountry;
        @JsonProperty("firstSixDigits")
        private String firstSixDigits;
    }
}
