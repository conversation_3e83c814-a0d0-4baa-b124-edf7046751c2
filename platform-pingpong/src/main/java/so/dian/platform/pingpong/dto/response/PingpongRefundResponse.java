package so.dian.platform.pingpong.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class PingpongRefundResponse {

    @JsonProperty("transactionId")
    private String transactionId;
    @JsonProperty("merchantTransactionId")
    private String merchantTransactionId;
    @JsonProperty("merchantRefundId")
    private String merchantRefundId;
    @JsonProperty("refundId")
    private String refundId;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("amount")
    private String amount;

    @JsonProperty("refundTime")
    private String refundTime;
    @JsonProperty("refundEndingTime")
    private String refundEndingTime;
    @JsonProperty("status")
    private String status;
    @JsonProperty("remark")
    private String remark;
}
