package so.dian.platform.pingpong.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class PingpongResponse<T> {

    @JsonProperty("accId")
    private String accId;
    @JsonProperty("bizContent")
    private String bizContent;
    @JsonProperty("clientId")
    private String clientId;
    @JsonProperty("code")
    private String code;
    @JsonProperty("description")
    private String description;
    @JsonProperty("sign")
    private String sign;
    @JsonProperty("signType")
    private String signType;

    private T bizContentObject;

}
