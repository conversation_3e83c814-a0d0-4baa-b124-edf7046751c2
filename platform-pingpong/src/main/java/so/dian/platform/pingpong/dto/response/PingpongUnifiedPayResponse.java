package so.dian.platform.pingpong.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class PingpongUnifiedPayResponse {
    @JsonProperty("requestId")
    private String requestId;
    @JsonProperty("merchantTransactionId")
    private String merchantTransactionId;
    @JsonProperty("amount")
    private String amount;
    @JsonProperty("threeDUnionParams")
    private ThreeDUnionParamsDTO threeDUnionParams;
    @JsonProperty("transactionId")
    private String transactionId;
    @JsonProperty("threeDContinue")
    private String threeDContinue;
    @JsonProperty("paymentMethod")
    private PaymentMethodDTO paymentMethod;
    @JsonProperty("captureDelayHours")
    private Integer captureDelayHours;
    @JsonProperty("status")
    private String status;
    @JsonProperty("action")
    private ActionDTO action;

    @NoArgsConstructor
    @Data
    public static class ThreeDUnionParamsDTO {
        @JsonProperty("threeDContinue")
        private String threeDContinue;
    }

    @NoArgsConstructor
    @Data
    public static class PaymentMethodDTO {
        @JsonProperty("type")
        private String type;
    }

    @NoArgsConstructor
    @Data
    public static class ActionDTO{
        @JsonProperty("type")
        private String type;
        @JsonProperty("qrCode")
        private String qrCode;
        @JsonProperty("qrUrl")
        private String qrUrl;
        @JsonProperty("paymentRedirectUrl")
        private String paymentRedirectUrl;
    }
}
