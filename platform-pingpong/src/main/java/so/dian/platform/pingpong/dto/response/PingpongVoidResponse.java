package so.dian.platform.pingpong.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class PingpongVoidResponse {

    @JsonProperty("transactionId")
    private String transactionId;
    @JsonProperty("merchantTransactionId")
    private String merchantTransactionId;
    @JsonProperty("merchantVoidId")
    private String merchantVoidId;
    @JsonProperty("description")
    private String description;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("amount")
    private String amount;


    @JsonProperty("voidTime")
    private String voidTime;
    @JsonProperty("voidEndingTime")
    private String voidEndingTime;
    @JsonProperty("status")
    private String status;
}
