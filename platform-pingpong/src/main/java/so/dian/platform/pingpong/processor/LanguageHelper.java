package so.dian.platform.pingpong.processor;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class LanguageHelper {

    private static final Map<String, String> LANG_MAP = new HashMap() {{
        put("de", "de");
        put("de-DE", "de");
        put("de-CH", "de");
        put("de-AT", "de");
        put("de-LU", "de");
        put("de-LI", "de");
        put("en", "en");
        put("en-US", "en");
        put("en-EG", "en");
        put("en-AU", "en");
        put("en-GB", "en");
        put("en-CA", "en");
        put("en-NZ", "en");
        put("en-IE", "en");
        put("en-ZA", "en");
        put("en-JM", "en");
        put("en-BZ", "en");
        put("en-TT", "en");
        put("en-SG", "en");
        put("es", "es");
        put("es-AR", "es");
        put("es-GT", "es");
        put("es-CR", "es");
        put("es-PA", "es");
        put("es-DO", "es");
        put("es-MX", "es");
        put("es-VE", "es");
        put("es-CO", "es");
        put("es-PE", "es");
        put("es-EC", "es");
        put("es-CL", "es");
        put("es-UY", "es");
        put("es-PY", "es");
        put("es-BO", "es");
        put("es-SV", "es");
        put("es-HN", "es");
        put("es-NI", "es");
        put("es-PR", "es");
        put("fr", "fr");
        put("fr-FR", "fr");
        put("fr-BE", "fr");
        put("fr-CA", "fr");
        put("fr-CH", "fr");
        put("fr-LU", "fr");
        put("it", "it");
        put("it-CH", "it");
        put("ja", "ja");
        put("ja-JP", "ja");
        put("ru-Latn", "ru");
        put("ru-MI", "ru");
        put("zh-CN", "zh");
        put("et-EE", "et");
        put("pt-BR", "pt-BR");
        put("bg-BG", "bg");
        put("pl-PL", "pl");
        put("da-DK", "da");
        put("fi-FI", "fi");
        put("zh-HK", "zh-HK");
        put("zh-MO", "zh-HK");
        put("kk-KZ", "kz");
        put("ko-KR", "ko-KR");
        put("nl-NL", "nl");
        put("nl-BE", "nl");
        put("fy-NL", "nl");
        put("cs-CZ", "cs");
        put("hr-HR", "cy");
        put("lv-LV", "lv");
        put("lt-LT", "lt");
        put("ro-RO", "ro");
        put("no", "no");
        put("nn", "no");
        put("sma-NO", "no");
        put("nn-NO", "no");
        put("pt-PT", "pt");
        put("sv-SE", "se");
        put("se-SE", "se");
        put("smj-SE", "se");
        put("sma-SE", "se");
        put("sr", "sp");
        put("sh", "sp");
        put("sk-SK", "sk");
        put("sl-SI", "si");
        put("zh-TW", "zh-TW");
        put("th-TH", "th");
        put("tr-TR", "tr");
        put("uk-UA", "ua");
        put("uz", "uz");
        put("vi-VIE", "vi");
        put("vi-VN", "vi");
        put("el-GR", "el");
        put("hu-HU", "hu");
        put("id-ID", "id");
    }};

    public static String lang(String lang) {
        // TODO kronos的语言改掉之后用这段逻辑
//        String language = LANG_MAP.get(lang);
//        return language == null ? lang : language;

        log.info("current lang: {}", lang);
        if (lang.equalsIgnoreCase("zh-hk")) {
            return "zh-HK";
        }
        if (lang.contains("-")) {
            return lang.split("-")[0];
        } else {
            return lang;
        }
    }
}
