/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.pingpong.processor;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.QueryPayProcessor;
import so.dian.hera.interceptor.dto.req.PayQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;
import so.dian.platform.pingpong.client.PingpongClient;
import so.dian.platform.pingpong.client.PingpongClientDelegate;
import so.dian.platform.pingpong.common.enums.PingPongConstant;
import so.dian.platform.pingpong.dto.requerst.PingpongQueryRequest;
import so.dian.platform.pingpong.dto.requerst.PingpongRequest;
import so.dian.platform.pingpong.dto.response.PingpongQueryResponse;
import so.dian.platform.pingpong.dto.response.PingpongResponse;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: PingPongPayResultProcessor.java, v 1.0 2024-04-16 6:50 PM Exp $
 */
@Slf4j
@Component
public class PingPongApmPayResultProcessor extends PingpongProcessor implements QueryPayProcessor {
    private final PingpongClientDelegate pingpongClientDelegate;
    public PingPongApmPayResultProcessor(ObjectProvider<PingpongClientDelegate> pingpongClientDelegateProvider){
        this.pingpongClientDelegate= pingpongClientDelegateProvider.getIfUnique();
    }
    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.PINGPONG_CHECKOUT_APM);
    }

    @Override
    public PayQueryResultDTO orderPayQuery(final PayQueryResultRequest req) {
        PingpongRequest<PingpongQueryRequest> request = new PingpongRequest<>();
        PingpongQueryRequest pingpongQueryRequest = new PingpongQueryRequest();
        pingpongQueryRequest.setMerchantTransactionId(req.getTradeNo());
        request.setBizRequest(pingpongQueryRequest);
        fillRequestParam(request);
        log.info("pingpong 支付查询请求参数:{}",JSON.toJSONString(request));
        PingpongResponse<PingpongQueryResponse> response = pingpongClientDelegate.paymentQuery(request);
        log.info("pingpong 支付查询返回结果:{}", JSON.toJSONString(response));
        PayQueryResultDTO resultDTO = new PayQueryResultDTO();
        resultDTO.setTradeNo(req.getTradeNo());
        if (Objects.isNull(response) || Objects.isNull(response.getBizContentObject())) {
            resultDTO.setStatus(PayStatus.FAIL.getCode());
        } else if(Objects.equals(PingPongConstant.STATUS_INIT, response.getBizContentObject().getStatus())
                || Objects.equals(PingPongConstant.STATUS_PROCESSING, response.getBizContentObject().getStatus())){
            resultDTO.setStatus(PayStatus.INIT.getCode());
        }else if(Objects.equals(PingPongConstant.STATUS_FAILED, response.getBizContentObject().getStatus())
                || Objects.equals(PingPongConstant.STATUS_CLOSED, response.getBizContentObject().getStatus())){
            resultDTO.setTradeNo(response.getBizContentObject().getMerchantTransactionId());
            resultDTO.setPayNo(response.getBizContentObject().getTransactionId());
            resultDTO.setStatus(PayStatus.FAIL.getCode());
            resultDTO.setResponseMsg(response.getBizContentObject().getResultDescription());
        }else if(Objects.equals(PingPongConstant.STATUS_SUCCESS, response.getBizContentObject().getStatus())){
            resultDTO.setStatus(PayStatus.PAID.getCode());
            resultDTO.setTradeNo(response.getBizContentObject().getMerchantTransactionId());
            resultDTO.setPayNo(response.getBizContentObject().getTransactionId());
            if(StringUtils.isNotBlank(response.getBizContentObject().getTransactionEndingTime())){
                resultDTO.setPayTime(new Date(Long.parseLong(response.getBizContentObject().getTransactionEndingTime())));
            }
        }else{
            resultDTO.setStatus(PayStatus.FAIL.getCode());
            resultDTO.setResponseMsg(response.getDescription());
        }
        return resultDTO;
    }
}