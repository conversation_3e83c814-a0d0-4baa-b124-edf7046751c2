package so.dian.platform.pingpong.processor;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 这个类用于对请求内容进行签名，以确保请求的安全性。
 */
public class PingPongCheckoutHelper {

    private final String salt;  // 盐值，用于增加签名的复杂度
    private final SignAlgorithm signAlgorithm; // 签名算法枚举

    /**
     * 构造函数，用于初始化盐值和签名算法。
     *
     * @param salt          盐值
     * @param signAlgorithm 签名算法
     */
    public PingPongCheckoutHelper(String salt, SignAlgorithm signAlgorithm) {
        this.salt = salt;
        this.signAlgorithm = signAlgorithm;
    }

    /**
     * 对请求内容进行签名，并将签名结果添加到请求参数中。
     *
     * @param requestBody 请求内容
     * @return 添加了签名结果的请求参数
     */
    public JSONObject signRequest(JSONObject requestBody) {
        String sign = getSign(salt, signAlgorithm, requestBody);  // 获取请求内容的签名
        requestBody.put("sign", sign);  // 将签名结果添加到请求参数中
        return requestBody;
    }

    /**
     * 获取请求内容的签名结果。
     *
     * @param salt          盐值
     * @param signAlgorithm 签名算法
     * @param requestBody   请求内容
     * @return 请求内容的签名结果
     */
    public static String getSign(String salt, SignAlgorithm signAlgorithm, JSONObject requestBody) {
        StringBuilder stringBuilder = new StringBuilder();
        List<String> keys = new ArrayList<>(requestBody.keySet());
        Collections.sort(keys);  // 对请求参数的键进行升序排序

        for (String key : keys) {
            Object valueObject = requestBody.get(key);
            // 剔除空值
            if (valueObject == null) {
                continue;
            }
            // 剔除非字符串类型的值
            if (!(valueObject instanceof String)) {
                throw new IllegalArgumentException("request body illegal");
            }

            String value = (String) valueObject;
            if (StringUtils.isNotBlank(value)) {
                stringBuilder.append(key).append("=").append(value).append("&");  // 将请求参数的键和值拼接成字符串
            }
        }
        String needSignStr = stringBuilder.toString();
        if (needSignStr.endsWith("&")) {
            needSignStr = needSignStr.substring(0, needSignStr.length() - 1);  // 去掉最后一个 & 符号
        }

        String sign = null;
        if (signAlgorithm == SignAlgorithm.MD5) {
            sign = md5Sign(salt, needSignStr);  // 调用 md5Sign 方法对字符串进行 MD5 加密
        } else if (signAlgorithm == SignAlgorithm.SHA256) {
            sign = sha256(salt, needSignStr);  // 调用 sha256 方法对字符串进行 SHA256 加密
        } else {
            throw new IllegalArgumentException("Signature algorithm not supported");  // 如果签名算法不支持，则抛出异常
        }

        return sign;  // 返回签名结果
    }

    /**
     * 对请求内容进行MD5签名。
     *
     * @param salt    盐值
     * @param content 请求内容
     * @return 请求内容的MD5签名结果
     */
    public static String md5Sign(String salt, String content) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");  // 创建 MD5 实例
            md.update(salt.getBytes());  // 将盐值加入到加密中
            md.update(content.getBytes());  // 将请求内容加入到加密中
            byte[] digest = md.digest();  // 获取字节数组
            return byteToHexString(digest);  // 将字节数组转换成十六进制字符串
        } catch (Exception e) {
            throw new RuntimeException("md5签名失败", e);  // 如果加密过程出错，则抛出异常
        }
    }

    /**
     * 将字节数组转化为十六进制字符串。
     *
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    public static String byteToHexString(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            hexString.append(hex.toUpperCase()); // 将转换后的十六进制字符串拼接起来
        }
        return hexString.toString();  // 返回转换后的十六进制字符串
    }

    /**
     * 对请求内容进行SHA256签名。
     *
     * @param salt    盐值
     * @param content 请求内容
     * @return 请求内容的SHA256签名结果
     */
    public static String sha256(String salt, String content) {
        try {
            String contentStr = salt.concat(content);  // 将盐值和请求内容拼接成一个字符串
            return DigestUtils.sha256Hex(contentStr.getBytes("UTF-8")).toUpperCase();  // 调用 DigestUtils.sha256Hex 方法对字符串进行 SHA256 加密，并将加密结果转换成大写的十六进制字符串
        } catch (Exception e) {
            throw new RuntimeException("sha256签名失败", e);  // 如果加密过程出错，则抛出异常
        }
    }

    /**
     * 签名算法枚举类。
     */
    public enum SignAlgorithm {
        MD5,
        SHA256
    }
}


