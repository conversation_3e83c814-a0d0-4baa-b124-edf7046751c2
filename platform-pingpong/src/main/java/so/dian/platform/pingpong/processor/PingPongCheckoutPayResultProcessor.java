/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.pingpong.processor;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.CaptureQueryResultDTO;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.QueryPayProcessor;
import so.dian.hera.interceptor.dto.req.PayQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.platform.pingpong.client.PingpongClient;
import so.dian.platform.pingpong.client.PingpongClientDelegate;
import so.dian.platform.pingpong.common.enums.PingPongConstant;
import so.dian.platform.pingpong.dto.requerst.PingpongCaptureQueryRequest;
import so.dian.platform.pingpong.dto.requerst.PingpongQueryRequest;
import so.dian.platform.pingpong.dto.requerst.PingpongRequest;
import so.dian.platform.pingpong.dto.response.PingpongCaptureQueryResponse;
import so.dian.platform.pingpong.dto.response.PingpongQueryResponse;
import so.dian.platform.pingpong.dto.response.PingpongResponse;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: PingPongPayResultProcessor.java, v 1.0 2024-04-16 6:50 PM Exp $
 */
@Slf4j
@Component
public class PingPongCheckoutPayResultProcessor extends PingpongProcessor implements QueryPayProcessor {
    private final PingpongClientDelegate pingpongClientDelegate;
    public PingPongCheckoutPayResultProcessor(ObjectProvider<PingpongClientDelegate> pingpongClientDelegateProvider){
        this.pingpongClientDelegate= pingpongClientDelegateProvider.getIfUnique();
    }
    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.PINGPONG_CHECKOUT_CARD);
    }

    @Override
    public PayQueryResultDTO orderPayQuery(final PayQueryResultRequest req) {
        PingpongCaptureQueryRequest queryRequest = new PingpongCaptureQueryRequest();
        queryRequest.setMerchantTransactionId(req.getPayNo());
        queryRequest.setMerchantCaptureId(req.getTradeNo());
        PingpongRequest<PingpongCaptureQueryRequest> request = new PingpongRequest<>();
        request.setBizRequest(queryRequest);
        fillRequestParam(request);
        PingpongResponse<PingpongCaptureQueryResponse> response = pingpongClientDelegate.captureQuery(request);
        PayQueryResultDTO resultDTO = new PayQueryResultDTO();
        resultDTO.setPayNo(req.getPayNo());
        resultDTO.setTradeNo(req.getTradeNo());
        if (response == null
                || response.getBizContentObject() == null) {
            resultDTO.setStatus(PayStatus.INIT.getCode());
            return resultDTO;
        } else {
            if (Objects.equals(PingPongConstant.STATUS_SUCCESS,response.getBizContentObject().getStatus())) {
                resultDTO.setStatus(PayStatus.PAID.getCode());
                resultDTO.setPayNo(response.getBizContentObject().getTransactionId());
                resultDTO.setTradeNo(response.getBizContentObject().getMerchantTransactionId());
                if(StringUtils.isNotBlank(response.getBizContentObject().getCaptureEndingTime())){
                    Date payTime= new DateBuild(Long.parseLong(response.getBizContentObject().getCaptureEndingTime())).toDate();
                    resultDTO.setPayTime(payTime);
                }
            }else if(Objects.equals(PingPongConstant.STATUS_FAILED, response.getBizContentObject().getStatus())){
                resultDTO.setStatus(PayStatus.FAIL.getCode());
                resultDTO.setPayNo(response.getBizContentObject().getTransactionId());
                resultDTO.setTradeNo(response.getBizContentObject().getMerchantTransactionId());
            }else{
                resultDTO.setStatus(PayStatus.INIT.getCode());
                resultDTO.setResponseMsg(response.getDescription());
            }
            return resultDTO;
        }

    }
}