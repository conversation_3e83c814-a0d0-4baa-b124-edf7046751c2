/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.pingpong.processor;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.QueryPayProcessor;
import so.dian.hera.interceptor.QueryRefundProcessor;
import so.dian.hera.interceptor.dto.req.RefundQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;
import so.dian.hera.interceptor.dto.rsp.RefundQueryResultDTO;
import so.dian.platform.pingpong.client.PingpongClient;
import so.dian.platform.pingpong.client.PingpongClientDelegate;
import so.dian.platform.pingpong.common.enums.PingPongConstant;
import so.dian.platform.pingpong.dto.requerst.PingpongQueryRequest;
import so.dian.platform.pingpong.dto.requerst.PingpongRefundQueryRequest;
import so.dian.platform.pingpong.dto.requerst.PingpongRequest;
import so.dian.platform.pingpong.dto.response.PingpongQueryResponse;
import so.dian.platform.pingpong.dto.response.PingpongRefundQueryResponse;
import so.dian.platform.pingpong.dto.response.PingpongResponse;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: PingPongPayResultProcessor.java, v 1.0 2024-04-16 6:50 PM Exp $
 */
@Slf4j
@Component
public class PingPongRefundResultProcessor extends PingpongProcessor implements QueryRefundProcessor {
    private final PingpongClientDelegate pingpongClientDelegate;
    public PingPongRefundResultProcessor(ObjectProvider<PingpongClientDelegate> pingpongClientDelegateProvider){
        this.pingpongClientDelegate= pingpongClientDelegateProvider.getIfUnique();
    }
    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.PINGPONG_CHECKOUT_CARD, PaywayEnum.PINGPONG_CHECKOUT_APM);
    }

    @Override
    public RefundQueryResultDTO orderRefundQuery(RefundQueryResultRequest queryResultRequest) {
        PingpongRequest<PingpongRefundQueryRequest> request = new PingpongRequest<>();
        PingpongRefundQueryRequest pingpongQueryRequest = new PingpongRefundQueryRequest();
        pingpongQueryRequest.setMerchantTransactionId(queryResultRequest.getRefundNo());
        request.setBizRequest(pingpongQueryRequest);
        fillRequestParam(request);
        log.info("pingpong 退款查询请求参数:{}",JSON.toJSONString(request));
        PingpongResponse<PingpongRefundQueryResponse> response = pingpongClientDelegate.refundQuery(request);
        log.info("pingpong 退款查询返回结果:{}", JSON.toJSONString(response));
        RefundQueryResultDTO resultDTO = new RefundQueryResultDTO();
        resultDTO.setRefundNo(queryResultRequest.getRefundNo());
        if (Objects.isNull(response) || Objects.isNull(response.getBizContentObject())) {
            resultDTO.setStatus(PayStatus.FAIL.getCode());
        } else if(Objects.equals(PingPongConstant.STATUS_PROCESSING, response.getBizContentObject().getStatus())){
            resultDTO.setStatus(PayStatus.REFUNDING.getCode());
        }else if(Objects.equals(PingPongConstant.STATUS_FAILED, response.getBizContentObject().getStatus())){
            resultDTO.setStatus(PayStatus.FAIL.getCode());
        }else if(Objects.equals(PingPongConstant.STATUS_SUCCESS, response.getBizContentObject().getStatus())){
            resultDTO.setStatus(PayStatus.REFUNDED.getCode());
            resultDTO.setRefundTime(new Date(Long.parseLong(response.getBizContentObject().getRefundEndingTime())));
        }else{
            resultDTO.setStatus(PayStatus.FAIL.getCode());
        }
        return resultDTO;
    }
}