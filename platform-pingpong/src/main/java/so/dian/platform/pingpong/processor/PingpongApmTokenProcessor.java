package so.dian.platform.pingpong.processor;

import com.alibaba.fastjson.JSON;
import com.chargebolt.eden.enums.ClientTypeEnum;
import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.apmtoken.req.ApmTokenPayRequest;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenApplyResultDTO;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenCheckResultDTO;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenPayResultDTO;
import com.chargebolt.hera.client.dto.pay.apmtoken.rsp.ApmTokenPrepareResultDTO;
import com.chargebolt.hera.client.enums.PayMethodEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;
import com.chargebolt.hera.domain.ApmTokenDO;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.TokenPayProcessor;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.utils.MoneyUtil;
import so.dian.platform.common.utils.ValidateUtil;
import so.dian.platform.pingpong.client.PingpongClient;
import so.dian.platform.pingpong.common.enums.PingpongPayMethodEnum;
import so.dian.platform.pingpong.dto.requerst.*;
import so.dian.platform.pingpong.dto.response.*;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.List;
import java.util.UUID;

@Slf4j
@Component
public class PingpongApmTokenProcessor extends PingpongProcessor implements TokenPayProcessor {

    @Resource
    protected PingpongClient pingpongClientDelegate;
    @Value("${hera.appScheme}")
    private String appScheme;

    @Override
    public ApmTokenPrepareResultDTO prepare(PrepayCreateRequest request, PaymentDO paymentDO) {
        PingpongRequest<PingpongApmTokenPrepareRequest> pingpongRequest = new PingpongRequest<>();
        PingpongApmTokenPrepareRequest bizRequest = new PingpongApmTokenPrepareRequest();
        bizRequest.setRequestId(UUID.randomUUID().toString());
        bizRequest.setMerchantUserId(String.valueOf(request.getUserId()));
        Integer payMethod = request.getExtInfo().getPayMethod();
        PayMethodEnum payMethodEnum = PayMethodEnum.explain(payMethod);
        ValidateUtil.requiredNotNull(paymentDO, HeraBizErrorCodeEnum.REQUIRE_NOT_NULL);
        PingpongPayMethodEnum pingpongPayMethodEnum = PingpongPayMethodEnum.explain(payMethodEnum);
        bizRequest.setPayMethod(pingpongPayMethodEnum.getMethod());
        bizRequest.setAuthRedirectUrl(pingpongProperty.getApmTokenApplyUrl()
                .replace("{userId}", String.valueOf(request.getUserId()))
                .replace("{clientType}", String.valueOf(request.getExtInfo().getClientType()))
        );
        bizRequest.setNotifyUrl(pingpongProperty.getApmTokenNotificationUrl());
        bizRequest.setDevice(new DeviceDTO());
        bizRequest.getDevice().setOrderTerminal(DeviceDTO.H5);
        pingpongRequest.setBizRequest(bizRequest);
        fillRequestParam(pingpongRequest);
        log.info("ApmTokenPrepare 请求参数：{}", JsonUtil.beanToJson(pingpongRequest));
        PingpongResponse<PingpongApmTokenPrepareResponse> response = pingpongClientDelegate.tokenPrepare(pingpongRequest);
        log.info("ApmTokenPrepare 返回结果：{}", JsonUtil.beanToJson(response));
        ApmTokenPrepareResultDTO apmTokenPrepareResultDTO = new ApmTokenPrepareResultDTO();
        if (response == null
                || response.getBizContentObject() == null
                || (response.getCode() != null && !"000000".equals(response.getCode()))) {
            log.warn("pingpong apm-token prepare failed. response = {}", JSON.toJSONString(response));
            apmTokenPrepareResultDTO.setStatus(TransStatusEnum.FAIL.getKey());
            apmTokenPrepareResultDTO.setSuccess(false);
            return apmTokenPrepareResultDTO;
        }
        PingpongApmTokenPrepareResponse bizContentObject = response.getBizContentObject();
        apmTokenPrepareResultDTO.setSuccess(true);
        apmTokenPrepareResultDTO.setTradeNo(paymentDO.getTradeNo());
        apmTokenPrepareResultDTO.getPrepayResult().put("paymentUrl", bizContentObject.getAuthUrl());
        return apmTokenPrepareResultDTO;
    }

    @Override
    public ApmTokenApplyResultDTO apply(PrepayCreateRequest prepayCreateRequest, PaymentDO paymentDO) {
        PingpongRequest<PingpongApmTokenApplyRequest> request = new PingpongRequest();
        PingpongApmTokenApplyRequest apmTokenApplyRequest = new PingpongApmTokenApplyRequest();
        apmTokenApplyRequest.setRequestId(UUID.randomUUID().toString());
        apmTokenApplyRequest.setAuthCode(prepayCreateRequest.getExtInfo().getAuthCode());
        apmTokenApplyRequest.setAuthState(prepayCreateRequest.getExtInfo().getAuthState());
        apmTokenApplyRequest.setMerchantUserId(String.valueOf(prepayCreateRequest.getUserId()));
        request.setBizRequest(apmTokenApplyRequest);
        fillRequestParam(request);
        PingpongResponse<PingpongApmTokenApplyResponse> response = pingpongClientDelegate.tokenApply(request);
        PingpongApmTokenApplyResponse apmTokenApplyResponse = response.getBizContentObject();
        ApmTokenApplyResultDTO resultDTO = new ApmTokenApplyResultDTO();
        if (response == null
                || response.getBizContentObject() == null
                || (response.getCode() != null && !"000000".equals(response.getCode()))) {
            log.warn("pingpong apm-token apply failed. response = {}", JSON.toJSONString(response));
            resultDTO.setStatus(TransStatusEnum.FAIL.getKey());
            resultDTO.setSuccess(false);
            return resultDTO;
        }
        resultDTO.setSuccess(true);
        resultDTO.setToken(apmTokenApplyResponse.getToken());
        resultDTO.setExpireTime(apmTokenApplyResponse.getExpireTime());
        Integer clientType = prepayCreateRequest.getExtInfo().getClientType();
        log.info("clientType: {}", clientType);
        if (clientType.equals(ClientTypeEnum.H5.getType())
                || clientType.equals(ClientTypeEnum.IOS_H5.getType())
                || clientType.equals(ClientTypeEnum.ANDROID_H5.getType())) {
            resultDTO.setRedirectUrl(pingpongProperty.getPayResultUrl().getH5()
                    + "?deviceNo=" + prepayCreateRequest.getExtInfo().getDeviceNo()
                    + "&cbPrepayTradeNo=" + paymentDO.getTradeNo()
                    + "&merchantTransactionId=" + paymentDO.getTradeNo());
        } else if (clientType.equals(ClientTypeEnum.APP_IOS.getType())
                || clientType.equals(ClientTypeEnum.APP_ANDROID.getType())
                || clientType.equals(ClientTypeEnum.APP_IOS_H5.getType())
                || clientType.equals(ClientTypeEnum.APP_ANDROID_H5.getType())) {
            resultDTO.setRedirectUrl(
                    appScheme + URLEncoder.encode(pingpongProperty.getPayResultUrl().getApp()
                            + "?preAuthOrderNo=" + paymentDO.getTradeNo()
                            + "&cbPrepayTradeNo=" + paymentDO.getTradeNo()
                            + "&merchantTransactionId=" + paymentDO.getTradeNo()));
        } else {

        }
        return resultDTO;
    }

    public PingpongResponse<PingpongApmTokenQueryResponse> apmTokenQuery(String token, Long userId) {
        PingpongRequest<PingpongApmTokenQueryRequest> request = new PingpongRequest<>();
        PingpongApmTokenQueryRequest apmTokenQueryRequest = new PingpongApmTokenQueryRequest();
        apmTokenQueryRequest.setToken(token);
        apmTokenQueryRequest.setRequestId(UUID.randomUUID().toString());
        apmTokenQueryRequest.setMerchantUserId(String.valueOf(userId));
        request.setBizRequest(apmTokenQueryRequest);
        fillRequestParam(request);
        return pingpongClientDelegate.tokenQuery(request);
    }

    public PingpongApmTokenCancelResponse apmTokenCancel(String token, Long userId) {
        PingpongRequest<PingpongApmTokenCancelRequest> request = new PingpongRequest<>();
        PingpongApmTokenCancelRequest apmTokenQueryRequest = new PingpongApmTokenCancelRequest();
        apmTokenQueryRequest.setToken(token);
        apmTokenQueryRequest.setRequestId(UUID.randomUUID().toString());
        apmTokenQueryRequest.setMerchantUserId(String.valueOf(userId));
        request.setBizRequest(apmTokenQueryRequest);
        fillRequestParam(request);
        return pingpongClientDelegate.cancelToken(request).getBizContentObject();
    }

    @Override
    public ApmTokenPayResultDTO pay(ApmTokenPayRequest request, ApmTokenDO apmTokenDO, PaymentDO paymentDO) {
        PingpongRequest<PingpongUnifiedPayRequest> pingpongRequest = new PingpongRequest();
        PingpongApmTokenPayRequest apmTokenPayRequest = new PingpongApmTokenPayRequest();
        // 主要参数
        apmTokenPayRequest.setToken(apmTokenDO.getToken());
        apmTokenPayRequest.setBizType("CodeGrant");
        apmTokenPayRequest.setMerchantUserId(String.valueOf(request.getUserId()));
        // 其他下单接口参数
        apmTokenPayRequest.setCaptureDelayHours(0);
        apmTokenPayRequest.setAmount(MoneyUtil.fen2Yuan(paymentDO.getPayAmount(),request.getCurrency()));
        apmTokenPayRequest.setCurrency(request.getCurrency());
        apmTokenPayRequest.setRequestId(UUID.randomUUID().toString());
        apmTokenPayRequest.setNotificationUrl(pingpongProperty.getApmTokenPaidNotificationUrl());
        apmTokenPayRequest.setMerchantTransactionId(paymentDO.getTradeNo());
        apmTokenPayRequest.setShopperIP(request.getIp());

        // 必然不为空
        PayMethodEnum payMethodEnum = PayMethodEnum.explain(apmTokenDO.getTool());
        apmTokenPayRequest.setPaymentMethod(new PingpongUnifiedPayRequest.PaymentMethodDTO());
        apmTokenPayRequest.getPaymentMethod().setType(PingpongPayMethodEnum.explain(payMethodEnum).getMethod());

        apmTokenPayRequest.setDevice(new DeviceDTO());
        apmTokenPayRequest.getDevice().setOrderTerminal(DeviceDTO.H5);

        pingpongRequest.setBizRequest(apmTokenPayRequest);
        fillRequestParam(pingpongRequest);
        PingpongResponse<PingpongUnifiedPayResponse> response = pingpongClientDelegate.unifiedPay(pingpongRequest);
        ApmTokenPayResultDTO resultDTO = new ApmTokenPayResultDTO();
        if (response == null
                || response.getBizContentObject() == null
                || (response.getCode() != null && !"000000".equals(response.getCode()))) {
            log.warn("pingpong apm-token pay failed. response = {}", JSON.toJSONString(response));
            resultDTO.setStatus(TransStatusEnum.FAIL.getKey());
            resultDTO.setSuccess(false);
            return resultDTO;
        }
        resultDTO.setSuccess(true);
        resultDTO.setTransactionId(response.getBizContentObject().getTransactionId());
        resultDTO.setMerchantTransactionId(response.getBizContentObject().getMerchantTransactionId());
        return resultDTO;
    }

    @Override
    public ApmTokenCheckResultDTO check(ApmTokenDO apmTokenDO) {
        ApmTokenCheckResultDTO resultDTO = new ApmTokenCheckResultDTO();
        PingpongResponse<PingpongApmTokenQueryResponse> response = apmTokenQuery(apmTokenDO.getToken(), apmTokenDO.getUserId());
        if (response.getCode() == null) {
            resultDTO.setValid(true);
        } else if ("102000".equals(response.getCode())) {
            resultDTO.setValid(false);
        } else {
            resultDTO.setValid(false);
        }
        return resultDTO;
    }

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.PINGPONG_ONE_CLICK);
    }
}
