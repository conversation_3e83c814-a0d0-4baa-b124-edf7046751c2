package so.dian.platform.pingpong.processor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.chargebolt.eden.enums.AppEnum;
import com.chargebolt.eden.enums.ClientTypeEnum;
import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.checkout.req.PreAuthCaptureRequest;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PreAuthCancelResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PreAuthCaptureResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PrepayCreateResultDTO;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundResultDTO;
import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.client.enums.PayMethodEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import so.dian.hera.interceptor.CheckoutPayProcessor;
import so.dian.hera.interceptor.RefundProcessor;
import so.dian.hera.interceptor.dto.req.ProcessorRefundRequest;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.common.utils.MoneyUtil;
import so.dian.platform.pingpong.client.PingpongClient;
import so.dian.platform.pingpong.common.enums.PingpongPayMethodEnum;
import so.dian.platform.pingpong.dto.requerst.*;
import so.dian.platform.pingpong.dto.response.*;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PingpongCheckoutHostedProcessor extends PingpongProcessor implements CheckoutPayProcessor, RefundProcessor {

    private static Map<Integer, PingpongPayMethodEnum> payMethodEnumMap = new HashMap<Integer, PingpongPayMethodEnum>() {{
        put(AppEnum.ALIPAY_CN.getCode(), PingpongPayMethodEnum.ALIPAY);
        put(AppEnum.ALIPAY_HK.getCode(), PingpongPayMethodEnum.ALIPAY_HK);
        put(AppEnum.WECHAT.getCode(), PingpongPayMethodEnum.WECHAT);
    }};

    @Resource
    protected PingpongClient pingpongClientDelegate;
    @Value("${hera.appScheme}")
    private String appScheme;

    @Override
    public PrepayCreateResultDTO doPrePay(PrepayCreateRequest prepayCreateRequest, PaymentDO paymentDO) {
        PingpongRequest<PingpongPrePayRequest> request = new PingpongRequest();
        PingpongPrePayRequest pingpongPrePayRequest = new PingpongPrePayRequest();
        String requestRegionCode = prepayCreateRequest.getExtInfo().getRegionCode();
        if (StringUtils.isEmpty(requestRegionCode)) {
            pingpongPrePayRequest.setTradeCountry(pingpongProperty.getRegionCode());
        } else {
            pingpongPrePayRequest.setTradeCountry(requestRegionCode);
        }
        if (PaywayEnum.PINGPONG_CHECKOUT_CARD.equals(prepayCreateRequest.getPayway())) {
            pingpongPrePayRequest.setCaptureDelayHours(-1);
            pingpongPrePayRequest.setPaymentMethods(pingpongProperty.getPaymentMethodsOfCard().stream().map(pingpongPayMethodEnum -> pingpongPayMethodEnum.getMethod()).collect(Collectors.toList()));
            pingpongPrePayRequest.setNotificationUrl(pingpongProperty.getCardNotificationUrl());
        } else if (PaywayEnum.PINGPONG_CHECKOUT_APM.equals(prepayCreateRequest.getPayway())) {
            pingpongPrePayRequest.setCaptureDelayHours(0);
            // 设置默认的钱包
            Integer app = prepayCreateRequest.getExtInfo().getApp();
            List<PingpongPayMethodEnum> tmp = Lists.newArrayList(pingpongProperty.getPaymentMethodsOfApm());
            List<PingpongPayMethodEnum> methods = Lists.newArrayList();
            // 排序
            PingpongPayMethodEnum payMethodEnum = payMethodEnumMap.get(app);
            if (payMethodEnum != null) {
                methods.add(payMethodEnum);
            } else {
                if (prepayCreateRequest.getExtInfo().getPayMethod() != null) {
                    PingpongPayMethodEnum pingpongPayMethodEnum = PingpongPayMethodEnum.explain(PayMethodEnum.explain(prepayCreateRequest.getExtInfo().getPayMethod()));
                    if (pingpongPayMethodEnum != null) {
                        methods.add(pingpongPayMethodEnum);
                    } else {
                        log.warn("pingping不支持这种支付工具 {}", prepayCreateRequest.getExtInfo().getPayMethod());
                        methods.addAll(tmp);
                    }
                } else {
                    methods.addAll(tmp);
                }
            }
            pingpongPrePayRequest.setPaymentMethods(methods.stream().map(pingpongPayMethodEnum -> pingpongPayMethodEnum.getMethod()).collect(Collectors.toList()));
            pingpongPrePayRequest.setNotificationUrl(pingpongProperty.getApmNotificationUrl());
        }
        // 10天
        pingpongPrePayRequest.setTimeExpire(String.valueOf(System.currentTimeMillis() / 1000 + 10 * 24 * 3600));
        pingpongPrePayRequest.setAmount(MoneyUtil.fen2Yuan(paymentDO.getPayAmount(), prepayCreateRequest.getCurrency()));
        pingpongPrePayRequest.setCurrency(paymentDO.getCurrency());
        pingpongPrePayRequest.setMerchantTransactionId(paymentDO.getTradeNo());
        Integer clientType = prepayCreateRequest.getExtInfo().getClientType();
        log.info("clientType: {}", clientType);
        if (clientType != null) {
            if (clientType.equals(ClientTypeEnum.H5.getType())
                    || clientType.equals(ClientTypeEnum.IOS_H5.getType())
                    || clientType.equals(ClientTypeEnum.ANDROID_H5.getType())) {
                /**
                 *     H5(1, "普通h5"),
                 *     ANDROID_H5(20, "Android H5"),
                 *     IOS_H5(21, "iOS H5"),
                 * */
                // H5页面跳转会丢失参数，
                // 所以deviceNo需要透传带回来给前端下一步调租借接口的操作
                // pingpong会额外追加token、merchantTransactionId、transactionId三个参数

                String payResultUrlFromRequest = prepayCreateRequest.getExtInfo().getPayResult();
                if (StringUtils.isEmpty(payResultUrlFromRequest)) {
                    pingpongPrePayRequest.setPayResultUrl(pingpongProperty.getPayResultUrl().getH5()
                            + "?deviceNo=" + prepayCreateRequest.getExtInfo().getDeviceNo()
                            + "&cbPrepayTradeNo=" + paymentDO.getTradeNo()
                            + "&merchantTransactionId=" + paymentDO.getTradeNo());
                } else {
                    pingpongPrePayRequest.setPayResultUrl(payResultUrlFromRequest
                            + "?deviceNo=" + prepayCreateRequest.getExtInfo().getDeviceNo()
                            + "&cbPrepayTradeNo=" + paymentDO.getTradeNo()
                            + "&merchantTransactionId=" + paymentDO.getTradeNo());
                }

                pingpongPrePayRequest.setPayCancelUrl(pingpongProperty.getPayCancelUrl().getH5());
            } else if (clientType.equals(ClientTypeEnum.APP_IOS.getType())
                    || clientType.equals(ClientTypeEnum.APP_ANDROID.getType())
                    || clientType.equals(ClientTypeEnum.APP_IOS_H5.getType())
                    || clientType.equals(ClientTypeEnum.APP_ANDROID_H5.getType())) {
                /**
                 *     APP_ANDROID(4, "安卓客户端"),
                 *     APP_IOS(5, "iOS客户端"),
                 *     APP_ANDROID_H5(6, "Android客户端内嵌H5"),
                 *     APP_IOS_H5(7, "iOS客户端内嵌H5"),
                 * */
                // APP
                // APP需要preAuthOrderNo来调state接口
                try {
                    pingpongPrePayRequest.setPayResultUrl(
                            appScheme + URLEncoder.encode(pingpongProperty.getPayResultUrl().getApp()
                                            + "?preAuthOrderNo=" + paymentDO.getTradeNo()
                                            + "&cbPrepayTradeNo=" + paymentDO.getTradeNo()
                                            + "&merchantTransactionId=" + paymentDO.getTradeNo(),
                                    "utf-8")

                    );
                } catch (UnsupportedEncodingException e) {
                    log.warn("urlencode ex.", e);
                    pingpongPrePayRequest.setPayResultUrl(pingpongProperty.getPayResultUrl().getApp());
                }
                pingpongPrePayRequest.setPayCancelUrl(pingpongProperty.getPayCancelUrl().getApp());
            } else {
                pingpongPrePayRequest.setPayResultUrl(pingpongProperty.getPayResultUrl().getH5());
                pingpongPrePayRequest.setPayCancelUrl(pingpongProperty.getPayCancelUrl().getH5());
            }
        }
        pingpongPrePayRequest.setShopperIP(prepayCreateRequest.getIp());
        Integer userSignType = prepayCreateRequest.getExtInfo().getUserSignType();
        if (userSignType != null && !userSignType.equals(90)) {
            pingpongPrePayRequest.setMerchantUserId(String.valueOf(paymentDO.getUserId()));
        }
        String language = prepayCreateRequest.getExtInfo().getLanguage();
        if (!StringUtils.isEmpty(language)) {
            String lang = LanguageHelper.lang(language);
            log.info("set lang: {}", lang);
            pingpongPrePayRequest.setLanguage(lang);
        }

        PingpongPrePayRequest.GoodsDTO goodsDTO = new PingpongPrePayRequest.GoodsDTO();
        JSONObject good = JSON.parseObject(prepayCreateRequest.getExtInfo().getGoodsJSON());
        goodsDTO.setName(good.getString("name"));
        goodsDTO.setAverageUnitPrice(good.getString("averageUnitPrice"));
        goodsDTO.setSku(good.getString("name"));
        goodsDTO.setNumber(good.getString("number"));
        goodsDTO.setDescription(good.getString("description"));
        goodsDTO.setVirtualProduct(good.getString("virtualProduct"));

        pingpongPrePayRequest.setGoods(Lists.newArrayList());
        pingpongPrePayRequest.getGoods().add(goodsDTO);

        request.setBizRequest(pingpongPrePayRequest);
        fillRequestParam(request);
        PingpongResponse<PingpongPrePayResponse> response = pingpongClientDelegate.prePay(request);
        log.info("pingpongClientDelegate.prePay resp:{}", JSON.toJSONString(response));

        PrepayCreateResultDTO prepayCreateResultDTO = new PrepayCreateResultDTO();
        prepayCreateResultDTO.setSuccess(true);
        prepayCreateResultDTO.setTradeNo(response.getBizContentObject().getMerchantTransactionId());
        prepayCreateResultDTO.setPayNo(response.getBizContentObject().getTransactionId());
        prepayCreateResultDTO.getPrepayResult().put("innerJsUrl", response.getBizContentObject().getInnerJsUrl());
        prepayCreateResultDTO.getPrepayResult().put("paymentUrl", response.getBizContentObject().getPaymentUrl());
        return prepayCreateResultDTO;
    }

    @Override
    public PreAuthCancelResultDTO doCancel(PaymentDO paymentDO) {
        PingpongRequest<PingpongVoidRequest> request = new PingpongRequest<>();
        PingpongVoidRequest voidRequest = new PingpongVoidRequest();
        voidRequest.setMerchantTransactionId(paymentDO.getTradeNo());
        // TODO
        voidRequest.setMerchantVoidId("V" + paymentDO.getTradeNo());
        voidRequest.setNotificationUrl(pingpongProperty.getCardNotificationUrl());
        voidRequest.setAmount(MoneyUtil.fen2Yuan(paymentDO.getPayAmount(), paymentDO.getCurrency()));
        voidRequest.setCurrency(paymentDO.getCurrency());

        request.setBizRequest(voidRequest);
        fillRequestParam(request);
        PingpongResponse<PingpongVoidResponse> response = pingpongClientDelegate.paymentVoid(request);

        PreAuthCancelResultDTO resultDTO = new PreAuthCancelResultDTO();
        if (response == null
                || response.getBizContentObject() == null) {
            log.warn("pingpong void failed. response = {}", JSON.toJSONString(response));
            resultDTO.setStatus(TransStatusEnum.FAIL.getKey());
            resultDTO.setSuccess(false);
            return resultDTO;
        } else {
            if ("SUCCESS".equals(response.getBizContentObject().getStatus())
                    || "PROCESSING".equals(response.getBizContentObject().getStatus())) {
                resultDTO.setSuccess(true);
                resultDTO.setStatus(TransStatusEnum.SUCCESS.getKey());
                return resultDTO;
            } else {
                resultDTO.setStatus(TransStatusEnum.FAIL.getKey());
                resultDTO.setSuccess(false);
                return resultDTO;
            }
        }
    }

    @Override
    public PreAuthCaptureResultDTO doCapture(PreAuthCaptureRequest preauthCaptureRequest, String captureId, PaymentDO capturePaymentDO) {
        PingpongRequest<PingpongCaptureRequest> request = new PingpongRequest();
        PingpongCaptureRequest captureRequest = new PingpongCaptureRequest();
        captureRequest.setAmount(MoneyUtil.fen2Yuan(preauthCaptureRequest.getOrderAmount(), preauthCaptureRequest.getCurrency()));
        captureRequest.setCurrency(preauthCaptureRequest.getCurrency());
        captureRequest.setMerchantCaptureId(capturePaymentDO.getTradeNo());
        captureRequest.setMerchantTransactionId(preauthCaptureRequest.getPreAuthTradeNo());
        captureRequest.setNotificationUrl(pingpongProperty.getCardNotificationUrl());

        request.setBizRequest(captureRequest);
        fillRequestParam(request);
        log.info("pingpong发起预授权扣款请求参数：{}", JsonUtil.beanToJson(request));
        PingpongResponse<PingpongCaptureResponse> response = pingpongClientDelegate.capture(request);
        log.info("pingpong发起预授权扣款返回结果：{}", JsonUtil.beanToJson(response));
        PreAuthCaptureResultDTO resultDTO = new PreAuthCaptureResultDTO();
        if (response == null
                || response.getBizContentObject() == null) {
            log.warn("pingpong capture failed. response = {}", JSON.toJSONString(response));
            resultDTO.setStatus(TransStatusEnum.FAIL.getKey());
            resultDTO.setSuccess(false);
            return resultDTO;
        } else {
            if ("SUCCESS".equals(response.getBizContentObject().getStatus())
                    || "PROCESSING".equals(response.getBizContentObject().getStatus())) {
                resultDTO.setSuccess(true);
                resultDTO.setStatus(TransStatusEnum.SUCCESS.getKey());
                return resultDTO;
            } else {
                resultDTO.setStatus(TransStatusEnum.FAIL.getKey());
                resultDTO.setSuccess(false);
                return resultDTO;
            }
        }
    }

    @Override
    public RefundResultDTO refund(ProcessorRefundRequest processorRefundRequest) {
        PingpongRequest<PingpongRefundRequest> request = new PingpongRequest<>();
        PingpongRefundRequest refundRequest = new PingpongRefundRequest();
        refundRequest.setMerchantTransactionId(processorRefundRequest.getTradeNo());
        refundRequest.setMerchantRefundId(processorRefundRequest.getRefundNo());
        refundRequest.setNotificationUrl(pingpongProperty.getCardNotificationUrl());
        refundRequest.setAmount(MoneyUtil.fen2Yuan(processorRefundRequest.getRefundAmount(), processorRefundRequest.getCurrency()));
        refundRequest.setCurrency(processorRefundRequest.getCurrency());

        request.setBizRequest(refundRequest);
        fillRequestParam(request);
        PingpongResponse<PingpongRefundResponse> response = pingpongClientDelegate.refund(request);
        RefundResultDTO refundResultDTO = new RefundResultDTO();
        if (response == null
                || response.getBizContentObject() == null) {
            log.warn("pingpong refund failed. response = {}", JSON.toJSONString(response));
            refundResultDTO.setSuccess(false);
            refundResultDTO.setStatus(TransStatusEnum.FAIL.getKey());
            return refundResultDTO;
        } else {
            if ("SUCCESS".equals(response.getBizContentObject().getStatus())
                    || "PROCESSING".equals(response.getBizContentObject().getStatus())) {
                refundResultDTO.setRefundPayNo(response.getBizContentObject().getRefundId());
                refundResultDTO.setSuccess(true);
                refundResultDTO.setStatus(TransStatusEnum.SUCCESS.getKey());
                return refundResultDTO;
            } else {
                log.warn("pingpong refund failed. response = {}", JSON.toJSONString(response));
                refundResultDTO.setSuccess(false);
                refundResultDTO.setStatus(TransStatusEnum.FAIL.getKey());
                refundResultDTO.setMsg(response.getDescription());
                return refundResultDTO;
            }
        }
    }

    @Override
    public ChannelEnum getRefundChannel() {
        return ChannelEnum.PINGPONG;
    }

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.PINGPONG_CHECKOUT_CARD, PaywayEnum.PINGPONG_CHECKOUT_APM);
    }
}
