package so.dian.platform.pingpong.processor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import so.dian.platform.pingpong.dto.requerst.PingpongRequest;

import javax.annotation.Resource;

@Slf4j
public abstract class PingpongProcessor {

    @Resource
    protected PingpongProperty pingpongProperty;

    protected PingpongRequest fillRequestParam(PingpongRequest request) {
        request.setAccId(pingpongProperty.getAccId());
        request.setClientId(pingpongProperty.getClientId());
        request.setSignType(PingPongCheckoutHelper.SignAlgorithm.SHA256.name());
        request.setVersion("1.0");
        if (StringUtils.isEmpty(request.getBizContent())) {
            log.warn("bizContent为空，确认是否已设置");
        }
        JSONObject object = (JSONObject) JSON.toJSON(request);
        String sign = PingPongCheckoutHelper.getSign(pingpongProperty.getSalt(), PingPongCheckoutHelper.SignAlgorithm.SHA256, object);
        request.setSign(sign);
        return request;
    }
}
