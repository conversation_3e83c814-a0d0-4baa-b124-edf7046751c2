package so.dian.platform.pingpong.processor;

import com.google.common.base.Function;
import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.exception.BizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.pingpong.common.enums.PingpongPayMethodEnum;

import javax.annotation.Nullable;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Component
@ConfigurationProperties(prefix = "channel.pingpong")
public class PingpongProperty {

    private String regionCode;
    private String url;
    private String cardNotificationUrl;
    private String apmNotificationUrl;
    private String apmTokenNotificationUrl;
    private String apmTokenPaidNotificationUrl;
    private String refundNotificationUrl;
    private String clientId;
    private String accId;
    private String salt;
    private List<PingpongPayMethodEnum> paymentMethodsOfCard;
    private List<PingpongPayMethodEnum> paymentMethodsOfApm;
    private PayUrl payResultUrl;
    private PayUrl payCancelUrl;

    private String apmTokenApplyUrl;

    @Data
    public static class PayUrl {
        private String h5;
        private String app;
    }

    public void setPaymentMethodsOfCard(List<String> paymentMethodsOfCard) {
        this.paymentMethodsOfCard = paymentMethodsOfCard.stream().map(s -> PingpongPayMethodEnum.explainName(s)).collect(Collectors.toList());
    }

    public void setPaymentMethodsOfApm(List<String> paymentMethodsOfApm) {
        this.paymentMethodsOfApm = paymentMethodsOfApm.stream().map(s -> PingpongPayMethodEnum.explainName(s)).collect(Collectors.toList());

    }
}
