/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.vietqr.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import so.dian.commons.eden.util.LogColorUtils;
import javax.annotation.PostConstruct;


/**
 * TODO
 *
 * <AUTHOR>
 * @version: VietqrAutoConfiguration.java, v 1.0 2024-06-18 上午11:01 Exp $
 */
@Slf4j
@Configuration
@ComponentScan("so.dian.platform.vietqr.*")
@ConditionalOnProperty(prefix = "channel.enable", name = "vietqr", havingValue = "true")
public class VietqrAutoConfiguration {
    static {
        log.info(LogColorUtils.blueMessage("Vietqr enable"));
    }

    @PostConstruct
    public void init(){

    }

}