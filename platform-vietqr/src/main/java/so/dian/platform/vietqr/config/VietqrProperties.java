/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.vietqr.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: VietqrProperties.java, v 1.0 2024-06-18 上午10:52 Exp $
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "channel.vietqr")
public class VietqrProperties implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202406177153901L;

    /**
     *
     */
    private String url;

    private String secretKey;
    /**
     *
     */
    private Authorization chargeboltAuth;
    private Authorization vietqrAuth;
}
