/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.vietqr.processor;

import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundResultDTO;
import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.RefundProcessor;
import so.dian.hera.interceptor.dto.req.ProcessorRefundRequest;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.vietqr.request.RefundRequest;
import so.dian.platform.vietqr.response.BaseVietqrResponse;
import so.dian.platform.vietqr.service.VietqrApiService;

import java.util.Date;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: VietqrRefundProcessor.java, v 1.0 2024-06-27 上午10:06 Exp $
 */
@Slf4j
@Component
public class VietqrRefundProcessor implements RefundProcessor {
    private static final String SUCCESS = "SUCCESS";
    private static final String FAILED = "FAILED";
    private final VietqrApiService vietqrApiService;
    public VietqrRefundProcessor(ObjectProvider<VietqrApiService> vietqrApiServiceProvider) {
        this.vietqrApiService = vietqrApiServiceProvider.getIfUnique();
    }
    @Override
    public RefundResultDTO refund(final ProcessorRefundRequest request) {
        log.info("vietqr 发起退款：{}", JsonUtil.beanToJson(request));
        RefundResultDTO resultDTO = new RefundResultDTO();
        resultDTO.setChannel(ChannelEnum.VIETQR.getChannelNo());
        resultDTO.setRspTime(new Date());

        RefundRequest refundRequest= new RefundRequest();
        refundRequest.setReferenceNumber(request.getReferenceNumber());
        refundRequest.setAmount(request.getRefundAmount().toString());
        refundRequest.setBankAccount(request.getBankAccount());
        refundRequest.setContent("Deposit Refund");
        BaseVietqrResponse response= vietqrApiService.refund(refundRequest);

        if(StringUtils.equalsIgnoreCase(response.getStatus(),SUCCESS)){
            resultDTO.setRefundPayNo(response.getMessage());
            resultDTO.setSuccess(Boolean.TRUE);
            resultDTO.setCode(0);
            resultDTO.setStatus(TransStatusEnum.SUCCESS.getKey());
            return resultDTO;
        }else if(StringUtils.equalsIgnoreCase(response.getStatus(),FAILED)){
            log.error("退款失败：{}", response.getMessage());
            resultDTO.setSuccess(Boolean.FALSE);
            resultDTO.setCode(-1);
            resultDTO.setStatus(TransStatusEnum.FAIL.getKey());
            return resultDTO;
        }
        return resultDTO;
    }

    @Override
    public ChannelEnum getRefundChannel() {
        return ChannelEnum.VIETQR;
    }
}