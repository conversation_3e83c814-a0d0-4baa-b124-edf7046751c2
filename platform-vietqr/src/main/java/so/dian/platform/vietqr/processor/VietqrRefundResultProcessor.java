/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.vietqr.processor;

import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.QueryRefundProcessor;
import so.dian.hera.interceptor.dto.req.RefundQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.RefundQueryResultDTO;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.vietqr.request.QueryTransactionResultRequest;
import so.dian.platform.vietqr.response.TransactionResultResponse;
import so.dian.platform.vietqr.service.VietqrApiService;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: VietqrRefundResultProcessor.java, v 1.0 2024-06-27 上午10:28 Exp $
 */
@Slf4j
@Component
public class VietqrRefundResultProcessor implements QueryRefundProcessor {
    private final VietqrApiService vietqrApiService;
    public VietqrRefundResultProcessor(ObjectProvider<VietqrApiService> vietqrApiServiceProvider) {
        this.vietqrApiService = vietqrApiServiceProvider.getIfUnique();
    }
    @Override
    public RefundQueryResultDTO orderRefundQuery(RefundQueryResultRequest queryResultRequest) {
        if(StringUtils.isBlank(queryResultRequest.getBankAccount())
                ||StringUtils.isBlank(queryResultRequest.getReferenceNumber())){
            throw HeraBizException.create(HeraBizErrorCodeEnum.REQUIRE_NOT_NULL);
        }
        QueryTransactionResultRequest queryTransactionResultRequest= new QueryTransactionResultRequest();
        queryTransactionResultRequest.setBankAccount(queryResultRequest.getBankAccount());
        queryTransactionResultRequest.setValue(queryResultRequest.getReferenceNumber());
        List<TransactionResultResponse> result= vietqrApiService.getRefundResult(queryTransactionResultRequest);
        RefundQueryResultDTO resultDTO= new RefundQueryResultDTO();
        if(CollectionUtils.isNotEmpty(result)&& result.get(0).getRefundCount()>0&& result.get(0).getAmountRefunded()>0){
            resultDTO.setStatus(PayStatus.REFUNDED.getCode());
            resultDTO.setRefundTime(new Date());
        }else{
            resultDTO.setStatus(PayStatus.REFUNDING.getCode());
        }
        return resultDTO;
    }

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.VIETQR_STATIC);
    }
}