/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.vietqr.request;

import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: GenerateCodeRequest.java, v 1.0 2024-07-19 上午9:42 Exp $
 */
@Data
public class GenerateCodeRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202407201094228L;

    private String bankCode;

    private String bankAccount;
    /**
     * Payment content. Maximum 19 characters, no special characters, Vietnamese without diacritics
     */
    private String content;

    private Integer qrType;
    private String terminalCode;
    /**
     *
     */
    private String userBankName;

    private String serviceCode;

    /**
     *
     */
    private String amount;

    /**
     * Transaction type: debit/credit (values: D/C). Default is “C”.
     */
    private String transType;

    private String orderId;
}