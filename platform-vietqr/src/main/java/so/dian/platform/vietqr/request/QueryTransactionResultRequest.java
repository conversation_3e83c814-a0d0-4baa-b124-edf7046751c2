/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.vietqr.request;

import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: QueryTransactionResultRequest.java, v 1.0 2024-07-08 下午6:21 Exp $
 */
@Data
public class QueryTransactionResultRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1712024088753865900L;

    private String bankAccount;
    /**
     * “C”: Credit 支付
     * “D”: Debit 退款
     */
    private Integer type;
    private String value;
    private String checkSum;
}