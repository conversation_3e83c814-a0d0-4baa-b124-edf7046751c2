/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.vietqr.request;

import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: RefundRequest.java, v 1.0 2024-07-08 下午6:21 Exp $
 */
@Data
public class RefundRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202407190182110L;

    private String bankAccount;
    private String referenceNumber;
    private String amount;
    private String content;
    private Boolean multiTimes;
    private String checkSum;
}