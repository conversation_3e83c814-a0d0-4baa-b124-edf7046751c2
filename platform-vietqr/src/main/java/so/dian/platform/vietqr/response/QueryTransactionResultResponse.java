/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.vietqr.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: QueryTransactionResultResponse.java, v 1.0 2024-07-11 上午10:17 Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryTransactionResultResponse extends BaseVietqrResponse implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202407193101728L;

    private TransactionResultResponse data;
}

