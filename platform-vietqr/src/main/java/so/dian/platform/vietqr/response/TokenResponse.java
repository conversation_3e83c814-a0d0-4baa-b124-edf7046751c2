/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.vietqr.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: TokenResponse.java, v 1.0 2024-07-08 下午6:18 Exp $
 */
@Data
public class TokenResponse implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202407190181831L;
    @JsonProperty("access_token")
    private String token;
    @JsonProperty("token_type")
    private String type;
    @JsonProperty("expires_in")
    private Integer expiresIn;
}