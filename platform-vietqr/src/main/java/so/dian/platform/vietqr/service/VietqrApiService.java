/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.vietqr.service;

import so.dian.platform.vietqr.request.GenerateCodeRequest;
import so.dian.platform.vietqr.request.QueryTransactionResultRequest;
import so.dian.platform.vietqr.request.RefundRequest;
import so.dian.platform.vietqr.response.BaseVietqrResponse;
import so.dian.platform.vietqr.response.GenerateCodeResponse;
import so.dian.platform.vietqr.response.QueryTransactionResultResponse;
import so.dian.platform.vietqr.response.TransactionResultResponse;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: VietqrService.java, v 1.0 2024-06-18 上午11:41 Exp $
 */
public interface VietqrApiService {
    /**
     * 支付结果查询
     *
     * @param request
     * @return
     */
    List<TransactionResultResponse> getPayResult(QueryTransactionResultRequest request);

    /**
     * 退款
     *
     * @param request
     * @return
     */
    BaseVietqrResponse refund(RefundRequest request);

    /**
     * 退款结果查询
     *
     * @param request
     * @return
     */
    List<TransactionResultResponse> getRefundResult(QueryTransactionResultRequest request);

    GenerateCodeResponse generateCode(GenerateCodeRequest request);
}