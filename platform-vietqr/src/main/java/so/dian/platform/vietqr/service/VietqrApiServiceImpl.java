/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.vietqr.service;

import cn.hutool.core.codec.Base64;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import okhttp3.RequestBody;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.mofa3.client.okhttp3.OkHttpHeader;
import so.dian.mofa3.client.okhttp3.OkHttpRequest;
import so.dian.mofa3.lang.common.constant.HttpConstants;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.mofa3.lang.util.Md5Util;
import so.dian.platform.vietqr.config.VietqrProperties;
import so.dian.platform.vietqr.request.GenerateCodeRequest;
import so.dian.platform.vietqr.request.QueryTransactionResultRequest;
import so.dian.platform.vietqr.request.RefundRequest;
import so.dian.platform.vietqr.response.BaseVietqrResponse;
import so.dian.platform.vietqr.response.GenerateCodeResponse;
import so.dian.platform.vietqr.response.QueryTransactionResultResponse;
import so.dian.platform.vietqr.response.TokenResponse;
import so.dian.platform.vietqr.response.TransactionResultResponse;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: VietqrService.java, v 1.0 2024-06-18 上午11:41 Exp $
 */
@Slf4j
@Service
public class VietqrApiServiceImpl implements VietqrApiService {
    /**
     * 超时时间
     */
    private static final Integer ALL_TIME_OUT= 45000;
    /**
     * 获取token
     */
    private static final String TOKEN_GENERATE_URL="/vqr/api/token_generate";
    /**
     * 退款
     */
    private static final String REFUND_RUL="/vqr/api/transaction/refund";
    /**
     * 交易结果查询
     */
    private static final String TRANSACTION_RESULT_CHECK_URL="/vqr/api/transactions/check-order";
    private static final String GENERATE_CUSTOMER_URL="/vqr/api/qr/generate-customer";

    OkHttpHeader headers = OkHttpHeader.newBuilder().contentType(HttpConstants.APP_FORM_JSON);
    private final VietqrProperties vietqrProperties;
    public VietqrApiServiceImpl(ObjectProvider<VietqrProperties> vietqrPropertiesProvider) {
        this.vietqrProperties= vietqrPropertiesProvider.getIfUnique();
    }
    @Override
    public List<TransactionResultResponse> getPayResult(QueryTransactionResultRequest request) {
        return queryTransactionResult(request);
    }

    @Override
    public BaseVietqrResponse refund(RefundRequest request) {
        String checkSum= buildMD5Input(Arrays.asList(vietqrProperties.getSecretKey(),
                request.getReferenceNumber(), request.getAmount(), request.getBankAccount()));
        request.setCheckSum(checkSum);
        // 设置为不可多次退款
        request.setMultiTimes(Boolean.FALSE);
        TokenResponse tokenResponse= getToken();
        headers.authorization(tokenResponse.getType()+" "+ tokenResponse.getToken());
        String result= OkHttpRequest.post(vietqrProperties.getUrl() + REFUND_RUL)
                .header(headers)
                .connectTimeout(ALL_TIME_OUT)
                .readTimeout(ALL_TIME_OUT)
                .writeTimeout(ALL_TIME_OUT)
                .json(JsonUtil.beanToJson(request))
                .exec()
                .toStr();
        log.info("VietQR退款请求参数：{}, 返回结果：{}", JsonUtil.beanToJson(request), result);
        return JsonUtil.jsonToBean(result, BaseVietqrResponse.class);
    }

    @Override
    public List<TransactionResultResponse> getRefundResult(QueryTransactionResultRequest request) {
        return queryTransactionResult(request);
    }

    @Override
    public GenerateCodeResponse generateCode(GenerateCodeRequest request) {
        TokenResponse tokenResponse= getToken();
        headers.authorization(tokenResponse.getType()+" "+ tokenResponse.getToken());
        String result= OkHttpRequest.post(vietqrProperties.getUrl() + GENERATE_CUSTOMER_URL)
                .header(headers)
                .connectTimeout(ALL_TIME_OUT)
                .readTimeout(ALL_TIME_OUT)
                .writeTimeout(ALL_TIME_OUT)
                .json(JsonUtil.beanToJson(request))
                .log()
                .exec()
                .toStr();
        log.info("VietQR code生成：{}, 返回结果：{}", JsonUtil.beanToJson(request), result);
        return JsonUtil.jsonToBean(result, GenerateCodeResponse.class);
    }


    private List<TransactionResultResponse> queryTransactionResult(QueryTransactionResultRequest request){
        request.setType(1);
        String checkSum= buildMD5Input(Arrays.asList(request.getBankAccount(), vietqrProperties.getVietqrAuth().getUserName()));
        request.setCheckSum(checkSum);
        TokenResponse tokenResponse= getToken();
        headers.authorization(tokenResponse.getType()+" "+ tokenResponse.getToken());
        String result= OkHttpRequest.post(vietqrProperties.getUrl() + TRANSACTION_RESULT_CHECK_URL)
                .header(headers)
                .connectTimeout(ALL_TIME_OUT)
                .readTimeout(ALL_TIME_OUT)
                .writeTimeout(ALL_TIME_OUT)
                .json(JsonUtil.beanToJson(request))
                .log()
                .exec()
                .toStr();
        log.info("VietQR交易结果查询参数：{}, 返回结果：{}", JsonUtil.beanToJson(request), result);
        // 正常返回数据是List JSON，异常是{"status":"FAILED","message":"E96"}，针对处理
        if (result.contains("FAILED")) {
            return new ArrayList<>();
        }
        return JsonUtil.jsonToBean(result, new TypeReference<List<TransactionResultResponse>>() {});
    }
    private TokenResponse getToken(){
        headers.authorization("Basic "+ Base64.encode(vietqrProperties.getVietqrAuth().getUserName()+":"+vietqrProperties.getVietqrAuth().getPassword()));
        String result = OkHttpRequest.post(vietqrProperties.getUrl() + TOKEN_GENERATE_URL)
                .header(headers)
                .connectTimeout(ALL_TIME_OUT)
                .readTimeout(ALL_TIME_OUT)
                .writeTimeout(ALL_TIME_OUT)
                .exec()
                .toStr();
        log.info("获取VietQR token：{}", result);
        return JsonUtil.jsonToBean(result, TokenResponse.class);
    }

    private static String buildMD5Input(List<String> params){
        if (CollectionUtils.isEmpty(params)) {
            throw new IllegalArgumentException("Input parameter list cannot be null or empty");
        }
        String mac= String.join("", params);
        return Md5Util.getEncryption(mac).toLowerCase();
    }
}