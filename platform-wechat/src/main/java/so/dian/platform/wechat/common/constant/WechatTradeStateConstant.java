/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.wechat.common.constant;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: WechatTradeStateConstant.java, v 1.0 2024-04-17 11:17 AM Exp $
 */
public class WechatTradeStateConstant {

    /**
     * 支付成功
     */
    public static final String SUCCESS = "SUCCESS";
    /**
     * 转入退款
     */
    public static final String REFUND = "REFUND";
    /**
     * 未支付
     */
    public static final String NOTPAY = "NOTPAY";
    /**
     * 已关闭
     */
    public static final String CLOSED = "CLOSED";
    /**
     * 处理中
     */
    public static final String PROCESSING = "PROCESSING";
    /**
     * 退款异常
     */
    public static final String ABNORMAL = "ABNORMAL";

}