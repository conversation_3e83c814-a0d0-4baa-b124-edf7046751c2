package so.dian.platform.wechat.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum WechatApiEnum {

    MINI_JSAPI("https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi","商户模式-即时支付-小程序JSAPI下单"),

    MINI_ORDER_QUERY_BY_TRANSACTION_ID("https://api.mch.weixin.qq.com/v3/pay/transactions/id/{transaction_id}","商户模式-即时支付-微信支付订单号查询"),

    MINI_ORDER_QUERY_BY_OUT_TRADE_NO("https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/{out_trade_no}","商户模式-即时支付-商户订单号查询"),

    MINI_ORDER_CLOSE("https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/{out_trade_no}/close","商户模式-即时支付-关闭订单"),

    MINI_ORDER_REFUND("https://api.mch.weixin.qq.com/v3/refund/domestic/refunds","商户模式|服务商模式-即时支付-申请退款"),

    MINI_ORDER_REFUND_QUERY("https://api.mch.weixin.qq.com/v3/refund/domestic/refunds/{out_refund_no}","商户模式|服务商模式-即时支付-查询单笔退款"),

    PAY_SCORE_CREATE_ORDER("https://api.mch.weixin.qq.com/v3/payscore/serviceorder","商户模式-信用支付-创建支付分订单"),

    PAY_SCORE_CANCEL_ORDER("https://api.mch.weixin.qq.com/v3/payscore/serviceorder/{out_order_no}/cancel","商户模式-信用支付-取消支付分订单"),

    PAY_SCORE_QUERY_ORDER("https://api.mch.weixin.qq.com/v3/payscore/serviceorder","商户模式-信用支付-查询支付分订单"),

    PAY_SCORE_MODIFY_ORDER("https://api.mch.weixin.qq.com/v3/payscore/serviceorder/{out_order_no}/modify","商户模式-信用支付-修改支付分订单"),

    PAY_SCORE_COMPLETE_ORDER("https://api.mch.weixin.qq.com/v3/payscore/serviceorder/{out_order_no}/complete","商户模式-信用支付-完结支付分订单"),

    FWS_MINI_JSAPI("https://api.mch.weixin.qq.com/v3/pay/partner/transactions/jsapi","服务商模式-小程序JSAPI下单"),

    FWS_MINI_ORDER_QUERY_BY_TRANSACTION_ID("https://api.mch.weixin.qq.com/v3/pay/partner/transactions/id/{transaction_id}","服务商模式-即时支付-微信支付订单号查询"),

    FWS_MINI_ORDER_QUERY_BY_OUT_TRADE_NO("https://api.mch.weixin.qq.com/v3/pay/partner/transactions/out-trade-no/{out_trade_no}","服务商模式-即时支付-商户订单号查询"),

    FWS_MINI_ORDER_CLOSE("https://api.mch.weixin.qq.com/v3/pay/partner/transactions/out-trade-no/{out_trade_no}/close","服务商模式-关闭订单"),

    FWS_PAY_SCORE_CREATE_ORDER("https://api.mch.weixin.qq.com/v3/payscore/partner/serviceorder","服务商模式-信用支付-创建支付分订单"),

    FWS_PAY_SCORE_QUERY_ORDER("https://api.mch.weixin.qq.com/v3/payscore/partner/serviceorder","服务商模式-信用支付-查询支付分订单"),

    FWS_PAY_SCORE_CANCEL_ORDER("https://api.mch.weixin.qq.com/v3/payscore/partner/serviceorder/{out_order_no}/cancel","服务商模式-信用支付-取消支付分订单"),

    FWS_PAY_SCORE_MODIFY_ORDER("https://api.mch.weixin.qq.com/v3/payscore/partner/serviceorder/{out_order_no}/modify","商户模式-信用支付-修改支付分订单"),

    FWS_PAY_SCORE_COMPLETE_ORDER("https://api.mch.weixin.qq.com/v3/payscore/partner/serviceorder/{out_order_no}/complete","服务商模式-信用支付-完结支付分订单"),


    ;

    private String api;

    private String desc;
}
