package so.dian.platform.wechat.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum WechatOverseaApiEnum {

    MINI_JSAPI("https://apihk.mch.weixin.qq.com/v3/global/transactions/jsapi","海外小程序JSAPI下单"),
    REFUND("https://apihk.mch.weixin.qq.com/v3/global/refunds","海外小程序退款"),
    REFUND_RESULT_QUERY("https://apihk.mch.weixin.qq.com/v3/global/refunds/out-refund-no/{out_refund_no}","海外小程序退款结果查询"),
    BALANCE_QUERY("https://apihk.mch.weixin.qq.com/v3/global/refund/recharge-balance","海外小程序查询充值余额"),
    ;

    private String api;

    private String desc;
}
