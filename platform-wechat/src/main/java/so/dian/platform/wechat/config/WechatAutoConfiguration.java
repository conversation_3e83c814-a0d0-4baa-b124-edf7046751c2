package so.dian.platform.wechat.config;

import com.chargebolt.hera.client.enums.ChannelEnum;
import com.wechat.pay.contrib.apache.httpclient.WechatPayHttpClientBuilder;
import com.wechat.pay.contrib.apache.httpclient.auth.PrivateKeySigner;
import com.wechat.pay.contrib.apache.httpclient.auth.Verifier;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Credentials;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Validator;
import com.wechat.pay.contrib.apache.httpclient.cert.CertificatesManager;
import com.wechat.pay.contrib.apache.httpclient.exception.HttpCodeException;
import com.wechat.pay.contrib.apache.httpclient.exception.NotFoundException;
import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;
import so.dian.commons.eden.util.LogColorUtils;
import so.dian.platform.common.enums.EnvironmentVariableEnum;
import so.dian.platform.wechat.factory.WechatV3BeanFactory;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.PrivateKey;

@Configuration
@ComponentScan("so.dian.platform.wechat.*")
@ConditionalOnProperty(prefix = "channel.enable", name = "wechat", havingValue = "true")
@Slf4j
public class WechatAutoConfiguration {

    static {
        log.info(LogColorUtils.blueMessage("Wechat enable"));
    }

    /**
     * 系统配置文件环境变量
     */
    @Value("${hera.profile}")
    private String env;

    @Resource
    private WechatProperty wechatProperty;

    @Resource
    private WechatV3BeanFactory wechatV3BeanFactory;

    /**
     * 此方式可兼容多个渠道商户配置，需要根据channelId从bean容器中匹配对应的渠道
     * 微信v3配置初始化
     */
    @PostConstruct
    public void init(){
        Integer channelId = ChannelEnum.WECHAT.getChannelNo();
        wechatV3BeanFactory.init(channelId, wechatProperty);
        log.info("微信v3#初始化配置信息#结束 channelId: {}",channelId);
    }

    /**
     * 此方式只能实现单个渠道商户配置，可直接在实现类属性注入方式引用
     * 这里添加Bean注解可以让系统启动的时候就加载，而且只加载一次，这样就不需要每次请求都重新调用了
     * 获取http请求对象,会自动的处理签名和验签，并进行证书自动更新
     * @return
     */
    //@Bean
    public CloseableHttpClient initWxPayClient() throws GeneralSecurityException, IOException, HttpCodeException, NotFoundException {

        // 获取私钥证书
        PrivateKey privateKey = getPrivateKey(wechatProperty.getPrivateKeyPath());

        // 获取证书管理器实例
        CertificatesManager certificatesManager = CertificatesManager.getInstance();

        // 向证书管理器增加需要自动更新平台证书的商户信息（初始化证书下载，以及开启定时任务更新证书：1440分钟会更新一次，即：24小时）
        certificatesManager.putMerchant(wechatProperty.getMchId(), new WechatPay2Credentials(wechatProperty.getMchId(),new PrivateKeySigner(wechatProperty.getCertSerialNo(), privateKey)), wechatProperty.getApiV3Key().getBytes(StandardCharsets.UTF_8));

        // 从证书管理器中获取verifier
        Verifier verifier = certificatesManager.getVerifier(wechatProperty.getMchId());

        WechatPayHttpClientBuilder builder = WechatPayHttpClientBuilder.create()
                .withMerchant(wechatProperty.getMchId(), wechatProperty.getCertSerialNo(), privateKey)
                .withValidator(new WechatPay2Validator(verifier));

        // 通过WechatPayHttpClientBuilder构造的HttpClient，会自动的处理签名和验签，并进行证书自动更新 实现类：SignatureExec
        CloseableHttpClient httpClient = builder.build();

        return httpClient;

    }

    /**
     * 这里添加Bean注解可以让系统启动的时候就加载，而且只加载一次，这样就不需要每次请求都重新调用了
     * @return
     * @throws NotFoundException
     * @throws GeneralSecurityException
     * @throws IOException
     * @throws HttpCodeException
     */
    //@Bean
    public Verifier  initVerifier() throws NotFoundException, GeneralSecurityException, IOException, HttpCodeException {

        // 获取私钥证书
        PrivateKey privateKey = getPrivateKey(wechatProperty.getPrivateKeyPath());

        // 获取证书管理器实例
        CertificatesManager certificatesManager = CertificatesManager.getInstance();

        // 向证书管理器增加需要自动更新平台证书的商户信息（初始化证书下载，以及开启定时任务更新证书：1440分钟会更新一次，即：24小时）
        certificatesManager.putMerchant(wechatProperty.getMchId(), new WechatPay2Credentials(wechatProperty.getMchId(),new PrivateKeySigner(wechatProperty.getCertSerialNo(), privateKey)), wechatProperty.getApiV3Key().getBytes(StandardCharsets.UTF_8));

        // 从证书管理器中获取verifier
        Verifier verifier = certificatesManager.getVerifier(wechatProperty.getMchId());

        return verifier;
    }

    /**
     * 获取商户的私钥文件
     * 预发或者生产环境，直接读取服务器目录文件
     * 开发或者测试环境，直接读取项目resources目录下的文件 cert/wechat/mchId/apiclient_key.pem
     * @param filePath
     * @return
     * @throws IOException
     */
    private PrivateKey getPrivateKey(String filePath) {

        try {
            // 预发或者生产环境，直接读取服务器目录文件
            if (EnvironmentVariableEnum.REAL.getCode().equalsIgnoreCase(env)
                    || EnvironmentVariableEnum.PRE.getCode().equalsIgnoreCase(env)) {
                log.info("{}环境，读取服务器目录下的文件 filePath: {}",env, filePath);
                return PemUtil.loadPrivateKey(new FileInputStream(filePath));

                // 开发或者测试环境，直接读取项目resources目录下的文件 cert/wechat/mchId/apiclient_key.pem
            } else {
                filePath = "cert/wechat/" + wechatProperty.getMchId() + "/apiclient_key.pem";
                log.info( "{}环境，读取项目resources目录下的文件 filePath: {}",env,filePath);
                ClassPathResource resource = new ClassPathResource(filePath);
                String content = StreamUtils.copyToString(resource.getInputStream(), Charset.forName("utf-8"));
                return PemUtil.loadPrivateKey(content);
            }
        }catch (Exception exception){
            throw new RuntimeException("加载微信密钥失败",exception);
        }

    }


}
