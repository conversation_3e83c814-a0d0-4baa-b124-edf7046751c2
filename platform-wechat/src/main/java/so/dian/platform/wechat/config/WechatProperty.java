package so.dian.platform.wechat.config;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@NoArgsConstructor
@Data
@Component
@ConfigurationProperties(prefix = "channel.wechat")
public class WechatProperty {

    private String url;
    private String mchId;
    private Integer payTypeRoute;
    private String mchKey;
    private String apiV3Key;
    private String certSerialNo;
    private String serviceId;
    private String keyPath;
    private String privateKeyPath;
    private String privateCertPath;
    private String appId;

    private String notifyUrl;

    private String payScoreNotifyUrl;
    /**
     * 小程序跳转AppId-信用分支付使用
     */
    private String platAppId;

    /**
     * 小程序跳转路径-信用分支付使用
     */
    private String platPath;

    private List<String> supportedCurrencies;
}
