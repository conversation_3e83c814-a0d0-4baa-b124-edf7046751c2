package so.dian.platform.wechat.dto.notify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 *
 * 通知报文体，必须按照微信官方文档的顺序排列，否则会导致验签失败
 */
@Data
public class NotifyBody {

    /**
     * 通知ID
     */
    @JsonProperty("id")
    private String id;

    /**
     * 通知创建时间
     */
    @JsonProperty("create_time")
    private String createTime;

    /**
     * 通知数据类型
     */
    @JsonProperty("resource_type")
    private String resourceType;

    /**
     * 通知类型
     */
    @JsonProperty("event_type")
    private String eventType;

    /**
     * 回调摘要
     */
    @JsonProperty("summary")
    private String summary;

    @JsonProperty("resource")
    private Resource resource;


    @Data
    public class Resource {

        /**
         * 原始类型
         */
        @JsonProperty("original_type")
        private String originalType;

        /**
         * 加密算法类型
         */
        @JsonProperty("algorithm")
        private String algorithm;

        /**
         * 数据密文
         */
        @JsonProperty("ciphertext")
        private String ciphertext;

        /**
         * 附加数据
         */
        @JsonProperty("associated_data")
        private String associatedData;

        /**
         * 随机串
         */
        @JsonProperty("nonce")
        private String nonce;

    }
}
