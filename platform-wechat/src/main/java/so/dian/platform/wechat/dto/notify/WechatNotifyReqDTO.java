package so.dian.platform.wechat.dto.notify;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
public class WechatNotifyReqDTO implements Serializable {
    private static final long serialVersionUID = -4330573610064922635L;

    /**
     * 微信支付签名
     */
    private String wechatPaySignature;
    /**
     * 微信支付平台证书序列号
     */
    private String wechatPaySerial;
    /**
     * 微信支付时间戳
     */
    private String wechatPayTimestamp;
    /**
     * 微信支付随机数
     */
    private String wechatPayNonce;

    /**
     * 微信支付通知原始报文体（真实数据报文加密）
     */
    private NotifyBody notifyBody;

    /**
     * 支付产品类型
     * @see CallbackOriginalTypeEnum
     */
    private String payProdType;

    /**
     * 渠道配置渠道编号
     */
    private Integer channelId;

}
