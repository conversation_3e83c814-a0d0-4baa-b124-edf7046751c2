package so.dian.platform.wechat.dto.notify;

import lombok.Data;
import so.dian.platform.wechat.common.enums.WechatRespEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@SuppressWarnings("all")
public class WechatNotifyRspDTO implements Serializable {

    private static final long serialVersionUID = -6440040139277561888L;

    /**
     * 应答码
     */
    private String code;

    /**
     * 应答描述
     */
    private String message;

    public WechatNotifyRspDTO(){
        this.code  = WechatRespEnum.SUCCESS.getCode();
        this.message = WechatRespEnum.SUCCESS.getDesc();
    }

    public WechatNotifyRspDTO(String code, String message){
        this.code  = code;
        this.message = message;
    }


}
