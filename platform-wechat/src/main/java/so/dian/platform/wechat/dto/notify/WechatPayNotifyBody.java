package so.dian.platform.wechat.dto.notify;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class WechatPayNotifyBody implements Serializable {

    private static final long serialVersionUID = 2003388262574898603L;

    /**
     * 应用ID
     */
    @SerializedName("appid")
    private String appid;

    /**
     * 商户号
     */
    @SerializedName("mchid")
    private String mchId;

    /**
     * 服务商模式-服务商应用ID
     */
    @SerializedName("sp_appid")
    private String spAppid;

    /**
     * 服务商模式-服务商户号
     */
    @SerializedName("sp_mchid")
    private String spMchid;

    /**
     * 服务商模式-子商户应用ID
     */
    @SerializedName("sub_appid")
    private String subAppid;

    /**
     * 服务商模式-子商户号
     */
    @SerializedName("sub_mchid")
    private String subMchid;

    /**
     * 商户订单号
     */
    @SerializedName("out_trade_no")
    private String outTradeNo;

    /**
     * 微信支付订单号
     */
    @SerializedName("transaction_id")
    private String transactionId;
    @SerializedName("id")
    private String id;

    /**
     * 交易类型
     *
     * 枚举值：
     * JSAPI：公众号支付
     * NATIVE：扫码支付
     * APP：APP支付
     * MICROPAY：付款码支付
     * MWEB：H5支付
     * FACEPAY：刷脸支付
     */
    @SerializedName("trade_type")
    private String tradeType;

    /**
     * 交易状态
     *
     * 枚举值：
     * SUCCESS：支付成功
     * REFUND：转入退款
     * NOTPAY：未支付
     * CLOSED：已关闭
     * REVOKED：已撤销（付款码支付）
     * USERPAYING：用户支付中（付款码支付）
     * PAYERROR：支付失败(其他原因，如银行返回失败)
     *
     */
    @SerializedName("trade_state")
    private String tradeState;

    /**
     * 交易状态描述
     */
    @SerializedName("trade_state_desc")
    private String tradeStateDesc;

    /**
     * 付款银行
     */
    @SerializedName("bank_type")
    private String bankType;

    /**
     * 附加数据
     */
    @SerializedName("attach")
    private String attach;

    /**
     * 支付完成时间
     */
    @SerializedName("success_time")
    private String successTime;

    /**
     * 支付者
     */
    @SerializedName("payer")
    private Payer payer;

    /**
     * 订单金额
     */
    @SerializedName("amount")
    private Amount amount;

    /**
     * 优惠功能
     */
    @SerializedName("promotion_detail")
    private List<PromotionDetail> promotionDetail;


    @Data
    @NoArgsConstructor
    public static class Payer implements Serializable {

        private static final long serialVersionUID = 6009922632587064583L;

        /**
         * 用户标识
         */
        @SerializedName("openid")
        private String openid;

    }

    @Data
    @NoArgsConstructor
    public static class Amount implements Serializable {


        private static final long serialVersionUID = 4255089745586274486L;
        /**
         * 订单总金额，单位为分。
         *
         * 必填：否
         */
        @SerializedName("total")
        private Integer total;

        /**
         * 货币类型
         * CNY：人民币，境内商户号仅支持人民币。
         * 必填：否
         */
        @SerializedName("currency")
        private String currency;

        /**
         * 用户支付金额，单位为分。
         *
         * 指使用优惠券的情况下，这里等于总金额-优惠券金额
         *
         * 必填：否
         */
        @SerializedName("payer_total")
        private Integer payerTotal;

        /**
         * 用户支付币种
         *
         * 必填：否
         */
        @SerializedName("payer_currency")
        private String payerCurrency;


    }

    @Data
    @NoArgsConstructor
    public static class PromotionDetail implements Serializable {
        private static final long serialVersionUID = -6180751996028678810L;

        /**
         * 券ID
         */
        @SerializedName("coupon_id")
        private String couponId;

        /**
         * 优惠名称
         */
        @SerializedName("name")
        private String name;

        /**
         * 优惠范围
         * GLOBAL：全场代金券
         * SINGLE：单品优惠
         */
        @SerializedName("scope")
        private String scope;

        /**
         * 优惠类型
         * CASH：充值型代金券
         * NOCASH：免充值型代金券
         */
        @SerializedName("type")
        private String type;

        /**
         * 优惠券面额
         */
        @SerializedName("amount")
        private Integer amount;

        /**
         * 活动ID
         */
        @SerializedName("stock_id")
        private String stockId;


        /**
         * 微信出资,单位为分
         */
        @SerializedName("wechatpay_contribute")
        private String wechatpayContribute;

        /**
         * 商户出资,单位为分
         */
        @SerializedName("merchant_contribute")
        private String merchantContribute;

        /**
         * 其他出资,单位为分
         */
        @SerializedName("other_contribute")
        private String otherContribute;

        /**
         * 优惠币种
         */
        @SerializedName("currency")
        private String currency;

    }

}
