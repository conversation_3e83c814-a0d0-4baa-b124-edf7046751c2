package so.dian.platform.wechat.dto.notify;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class WechatRefundNotifyBody implements Serializable {
    private static final long serialVersionUID = -7439946577614368959L;

    /**
     * 商户号
     */
    @SerializedName("mchid")
    private String mchId;

    /**
     * 商户订单号
     */
    @SerializedName("out_trade_no")
    private String outTradeNo;

    /**
     * 微信支付订单号
     */
    @SerializedName("transaction_id")
    private String transactionId;

    /**
     * 微信支付订单号
     */
    @SerializedName("out_refund_no")
    private String outRefundNo;

    /**
     * 微信支付退款单号
     */
    @SerializedName("refund_id")
    private String refundId;

    /**
     * 退款状态
     *
     * 枚举值：
     * SUCCESS：退款成功
     * CLOSED：退款关闭
     * ABNORMAL：退款异常，退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，可前往【商户平台—>交易中心】，手动处理此笔退款
     */
    @SerializedName("refund_status")
    private String refundStatus;

    /**
     * 退款成功时间
     *
     * 1、退款成功时间，遵循rfc3339标准格式，格式为yyyy-MM-DDTHH:mm:ss+TIMEZONE，yyyy-MM-DD表示年月日，T出现在字符串中，表示time元素的开头，HH:mm:ss表示时分秒，TIMEZONE表示时区（+08:00表示东八区时间，领先UTC 8小时，即北京时间）。例如：2015-05-20T13:29:35+08:00表示，北京时间2015年5月20日13点29分35秒。
     * 2、当退款状态为退款成功时返回此参数。
     * 示例值：2018-06-08T10:34:56+08:00
     */
    @SerializedName("success_time")
    private String successTime;

    /**
     * 退款入账账户
     *
     * 取当前退款单的退款入账方。
     * 1、退回银行卡：{银行名称}{卡类型}{卡尾号}
     * 2、退回支付用户零钱: 支付用户零钱
     * 3、退还商户: 商户基本账户、商户结算银行账户
     * 4、退回支付用户零钱通：支付用户零钱通
     */
    @SerializedName("user_received_account")
    private String userReceivedAccount;

    /**
     * 金额信息
     */
    @SerializedName("amount")
    private Amount amount;


    @Data
    @NoArgsConstructor
    public static class Amount implements Serializable {
        private static final long serialVersionUID = -4149650936613566089L;

        /**
         * 原订单金额,单位分
         *
         * 必填：是
         */
        @SerializedName("total")
        private Integer total;


        /**
         * 退款金额,单位分
         *
         * 必填：是
         */
        @SerializedName("refund")
        private Integer refund;

        /**
         * 退款出资账户及金额
         */
        private List<From> from;


        /**
         * 用户支付金额
         *
         */
        @SerializedName("payer_total")
        private Integer payerTotal;

        /**
         * 用户退款金额
         *
         */
        @SerializedName("payer_refund")
        private Integer payerRefund;


        /**
         * 应结退款金额
         *
         */
        @SerializedName("settlement_refund")
        private Integer settlementRefund;

        /**
         * 应结订单金额
         *
         */
        @SerializedName("settlement_total")
        private Integer settlementTotal;

        /**
         * 优惠退款金额
         *
         */
        @SerializedName("discount_refund")
        private Integer discountRefund;


        /**
         * 退款币种
         *
         */
        @SerializedName("currency")
        private String currency;

        /**
         * 手续费退款金额
         */
        @SerializedName("refund_fee")
        private String refundFee;

        @Data
        @NoArgsConstructor
        public static class From implements Serializable {
            private static final long serialVersionUID = -987688807751016084L;

            /**
             * 出资账户类型
             * 枚举值：
             * AVAILABLE : 可用余额
             * UNAVAILABLE : 不可用余额
             *
             * 必填：是
             */
            @SerializedName("account")
            private String account;

            /**
             * 出资金额
             *
             * 必填：是
             */
            @SerializedName("amount")
            private Integer amount;
        }

    }

}
