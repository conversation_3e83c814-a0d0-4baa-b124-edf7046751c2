package so.dian.platform.wechat.dto.notify;

import lombok.Data;

@Data
public class WxAppCreditConfirmNotifyDTO {

    private String id;
    private String create_time;
    private String event_type;
    private String resource_type;
    private WxAppCreditConfirmNotifyResourceDto resource;

    @Data
    public static class WxAppCreditConfirmNotifyResourceDto {
        private String algorithm;
        private String ciphertext;
        private String associated_data;
        private String nonce;
    }

    private Integer creditProdType;

    private Integer parentClientType;
    private Integer payTypeRoute;
}
