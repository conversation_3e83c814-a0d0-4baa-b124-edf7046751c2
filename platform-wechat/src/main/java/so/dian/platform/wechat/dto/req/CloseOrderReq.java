package so.dian.platform.wechat.dto.req;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;


/**
 * 订单关闭
 * <AUTHOR>
 */
@Data
public class CloseOrderReq implements Serializable {
    private static final long serialVersionUID = -3988264952354741750L;

    /**
     * 商户ID
     */
    @SerializedName("mchid")
    private String mchid;

    /**
     * 服务商模式-服务商户号
     */
    @SerializedName("sp_mchid")
    private String spMchid;

    /**
     * 服务商模式-子商户号
     */
    @SerializedName("sub_mchid")
    private String subMchid;

}
