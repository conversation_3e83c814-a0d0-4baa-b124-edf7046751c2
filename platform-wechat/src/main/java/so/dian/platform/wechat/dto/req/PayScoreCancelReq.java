package so.dian.platform.wechat.dto.req;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class PayScoreCancelReq implements Serializable {
    private static final long serialVersionUID = 7332341582587254956L;


    /**
     * 应用ID
     */
    @SerializedName("appid")
    private String appid;

    /**
     * 服务ID
     */
    @SerializedName("service_id")
    private String serviceId;

    /**
     * 撤销原因
     */
    @SerializedName("reason")
    private String reason;

    /**
     * 服务商模式-子商户号
     */
    @SerializedName("sub_mchid")
    private String subMchid;

}
