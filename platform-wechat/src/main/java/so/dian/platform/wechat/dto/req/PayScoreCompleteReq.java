package so.dian.platform.wechat.dto.req;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PayScoreCompleteReq implements Serializable {
    private static final long serialVersionUID = 6142199793393724470L;

    /**
     * 商户服务订单号
     */
    @SerializedName("out_order_no")
    private String outOrderNo;

    /**
     * 应用ID
     */
    @SerializedName("appid")
    private String appid;

    /**
     * 服务ID
     */
    @SerializedName("service_id")
    private String serviceId;

    /**
     * 服务商模式-子商户号
     */
    @SerializedName("sub_mchid")
    private String subMchid;

    /**
     * 后付费项目
     */
    @SerializedName("post_payments")
    private List<PostPayment> postPayments;

    /**
     * 后付费商户优惠
     */
    @SerializedName("post_discounts")
    private List<PostDiscount> postDiscounts;

    /**
     * 总金额
     */
    @SerializedName("total_amount")
    private Integer totalAmount;

    /**
     * 服务时间段
     */
    @SerializedName("time_range")
    private TimeRange timeRange;

    /**
     * 服务位置
     */
    @SerializedName("location")
    private Location location;

    /**
     * 微信支付服务分账标记
     *
     * false：不分账，默认：false
     * true：分账。
     */
    @SerializedName("profit_sharing")
    private Boolean profitSharing;

    /**
     * 完结服务时间
     * 格式为yyyy-MM-DDTHH:mm:ss.sss+TIMEZONE，yyyy-MM-DD表示年月日，T出现在字符串中，表示time元素的开头，
     * HH:mm:ss.sss表示时分秒毫秒，TIMEZONE表示时区（+08:00表示东八区时间，领先UTC 8小时，即北京时间）。
     * 例如：2015-05-20T13:29:35.120+08:00表示北京时间2015年05月20日13点29分35秒
     */
    @SerializedName("complete_time")
    private String completeTime;

    /**
     * 订单优惠标记
     */
    @SerializedName("goods_tag")
    private String goodsTag;




    @Data
    @NoArgsConstructor
    public static class PostPayment implements Serializable {
        private static final long serialVersionUID = 2007722927556382895L;
        /**
         * name : 就餐费用服务费
         * amount : 4000
         * description : 就餐人均100元服务费：100/小时
         * count : 1
         */
        @SerializedName("name")
        private String name;

        /**
         * 金额	单位为分
         */
        @SerializedName("amount")
        private Long amount;

        /**
         * 计费说明
         */
        @SerializedName("description")
        private String description;

        /**
         * 付费数量
         */
        @SerializedName("count")
        private Integer count;
    }

    @Data
    @NoArgsConstructor
    public static class PostDiscount implements Serializable {
        private static final long serialVersionUID = 2764537888242763379L;
        /**
         * name : 满20减1元
         * description : 不与其他优惠叠加
         */
        @SerializedName("name")
        private String name;

        @SerializedName("description")
        private String description;

        @SerializedName("count")
        private int count;

        @SerializedName("amount")
        private int amount;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeRange implements Serializable {
        private static final long serialVersionUID = 8169562173656314930L;
        /**
         * start_time : 20091225091010
         * end_time : 20091225121010
         */
        @SerializedName("start_time")
        private String startTime;
        @SerializedName("end_time")
        private String endTime;
    }

    @Data
    @NoArgsConstructor
    public static class Location implements Serializable {
        private static final long serialVersionUID = -4510224826631515344L;
        /**
         * start_location : 嗨客时尚主题展餐厅
         * end_location : 嗨客时尚主题展餐厅
         */
        @SerializedName("start_location")
        private String startLocation;
        @SerializedName("end_location")
        private String endLocation;
    }
}
