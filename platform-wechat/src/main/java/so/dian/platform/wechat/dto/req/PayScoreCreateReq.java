package so.dian.platform.wechat.dto.req;

import com.github.binarywang.wxpay.bean.payscore.Detail;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 信用分订单创建请求参数
 * <AUTHOR>
 */
@Data
public class PayScoreCreateReq implements Serializable {

    /**
     * 服务商模式-子商户应用ID
     */
    @SerializedName("sub_appid")
    private String subAppid;

    /**
     * 服务商模式-子商户号
     */
    @SerializedName("sub_mchid")
    private String subMchid;

    /**
     * 商户服务订单号
     */
    @SerializedName("out_order_no")
    private String outOrderNo;

    /**
     * 应用ID
     */
    @SerializedName("appid")
    private String appid;

    /**
     * 服务ID
     */
    @SerializedName("service_id")
    private String serviceId;

    /**
     * 服务信息
     */
    @SerializedName("service_introduction")
    private String serviceIntroduction;

    /**
     * 后付费项目
     */
    @SerializedName("post_payments")
    private List<PostPayment> postPayments;

    /**
     * 后付费商户优惠
     */
    @SerializedName("post_discounts")
    private List<PostDiscount> postDiscounts;

    /**
     * 服务时间段
     */
    @SerializedName("time_range")
    private TimeRange timeRange;

    /**
     * 服务位置
     */
    @SerializedName("location")
    private Location location;

    /**
     * 订单风险金
     */
    @SerializedName("risk_fund")
    private RiskFund riskFund;

    /**
     * 商户数据包
     */
    @SerializedName("attach")
    private String attach;

    /**
     * 商户回调地址
     */
    @SerializedName("notify_url")
    private String notifyUrl;

    /**
     * 用户标识
     */
    @SerializedName("openid")
    private String openid;

    /**
     * 服务商模式-子商户公众号下的用户标识
     * 微信用户在子商户公众号sub_appid下的唯一标识； 免确认订单模式（见 need_user_confirm 字段描述）下
     * openid与sub_openid必须填写并且只能填写一个
     * 若填写了sub_openid，那么sub_appid必填
     */
    @SerializedName("sub_openid")
    private String subOpenid;

    /**
     * 是否需要用户确认
     */
    @SerializedName("need_user_confirm")
    private Boolean needUserConfirm;


    @SerializedName("profit_sharing")
    private Boolean profitSharing;

    @SerializedName("total_amount")
    private Integer totalAmount;
    @SerializedName("reason")
    private String reason;
    @SerializedName("goods_tag")
    private String goodsTag;
    @SerializedName("type")
    private String type;
    @SerializedName("detail")
    private Detail detail;
    @SerializedName("authorization_code")
    private  String authorizationCode;


    @Data
    @NoArgsConstructor
    public static class PostPayment implements Serializable {
        private static final long serialVersionUID = 2007722927556382895L;
        /**
         * name : 就餐费用服务费
         * amount : 4000
         * description : 就餐人均100元服务费：100/小时
         * count : 1
         */
        @SerializedName("name")
        private String name;

        /**
         * 金额	单位为分
         */
        @SerializedName("amount")
        private Integer amount;

        /**
         * 计费说明
         */
        @SerializedName("description")
        private String description;

        /**
         * 付费数量
         */
        @SerializedName("count")
        private Integer count;
    }

    @Data
    @NoArgsConstructor
    public static class PostDiscount implements Serializable {
        private static final long serialVersionUID = 2764537888242763379L;
        /**
         * name : 满20减1元
         * description : 不与其他优惠叠加
         */
        @SerializedName("name")
        private String name;

        @SerializedName("description")
        private String description;

        @SerializedName("count")
        private int count;

        @SerializedName("amount")
        private int amount;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeRange implements Serializable {
        private static final long serialVersionUID = 8169562173656314930L;
        /**
         * start_time : 20091225091010
         * end_time : 20091225121010
         */
        @SerializedName("start_time")
        private String startTime;
        @SerializedName("end_time")
        private String endTime;
    }

    @Data
    @NoArgsConstructor
    public static class Location implements Serializable {
        private static final long serialVersionUID = -4510224826631515344L;
        /**
         * start_location : 嗨客时尚主题展餐厅
         * end_location : 嗨客时尚主题展餐厅
         */
        @SerializedName("start_location")
        private String startLocation;
        @SerializedName("end_location")
        private String endLocation;
    }


    @Data
    @NoArgsConstructor
    public static class RiskFund implements Serializable {
        private static final long serialVersionUID = -3583406084396059152L;
        /**
         * name :  ESTIMATE_ORDER_COST
         * amount : 10000
         * description : 就餐的预估费用
         */
        @SerializedName("name")
        private String name;
        @SerializedName("amount")
        private int amount;
        @SerializedName("description")
        private String description;
    }
}
