package so.dian.platform.wechat.dto.req;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PayScoreModifyReq implements Serializable {
    private static final long serialVersionUID = 6142199793393724470L;

    /**
     * 商户服务订单号
     */
    @SerializedName("out_order_no")
    private String outOrderNo;

    /**
     * 应用ID
     */
    @SerializedName("appid")
    private String appid;

    /**
     * 服务ID
     */
    @SerializedName("service_id")
    private String serviceId;

    /**
     * 服务商模式-子商户号
     */
    @SerializedName("sub_mchid")
    private String subMchid;

    /**
     * 后付费项目
     */
    @SerializedName("post_payments")
    private List<PostPayment> postPayments;

    /**
     * 后付费商户优惠
     */
    @SerializedName("post_discounts")
    private List<PostDiscount> postDiscounts;

    /**
     * 总金额
     */
    @SerializedName("total_amount")
    private Integer totalAmount;

    @SerializedName("reason")
    private String reason;

    @Data
    @NoArgsConstructor
    public static class PostPayment implements Serializable {
        private static final long serialVersionUID = 2007722927556382895L;
        /**
         * name : 就餐费用服务费
         * amount : 4000
         * description : 就餐人均100元服务费：100/小时
         * count : 1
         */
        @SerializedName("name")
        private String name;

        /**
         * 金额	单位为分
         */
        @SerializedName("amount")
        private Integer amount;

        /**
         * 计费说明
         */
        @SerializedName("description")
        private String description;

        /**
         * 付费数量
         */
        @SerializedName("count")
        private Integer count;
    }

    @Data
    @NoArgsConstructor
    public static class PostDiscount implements Serializable {
        private static final long serialVersionUID = 2764537888242763379L;
        /**
         * name : 满20减1元
         * description : 不与其他优惠叠加
         */
        @SerializedName("name")
        private String name;

        @SerializedName("description")
        private String description;

        @SerializedName("count")
        private int count;

        @SerializedName("amount")
        private int amount;
    }

}
