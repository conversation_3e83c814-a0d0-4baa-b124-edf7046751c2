package so.dian.platform.wechat.dto.req;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 预下单支付请求对象
 * <AUTHOR>
 */
@Data
public class PrepayOrderReq implements Serializable {

    private static final long serialVersionUID = 5808467208469623433L;

    /**
     * 应用ID
     */
    @SerializedName("appid")
    private String appid;

    /**
     * 直连商户号
     */
    @SerializedName("mchid")
    private String mchid;

    /**
     * 服务商模式-服务商应用ID
     */
    @SerializedName("sp_appid")
    private String spAppid;

    /**
     * 服务商模式-服务商户号
     */
    @SerializedName("sp_mchid")
    private String spMchid;

    /**
     * 服务商模式-子商户应用ID
     */
    @SerializedName("sub_appid")
    private String subAppid;

    /**
     * 服务商模式-子商户号
     */
    @SerializedName("sub_mchid")
    private String subMchid;

    /**
     * 商品描述
     */
    @SerializedName("description")
    private String description;

    /**
     * 商户订单号
     */
    @SerializedName("out_trade_no")
    private String outTradeNo;


    @SerializedName("trade_type")
    private String tradeType;
    @SerializedName("merchant_category_code")
    private String merchantCategoryCode;

    /**
     * 交易结束时间
     *
     * 订单失效时间，遵循rfc3339标准格式，格式为yyyy-MM-DDTHH:mm:ss+TIMEZONE，
     * yyyy-MM-DD表示年月日，T出现在字符串中，表示time元素的开头，HH:mm:ss表示时分秒，
     * TIMEZONE表示时区（+08:00表示东八区时间，领先UTC8小时，即北京时间）。
     * 例如：2015-05-20T13:29:35+08:00表示，北京时间2015年5月20日 13点29分35秒。
     */
    @SerializedName("time_expire")
    private String timeExpire;

    /**
     * 附加数据
     */
    @SerializedName("attach")
    private String attach;

    /**
     * 通知地址
     */
    @SerializedName("notify_url")
    private String notifyUrl;

    /**
     * 订单优惠标记
     */
    @SerializedName("goods_tag")
    private String goodsTag;

    /**
     * 电子发票入口开放标识
     */
    @SerializedName("support_fapiao")
    private Boolean supportFapiao;

    /**
     * 订单金额
     */
    @SerializedName("amount")
    private Amount amount;

    /**
     * 支付者
     */
    @SerializedName("payer")
    private Payer payer;

    /**
     * 优惠功能
     */
    @SerializedName("detail")
    private Detail detail;

    /**
     * 场景信息
     */
    @SerializedName("scene_info")
    private SceneInfo sceneInfo;

    /**
     * 结算信息
     */
    @SerializedName("settle_info")
    private SettleInfo settleInfo;

    /**
     * 支付方式限制
     */
    @SerializedName("limit_pay")
    private List<LimitPayEnum> limitPay;

    @Data
    @NoArgsConstructor
    public static class Amount {

        /**
         * 总金额，单位为分。
         */
        @SerializedName("total")
        private Integer total;

        /**
         * 货币类型
         * CNY：人民币，境内商户号仅支持人民币。
         */
        @SerializedName("currency")
        private String currency;
    }

    @Data
    @NoArgsConstructor
    public static class Payer {

        /**
         * 用户标识
         * 用户在直连商户appid下的唯一标识。 下单前需获取到用户的Openid，Openid获取详见:https://pay.weixin.qq.com/wiki/doc/apiv3/terms_definition/chapter1_1_3.shtml#part-3
         */
        @SerializedName("openid")
        private String openid;

        /**
         * 服务商模式-用户服务标识
         *
         * 与 sub_openid 二选一
         */
        @SerializedName("sp_openid")
        private String spOpenid;

        /**
         * 服务商模式-用户子标识
         * 用户在子商户appid下的唯一标识。若传sub_openid，那sub_appid必填。下单前需获取到用户的Openid
         * 与 sp_openid 二选一
         */
        @SerializedName("sub_openid")
        private String subOpenid;

    }

    @Data
    @NoArgsConstructor
    public static class Detail {
        /**
         * 订单原价,单位：分
         */
        @SerializedName("cost_price")
        private Integer costPrice;

        /**
         * 商品小票ID
         */
        @SerializedName("invoice_id")
        private String invoiceId;

        /**
         * 单品列表
         */
        @SerializedName("goods_detail")
        private List<GoodsDetail> goodsDetail;


        @Data
        @NoArgsConstructor
        public static class GoodsDetail {
            /**
             * 商户侧商品编码
             */
            @SerializedName("merchant_goods_id")
            private String merchantGoodsId;

            /**
             * 微信支付商品编码
             */
            @SerializedName("wechatpay_goods_id")
            private String wechatpayGoodsId;

            /**
             * 商品名称
             */
            @SerializedName("goods_name")
            private String goodsName;

            /**
             * 商品数量
             */
            @SerializedName("quantity")
            private Integer quantity;

            /**
             * 商品单价,单位为：分
             */
            @SerializedName("unit_price")
            private Integer unitPrice;
        }

    }

    @Data
    @NoArgsConstructor
    public static class SceneInfo {
        /**
         * 用户终端IP
         */
        @SerializedName("payer_client_ip")
        private String payerClientIp;

        /**
         * 商户端设备号
         */
        @SerializedName("device_id")
        private String deviceId;

        /**
         * 商户门店信息
         */
        @SerializedName("store_info")
        private StoreInfo storeInfo;

        @Data
        @NoArgsConstructor
        public static class StoreInfo {

            /**
             * 门店编号
             */
            @SerializedName("id")
            private String id;

            /**
             * 门店名称
             */
            @SerializedName("name")
            private String name;

            /**
             * 地区编码
             */
            @SerializedName("area_code")
            private String areaCode;

            /**
             * 详细地址
             */
            @SerializedName("address")
            private String address;
        }
    }

    @Data
    @NoArgsConstructor
    public static class SettleInfo {

        /**
         * 是否指定分账 false-否 true-是
         */
        @SerializedName("profit_sharing")
        private Boolean profitSharing;
    }

    @Getter
    public enum LimitPayEnum {
        @SerializedName("no_balance")
        NO_BALANCE,
        @SerializedName("no_credit")
        NO_CREDIT,
        @SerializedName("no_debit")
        NO_DEBIT,
        @SerializedName("balance_only")
        BALANCE_ONLY;

        private LimitPayEnum() {}
    }

}
