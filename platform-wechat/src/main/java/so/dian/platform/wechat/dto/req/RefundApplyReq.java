package so.dian.platform.wechat.dto.req;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RefundApplyReq implements Serializable {



    @SerializedName("mchid")
    private String mchid;
    @SerializedName("appid")
    private String appid;



    /**
     * 服务商模式-子商户号
     */
    @SerializedName("sub_mchid")
    private String subMchid;

    /**
     * 商户订单号
     *
     * 必填：否 与 transactionId 二选一
     */
    @SerializedName("out_trade_no")
    private String outTradeNo;

    /**
     * 微信支付订单号
     *
     * 必填：否 与 outTradeNo 二选一
     */
    @SerializedName("transaction_id")
    private String transactionId;

    /**
     * 商户退款单号
     *
     * 必填：是
     */
    @SerializedName("out_refund_no")
    private String outRefundNo;

    /**
     * 退款原因
     *
     * 必填：否
     */
    @SerializedName("reason")
    private String reason;

    /**
     * 退款结果回调url
     *
     * 必填：否
     */
    @SerializedName("notify_url")
    private String notifyUrl;

    /**
     * 退款资金来源
     * 若传递此参数则使用对应的资金账户退款，否则默认使用未结算资金退款（仅对老资金流商户适用）
     * 枚举值：
     * AVAILABLE：可用余额账户
     *
     * 必填：否
     */
    @SerializedName("funds_account")
    private String fundsAccount;

    /**
     * 金额信息
     *
     * 必填：是
     */
    @SerializedName("amount")
    private Amount amount;

    @Data
    @NoArgsConstructor
    public static class Amount implements Serializable {
        private static final long serialVersionUID = 4303793623232200660L;

        /**
         * 退款金额,单位分
         *
         * 必填：是
         */
        @SerializedName("refund")
        private Integer refund;

        /**
         * 退款出资账户及金额
         *
         * 款需要从指定账户出资时，传递此参数指定出资金额（币种的最小单位，只能为整数）。
         * 同时指定多个账户出资退款的使用场景需要满足以下条件：
         *   1、未开通退款支出分离产品功能；
         *   2、订单属于分账订单，且分账处于待分账或分账中状态。
         * 参数传递需要满足条件：
         *   1、基本账户可用余额出资金额与基本账户不可用余额出资金额之和等于退款金额；
         *   2、账户类型不能重复。
         * 上述任一条件不满足将返回错误
         *
         */
        @SerializedName("from")
        private List<From> from;

        /**
         * 原订单金额,单位分
         *
         * 必填：是
         */
        @SerializedName("total")
        private Integer total;

        /**
         * 退款币种
         *
         * 必填：是
         */
        @SerializedName("currency")
        private String currency;

        @Data
        @NoArgsConstructor
        public static class From implements Serializable {
            private static final long serialVersionUID = -987688807751016084L;

            /**
             * 出资账户类型
             * 枚举值：
             * AVAILABLE : 可用余额
             * UNAVAILABLE : 不可用余额
             *
             * 必填：是
             */
            @SerializedName("account")
            private String account;

            /**
             * 出资金额
             *
             * 必填：是
             */
            @SerializedName("amount")
            private Integer amount;
        }

    }

}
