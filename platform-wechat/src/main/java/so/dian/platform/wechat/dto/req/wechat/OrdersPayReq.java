package so.dian.platform.wechat.dto.req.wechat;

import lombok.Data;

import java.util.Date;

/**
 * Created by ailun on 2017/11/4.
 */
@Data
public class OrdersPayReq {

    private String tradeNo;

    private Date orderDate;

    private Long orderAmount;

    private String paltOpenId;

    private String subBizType;

    private Integer channel;

    private String subChannel;

    private String currency;

    private String clientIp;

    private String expireTime;

    private String body;

    private String subject;

    private String description;

    private String returnUrl;
}
