package so.dian.platform.wechat.dto.rsp;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;
import so.dian.platform.wechat.dto.notify.WechatPayNotifyBody;

import java.io.Serializable;
import java.util.List;

/**
 * 订单信息查询响应信息
 * <AUTHOR>
 */
@Data
public class OrderInfoQueryRes implements Serializable {
    private static final long serialVersionUID = -8666658540042433847L;

    /**
     * 应用ID
     *
     * 必填：是
     */
    @SerializedName("appid")
    private String appid;

    /**
     * 商户号
     *
     * 必填：是
     */
    @SerializedName("mchid")
    private String mchId;

    /**
     * 服务商模式-服务商应用ID
     */
    @SerializedName("sp_appid")
    private String spAppid;

    /**
     * 服务商模式-服务商户号
     */
    @SerializedName("sp_mchid")
    private String spMchid;

    /**
     * 服务商模式-子商户应用ID
     */
    @SerializedName("sub_appid")
    private String subAppid;

    /**
     * 服务商模式-子商户号
     */
    @SerializedName("sub_mchid")
    private String subMchid;

    /**
     * 商户订单号
     *
     * 必填：是
     */
    @SerializedName("out_trade_no")
    private String outTradeNo;

    /**
     * 微信支付订单号
     *
     * 必填：否
     */
    @SerializedName("transaction_id")
    private String transactionId;

    /**
     * 交易类型
     * JSAPI：公众号支付
     * NATIVE：扫码支付
     * APP：APP支付
     * MICROPAY：付款码支付
     * MWEB：H5支付
     * FACEPAY：刷脸支付
     *
     * 必填：否
     */
    @SerializedName("trade_type")
    private String tradeType;

    /**
     * 交易状态
     *
     * SUCCESS：支付成功
     * REFUND：转入退款
     * NOTPAY：未支付
     * CLOSED：已关闭
     * REVOKED：已撤销（仅付款码支付会返回）
     * USERPAYING：用户支付中（仅付款码支付会返回）
     * PAYERROR：支付失败（仅付款码支付会返回）
     *
     * 必填：是
     */
    @SerializedName("trade_state")
    private String tradeState;

    /**
     * 交易状态描述
     *
     * 必填：是
     */
    @SerializedName("trade_state_desc")
    private String tradeStateDesc;

    /**
     * 付款银行
     *
     * 必填：否
     */
    @SerializedName("bank_type")
    private String bankType;

    /**
     * 附加数据
     *
     * 必填：否
     */
    @SerializedName("attach")
    private String attach;

    /**
     * 支付完成时间
     *
     * 支付完成时间，遵循rfc3339标准格式，格式为yyyy-MM-DDTHH:mm:ss+TIMEZONE，yyyy-MM-DD表示年月日，
     * T出现在字符串中，表示time元素的开头，HH:mm:ss表示时分秒，TIMEZONE表示时区（+08:00表示东八区时间，领先UTC 8小时，即北京时间）。
     * 例如：2015-05-20T13:29:35+08:00表示，北京时间2015年5月20日 13点29分35秒。
     *
     * 必填：否
     */
    @SerializedName("success_time")
    private String successTime;


    /**
     * 支付者
     *
     * 必填：是
     */
    private Payer payer;

    /**
     * 订单金额
     *
     * 必填：否
     */
    private Amount amount;

    /**
     * 场景信息
     *
     * 必填：否
     */
    @SerializedName("scene_info")
    private SceneInfo sceneInfo;

    /**
     * 优惠功能
     */
    @SerializedName("promotion_detail")
    private List<WechatPayNotifyBody.PromotionDetail> promotionDetail;


    @Data
    @NoArgsConstructor
    public static class Payer {

        /**
         * 普通商户模式-用户标识
         */
        @SerializedName("openid")
        private String openid;

        /**
         * 服务商模式-用户服务标识
         *
         */
        @SerializedName("sp_openid")
        private String spOpenid;

        /**
         * 服务商模式-用户子标识
         * 用户在子商户appid下的唯一标识。如果返回sub_appid，那么sub_openid一定会返回
         */
        @SerializedName("sub_openid")
        private String subOpenid;
    }

    @Data
    @NoArgsConstructor
    public static class Amount implements Serializable {


        private static final long serialVersionUID = 4255089745586274486L;
        /**
         * 订单总金额，单位为分。
         *
         * 必填：否
         */
        @SerializedName("total")
        private Integer total;

        /**
         * 货币类型
         * CNY：人民币，境内商户号仅支持人民币。
         * 必填：否
         */
        @SerializedName("currency")
        private String currency;

        /**
         * 用户支付金额，单位为分。
         *
         * 指使用优惠券的情况下，这里等于总金额-优惠券金额
         *
         * 必填：否
         */
        @SerializedName("payer_total")
        private Integer payerTotal;

        /**
         * 用户支付币种
         *
         * 必填：否
         */
        @SerializedName("payer_currency")
        private String payerCurrency;


    }


    @Data
    @NoArgsConstructor
    public static class SceneInfo {

        /**
         * 商户端设备号
         */
        @SerializedName("device_id")
        private String deviceId;

    }

    @Data
    @NoArgsConstructor
    public static class PromotionDetail implements Serializable {
        private static final long serialVersionUID = -6180751996028678810L;

        /**
         * 券ID
         */
        @SerializedName("coupon_id")
        private String couponId;

        /**
         * 优惠名称
         */
        @SerializedName("name")
        private String name;

        /**
         * 优惠范围
         * GLOBAL：全场代金券
         * SINGLE：单品优惠
         */
        @SerializedName("scope")
        private String scope;

        /**
         * 优惠类型
         * CASH：充值型代金券
         * NOCASH：免充值型代金券
         */
        @SerializedName("type")
        private String type;

        /**
         * 优惠券面额
         */
        @SerializedName("amount")
        private Integer amount;

        /**
         * 活动ID
         */
        @SerializedName("stock_id")
        private String stockId;


        /**
         * 微信出资,单位为分
         */
        @SerializedName("wechatpay_contribute")
        private String wechatpayContribute;

        /**
         * 商户出资,单位为分
         */
        @SerializedName("merchant_contribute")
        private String merchantContribute;

        /**
         * 其他出资,单位为分
         */
        @SerializedName("other_contribute")
        private String otherContribute;

        /**
         * 优惠币种
         */
        @SerializedName("currency")
        private String currency;

    }

}
