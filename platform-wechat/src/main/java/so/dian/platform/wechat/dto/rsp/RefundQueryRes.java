package so.dian.platform.wechat.dto.rsp;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RefundQueryRes implements Serializable {
    private static final long serialVersionUID = 8597980640511855275L;

    /**
     * 微信支付退款单号
     */
    @SerializedName("refund_id")
    private String refundId;

    /**
     * 商户订单号
     */
    @SerializedName("out_trade_no")
    private String outTradeNo;

    /**
     * 微信支付订单号
     */
    @SerializedName("transaction_id")
    private String transactionId;

    /**
     * 商户退款单号
     */
    @SerializedName("out_refund_no")
    private String outRefundNo;

    /**
     * 退款渠道
     *
     * 枚举值：
     * ORIGINAL：原路退款
     * BALANCE：退回到余额
     * OTHER_BALANCE：原账户异常退到其他余额账户
     * OTHER_BANKCARD：原银行卡异常退到其他银行卡
     */
    @SerializedName("channel")
    private String channel;

    /**
     * 退款入账账户
     *
     * 取当前退款单的退款入账方，有以下几种情况：
     * 1）退回银行卡：{银行名称}{卡类型}{卡尾号}
     * 2）退回支付用户零钱:支付用户零钱
     * 3）退还商户:商户基本账户商户结算银行账户
     * 4）退回支付用户零钱通:支付用户零钱通
     *
     */
    @SerializedName("user_received_account")
    private String userReceivedAccount;

    /**
     * 退款成功时间
     *
     * 2020-12-01T16:18:12+08:00
     */
    @SerializedName("success_time")
    private String successTime;

    /**
     * 退款创建时间
     *
     * 2020-12-01T16:18:12+08:00
     */
    @SerializedName("create_time")
    private String createTime;


    /**
     * 退款状态
     *
     * 退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，可前往商户平台-交易中心，手动处理此笔退款。
     * 枚举值：
     * SUCCESS：退款成功
     * CLOSED：退款关闭
     * PROCESSING：退款处理中
     * ABNORMAL：退款异常
     *
     */
    @SerializedName("status")
    private String status;

    /**
     * 资金账户
     *
     * 退款所使用资金对应的资金账户类型
     * 枚举值：
     * UNSETTLED : 未结算资金
     * AVAILABLE : 可用余额
     * UNAVAILABLE : 不可用余额
     * OPERATION : 运营户
     * BASIC : 基本账户（含可用余额和不可用余额）
     */
    @SerializedName("funds_account")
    private String fundsAccount;

    /**
     * 金额信息
     */
    @SerializedName("amount")
    private Amount amount;

    @Data
    @NoArgsConstructor
    public static class Amount implements Serializable {
        private static final long serialVersionUID = -4149650936613566089L;

        /**
         * 原订单金额,单位分
         *
         * 必填：是
         */
        @SerializedName("total")
        private Integer total;


        /**
         * 退款金额,单位分
         *
         * 必填：是
         */
        @SerializedName("refund")
        private Integer refund;

        /**
         * 退款出资账户及金额
         */
        private List<From> from;


        /**
         * 用户支付金额
         *
         */
        @SerializedName("payer_total")
        private Integer payerTotal;

        /**
         * 用户退款金额
         *
         */
        @SerializedName("payer_refund")
        private Integer payerRefund;


        /**
         * 应结退款金额
         *
         */
        @SerializedName("settlement_refund")
        private Integer settlementRefund;

        /**
         * 应结订单金额
         *
         */
        @SerializedName("settlement_total")
        private Integer settlementTotal;

        /**
         * 优惠退款金额
         *
         */
        @SerializedName("discount_refund")
        private Integer discountRefund;


        /**
         * 退款币种
         *
         */
        @SerializedName("currency")
        private String currency;

        /**
         * 手续费退款金额
         */
        @SerializedName("refund_fee")
        private String refundFee;

        @Data
        @NoArgsConstructor
        public static class From implements Serializable {
            private static final long serialVersionUID = -987688807751016084L;

            /**
             * 出资账户类型
             * 枚举值：
             * AVAILABLE : 可用余额
             * UNAVAILABLE : 不可用余额
             *
             * 必填：是
             */
            @SerializedName("account")
            private String account;

            /**
             * 出资金额
             *
             * 必填：是
             */
            @SerializedName("amount")
            private Integer amount;
        }

    }

}
