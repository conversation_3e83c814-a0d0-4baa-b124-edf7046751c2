package so.dian.platform.wechat.dto.rsp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class WechatRefundRes {
    @JsonProperty("id")
    private String id;
    @JsonProperty("out_refund_no")
    private String outRefundNo;
    @JsonProperty("create_time")
    private String createTime;
    @JsonProperty("amount")
    private AmountDTO amount;
    @JsonProperty("detail")
    private List<DetailDTO> detail;

    @NoArgsConstructor
    @Data
    public static class AmountDTO {
        @JsonProperty("refund")
        private Integer refund;
        @JsonProperty("currency")
        private String currency;
        @JsonProperty("payer_refund")
        private Integer payerRefund;
        @JsonProperty("payer_currency")
        private String payerCurrency;
        @JsonProperty("settlement_refund")
        private Integer settlementRefund;
        @JsonProperty("settlement_currency")
        private String settlementCurrency;
        @JsonProperty("exchange_rate")
        private ExchangeRateDTO exchangeRate;

        @NoArgsConstructor
        @Data
        public static class ExchangeRateDTO {
            @JsonProperty("type")
            private String type;
            @JsonProperty("rate")
            private Integer rate;
        }
    }

    @NoArgsConstructor
    @Data
    public static class DetailDTO {
        @JsonProperty("promotion_id")
        private String promotionId;
        @JsonProperty("scope")
        private String scope;
        @JsonProperty("type")
        private String type;
        @JsonProperty("amount")
        private Integer amount;
        @JsonProperty("refund_amount")
        private Integer refundAmount;
        @JsonProperty("currency")
        private String currency;
    }
}
