package so.dian.platform.wechat.dto.rsp;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class WxPayScoreRes implements Serializable {
    private static final long serialVersionUID = 4919936913222001441L;

    /**
     * 应用ID
     */
    @SerializedName("appid")
    private String appid;

    /**
     * 商户号
     */
    @SerializedName("mchid")
    private String mchid;

    /**
     * 商户服务订单号
     */
    @SerializedName("out_order_no")
    private String outOrderNo;

    /**
     * 服务ID
     */
    @SerializedName("service_id")
    private String serviceId;

    /**
     * 服务信息
     */
    @SerializedName("service_introduction")
    private String serviceIntroduction;

    /**
     * 服务订单状态
     * 枚举值：
     * CREATED：商户已创建服务订单；
     * DOING：服务订单进行中；
     * DONE：服务订单完成；
     * REVOKED：商户取消服务订单；
     * EXPIRED：服务订单已失效，"商户已创建服务订单"状态超过30天未变动，则订单失效
     *
     */
    @SerializedName("state")
    private String state;

    /**
     * 订单状态说明
     */
    @SerializedName("state_description")
    private String stateDescription;

    /**
     * 商户收款总金额
     */
    @SerializedName("total_amount")
    private String totalAmount;

    /**
     * 后付费项目
     */
    @SerializedName("post_payments")
    private List<PostPayment> postPayments;

    /**
     * 后付费商户优惠
     */
    @SerializedName("post_discounts")
    private List<PostDiscount> postDiscounts;

    /**
     * 订单风险金
     */
    @SerializedName("risk_fund")
    private RiskFund riskFund;

    /**
     * 服务时间段
     */
    @SerializedName("time_range")
    private TimeRange timeRange;

    /**
     * 服务位置
     */
    @SerializedName("location")
    private Location location;

    /**
     * 商户数据包
     */
    @SerializedName("attach")
    private String attach;

    /**
     * 商户回调地址
     */
    @SerializedName("notify_url")
    private String notifyUrl;

    /**
     * 微信支付服务订单号
     */
    @SerializedName("order_id")
    private String orderId;

    /**
     * 是否需要收款
     */
    @SerializedName("need_collection")
    private boolean needCollection;

    /**
     * 收款信息
     */
    @SerializedName("collection")
    private Collection collection;

    /**
     * 用户标识
     */
    @SerializedName("openid")
    private String openid;

    @SerializedName("package")
    private String packageX;

    /**
     * 用于跳转的sign注意区分需确认模式和无需确认模式的数据差别。创单接口会返回，查询请自行组装
     */
    @SerializedName("payScoreSignInfo")
    private Map<String, String> payScoreSignInfo;

    @SerializedName("apply_permissions_token")
    private  String applyPermissionsToken;

    @SerializedName("authorization_code")
    private  String authorizationCode;

    @SerializedName("authorization_state")
    private  String authorizationState;

    @SerializedName("cancel_authorization_time")
    private  String cancelAuthorizationTime;

    @SerializedName("authorization_success_time")
    private  String authorizationSuccessTime;


    /**
     * 收款信息
     */
    @Data
    @NoArgsConstructor
    public static class Collection implements Serializable {
        private static final long serialVersionUID = 2279516555276133086L;
        /**
         * state : USER_PAID
         * total_amount : 3900
         * paying_amount : 3000
         * paid_amount : 900
         * details : [{"seq":1,"amount":900,"paid_type":"NEWTON","paid_time":"20091225091210","transaction_id":"15646546545165651651"}]
         */
        @SerializedName("state")
        private String state;

        @SerializedName("total_amount")
        private int totalAmount;

        @SerializedName("paying_amount")
        private int payingAmount;

        @SerializedName("paid_amount")
        private int paidAmount;

        @SerializedName("details")
        private List<Detail> details;
    }

    @Data
    @NoArgsConstructor
    public class Detail implements Serializable {
        private static final long serialVersionUID = -3901373259400050385L;
        /**
         * seq : 1
         * amount : 900
         * paid_type : NEWTON
         * paid_time : 20091225091210
         * transaction_id : 15646546545165651651
         */
        @SerializedName("seq")
        private int seq;

        @SerializedName("amount")
        private int amount;

        @SerializedName("paid_type")
        private String paidType;

        @SerializedName("paid_time")
        private String paidTime;

        @SerializedName("transaction_id")
        private String transactionId;
    }

    @Data
    @NoArgsConstructor
    public class RiskFund implements Serializable {
        private static final long serialVersionUID = -3583406084396059152L;
        /**
         * 风险金名称
         * name :  ESTIMATE_ORDER_COST
         * amount : 10000
         * description : 就餐的预估费用
         */
        @SerializedName("name")
        private String name;

        /**
         * 风险金额
         * 1、数字，必须>0（单位分）
         * 2、风险金额≤每个服务ID的风险金额上限
         * 3、当商户优惠字段为空时，付费项目总金额≤服务ID的风险金额上限 （未填写金额的付费项目，视为该付费项目金额为0）
         */
        @SerializedName("amount")
        private int amount;

        /**
         * 风险说明
         */
        @SerializedName("description")
        private String description;
    }

    @Data
    @NoArgsConstructor
    public class PostPayment implements Serializable {
        private static final long serialVersionUID = 2007722927556382895L;
        /**
         * 付费项目名称
         * name : 就餐费用服务费
         * amount : 4000
         * description : 就餐人均100元服务费：100/小时
         * count : 1
         */
        @SerializedName("name")
        private String name;

        /**
         * 金额
         * 此付费项目总金额，大于等于0，单位为分，等于0时代表不需要扣费
         */
        @SerializedName("amount")
        private Integer amount;

        /**
         * 计费说明
         */
        @SerializedName("description")
        private String description;

        /**
         * 付费数量
         */
        @SerializedName("count")
        private Integer count;
    }

    @Data
    @NoArgsConstructor
    public class PostDiscount implements Serializable {
        private static final long serialVersionUID = 2764537888242763379L;
        /**
         * 优惠名称
         * name : 满20减1元
         * description : 不与其他优惠叠加
         */
        @SerializedName("name")
        private String name;

        /**
         * 优惠说明
         */
        @SerializedName("description")
        private String description;

        /**
         * 优惠金额
         */
        @SerializedName("amount")
        private int amount;

        /**
         * 付费数量
         */
        @SerializedName("count")
        private int count;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class TimeRange implements Serializable {
        private static final long serialVersionUID = 8169562173656314930L;
        /**
         *
         * 服务开始时间
         * start_time : 20091225091010
         * end_time : 20091225121010
         */
        @SerializedName("start_time")
        private String startTime;

        /**
         * 服务开始时间备注
         */
        @SerializedName("end_time")
        private String endTime;
    }

    @Data
    @NoArgsConstructor
    public class Location implements Serializable {
        private static final long serialVersionUID = -4510224826631515344L;
        /**
         * 服务开始地点
         * start_location : 嗨客时尚主题展餐厅
         * end_location : 嗨客时尚主题展餐厅
         */
        @SerializedName("start_location")
        private String startLocation;

        /**
         * 服务结束位置
         */
        @SerializedName("end_location")
        private String endLocation;
    }
}
