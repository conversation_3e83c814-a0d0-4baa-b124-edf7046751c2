package so.dian.platform.wechat.factory;

import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import com.wechat.pay.contrib.apache.httpclient.WechatPayHttpClientBuilder;
import com.wechat.pay.contrib.apache.httpclient.auth.PrivateKeySigner;
import com.wechat.pay.contrib.apache.httpclient.auth.Verifier;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Credentials;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Validator;
import com.wechat.pay.contrib.apache.httpclient.cert.CertificatesManager;
import com.wechat.pay.contrib.apache.httpclient.exception.HttpCodeException;
import com.wechat.pay.contrib.apache.httpclient.exception.NotFoundException;
import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;
import so.dian.platform.common.utils.CertFileUtil;
import so.dian.platform.wechat.config.WechatProperty;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.PrivateKey;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WechatV3BeanFactory {

    /**
     * 系统配置文件环境变量
     */
    @Value("${hera.profile}")
    private String env;

    private ConcurrentHashMap<Integer, PrivateKey> privateKeyMap = new ConcurrentHashMap<>();

    /**
     * 装载证书管理对象容器
     * key：渠道配置ID
     */
    private ConcurrentHashMap<Integer, Verifier> verifierMap = new ConcurrentHashMap<>();

    /**
     * 装载httpClient对象容器
     * key：渠道配置ID
     */
    private ConcurrentHashMap<Integer, CloseableHttpClient> httpClientMap = new ConcurrentHashMap<>();

    /**
     * 获取证书管理对象
     * @param channelId
     * @param wechatProperty
     * @return
     */
    public Verifier getVerifier(Integer channelId, WechatProperty wechatProperty){
        Verifier verifier = verifierMap.get(channelId);
        if(Objects.nonNull(verifier)){
            return verifier;
        }

        // 兜底：重新初始化
        return initVerifier(channelId, wechatProperty);
    }

    /**
     * 获取HttpClient客户端
     * @param channelId
     * @param wechatProperty
     * @return
     */
    public CloseableHttpClient getHttpClient(Integer channelId, WechatProperty wechatProperty){
        CloseableHttpClient httpClient = httpClientMap.get(channelId);
        if(Objects.nonNull(httpClient)){
            return httpClient;
        }
        throw HeraBizException.create(HeraBizErrorCodeEnum.ILLEGAL_ERROR, "no httpClient for channelId:" + channelId);
    }

    public PrivateKey getPrivateKey(Integer channelId, WechatProperty wechatProperty){
        PrivateKey privateKey = privateKeyMap.get(channelId);
        if(Objects.nonNull(privateKey)){
            return privateKey;
        }

        // 兜底：重新初始化
        return initPrivateKey(channelId, wechatProperty);
    }

    /**
     * 服务启动时加载到容器
     * @param channelId
     * @param wechatProperty
     */
    public void init(Integer channelId, WechatProperty wechatProperty){
        initPrivateKey(channelId, wechatProperty);
        initVerifier(channelId, wechatProperty);
        initWxPayClient(channelId, wechatProperty);
    }

    /**
     * 初始化商户私钥证书
     * @param channelId
     * @param wechatProperty
     */
    public PrivateKey initPrivateKey(Integer channelId, WechatProperty wechatProperty) {
        PrivateKey privateKey = getPrivateKey(wechatProperty);
        privateKeyMap.put(channelId,privateKey);
        return privateKey;
    }

    /**
     * 初始化证书管理对象
     * @param channelId
     * @param wechatProperty
     * @return
     */
    public Verifier initVerifier(Integer channelId, WechatProperty wechatProperty){

        try {
            // 获取私钥证书
            PrivateKey privateKey = privateKeyMap.get(channelId);

            // 获取证书管理器实例
            CertificatesManager certificatesManager = CertificatesManager.getInstance();

            // 向证书管理器增加需要自动更新平台证书的商户信息（初始化证书下载，以及开启定时任务更新证书：1440分钟会更新一次，即：24小时）
            certificatesManager.putMerchant(wechatProperty.getMchId(), new WechatPay2Credentials(wechatProperty.getMchId(), new PrivateKeySigner(wechatProperty.getCertSerialNo(), privateKey)), wechatProperty.getApiV3Key().getBytes(StandardCharsets.UTF_8));

            // 从证书管理器中获取verifier
            Verifier verifier = certificatesManager.getVerifier(wechatProperty.getMchId());

            // 加入容器
            verifierMap.put(channelId,verifier);
            return verifier;
        } catch (NotFoundException|GeneralSecurityException|HttpCodeException|IOException exception ){
            log.error("微信v3#初始化证书管理对象#未知异常",exception);
            throw HeraBizException.create(HeraBizErrorCodeEnum.ILLEGAL_ERROR,"初始化证书管理对象未知异常-"+exception.getMessage());
        }
    }

    /**
     * 初始化httpclient客户端
     * @param channelId
     * @param wechatProperty
     * @return
     * @throws GeneralSecurityException
     * @throws IOException
     * @throws HttpCodeException
     * @throws NotFoundException
     */
    public CloseableHttpClient initWxPayClient(Integer channelId, WechatProperty wechatProperty) {
        // 获取私钥证书
        PrivateKey privateKey = privateKeyMap.get(channelId);

        // 从证书管理器中获取verifier
        Verifier verifier = verifierMap.get(channelId);

        WechatPayHttpClientBuilder builder = WechatPayHttpClientBuilder.create()
                .withMerchant(wechatProperty.getMchId(), wechatProperty.getCertSerialNo(), privateKey)
                .withValidator(new WechatPay2Validator(verifier));

        // 通过WechatPayHttpClientBuilder构造的HttpClient，会自动的处理签名和验签，并进行证书自动更新 实现类：SignatureExec
        CloseableHttpClient httpClient = builder.build();

        // 加入容器
        httpClientMap.put(channelId,httpClient);
        return httpClient;
    }


    /**
     * 获取商户的私钥文件
     * 预发或者生产环境，直接读取服务器目录文件
     * 开发或者测试环境，直接读取项目resources目录下的文件 cert/wechat/{mchId}/apiclient_key.pem
     * @return
     * @throws IOException
     */
    private PrivateKey getPrivateKey(WechatProperty wechatProperty) {

        try {
            // 预发或者生产环境，直接读取服务器目录文件
//            if (EnvironmentVariableEnum.REAL.getCode().equalsIgnoreCase(env)
//                    || EnvironmentVariableEnum.PRE.getCode().equalsIgnoreCase(env)) {
//                log.info("{}环境，读取服务器目录下的文件 filePath: {}",env, wechatProperty.getPrivateKeyPath());
//                return PemUtil.loadPrivateKey(new FileInputStream(wechatProperty.getPrivateKeyPath()));
//            } else {
//
//            }
            log.info( "{}环境，读取项目resources目录下的文件 filePath: {}",env,"cert/wechat/" + wechatProperty.getMchId() + "/apiclient_key.pem");
            String content = StreamUtils.copyToString(CertFileUtil.getCertFileInputStream(CertFileUtil.CertProvider.Wechat, wechatProperty.getMchId(), "apiclient_key.pem"), Charset.forName("utf-8"));
            return PemUtil.loadPrivateKey(content);
        }catch (Exception exception){
            log.error("微信v3#获取商户的私钥文件#未知异常",exception);
            throw HeraBizException.create(HeraBizErrorCodeEnum.ILLEGAL_ERROR,"获取商户的私钥文件未知异常-"+exception.getMessage());
        }

    }






}
