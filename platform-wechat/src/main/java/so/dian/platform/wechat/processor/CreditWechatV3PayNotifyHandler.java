package so.dian.platform.wechat.processor;

import com.chargebolt.hera.client.enums.CallbackOriginalTypeEnum;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.PayNotifyHandler;

/**
 * 即时支付回调业务处理
 * <AUTHOR>
 */
@Slf4j
@Component
public class CreditWechatV3PayNotifyHandler implements PayNotifyHandler {

    @Override
    public String key() {
        return CallbackOriginalTypeEnum.getKey(CallbackOriginalTypeEnum.PAYSCORE);
    }

    @Override
    public void handlerNotify(String content) {
        throw new HeraBizException(HeraBizErrorCodeEnum.OPERATE_BUSY);
    }
}
