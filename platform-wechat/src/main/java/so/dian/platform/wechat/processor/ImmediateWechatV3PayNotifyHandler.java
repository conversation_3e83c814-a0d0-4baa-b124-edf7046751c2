package so.dian.platform.wechat.processor;

import cn.hutool.core.date.DateUtil;
import com.chargebolt.hera.client.dto.callback.impay.WxImPayCallbackDTO;
import com.chargebolt.hera.client.enums.CallbackOriginalTypeEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wechat.pay.java.core.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import so.dian.hera.interceptor.PayNotifyHandler;
import so.dian.platform.common.mq.producer.SelfMqProducer;
import so.dian.platform.wechat.dto.notify.WechatPayNotifyBody;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static so.dian.platform.common.constants.BusinessConstants.*;

/**
 * 即时支付回调业务处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ImmediateWechatV3PayNotifyHandler implements PayNotifyHandler {

    @Resource
    private SelfMqProducer selfMqProducer;

    @Override
    public String key() {
        return CallbackOriginalTypeEnum.getKey(CallbackOriginalTypeEnum.PAY);
    }

    @Override
    public void handlerNotify(String content) {
        log.info("微信支付回调消息: {}", content);
        WechatPayNotifyBody notifyBody = GsonUtil.getGson().fromJson(content, WechatPayNotifyBody.class);
        if (Objects.equals(REFUND, notifyBody.getTradeState()) || Objects.equals(NOTPAY, notifyBody.getTradeState())
                || Objects.equals(USERPAYING, notifyBody.getTradeState())) {
            log.info("支付状态非明确失败，不做处理 trade_state: {}", notifyBody.getTradeState());
            return;
        }

        // 1、构建消息体
        WxImPayCallbackDTO wxImPayCallbackDTO = new WxImPayCallbackDTO();
        wxImPayCallbackDTO.setPayStatus(SUCCESS.equals(notifyBody.getTradeState()));
        wxImPayCallbackDTO.setPaymentTradeNo(!StringUtils.isEmpty(notifyBody.getTransactionId()) ? notifyBody.getTransactionId() : notifyBody.getId());
        wxImPayCallbackDTO.setOutTradeNo(notifyBody.getOutTradeNo());
        WechatPayNotifyBody.Amount amount = notifyBody.getAmount();
        if (wxImPayCallbackDTO.getPayStatus()) {
            wxImPayCallbackDTO.setCurrency(amount.getCurrency());
            wxImPayCallbackDTO.setPayerCurrency(amount.getPayerCurrency());
            wxImPayCallbackDTO.setTotalFee(amount.getTotal().longValue());
            wxImPayCallbackDTO.setPayTime(DateUtil.parse(notifyBody.getSuccessTime(), WECHAT_TIME_FORMAT));
            if (!CollectionUtils.isEmpty(notifyBody.getPromotionDetail())) {
                List<WxImPayCallbackDTO.Coupon> coupons = Lists.newArrayList();
                Long couponTotalFee = 0L;
                for (WechatPayNotifyBody.PromotionDetail item : notifyBody.getPromotionDetail()) {
                    WxImPayCallbackDTO.Coupon coupon = new WxImPayCallbackDTO.Coupon()
                            .setCouponType(item.getType())
                            .setCouponId(item.getCouponId())
                            .setCouponFee(item.getAmount().longValue());
                    coupons.add(coupon);
                    couponTotalFee += item.getAmount().longValue();
                }
                Long cashFee = wxImPayCallbackDTO.getTotalFee() - couponTotalFee;
                wxImPayCallbackDTO.setSettlementTotalFee(cashFee);
                wxImPayCallbackDTO.setCashFee(cashFee);
                wxImPayCallbackDTO.setCouponTotalFee(couponTotalFee);
                wxImPayCallbackDTO.setCouponCount(notifyBody.getPromotionDetail().size());
            }
        } else {
            Map errorDetail = Maps.newHashMap();
            errorDetail.put(ERROR_CODE, notifyBody.getTradeState());
            errorDetail.put(ERROR_CODE_DESC, notifyBody.getTradeStateDesc());
            wxImPayCallbackDTO.setErrorDetail(errorDetail);
            wxImPayCallbackDTO.setErrorMsg(String.format("%s|%s", notifyBody.getTradeState(), notifyBody.getTradeStateDesc()));
        }

        // 2、发送消息：hera自己消费（HeraWechatV3Consumer）
        selfMqProducer.sendSelfWechatPayMsg(wxImPayCallbackDTO);
    }
}
