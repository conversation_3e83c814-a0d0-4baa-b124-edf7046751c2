package so.dian.platform.wechat.processor;

import cn.hutool.core.date.DateUtil;
import com.chargebolt.hera.client.dto.callback.refund.WxRefundCallbackDTO;
import com.chargebolt.hera.client.enums.CallbackOriginalTypeEnum;
import com.wechat.pay.java.core.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.PayNotifyHandler;
import so.dian.platform.common.mq.producer.SelfMqProducer;
import so.dian.platform.wechat.dto.notify.WechatRefundNotifyBody;

import javax.annotation.Resource;

import static so.dian.platform.common.constants.BusinessConstants.SUCCESS;
import static so.dian.platform.common.constants.BusinessConstants.WECHAT_TIME_FORMAT;

/**
 * <AUTHOR>
 * 即时支付-退款回调
 */
@Slf4j
@Component
public class ImmediateWechatV3RefundNotifyHandler implements PayNotifyHandler {

    @Resource
    private SelfMqProducer selfMqProducer;

    @Override
    public String key() {
        return CallbackOriginalTypeEnum.getKey(CallbackOriginalTypeEnum.REFUND);
    }

    @Override
    public void handlerNotify(String content) {
        WechatRefundNotifyBody notifyBody =  GsonUtil.getGson().fromJson(content, WechatRefundNotifyBody.class);

        // 1、构建消息体
        WxRefundCallbackDTO wxRefundCallbackDTO = new WxRefundCallbackDTO();
        wxRefundCallbackDTO.setRefundStatus(SUCCESS.equals(notifyBody.getRefundStatus()));
        wxRefundCallbackDTO.setRefundOrderNo(notifyBody.getOutRefundNo());
        wxRefundCallbackDTO.setRefundId(notifyBody.getRefundId());
        if(wxRefundCallbackDTO.getRefundStatus()){
            WechatRefundNotifyBody.Amount amount = notifyBody.getAmount();
            wxRefundCallbackDTO.setRefundAmount(amount.getRefund());
            wxRefundCallbackDTO.setRefundTime(DateUtil.parse(notifyBody.getSuccessTime(), WECHAT_TIME_FORMAT));
            wxRefundCallbackDTO.setTotalFee(amount.getTotal().longValue());
            wxRefundCallbackDTO.setRefundFee(amount.getRefund().longValue());
            wxRefundCallbackDTO.setSettlementTotalFee(null);
        }else{
            wxRefundCallbackDTO.setRefundStatus(false);
            wxRefundCallbackDTO.setResponseMsg(notifyBody.getRefundStatus());
        }

        // 2、发送消息：hera自己消费（HeraWechatV3Consumer）
        selfMqProducer.sendSelfWechatRefundMsg(wxRefundCallbackDTO);



    }
}
