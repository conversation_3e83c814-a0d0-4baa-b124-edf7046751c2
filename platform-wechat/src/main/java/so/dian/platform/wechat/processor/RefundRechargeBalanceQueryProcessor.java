package so.dian.platform.wechat.processor;

import javax.annotation.Resource;

import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.enums.ChannelEnum;

import lombok.extern.slf4j.Slf4j;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.wechat.common.enums.WechatOverseaApiEnum;
import so.dian.platform.wechat.config.WechatProperty;
import so.dian.platform.wechat.factory.WechatV3BeanFactory;
import so.dian.platform.wechat.support.WechatV3OrderPaySupport;

/**
 * 查询充值余额
 */
@Slf4j
@Component
public class RefundRechargeBalanceQueryProcessor extends WechatProcessor{

    @Resource
    private WechatV3BeanFactory wechatV3BeanFactory;
    @Resource
    private WechatProperty wechatProperty;

    public String queryRechargeBalance() {
        // 获取httpClient客户端
        CloseableHttpClient httpClient = wechatV3BeanFactory.getHttpClient(ChannelEnum.WECHAT.getChannelNo(), wechatProperty);
        // 发起退款请求
        String url = WechatOverseaApiEnum.BALANCE_QUERY.getApi();
        try {
            HttpGet httpGet = buildHttpGet(WechatV3OrderPaySupport.assembleRefundOrderQueryReq(null,url).build());
            CloseableHttpResponse response = httpClient.execute(httpGet);
            return EntityUtils.toString(response.getEntity());
        } catch (Exception e) {
            log.error("微信v3【查询充值余额】#异常",e); 
            throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(),"查询充值余额" + e.getMessage());
        }
    }

}
