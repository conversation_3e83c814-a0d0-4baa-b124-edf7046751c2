/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.wechat.processor;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.pay.rsp.OrderPayQueryRsp;
import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.google.common.collect.Lists;
import com.wechat.pay.java.core.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.QueryPayProcessor;
import so.dian.hera.interceptor.dto.req.PayQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.wechat.common.constant.WechatTradeStateConstant;
import so.dian.platform.wechat.common.enums.WechatApiEnum;
import so.dian.platform.wechat.config.WechatProperty;
import so.dian.platform.wechat.dto.rsp.ErrorInfo;
import so.dian.platform.wechat.dto.rsp.OrderInfoQueryRes;
import so.dian.platform.wechat.factory.WechatV3BeanFactory;
import so.dian.platform.wechat.support.WechatV3OrderPaySupport;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;

import static so.dian.platform.common.constants.BusinessConstants.COLON;
import static so.dian.platform.common.constants.BusinessConstants.EMPTY;
import static so.dian.platform.common.constants.BusinessConstants.PATH_OUT_TRADE_NO;
import static so.dian.platform.common.constants.BusinessConstants.WECHAT_TIME_FORMAT;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: WechatPayResultProcessor.java, v 1.0 2024-04-17 11:01 AM Exp $
 */
@Slf4j
@Component
public class WechatPayResultProcessor extends WechatProcessor implements QueryPayProcessor {
    private final WechatV3BeanFactory wechatV3BeanFactory;
    private final WechatProperty wechatProperty;
    public WechatPayResultProcessor(ObjectProvider<WechatV3BeanFactory> wechatV3BeanFactoryProvider,
                                    ObjectProvider<WechatProperty> wechatPropertyProvider){
        this.wechatV3BeanFactory= wechatV3BeanFactoryProvider.getIfUnique();
        this.wechatProperty= wechatPropertyProvider.getIfUnique();
    }
    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.WECHAT_MINIPROGRAM);
    }

    @Override
    public PayQueryResultDTO orderPayQuery(final PayQueryResultRequest req) {
        CloseableHttpClient httpClient = wechatV3BeanFactory.getHttpClient(ChannelEnum.WECHAT.getChannelNo(), wechatProperty);
        try {
            String url = WechatApiEnum.MINI_ORDER_QUERY_BY_OUT_TRADE_NO.getApi().replace(PATH_OUT_TRADE_NO, req.getTradeNo());
            // 服务商模式与普通商户模式参数不同
            HttpGet httpGet = buildHttpGet(WechatV3OrderPaySupport.assembleOrderQueryReq(null, wechatProperty,url).build());
            log.info("微信v3【查询订单】#请求参数 tradeNo: {}", req.getTradeNo());
            CloseableHttpResponse response = httpClient.execute(httpGet);
            String jsonResBody = EntityUtils.toString(response.getEntity());
            log.info("微信v3【查询订单】#响应参数 response: {}",jsonResBody);
            if(!Objects.equals(response.getStatusLine().getStatusCode(), HttpStatus.SC_OK)){
                ErrorInfo errorInfo = GsonUtil.getGson().fromJson(jsonResBody,ErrorInfo.class);
                String message = Objects.nonNull(errorInfo) && StringUtils.isNotBlank(errorInfo.getMessage()) ? COLON + errorInfo.getMessage() : EMPTY;
                throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(),"查询订单" + message);
            }
            // 构建应答体
            OrderInfoQueryRes orderInfo = GsonUtil.getGson().fromJson(jsonResBody, OrderInfoQueryRes.class);
            PayQueryResultDTO resp= new PayQueryResultDTO();
            resp.setTradeNo(req.getTradeNo());
            if(StringUtils.equalsIgnoreCase(WechatTradeStateConstant.SUCCESS, orderInfo.getTradeState())
                    ||StringUtils.equalsIgnoreCase(WechatTradeStateConstant.REFUND, orderInfo.getTradeState())){
                resp.setStatus(PayStatus.PAID.getCode());
                if(StringUtils.isNotBlank(orderInfo.getSuccessTime())){
                    resp.setPayTime(DateUtil.parse(orderInfo.getSuccessTime(), WECHAT_TIME_FORMAT));
                }
                resp.setPayNo(orderInfo.getTransactionId());
            }else if(StringUtils.equalsIgnoreCase(WechatTradeStateConstant.NOTPAY, orderInfo.getTradeState())){
                resp.setStatus(PayStatus.FAIL.getCode());
            }else if(StringUtils.equalsIgnoreCase(WechatTradeStateConstant.CLOSED, orderInfo.getTradeState())){
                resp.setStatus(PayStatus.CANCEL.getCode());
            }else{
                // 其他状态，先以处理中返回
                resp.setStatus(PayStatus.INIT.getCode());
            }
            return resp;
        } catch (IOException | URISyntaxException e) {
            log.error("微信v3【查询订单】#未知异常 ",e);
            throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(),"未知异常");
        }
    }
}