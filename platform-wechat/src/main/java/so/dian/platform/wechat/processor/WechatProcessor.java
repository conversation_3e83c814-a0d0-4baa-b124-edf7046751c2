package so.dian.platform.wechat.processor;

import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;

import java.net.URI;

import static so.dian.platform.common.constants.BusinessConstants.*;

abstract class WechatProcessor {
    /**
     * 构建HttpPost请求
     * @param url
     * @param jsonReqBody
     * @return
     */
    protected HttpPost buildHttpPost(String url, String jsonReqBody){
        HttpPost httpPost = new HttpPost(url);
        httpPost.addHeader(ACCEPT, ACCEPT_VALUE);
        httpPost.addHeader(CONTENT_TYPE,CONTENT_TYPE_VALUE);
        httpPost.setEntity(new StringEntity(jsonReqBody, "UTF-8"));
        return httpPost;
    }

    /**
     * 构建HttpGet请求
     * @param uri
     * @return
     */
    protected HttpGet buildHttpGet(URI uri){
        HttpGet httpGet = new HttpGet(uri);
        httpGet.addHeader(ACCEPT, ACCEPT_VALUE);
        return httpGet;
    }
}
