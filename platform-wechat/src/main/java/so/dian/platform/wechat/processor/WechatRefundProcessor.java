package so.dian.platform.wechat.processor;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundResultDTO;
import com.chargebolt.hera.client.enums.ChannelEnum;
import com.wechat.pay.java.core.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.RefundProcessor;
import so.dian.hera.interceptor.dto.req.ProcessorRefundRequest;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.wechat.common.enums.WechatOverseaApiEnum;
import so.dian.platform.wechat.config.WechatProperty;
import so.dian.platform.wechat.dto.req.RefundApplyReq;
import so.dian.platform.wechat.dto.rsp.ErrorInfo;
import so.dian.platform.wechat.dto.rsp.WechatRefundRes;
import so.dian.platform.wechat.factory.WechatV3BeanFactory;
import so.dian.platform.wechat.support.WechatV3OrderPaySupport;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Objects;

import static so.dian.platform.common.constants.BusinessConstants.*;
import static so.dian.platform.common.constants.BusinessConstants.PATH_CHANNEL_ID;

@Slf4j
@Component
public class WechatRefundProcessor extends WechatProcessor implements RefundProcessor {

    @Resource
    private WechatV3BeanFactory wechatV3BeanFactory;
    @Resource
    private WechatProperty wechatProperty;

    private RefundApplyReq assembleRefundOrderReq(ProcessorRefundRequest request, WechatProperty wechatProperty) {
        RefundApplyReq refundReq = new RefundApplyReq();
        refundReq.setMchid(wechatProperty.getMchId());
        refundReq.setAppid(wechatProperty.getAppId());
        refundReq.setOutTradeNo(request.getTradeNo());
        refundReq.setOutRefundNo(request.getRefundNo());
        refundReq.setReason(request.getReason());
        String notifyUrl = wechatProperty.getNotifyUrl()
                .replace(PATH_APPLICATION_ID, "hera")
                .replace(PATH_CHANNEL_ID, String.valueOf(ChannelEnum.WECHAT.getChannelNo()));
        refundReq.setNotifyUrl(notifyUrl);
        RefundApplyReq.Amount amount = new RefundApplyReq.Amount();
        amount.setRefund(request.getRefundAmount());
        amount.setCurrency(request.getCurrency());
        amount.setTotal(request.getPayOriginalAmount());
        refundReq.setAmount(amount);
        return refundReq;
    }

    @Override
    public RefundResultDTO refund(ProcessorRefundRequest request) {
        // 获取httpClient客户端
        CloseableHttpClient httpClient = wechatV3BeanFactory.getHttpClient(ChannelEnum.WECHAT.getChannelNo(), wechatProperty);
        String url = WechatOverseaApiEnum.REFUND.getApi();
        // 构建请求体
        String jsonReqBody = GsonUtil.toJson(assembleRefundOrderReq(request, wechatProperty));
        RefundResultDTO refundResultDTO = new RefundResultDTO();
        try {
            HttpPost httpPost = buildHttpPost(url, jsonReqBody);
            // 发起退款请求
            log.info("微信v3【订单退款申请】#请求参数 request: {},httpPost: {},isPartner: {}", jsonReqBody, JSON.toJSONString(httpPost));
            CloseableHttpResponse response = httpClient.execute(httpPost);
            String jsonResBody = EntityUtils.toString(response.getEntity());
            log.info("微信v3【订单退款申请】#响应参数 response: {}", jsonResBody);
            if (!Objects.equals(response.getStatusLine().getStatusCode(), HttpStatus.SC_OK)) {
                log.error("微信v3【订单退款申请】#http应答不成功  statusCode: {},req: {}", response.getStatusLine().getStatusCode(), JSON.toJSONString(request));
                ErrorInfo errorInfo = GsonUtil.getGson().fromJson(jsonResBody, ErrorInfo.class);
                String message = Objects.nonNull(errorInfo) && StringUtils.isNotBlank(errorInfo.getMessage()) ? COLON + errorInfo.getMessage() : EMPTY;
                throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(), "订单退款申请" + message);
            }
            WechatRefundRes wechatRefundRes = JSON.parseObject(jsonResBody, WechatRefundRes.class);
            refundResultDTO.setSuccess(true);
            refundResultDTO.setRefundNo(request.getRefundNo());
            refundResultDTO.setRefundPayNo(wechatRefundRes.getOutRefundNo());
        } catch (IOException e) {
            log.error("微信v3【订单退款申请】#未知异常 ", e);
            throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(), "未知异常");
        }
        // 构建应答体
        return refundResultDTO;
    }

    @Override
    public ChannelEnum getRefundChannel() {
        return ChannelEnum.WECHAT;
    }
}
