/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.wechat.processor;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.pay.rsp.OrderRefundQueryRsp;
import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.google.common.collect.Lists;
import com.wechat.pay.java.core.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.QueryPayProcessor;
import so.dian.hera.interceptor.QueryRefundProcessor;
import so.dian.hera.interceptor.dto.req.RefundQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;
import so.dian.hera.interceptor.dto.rsp.RefundQueryResultDTO;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.wechat.common.constant.WechatTradeStateConstant;
import so.dian.platform.wechat.common.enums.WechatApiEnum;
import so.dian.platform.wechat.common.enums.WechatOverseaApiEnum;
import so.dian.platform.wechat.config.WechatProperty;
import so.dian.platform.wechat.dto.rsp.ErrorInfo;
import so.dian.platform.wechat.dto.rsp.OrderInfoQueryRes;
import so.dian.platform.wechat.dto.rsp.RefundQueryRes;
import so.dian.platform.wechat.factory.WechatV3BeanFactory;
import so.dian.platform.wechat.support.WechatV3OrderPaySupport;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;

import static so.dian.platform.common.constants.BusinessConstants.COLON;
import static so.dian.platform.common.constants.BusinessConstants.EMPTY;
import static so.dian.platform.common.constants.BusinessConstants.OUT_REFUND_NO;
import static so.dian.platform.common.constants.BusinessConstants.PATH_OUT_TRADE_NO;
import static so.dian.platform.common.constants.BusinessConstants.WECHAT_CODE_RESOURCE_NOT_EXISTS;
import static so.dian.platform.common.constants.BusinessConstants.WECHAT_TIME_FORMAT;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: WechatRefundResultProcessor.java, v 1.0 2024-04-17 11:01 AM Exp $
 */
@Slf4j
@Component
public class WechatRefundResultProcessor extends WechatProcessor implements QueryRefundProcessor {
    private final WechatV3BeanFactory wechatV3BeanFactory;
    private final WechatProperty wechatProperty;
    public WechatRefundResultProcessor(ObjectProvider<WechatV3BeanFactory> wechatV3BeanFactoryProvider,
                                       ObjectProvider<WechatProperty> wechatPropertyProvider){
        this.wechatV3BeanFactory= wechatV3BeanFactoryProvider.getIfUnique();
        this.wechatProperty= wechatPropertyProvider.getIfUnique();
    }
    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.WECHAT_MINIPROGRAM);
    }

    @Override
    public RefundQueryResultDTO orderRefundQuery(RefundQueryResultRequest queryResultRequest) {
        // 获取httpClient客户端
        CloseableHttpClient httpClient = wechatV3BeanFactory.getHttpClient(ChannelEnum.WECHAT.getChannelNo(), wechatProperty);
        // 发起退款请求
        String url = WechatOverseaApiEnum.REFUND_RESULT_QUERY.getApi().replace(OUT_REFUND_NO, queryResultRequest.getRefundNo());
//        String url = WechatOverseaApiEnum.REFUND_RESULT_QUERY.getApi().replace(OUT_REFUND_NO, "50200802522025022635813974540");
        url+=buildParams();
        try {
            HttpGet httpGet = buildHttpGet(WechatV3OrderPaySupport.assembleRefundOrderQueryReq(null,url).build());
            log.info("微信v3【订单退款查询】#请求参数 request: {}", JSON.toJSONString(httpGet));
            CloseableHttpResponse response = httpClient.execute(httpGet);
            String jsonResBody = EntityUtils.toString(response.getEntity());
            log.info("微信v3【订单退款查询】#响应参数 response: {}",jsonResBody);
            RefundQueryResultDTO resp= new RefundQueryResultDTO();
            if(!Objects.equals(response.getStatusLine().getStatusCode(), HttpStatus.SC_OK)){
                ErrorInfo errorInfo = GsonUtil.getGson().fromJson(jsonResBody,ErrorInfo.class);
                String message = Objects.nonNull(errorInfo) && StringUtils.isNotBlank(errorInfo.getMessage()) ? COLON + errorInfo.getMessage() : EMPTY;
                // 订单不存在
                if(Objects.nonNull(errorInfo) && Objects.equals(errorInfo.getCode(),WECHAT_CODE_RESOURCE_NOT_EXISTS)){
                    resp.setStatus(PayStatus.FAIL.getCode());
                    return resp;
                }
                throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(),"订单退款查询" + message);
            }
            // 构建应答体
            RefundQueryRes refundApplyRes = GsonUtil.getGson().fromJson(jsonResBody, RefundQueryRes.class);

            resp.setRefundNo(queryResultRequest.getRefundNo());
            if(StringUtils.equalsIgnoreCase(WechatTradeStateConstant.SUCCESS,refundApplyRes.getStatus())){
                resp.setStatus(PayStatus.REFUNDED.getCode());
                resp.setRefundTime(DateUtil.parse(refundApplyRes.getSuccessTime(), WECHAT_TIME_FORMAT));
                resp.setOutTraceNo(refundApplyRes.getRefundId());
            }else if(StringUtils.equalsIgnoreCase(WechatTradeStateConstant.PROCESSING,refundApplyRes.getStatus())){
                resp.setStatus(PayStatus.REFUNDING.getCode());
            }else{
                // 其他，以退款失败处理
                resp.setStatus(PayStatus.FAIL.getCode());
            }
            return resp;
        } catch (IOException|URISyntaxException e) {
            log.error("微信v3【订单退款查询】#未知异常 ",e);
            throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(),"未知异常");
        }
    }


    private String buildParams(){
        String params = "?mchid="+wechatProperty.getMchId();
//                +"&sub_mchid="+wechatProperty.getMchId()
//                +"&sp_mchid="+wechatProperty.getMchId();
        return params;
    }
//    @Override
//    public PayQueryResultDTO orderPayQuery(final String tradeNo) {
//        CloseableHttpClient httpClient = wechatV3BeanFactory.getHttpClient(ChannelEnum.WECHAT.getChannelNo(), wechatProperty);
//        try {
//            String url = WechatApiEnum.MINI_ORDER_QUERY_BY_OUT_TRADE_NO.getApi().replace(PATH_OUT_TRADE_NO, tradeNo);
//            // 服务商模式与普通商户模式参数不同
//            HttpGet httpGet = buildHttpGet(WechatV3OrderPaySupport.assembleOrderQueryReq(null, wechatProperty,url).build());
//            log.info("微信v3【查询订单】#请求参数 tradeNo: {}", tradeNo);
//            CloseableHttpResponse response = httpClient.execute(httpGet);
//            String jsonResBody = EntityUtils.toString(response.getEntity());
//            log.info("微信v3【查询订单】#响应参数 response: {}",jsonResBody);
//            if(!Objects.equals(response.getStatusLine().getStatusCode(), HttpStatus.SC_OK)){
//                ErrorInfo errorInfo = GsonUtil.getGson().fromJson(jsonResBody,ErrorInfo.class);
//                String message = Objects.nonNull(errorInfo) && StringUtils.isNotBlank(errorInfo.getMessage()) ? COLON + errorInfo.getMessage() : EMPTY;
//                throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(),"查询订单" + message);
//            }
//            // 构建应答体
//            OrderInfoQueryRes orderInfo = GsonUtil.getGson().fromJson(jsonResBody, OrderInfoQueryRes.class);
//            PayQueryResultDTO resp= new PayQueryResultDTO();
//            resp.setTradeNo(tradeNo);
//            if(StringUtils.equalsIgnoreCase(WechatTradeStateConstant.SUCCESS, orderInfo.getTradeState())
//                    ||StringUtils.equalsIgnoreCase(WechatTradeStateConstant.REFUND, orderInfo.getTradeState())){
//                resp.setStatus(PayStatus.PAID.getCode());
//
//            }else if(StringUtils.equalsIgnoreCase(WechatTradeStateConstant.NOTPAY, orderInfo.getTradeState())
//                    ||StringUtils.equalsIgnoreCase(WechatTradeStateConstant.CLOSED, orderInfo.getTradeState())){
//                resp.setStatus(PayStatus.FAIL.getCode());
//            }else{
//                // 其他状态，先以处理中返回
//                resp.setStatus(PayStatus.INIT.getCode());
//            }
//            return resp;
//        } catch (IOException | URISyntaxException e) {
//            log.error("微信v3【查询订单】#未知异常 ",e);
//            throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(),"未知异常");
//        }
//    }
}