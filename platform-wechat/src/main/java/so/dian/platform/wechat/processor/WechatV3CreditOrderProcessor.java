package so.dian.platform.wechat.processor;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.credit.req.*;
import com.chargebolt.hera.client.dto.credit.rsp.*;
import com.chargebolt.hera.client.enums.PaywayEnum;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import com.github.wxpay.sdk.WXPayConstants;
import com.github.wxpay.sdk.WXPayUtil;
import com.google.common.collect.Lists;
import com.wechat.pay.java.core.util.GsonUtil;
import com.wechat.pay.java.core.util.NonceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.CreditOrderProcessor;
import so.dian.platform.wechat.common.enums.WechatApiEnum;
import so.dian.platform.wechat.config.WechatProperty;
import so.dian.platform.wechat.dto.rsp.ErrorInfo;
import so.dian.platform.wechat.dto.rsp.WxPayScoreRes;
import so.dian.platform.wechat.factory.WechatV3BeanFactory;
import so.dian.platform.wechat.support.WechatV3OrderPaySupport;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.*;

import static so.dian.platform.common.constants.BusinessConstants.*;

/**
 * 微信v3版本功能接口实现
 * <AUTHOR>
 */
@Slf4j
@Component
public class WechatV3CreditOrderProcessor extends WechatProcessor implements CreditOrderProcessor {

    @Resource
    private WechatV3BeanFactory wechatV3BeanFactory;
    @Resource
    private WechatProperty wechatProperty;

    @Override
    public CreditOrderCreateRsp createCreditOrder(CreditOrderCreateReq req) {
        // 获取httpClient客户端
        CloseableHttpClient httpClient = wechatV3BeanFactory.getHttpClient(req.getChannelId(), wechatProperty);
        // 发起创建支付分订单请求
        String url =WechatApiEnum.PAY_SCORE_CREATE_ORDER.getApi();
        // 构建请求体
        String jsonReqBody = GsonUtil.toJson(WechatV3OrderPaySupport.assembleCreateCreditOrderReq(req, wechatProperty));
        try {
            HttpPost httpPost = buildHttpPost(url,jsonReqBody);
            log.info("微信v3【创建支付分订单】#请求参数 request: {},httpPost: {}",jsonReqBody,JSON.toJSONString(httpPost));
            CloseableHttpResponse response = httpClient.execute(httpPost);
            String jsonResBody = EntityUtils.toString(response.getEntity());
            log.info("微信v3【创建支付分订单】#响应参数 response: {}",jsonResBody);
            if(!Objects.equals(response.getStatusLine().getStatusCode(), HttpStatus.SC_OK)){
                log.error("微信v3【创建支付分订单】#http应答不成功  statusCode: {},req: {}",response.getStatusLine().getStatusCode(),JSON.toJSONString(req));
                ErrorInfo errorInfo = GsonUtil.getGson().fromJson(jsonResBody,ErrorInfo.class);
                String message = Objects.nonNull(errorInfo) && StringUtils.isNotBlank(errorInfo.getMessage()) ? COLON + errorInfo.getMessage() : EMPTY;
                throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(),"创建支付分订单" + message);
            }
            // 构建应答体
            WxPayScoreRes wxPayScoreResult =  GsonUtil.getGson().fromJson(jsonResBody, WxPayScoreRes.class);
            return WechatV3OrderPaySupport.assembleCreateCreditOrderRsp(wxPayScoreResult, wechatProperty);
        } catch (Exception e) {
            log.error("微信v3【创建支付分订单】#未知异常 ",e);
            throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(),"未知异常");
        }
    }

    @Override
    public CreditOrderCancelRsp cancelCreditOrder(CreditOrderCancelReq req) {
        // 获取httpClient客户端
        CloseableHttpClient httpClient = wechatV3BeanFactory.getHttpClient(req.getChannelId(), wechatProperty);
        // 发起创建支付分订单请求
        String url = WechatApiEnum.PAY_SCORE_CANCEL_ORDER.getApi().replace(PATH_OUT_ORDER_NO, req.getOutOrderNo());
        // 构建请求体
        String jsonReqBody = GsonUtil.toJson(WechatV3OrderPaySupport.assembleCancelCreditOrderReq(req, wechatProperty));
        try {
            HttpPost httpPost = buildHttpPost(url,jsonReqBody);
            log.info("微信v3【取消支付分订单】#请求参数 request: {},httpPost: {}",jsonReqBody,JSON.toJSONString(httpPost));
            CloseableHttpResponse response = httpClient.execute(httpPost);
            String jsonResBody = EntityUtils.toString(response.getEntity());
            log.info("微信v3【取消支付分订单】#响应参数 response: {}",jsonResBody);
            if(!Objects.equals(response.getStatusLine().getStatusCode(), HttpStatus.SC_OK)){
                log.error("微信v3【取消支付分订单】#http应答不成功  statusCode: {},req: {}",response.getStatusLine().getStatusCode(),JSON.toJSONString(req));
                ErrorInfo errorInfo = GsonUtil.getGson().fromJson(jsonResBody,ErrorInfo.class);
                String message = Objects.nonNull(errorInfo) && StringUtils.isNotBlank(errorInfo.getMessage()) ? COLON + errorInfo.getMessage() : EMPTY;
                throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(),"取消支付分订单" + message);
            }
            // 构建应答体
            WxPayScoreRes wxPayScoreResult =  GsonUtil.getGson().fromJson(jsonResBody, WxPayScoreRes.class);
            return WechatV3OrderPaySupport.assembleCancelCreditOrderRsp(req,wxPayScoreResult);
        } catch (Exception e) {
            log.error("微信v3【取消支付分订单】#未知异常 ",e);
            throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(),"未知异常");
        }
    }

    @Override
    public CreditOrderCompleteRsp completeCreditOrder(CreditOrderCompleteReq req) {
        // 获取httpClient客户端
        CloseableHttpClient httpClient = wechatV3BeanFactory.getHttpClient(req.getChannelId(), wechatProperty);
        String url = WechatApiEnum.PAY_SCORE_COMPLETE_ORDER.getApi().replace(PATH_OUT_ORDER_NO, req.getOutOrderNo());
        // 构建请求体
        String jsonReqBody = GsonUtil.toJson(WechatV3OrderPaySupport.assembleCompleteCreditOrderReq(req, wechatProperty));
        try {
            HttpPost httpPost = buildHttpPost(url,jsonReqBody);
            log.info("微信v3【完结支付分订单】#请求参数 request: {},httpPost: {}",jsonReqBody,JSON.toJSONString(httpPost));
            CloseableHttpResponse response = httpClient.execute(httpPost);
            String jsonResBody = EntityUtils.toString(response.getEntity());
            log.info("微信v3【完结支付分订单】#响应参数 response: {}",jsonResBody);
            if(!Objects.equals(response.getStatusLine().getStatusCode(), HttpStatus.SC_OK)){
                log.error("微信v3【完结支付分订单】#http应答不成功  statusCode: {},req: {}",response.getStatusLine().getStatusCode(),JSON.toJSONString(req));
                ErrorInfo errorInfo = GsonUtil.getGson().fromJson(jsonResBody,ErrorInfo.class);
                String message = Objects.nonNull(errorInfo) && StringUtils.isNotBlank(errorInfo.getMessage()) ? COLON + errorInfo.getMessage() : EMPTY;
                throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(),"完结支付分订单" + message);
            }
            // 构建应答体
            WxPayScoreRes wxPayScoreResult =  GsonUtil.getGson().fromJson(jsonResBody, WxPayScoreRes.class);
            return WechatV3OrderPaySupport.assembleCompleteCreditOrderRsp(req,wxPayScoreResult);
        } catch (Exception e) {
            log.error("微信v3【完结支付分订单】#未知异常 ",e);
            throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(),"未知异常");
        }
    }

    @Override
    public CreditOrderModifyRsp modifyCreditOrder(CreditOrderModifyReq req) {
        // 获取httpClient客户端
        CloseableHttpClient httpClient = wechatV3BeanFactory.getHttpClient(req.getChannelId(), wechatProperty);
        String url = WechatApiEnum.PAY_SCORE_MODIFY_ORDER.getApi().replace(PATH_OUT_ORDER_NO, req.getOutOrderNo());
        // 构建请求体
        String jsonReqBody = GsonUtil.toJson(WechatV3OrderPaySupport.assembleModifyCreditOrderReq(req, wechatProperty));
        try {
            HttpPost httpPost = buildHttpPost(url,jsonReqBody);
            log.info("微信v3【修改支付分订单】#请求参数 request: {},httpPost: {}",jsonReqBody,JSON.toJSONString(httpPost));
            CloseableHttpResponse response = httpClient.execute(httpPost);
            String jsonResBody = EntityUtils.toString(response.getEntity());
            log.info("微信v3【修改支付分订单】#响应参数 response: {}",jsonResBody);
            if(!Objects.equals(response.getStatusLine().getStatusCode(), HttpStatus.SC_OK)){
                log.error("微信v3【修改支付分订单】#http应答不成功  statusCode: {},req: {}",response.getStatusLine().getStatusCode(),JSON.toJSONString(req));
                ErrorInfo errorInfo = GsonUtil.getGson().fromJson(jsonResBody,ErrorInfo.class);
                String message = Objects.nonNull(errorInfo) && StringUtils.isNotBlank(errorInfo.getMessage()) ? COLON + errorInfo.getMessage() : EMPTY;
                throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(),"修改支付分订单" + message);
            }
            // 构建应答体
            WxPayScoreRes wxPayScoreResult =  GsonUtil.getGson().fromJson(jsonResBody, WxPayScoreRes.class);
            return WechatV3OrderPaySupport.assembleModifyCreditOrderRsp(req,wxPayScoreResult);
        } catch (Exception e) {
            log.error("微信v3【修改支付分订单】#未知异常 ",e);
            throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(),"未知异常");
        }
    }

    @Override
    public CreditOrderJumpRsp jumpCreditOrder(CreditOrderJumpReq req) {
        Map<String, String> extraData = new HashMap<>();
        extraData.put(OUT_ORDER_NO, req.getOutOrderNo());
        extraData.put(TIMESTAMP, String.valueOf((new Date()).getTime() / 1000));
        extraData.put(NONCE_STR, NonceUtil.createNonce(20));
        extraData.put(SIGN_TYPE, WXPayConstants.HMACSHA256);
        extraData.put(SERVICE_ID, wechatProperty.getServiceId());
        extraData.put(MCH_ID, wechatProperty.getMchId());
        try {
            extraData.put(WXPayConstants.FIELD_SIGN, WXPayUtil.generateSignature(extraData, wechatProperty.getMchKey(), WXPayConstants.SignType.HMACSHA256));
            return new CreditOrderJumpRsp(extraData);
        } catch (Exception e) {
            log.error("微信v3【小程序调起支付分-订单详情】#未知异常 req: {}", JSON.toJSONString(req), e);
            throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR);
        }
    }

    @Override
    public CreditOrderQueryRsp queryCreditOrder(CreditOrderQueryReq req) {
        // 获取httpClient客户端
        CloseableHttpClient httpClient = wechatV3BeanFactory.getHttpClient(req.getChannelId(), wechatProperty);
        String url = WechatApiEnum.PAY_SCORE_QUERY_ORDER.getApi();
        // 发起退款请求
        try {
            HttpGet httpGet = buildHttpGet(WechatV3OrderPaySupport.assembleCreditOrderQueryReq(req, wechatProperty,url).build());
            log.info("微信v3【查询支付分订单】#请求参数 request: {}",JSON.toJSONString(httpGet));
            CloseableHttpResponse response = httpClient.execute(httpGet);
            String jsonResBody = EntityUtils.toString(response.getEntity());
            log.info("微信v3【查询支付分订单】#响应参数 response: {}",jsonResBody);
            if(!Objects.equals(response.getStatusLine().getStatusCode(), HttpStatus.SC_OK)){
                log.error("微信v3【查询支付分订单】#http应答不成功  statusCode: {},req: {}",response.getStatusLine().getStatusCode(),JSON.toJSONString(req));
                ErrorInfo errorInfo = GsonUtil.getGson().fromJson(jsonResBody,ErrorInfo.class);
                String message = Objects.nonNull(errorInfo) && StringUtils.isNotBlank(errorInfo.getMessage()) ? COLON + errorInfo.getMessage() : EMPTY;
                throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(),"查询支付分订单" + message);
            }
            // 构建应答体
            WxPayScoreRes result = GsonUtil.getGson().fromJson(jsonResBody, WxPayScoreRes.class);
            return WechatV3OrderPaySupport.assembleCreditOrderQueryRsp(result);
        } catch (IOException | URISyntaxException e) {
            log.error("微信v3【查询支付分订单】#未知异常 ",e);
            throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(),"未知异常");
        }
    }

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.WECHAT_CREDIT);
    }
}
