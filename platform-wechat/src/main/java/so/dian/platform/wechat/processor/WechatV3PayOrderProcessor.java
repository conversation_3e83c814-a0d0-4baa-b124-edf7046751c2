package so.dian.platform.wechat.processor;

import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.pay.req.*;
import com.chargebolt.hera.client.dto.pay.rsp.*;
import com.chargebolt.hera.client.enums.PaywayEnum;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import com.google.common.collect.Lists;
import com.wechat.pay.java.core.util.GsonUtil;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.PayOrderProcessor;
import so.dian.platform.wechat.common.enums.WechatApiEnum;
import so.dian.platform.wechat.common.enums.WechatOverseaApiEnum;
import so.dian.platform.wechat.config.WechatProperty;
import so.dian.platform.wechat.dto.rsp.ErrorInfo;
import so.dian.platform.wechat.dto.rsp.OrderInfoQueryRes;
import so.dian.platform.wechat.dto.rsp.RefundQueryRes;
import so.dian.platform.wechat.factory.WechatV3BeanFactory;
import so.dian.platform.wechat.support.WechatV3OrderPaySupport;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URISyntaxException;
import java.security.PrivateKey;
import java.util.List;
import java.util.Objects;

import static so.dian.platform.common.constants.BusinessConstants.*;

/**
 * 微信v3版本功能接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WechatV3PayOrderProcessor extends WechatProcessor implements PayOrderProcessor {

    @Resource
    private WechatProperty wechatProperty;

    @Resource
    private WechatV3BeanFactory wechatV3BeanFactory;


    /**
     * 即时支付-预下单
     * 接口文档：
     * 普通商户模式：https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_5_1.shtml
     * 服务商模式：https://pay.weixin.qq.com/wiki/doc/apiv3_partner/apis/chapter4_5_1.shtml
     *
     * @param req
     * @return
     */
    @Override
    public OrderPayRsp payOrder(OrderPayReq req) {
        if (!wechatProperty.getSupportedCurrencies().contains(req.getCurrency())){
            throw new HeraBizException(HeraBizErrorCodeEnum.UNSUPPORTED_CURRENCY);
        }
        // 获取httpClient客户端
        CloseableHttpClient httpClient = wechatV3BeanFactory.getHttpClient(req.getChannelId(), wechatProperty);
        // 构建请求体
        String jsonReqBody = GsonUtil.toJson(WechatV3OrderPaySupport.assembleOrderPayReq(req, wechatProperty));
        try {
            HttpPost httpPost = buildHttpPost(WechatOverseaApiEnum.MINI_JSAPI.getApi(), jsonReqBody);
            // 发起下单请求
            log.info("微信v3【JSAPI下单】#请求参数 request: {},httpPost: {}", jsonReqBody, JSON.toJSONString(httpPost));
            CloseableHttpResponse response = httpClient.execute(httpPost);
            String jsonResBody = EntityUtils.toString(response.getEntity());
            log.info("微信v3【JSAPI下单】#响应参数 response: {}", jsonResBody);
            //jsonResBody = "{\"prepay_id\":\"wx26112221580621e9b071c00d9e093b0000\"}";
            if (!Objects.equals(response.getStatusLine().getStatusCode(), HttpStatus.SC_OK)) {
                log.error("微信v3【JSAPI下单】#http应答不成功  statusCode: {},req: {}", response.getStatusLine().getStatusCode(), JSON.toJSONString(req));
                ErrorInfo errorInfo = GsonUtil.getGson().fromJson(jsonResBody, ErrorInfo.class);
                String message = Objects.nonNull(errorInfo) && StringUtils.isNotBlank(errorInfo.getMessage()) ? COLON + errorInfo.getMessage() : EMPTY;
                throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(), "预下单" + message);
            }
            // 生成支付签名并构建应答体
            PrepayResponse prepayResponse = GsonUtil.getGson().fromJson(jsonResBody, PrepayResponse.class);
            PrivateKey privateKey = wechatV3BeanFactory.getPrivateKey(req.getChannelId(), wechatProperty);
            return WechatV3OrderPaySupport.assembleOrderPayRsp(req, prepayResponse, wechatProperty, privateKey);
        } catch (IOException e) {
            log.error("微信v3【JSAPI下单】#未知异常 ", e);
            throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(), "未知异常");
        }

    }

    /**
     * 即时支付-订单关闭
     * 接口文档：
     * 普通商户模式：https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_5_3.shtml
     * 服务商模式：https://pay.weixin.qq.com/wiki/doc/apiv3_partner/apis/chapter4_5_3.shtml
     *
     * @param req
     * @return
     */
    @Override
    public OrderCloseRsp closeOrder(OrderCloseReq req) {
        // 获取httpClient客户端
        CloseableHttpClient httpClient = wechatV3BeanFactory.getHttpClient(req.getChannelId(), wechatProperty);
        String url = WechatApiEnum.MINI_ORDER_CLOSE.getApi().replace(PATH_OUT_TRADE_NO, req.getTradeNo());
        // 构建请求体
        String jsonReqBody = GsonUtil.toJson(WechatV3OrderPaySupport.assembleCloseOrderReq(req, wechatProperty));
        try {
            HttpPost httpPost = buildHttpPost(url, jsonReqBody);
            // 发起关单请求
            log.info("微信v3【订单关闭】#请求参数 request: {},httpPost: {},isPartner: {}", jsonReqBody, JSON.toJSONString(httpPost));
            CloseableHttpResponse response = httpClient.execute(httpPost);
            log.info("微信v3【订单关闭】#响应参数 response: {}", JSON.toJSONString(response));
            if (!Objects.equals(response.getStatusLine().getStatusCode(), HttpStatus.SC_NO_CONTENT)) {
                String jsonResBody = EntityUtils.toString(response.getEntity());
                log.error("微信v3【订单关闭】#http应答不成功  statusCode: {},req: {},jsonResBody: {}", response.getStatusLine().getStatusCode(), JSON.toJSONString(req), jsonResBody);
                ErrorInfo errorInfo = GsonUtil.getGson().fromJson(jsonResBody, ErrorInfo.class);
                String message = Objects.nonNull(errorInfo) && StringUtils.isNotBlank(errorInfo.getMessage()) ? COLON + errorInfo.getMessage() : EMPTY;
                // 订单已关闭的情况，返回成功
                if (Objects.nonNull(errorInfo) && Objects.equals(errorInfo.getCode(), WECHAT_CODE_ORDER_CLOSED)) {
                    return WechatV3OrderPaySupport.assembleCloseOrderRsp(req);
                }
                throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(), "订单关闭" + message);
            }
            // 构建应答体
            return WechatV3OrderPaySupport.assembleCloseOrderRsp(req);
        } catch (IOException e) {
            log.error("微信v3【订单关闭】#未知异常 ", e);
            throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(), "未知异常");
        }
    }

    /**
     * 即时支付-订单信息查询
     * 接口文档：
     * 普通商户模式：https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_5_2.shtml
     * 服务商模式：https://pay.weixin.qq.com/wiki/doc/apiv3_partner/apis/chapter4_5_2.shtml
     *
     *
     * 推荐使用{@link WechatPayResultProcessor#orderPayQuery(String)}
     * @param req
     * @return
     */
    @Deprecated
    @Override
    public OrderPayQueryRsp queryPayOrder(OrderPayQueryReq req) {
        // 获取httpClient客户端
        CloseableHttpClient httpClient = wechatV3BeanFactory.getHttpClient(req.getChannelId(), wechatProperty);
        try {
            String url = WechatApiEnum.MINI_ORDER_QUERY_BY_OUT_TRADE_NO.getApi().replace(PATH_OUT_TRADE_NO, req.getTradeNo());
            // 服务商模式与普通商户模式参数不同
            HttpGet httpGet = buildHttpGet(WechatV3OrderPaySupport.assembleOrderQueryReq(req, wechatProperty, url).build());
            log.info("微信v3【查询订单】#请求参数 request: {},isPartner: {}", JSON.toJSONString(httpGet));
            CloseableHttpResponse response = httpClient.execute(httpGet);
            String jsonResBody = EntityUtils.toString(response.getEntity());
            log.info("微信v3【查询订单】#响应参数 response: {}", jsonResBody);
            if (!Objects.equals(response.getStatusLine().getStatusCode(), HttpStatus.SC_OK)) {
                log.error("微信v3【查询订单】#http应答不成功  statusCode: {},req: {}", response.getStatusLine().getStatusCode(), JSON.toJSONString(req));
                ErrorInfo errorInfo = GsonUtil.getGson().fromJson(jsonResBody, ErrorInfo.class);
                String message = Objects.nonNull(errorInfo) && StringUtils.isNotBlank(errorInfo.getMessage()) ? COLON + errorInfo.getMessage() : EMPTY;
                throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(), "查询订单" + message);
            }
            // 构建应答体
            OrderInfoQueryRes orderInfo = GsonUtil.getGson().fromJson(jsonResBody, OrderInfoQueryRes.class);
            OrderPayQueryRsp orderPayQueryRsp = WechatV3OrderPaySupport.assembleOrderQueryRsp(req, orderInfo);
            orderPayQueryRsp.setContent(jsonResBody);
            return orderPayQueryRsp;
        } catch (IOException | URISyntaxException e) {
            log.error("微信v3【查询订单】#未知异常 ", e);
            throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(), "未知异常");
        }
    }

    /**
     * 即时支付-订单退款申请
     * 接口文档：
     * 普通商户模式：https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_5_9.shtml
     * 服务商模式：https://pay.weixin.qq.com/wiki/doc/apiv3_partner/apis/chapter4_5_9.shtml
     *
     * @param req
     * @return
     */
    @Override
    public OrderRefundRsp refundOrder(OrderRefundReq req) {
        // 获取httpClient客户端
        CloseableHttpClient httpClient = wechatV3BeanFactory.getHttpClient(req.getChannelId(), wechatProperty);
        String url = WechatOverseaApiEnum.REFUND.getApi();
        // 构建请求体
        String jsonReqBody = GsonUtil.toJson(WechatV3OrderPaySupport.assembleRefundOrderReq(req, wechatProperty));
        try {
            HttpPost httpPost = buildHttpPost(url, jsonReqBody);
            // 发起退款请求
            log.info("微信v3【订单退款申请】#请求参数 request: {},httpPost: {},isPartner: {}", jsonReqBody, JSON.toJSONString(httpPost));
            CloseableHttpResponse response = httpClient.execute(httpPost);
            String jsonResBody = EntityUtils.toString(response.getEntity());
            log.info("微信v3【订单退款申请】#响应参数 response: {}", jsonResBody);
            if (!Objects.equals(response.getStatusLine().getStatusCode(), HttpStatus.SC_OK)) {
                log.error("微信v3【订单退款申请】#http应答不成功  statusCode: {},req: {}", response.getStatusLine().getStatusCode(), JSON.toJSONString(req));
                ErrorInfo errorInfo = GsonUtil.getGson().fromJson(jsonResBody, ErrorInfo.class);
                String message = Objects.nonNull(errorInfo) && StringUtils.isNotBlank(errorInfo.getMessage()) ? COLON + errorInfo.getMessage() : EMPTY;
                throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(), "订单退款申请" + message);
            }

            // 构建应答体
            return WechatV3OrderPaySupport.assembleRefundOrderRsp();
        } catch (IOException e) {
            log.error("微信v3【订单退款申请】#未知异常 ", e);
            throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(), "未知异常");
        }
    }

    /**
     * 即时支付-订单退款查询
     * 接口文档：
     * 普通商户模式：https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_5_10.shtml
     * 服务商模式：https://pay.weixin.qq.com/wiki/doc/apiv3_partner/apis/chapter4_5_10.shtml
     *
     *
     * 推荐使用{@link WechatRefundResultProcessor#orderRefundQuery(String)}
     * @param req
     * @return
     */
    @Deprecated
    @Override
    public OrderRefundQueryRsp queryRefundOrder(OrderRefundQueryReq req) {
        // 获取httpClient客户端
        CloseableHttpClient httpClient = wechatV3BeanFactory.getHttpClient(req.getChannelId(), wechatProperty);
        // 发起退款请求
        String url = WechatApiEnum.MINI_ORDER_REFUND_QUERY.getApi().replace(OUT_REFUND_NO, req.getRefundOrderNo());
        try {
            HttpGet httpGet = buildHttpGet(WechatV3OrderPaySupport.assembleRefundOrderQueryReq(req, url).build());
            log.info("微信v3【订单退款查询】#请求参数 request: {}", JSON.toJSONString(httpGet));
            CloseableHttpResponse response = httpClient.execute(httpGet);
            String jsonResBody = EntityUtils.toString(response.getEntity());
            log.info("微信v3【订单退款查询】#响应参数 response: {}", jsonResBody);
            if (!Objects.equals(response.getStatusLine().getStatusCode(), HttpStatus.SC_OK)) {
                log.error("微信v3【订单退款查询】#http应答不成功  statusCode: {},req: {}", response.getStatusLine().getStatusCode(), JSON.toJSONString(req));
                ErrorInfo errorInfo = GsonUtil.getGson().fromJson(jsonResBody, ErrorInfo.class);
                String message = Objects.nonNull(errorInfo) && StringUtils.isNotBlank(errorInfo.getMessage()) ? COLON + errorInfo.getMessage() : EMPTY;
                // 订单不存在
                if (Objects.nonNull(errorInfo) && Objects.equals(errorInfo.getCode(), WECHAT_CODE_RESOURCE_NOT_EXISTS)) {
                    return WechatV3OrderPaySupport.assembleRefundOrderQueryErrRsp(errorInfo);
                }
                throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(), "订单退款查询" + message);
            }
            // 构建应答体
            RefundQueryRes refundApplyRes = GsonUtil.getGson().fromJson(jsonResBody, RefundQueryRes.class);
            OrderRefundQueryRsp queryRsp = WechatV3OrderPaySupport.assembleRefundOrderQueryRsp(req, refundApplyRes);
            queryRsp.setContent(jsonResBody);
            return queryRsp;
        } catch (IOException | URISyntaxException e) {
            log.error("微信v3【订单退款查询】#未知异常 ", e);
            throw new HeraBizException(HeraBizErrorCodeEnum.WXRSP_ERROR.getCode(), "未知异常");
        }
    }

    @Override
    public List<PaywayEnum> getPayway() {
//        return Lists.newArrayList();
        return Lists.newArrayList(PaywayEnum.WECHAT_MINIPROGRAM);
    }

}
