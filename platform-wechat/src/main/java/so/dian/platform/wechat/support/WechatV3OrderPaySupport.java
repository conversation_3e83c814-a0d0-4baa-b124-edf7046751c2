package so.dian.platform.wechat.support;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.credit.req.*;
import com.chargebolt.hera.client.dto.credit.rsp.*;
import com.chargebolt.hera.client.dto.pay.req.*;
import com.chargebolt.hera.client.dto.pay.rsp.*;
import com.chargebolt.hera.client.dto.platPay.rsp.WxPaymentDetailQueryRsp;
import com.chargebolt.hera.client.dto.wechat.v3.response.*;
import so.dian.platform.common.exception.HeraBizException;
import so.dian.platform.common.enums.HeraBizErrorCodeEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.chargebolt.hera.client.enums.status.RefundStatus;
import com.github.binarywang.wxpay.bean.payscore.PostDiscount;
import com.github.binarywang.wxpay.bean.payscore.PostPayment;
import com.github.wxpay.sdk.WXPayConstants;
import com.github.wxpay.sdk.WXPayUtil;
import com.google.common.collect.Maps;
import com.wechat.pay.java.core.util.NonceUtil;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.util.CollectionUtils;
import so.dian.platform.common.constants.BusinessConstants;
import so.dian.platform.wechat.config.WechatProperty;
import so.dian.platform.wechat.dto.notify.WechatPayNotifyBody;
import so.dian.platform.wechat.dto.req.*;
import so.dian.platform.wechat.dto.rsp.ErrorInfo;
import so.dian.platform.wechat.dto.rsp.OrderInfoQueryRes;
import so.dian.platform.wechat.dto.rsp.RefundQueryRes;
import so.dian.platform.wechat.dto.rsp.WxPayScoreRes;

import java.net.URISyntaxException;
import java.security.*;
import java.util.*;

import static so.dian.platform.common.constants.BusinessConstants.*;

/**
 * <AUTHOR>
 */
@Slf4j
public class WechatV3OrderPaySupport {

    /**
     * 微信v3 下单请求参数封装
     * @param req
     * @param wechatProperty
     * @return
     */
    public static PrepayOrderReq assembleOrderPayReq(OrderPayReq req, WechatProperty wechatProperty) {
        String notifyUrl = wechatProperty.getNotifyUrl()
                .replace(PATH_APPLICATION_ID, req.getApplicationId())
                .replace(PATH_CHANNEL_ID, String.valueOf(req.getChannelId()));
        PrepayOrderReq request = new PrepayOrderReq();
        request.setTradeType("JSAPI");
        request.setMerchantCategoryCode("7394");
        PrepayOrderReq.Payer payer = new PrepayOrderReq.Payer();
        PrepayOrderReq.Amount  amount = new PrepayOrderReq.Amount();
        request.setAppid(wechatProperty.getAppId());
        request.setMchid(wechatProperty.getMchId());
        payer.setOpenid(req.getPlatOpenId());
        request.setDescription(req.getDescription());
        request.setOutTradeNo(req.getTradeNo());
        // 默认10分钟关闭订单
        int expireTime = StringUtils.isNotBlank(req.getExpireTime())?Integer.parseInt(req.getExpireTime()):10;
        request.setTimeExpire(DateUtil.format(so.dian.hera.interceptor.utils.DateUtil.addMinute(expireTime), WECHAT_TIME_FORMAT));
        request.setNotifyUrl(notifyUrl);
        amount.setTotal(req.getOrderAmount().intValue());
        amount.setCurrency(req.getCurrency());
        request.setAmount(amount);
        request.setPayer(payer);
        return request;
    }


    /**
     * 微信v3 下单响应参数封装
     * @param req
     * @param prepayResponse
     * @param wechatProperty
     * @param privateKey
     * @return
     */
    public static OrderPayRsp assembleOrderPayRsp(OrderPayReq req, PrepayResponse prepayResponse, WechatProperty wechatProperty, PrivateKey privateKey) {
        OrderPayRsp orderPayRsp = new OrderPayRsp();
        String appId = wechatProperty.getAppId();
        String timestamp = String.valueOf(System.currentTimeMillis()/1000);
        String nonceStr = NonceUtil.createNonce(20);
        String packageVal = "prepay_id="+prepayResponse.getPrepayId();
        String getSignStr = String.format("%s\n%s\n%s\n%s\n",appId,timestamp,nonceStr,packageVal);
        String signStr = sign(getSignStr,privateKey);
        Map<String, Object> paySign = new HashMap<>();
        Map<String, Object> payMap = new HashMap<>();
        payMap.put("timeStamp",timestamp);
        payMap.put("packageStr",packageVal);
//        payMap.put("package",packageVal);
        payMap.put("paySign",signStr);
        payMap.put("appId",appId);
//        payMap.put("signType","RSA");
//        payMap.put("partnerId", wechatProperty.getMchId());
//        payMap.put("prepayId",prepayResponse.getPrepayId());
        payMap.put("nonceStr",nonceStr);
        paySign.put("payMap",payMap);
        orderPayRsp.setPaySign(JSON.toJSONString(paySign));
        orderPayRsp.setOrderNo(req.getOrderNo());
        orderPayRsp.setPayTradeNo(req.getTradeNo());
        orderPayRsp.setUserId(req.getUserId());
        orderPayRsp.setBizType(req.getBizType());
        return orderPayRsp;
    }

    /**
     * 签名
     * @param string
     * @param privateKey
     * @return
     */
    public static String sign(String string, PrivateKey privateKey) {
        try {
            Signature sign = Signature.getInstance("SHA256withRSA");
            sign.initSign(privateKey);
            sign.update(string.getBytes());
            return Base64.getEncoder().encodeToString(sign.sign());
        } catch (NoSuchAlgorithmException | SignatureException | InvalidKeyException exception) {
            log.error("微信v3【充电桩】#签名#未知异常",exception);
            throw new HeraBizException(HeraBizErrorCodeEnum.GEN_SIGN_ERROR);
        }
    }

    /**
     * v3支付订单查询请求参数封装
     * @param req
     * @param wechatProperty
     * @param url
     * @return
     * @throws URISyntaxException
     */
    public static URIBuilder assembleOrderQueryReq(OrderPayQueryReq req, WechatProperty wechatProperty, String url) throws URISyntaxException {
        URIBuilder uriBuilder = new URIBuilder(url);
        uriBuilder.setParameter(MCHID, wechatProperty.getMchId());
        return uriBuilder;
    }

    /**
     * v3支付订单查询响应参数封装
     * @param orderInfo
     * @return
     */
    public static OrderPayQueryRsp assembleOrderQueryRsp(OrderPayQueryReq req,OrderInfoQueryRes orderInfo) {
        OrderPayQueryRsp queryRsp = new OrderPayQueryRsp();
        queryRsp.setThirdPayTradeNo(orderInfo.getTransactionId());
        queryRsp.setPayTradeNo(req.getTradeNo());
        queryRsp.setOrderNo(req.getOrderNo());
        if(Objects.nonNull(orderInfo.getAmount()) && Objects.nonNull(orderInfo.getAmount().getTotal())){
            queryRsp.setTotalFee(orderInfo.getAmount().getTotal());
        }
        if(StringUtils.isNotBlank(orderInfo.getSuccessTime())){
            queryRsp.setPayTime(DateUtil.parse(orderInfo.getSuccessTime(), WECHAT_TIME_FORMAT));
        }
        WxPaymentDetailQueryRsp detailQueryRsp = new WxPaymentDetailQueryRsp();
        if(Objects.nonNull(orderInfo.getAmount()) && Objects.nonNull(orderInfo.getAmount().getPayerTotal())){
            detailQueryRsp.setCashFee(orderInfo.getAmount().getPayerTotal().longValue());
            if(!CollectionUtils.isEmpty(orderInfo.getPromotionDetail())){
                List<WxPaymentDetailQueryRsp.Coupon> coupons = new ArrayList<>();
                Long couponTotalFee = 0L;
                for (WechatPayNotifyBody.PromotionDetail item :  orderInfo.getPromotionDetail()){
                    WxPaymentDetailQueryRsp.Coupon coupon = new WxPaymentDetailQueryRsp.Coupon();
                    coupon.setCouponFee(item.getAmount().longValue());
                    coupon.setCouponType(item.getType());
                    coupon.setCouponId(item.getCouponId());
                    coupons.add(coupon);
                    couponTotalFee += item.getAmount().longValue();
                }
                detailQueryRsp.setSettlementTotalFee(detailQueryRsp.getCashFee() - couponTotalFee);
                detailQueryRsp.setCouponTotalFee(couponTotalFee);
                detailQueryRsp.setCouponCount(orderInfo.getPromotionDetail().size());
                detailQueryRsp.setCoupons(coupons);
            }
        }
        queryRsp.setData(JSON.toJSONString(detailQueryRsp));
        queryRsp.setStatus(convertPayStatus(orderInfo.getTradeState()));
        return queryRsp;
    }

    /**
     * 订单状态枚举值映射
     * @param tradeState
     * @return
     */
    private static String convertPayStatus(String tradeState){
        switch (tradeState){
            case "SUCCESS":
                return PayStatus.PAID.getKey();
            case "REFUND":
                return PayStatus.REFUNDED.getKey();
            case "NOTPAY":
            case "USERPAYING":
                return PayStatus.INIT.getKey();
            default:
                return PayStatus.FAIL.getKey();
        }
    }

    /**
     *  微信v3 关单请求参数封装
     * @param wechatProperty
     * @return
     */
    public static CloseOrderReq assembleCloseOrderReq(OrderCloseReq req, WechatProperty wechatProperty) {
        CloseOrderReq request = new CloseOrderReq();
        request.setMchid(wechatProperty.getMchId());
        return request;
    }

    /**
     *  微信v3 关单响应参数封装
     * @param req
     * @return
     */
    public static OrderCloseRsp assembleCloseOrderRsp(OrderCloseReq req) {
        OrderCloseRsp response = new OrderCloseRsp();
        response.setOrderNo(req.getOrderNo());
        response.setPayTradeNo(req.getTradeNo());
        return response;
    }

    /**
     *  微信v3 订单退款请求参数封装
     * @param wechatProperty
     * @return
     */
    public static RefundApplyReq assembleRefundOrderReq(OrderRefundReq req, WechatProperty wechatProperty) {
        RefundApplyReq refundReq = new RefundApplyReq();
        refundReq.setMchid(wechatProperty.getMchId());
        refundReq.setAppid(wechatProperty.getAppId());
        refundReq.setOutTradeNo(req.getTradeNo());
        refundReq.setOutRefundNo(req.getRefundNo());
        refundReq.setReason(req.getRefundReason());
        String notifyUrl = wechatProperty.getNotifyUrl()
                .replace(PATH_APPLICATION_ID, req.getApplicationId())
                .replace(PATH_CHANNEL_ID, String.valueOf(req.getChannelId()));
        refundReq.setNotifyUrl(notifyUrl);
        RefundApplyReq.Amount amount = new RefundApplyReq.Amount();
        amount.setRefund(req.getRefundAmount().intValue());
        amount.setCurrency(req.getCurrency());
        amount.setTotal(req.getOriOrderAmount().intValue());
        refundReq.setAmount(amount);
        return refundReq;
    }

    /**
     *  微信v3 订单退款响应参数封装
     * @return
     */
    public static OrderRefundRsp assembleRefundOrderRsp() {
        OrderRefundRsp refundRsp = new OrderRefundRsp();
        return refundRsp;
    }

    /**
     * 微信v3 订单退款查询请求参数封装
     * @param req
     * @param url
     * @return
     * @throws URISyntaxException
     */
    public static URIBuilder assembleRefundOrderQueryReq(OrderRefundQueryReq req, String url) throws URISyntaxException {
        URIBuilder uriBuilder = new URIBuilder(url);
        return uriBuilder;
    }

    /**
     *  微信v3 订单退款查询响应参数封装
     * @return
     */
    public static OrderRefundQueryRsp assembleRefundOrderQueryRsp(OrderRefundQueryReq req,RefundQueryRes refundApplyRes) {
        OrderRefundQueryRsp queryRsp = new OrderRefundQueryRsp();
        queryRsp.setRefundOrderNo(req.getRefundOrderNo());
        queryRsp.setPayRefundNo(req.getRefundNo());
        queryRsp.setThirdRefundNo(refundApplyRes.getRefundId());
        queryRsp.setRefundAccount(refundApplyRes.getFundsAccount());
        RefundQueryRes.Amount amount = refundApplyRes.getAmount();
        queryRsp.setPayAmount(amount.getPayerTotal());
        queryRsp.setRefundAmount(amount.getRefund());
        if(StringUtils.isNotBlank(refundApplyRes.getSuccessTime())){
            queryRsp.setRefundTime(DateUtil.parse(refundApplyRes.getSuccessTime(), WECHAT_TIME_FORMAT));
        }
        queryRsp.setStatus(convertRefundStatus(refundApplyRes.getStatus()));
        return queryRsp;
    }

    /**
     * 订单状态枚举值映射
     * @param status
     * @return
     */
    private static String convertRefundStatus(String status){
        switch (status){
            case "SUCCESS":
                return RefundStatus.REFUNDED.getKey();
            case "PROCESSING":
                return RefundStatus.REFUNDING.getKey();
            default:
                return RefundStatus.FAIL.getKey();
        }
    }

    /**
     *  微信v3 订单退款查询(订单不存在)响应参数封装
     * @return
     */
    public static OrderRefundQueryRsp assembleRefundOrderQueryErrRsp(ErrorInfo errorInfo) {
        OrderRefundQueryRsp queryRsp = new OrderRefundQueryRsp();
        queryRsp.setStatus(PayStatus.FAIL.getKey());
        return queryRsp;
    }

    /**
     *  微信v3 创建支付分订单请求参数封装
     * @return
     */
    public static PayScoreCreateReq assembleCreateCreditOrderReq(CreditOrderCreateReq req, WechatProperty wechatProperty) {
        PayScoreCreateReq request = new PayScoreCreateReq();
        request.setOutOrderNo(req.getOutOrderNo());
        request.setAppid(wechatProperty.getAppId());
        request.setServiceId(wechatProperty.getServiceId());
        request.setServiceIntroduction(req.getServiceIntroduction());
        request.setTimeRange(JSON.parseObject(JSON.toJSONString(req.getTimeRange()), PayScoreCreateReq.TimeRange.class));
        request.setRiskFund(JSON.parseObject(JSON.toJSONString(req.getRiskFund()), PayScoreCreateReq.RiskFund.class));
        request.setOpenid(req.getOpenid());
        request.setNeedUserConfirm(req.getNeedUserConfirm());
        request.setNotifyUrl(wechatProperty.getPayScoreNotifyUrl().replace(PATH_APPLICATION_ID, req.getApplicationId()).replace(PATH_CHANNEL_ID, String.valueOf(req.getChannelId())));
        return request;
    }


    /**
     *  微信v3 创建支付分订单响应参数封装
     * @return
     */
    public static CreditOrderCreateRsp assembleCreateCreditOrderRsp(WxPayScoreRes result, WechatProperty wechatProperty) throws Exception {
        CreditOrderCreateRsp rsp = new CreditOrderCreateRsp();
        rsp.setStateDescription(result.getStateDescription());
        rsp.setState(result.getState());
        rsp.setOrderId(result.getOrderId());
        rsp.setOutOrderNo(result.getOutOrderNo());

        WechatCreditCreateRspDto rspDto = new WechatCreditCreateRspDto();
        rspDto.setState(result.getState());
        rspDto.setState_description(result.getStateDescription());
        rspDto.setOrder_id(result.getOrderId());
        rspDto.setOut_order_no(result.getOutOrderNo());

        WechatCreditJumpParamRspDto jumpParam = new WechatCreditJumpParamRspDto();
        jumpParam.setPackage(result.getPackageX());
        jumpParam.setMch_id(result.getMchid());
        jumpParam.setTimestamp(String.valueOf(System.currentTimeMillis()));
        jumpParam.setNonce_str(NonceUtil.createNonce(20));
        jumpParam.setSign_type(WXPayConstants.HMACSHA256);

        Map<String, String> map = Maps.newTreeMap();
        map.put(MCH_ID, jumpParam.getMch_id());
        map.put(PACKAGE, jumpParam.getPackage());
        map.put(TIMESTAMP, jumpParam.getTimestamp());
        map.put(NONCE_STR, jumpParam.getNonce_str());
        map.put(SIGN_TYPE, jumpParam.getSign_type());
        jumpParam.setSign(WXPayUtil.generateSignature(map, wechatProperty.getMchKey(), WXPayConstants.SignType.HMACSHA256));
        jumpParam.setPlatAppId(wechatProperty.getPlatAppId());
        jumpParam.setPlatPath(wechatProperty.getPlatPath());
        rsp.setJumpParam(jumpParam);
        rspDto.setJumpParam(jumpParam);
        rsp.setResult(rspDto);
        return rsp;
    }

    /**
     *  微信v3 取消支付分订单请求参数封装
     * @param req
     * @param wechatProperty
     * @return
     */
    public static PayScoreCancelReq assembleCancelCreditOrderReq(CreditOrderCancelReq req, WechatProperty wechatProperty) {
        PayScoreCancelReq cancelReq = new PayScoreCancelReq();
        cancelReq.setServiceId(wechatProperty.getServiceId());
        cancelReq.setReason(req.getReason());
        cancelReq.setAppid(wechatProperty.getAppId());
        return cancelReq;
    }

    /**
     *  微信v3 取消支付分订单响应参数封装
     * @param req
     * @param wxPayScoreResult
     * @return
     */
    public static CreditOrderCancelRsp assembleCancelCreditOrderRsp(CreditOrderCancelReq req, WxPayScoreRes wxPayScoreResult) {
        CreditOrderCancelRsp cancelRsp = new CreditOrderCancelRsp();
        cancelRsp.setOrderId(wxPayScoreResult.getOrderId());
        cancelRsp.setOutOrderNo(wxPayScoreResult.getOutOrderNo());
        WechatCreditCancelRspDto rspDto = new WechatCreditCancelRspDto();
        rspDto.setOrder_id(wxPayScoreResult.getOrderId());
        rspDto.setOut_order_no(wxPayScoreResult.getOutOrderNo());
        cancelRsp.setResult(rspDto);
        return cancelRsp;
    }

    /**
     * 微信v3 查询支付分订单请求参数封装
     * @param req
     * @param wechatProperty
     * @param url
     * @return
     * @throws URISyntaxException
     */
    public static URIBuilder assembleCreditOrderQueryReq(CreditOrderQueryReq req, WechatProperty wechatProperty, String url) throws URISyntaxException {
        URIBuilder uriBuilder = new URIBuilder(url);
        uriBuilder.setParameter(OUT_ORDER_NO, req.getOutTradeNo());
        uriBuilder.setParameter(SERVICE_ID, wechatProperty.getServiceId());
        uriBuilder.setParameter(APPID, wechatProperty.getAppId());
        return uriBuilder;
    }

    /**
     * 微信v3 查询支付分订单响应参数封装
     * @param result
     * @return
     */
    public static CreditOrderQueryRsp assembleCreditOrderQueryRsp(WxPayScoreRes result) {
        CreditOrderQueryRsp rsp = new CreditOrderQueryRsp();
        rsp.setOutOrderNo(result.getOutOrderNo());
        rsp.setServiceIntroduction(result.getServiceIntroduction());
        rsp.setState(result.getState());
        rsp.setStateDescription(result.getStateDescription());
        rsp.setPostPayments(JSON.parseArray(JSON.toJSONString(result.getPostPayments()), PostPayment.class));
        rsp.setPostDiscounts(JSON.parseArray(JSON.toJSONString(result.getPostDiscounts()), PostDiscount.class));
        rsp.setRiskFund(JSON.parseObject(JSON.toJSONString(result.getRiskFund()), Map.class));
        rsp.setTimeRange(JSON.parseObject(JSON.toJSONString(result.getTimeRange()), Map.class));
        rsp.setLocation(JSON.parseObject(JSON.toJSONString(result.getLocation()), Map.class));
        rsp.setOrderId(result.getOrderId());
        rsp.setNeedCollection(result.isNeedCollection());
        rsp.setCollection(JSON.parseObject(JSON.toJSONString(result.getCollection()), Map.class));

        WechatCreditQueryRspDto rspDto = new WechatCreditQueryRspDto();
        rspDto.setOut_order_no(rsp.getOutOrderNo());
        rspDto.setService_introduction(rsp.getServiceIntroduction());
        rspDto.setState(rsp.getState());
        rspDto.setState_description(rsp.getStateDescription());
        rspDto.setPost_payments(rsp.getPostPayments());
        rspDto.setPost_discounts(rsp.getPostDiscounts());
        rspDto.setRisk_fund(rsp.getRiskFund());
        rspDto.setTime_range(rsp.getTimeRange());
        rspDto.setLocation(rsp.getLocation());
        rspDto.setOrder_id(rsp.getOrderId());
        rspDto.setNeed_collection(rsp.getNeedCollection());
        rspDto.setCollection(rsp.getCollection());

        rsp.setResult(rspDto);
        return rsp;
    }

    /**
     * 微信v3 完结支付分订单参数封装
     * @param req
     * @param wechatProperty
     * @return
     */
    public static PayScoreCompleteReq assembleCompleteCreditOrderReq(CreditOrderCompleteReq req, WechatProperty wechatProperty) {
        PayScoreCompleteReq completeReq = new PayScoreCompleteReq();
        completeReq.setOutOrderNo(req.getOutOrderNo());
        completeReq.setServiceId(wechatProperty.getServiceId());
        completeReq.setTimeRange(JSON.parseObject(JSON.toJSONString(req.getTimeRange()), PayScoreCompleteReq.TimeRange.class));
        completeReq.setPostPayments(JSON.parseArray(JSON.toJSONString(req.getPostPayments()), PayScoreCompleteReq.PostPayment.class));
        completeReq.setPostDiscounts(JSON.parseArray(JSON.toJSONString(req.getPostDiscounts()), PayScoreCompleteReq.PostDiscount.class));
        completeReq.setTotalAmount(req.getTotalAmount().intValue());
        completeReq.setAppid(wechatProperty.getAppId());
        return completeReq;
    }

    /**
     * 微信v3 完结支付分订单响应参数封装
     * @param req
     * @param result
     * @return
     */
    public static CreditOrderCompleteRsp assembleCompleteCreditOrderRsp(CreditOrderCompleteReq req, WxPayScoreRes result) {
        CreditOrderCompleteRsp rsp = new CreditOrderCompleteRsp();
        // f服务商模式不返回数据
        rsp.setServiceIntroduction(result.getServiceIntroduction());
        rsp.setState(result.getState());
        rsp.setStateDescription(result.getStateDescription());
        rsp.setPostPayments(JSON.parseArray(JSON.toJSONString(result.getPostPayments()), PostPayment.class));
        rsp.setPostDiscounts(JSON.parseArray(JSON.toJSONString(result.getPostDiscounts()), PostDiscount.class));
        rsp.setRiskFund(JSON.parseObject(JSON.toJSONString(result.getRiskFund()), Map.class));
        rsp.setTimeRange(JSON.parseObject(JSON.toJSONString(result.getTimeRange()), Map.class));
        rsp.setLocation(JSON.parseObject(JSON.toJSONString(result.getLocation()), Map.class));
        rsp.setOrderId(result.getOrderId());
        rsp.setOutOrderNo(result.getOutOrderNo());
        rsp.setNeedCollection(result.isNeedCollection());

        WechatCreditCompleteRspDto rspDto = new WechatCreditCompleteRspDto();
        rspDto.setService_introduction(rsp.getServiceIntroduction());
        rspDto.setState(rsp.getState());
        rspDto.setState_description(rsp.getStateDescription());
        rspDto.setPost_payments(rsp.getPostPayments());
        rspDto.setPost_discounts(rsp.getPostDiscounts());
        rspDto.setRisk_fund(rsp.getRiskFund());
        rspDto.setTime_range(rsp.getTimeRange());
        rspDto.setLocation(rsp.getLocation());
        rspDto.setOrder_id(rsp.getOrderId());
        rspDto.setOut_order_no(rsp.getOutOrderNo());
        rspDto.setNeed_collection(rsp.getNeedCollection());
        rsp.setResult(rspDto);
        return rsp;
    }

    /**
     * 微信v3 修改支付分订单请求参数封装
     * @param req
     * @param wechatProperty
     * @return
     */
    public static PayScoreModifyReq assembleModifyCreditOrderReq(CreditOrderModifyReq req, WechatProperty wechatProperty) {
        PayScoreModifyReq modifyReq = new PayScoreModifyReq();
        modifyReq.setServiceId(wechatProperty.getServiceId());
        modifyReq.setOutOrderNo(req.getOutOrderNo());
        modifyReq.setPostPayments(JSON.parseArray(JSON.toJSONString(req.getPostPayments()), PayScoreModifyReq.PostPayment.class));
        modifyReq.setTotalAmount(req.getTotalAmount().intValue());
        modifyReq.setReason(req.getReason());
        modifyReq.setAppid(wechatProperty.getAppId());
        return modifyReq;
    }

    /**
     * 微信v3 修改支付分订单响应参数封装
     * @param req
     * @param result
     * @return
     */
    public static CreditOrderModifyRsp assembleModifyCreditOrderRsp(CreditOrderModifyReq req, WxPayScoreRes result) {
        CreditOrderModifyRsp rsp = new CreditOrderModifyRsp();
        rsp.setOutOrderNo(req.getOutOrderNo());
        WechatCreditChangePriceRspDto rspDto = new WechatCreditChangePriceRspDto();
        rspDto.setOut_order_no(req.getOutOrderNo());
        rsp.setResult(rspDto);
        return rsp;
    }
}
