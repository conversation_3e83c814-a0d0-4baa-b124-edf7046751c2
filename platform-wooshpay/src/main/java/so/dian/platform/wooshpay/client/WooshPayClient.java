package so.dian.platform.wooshpay.client;


import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import so.dian.platform.wooshpay.dto.notify.WooshPayWebhookNotifyDTO;
import so.dian.platform.wooshpay.dto.request.*;
import so.dian.platform.wooshpay.dto.response.*;

import java.util.Map;

@FeignClient(name = "wooshPayClient", url = "${channel.wooshpay.url}", fallbackFactory = WooshPayClient.WooshPayClientFallbackFactory.class)
public interface WooshPayClient {

    @PostMapping("/v1/webhook_endpoints")
    WooshPayWebhookEndpointDTO webhook_endpoints(@RequestBody WooshPayWebhookEndpointRequest request,
                                                 @RequestHeader Map header);
    @GetMapping("/v1/webhook_endpoints")
    Object webhook_endpoints(@RequestHeader Map header);

    @DeleteMapping("/v1/webhook_endpoints/{id}")
    Object webhook_endpoints(@PathVariable("id") String id,
                             @RequestHeader Map header);

    @PostMapping("/v1/payment_intents")
    WooshPayPaymentIntentCreateDTO paymentIntentsCreate(@RequestBody WooshPayPaymentIntentCreateRequest request,
                                                        @RequestHeader Map header);

    @PostMapping("/v1/payment_intents/{id}/capture")
    WooshPayPaymentIntentCaptureDTO paymentIntentsCapture(@PathVariable("id") String id,
                                                          @RequestBody WooshPayPaymentIntentCaptureRequest request,
                                                          @RequestHeader Map header);

    @PostMapping("/v1/payment_intents/{id}/cancel")
    WooshPayPaymentIntentCancelDTO paymentIntentsCancel(@PathVariable("id") String id,
                                                        @RequestBody WooshPayPaymentIntentCancelRequest request,
                                                        @RequestHeader Map header);

    @PostMapping("/v1/refunds")
    WooshPayRefundResultDTO refund(@RequestBody WooshPayRefundRequest request,
                                   @RequestHeader Map header);

    @GetMapping("/v1/refunds/{id}")
    WooshPayRefundResultDTO retrieveRefund(@PathVariable("id") String id,
                                           @RequestHeader Map header);


    @GetMapping("/v1/payment_intents/{id}")
    WooshPayPaymentIntentRetrieveDTO paymentIntentsRetrieve(@PathVariable("id") String id,
                                                            @RequestHeader Map header);

    @GetMapping("/v1/events/{id}")
    WooshPayWebhookNotifyDTO retrieveEvent(@PathVariable("id") String id,
                                           @RequestHeader Map header);

    @Slf4j
    @Component
    class WooshPayClientFallbackFactory implements FallbackFactory<WooshPayClient> {

        @Override
        public WooshPayClient create(Throwable cause) {
            return new WooshPayClient() {
                @Override
                public WooshPayWebhookEndpointDTO webhook_endpoints(WooshPayWebhookEndpointRequest request, Map header) {
                    log.warn("webhook_endpoints, {}", cause);
                    return null;
                }

                @Override
                public Object webhook_endpoints(Map header) {
                    log.warn("webhook_endpoints, {}", cause);
                    return null;
                }

                @Override
                public Object webhook_endpoints(String id, Map header) {
                    return null;
                }

                @Override
                public WooshPayPaymentIntentCreateDTO paymentIntentsCreate(WooshPayPaymentIntentCreateRequest request, Map header) {
                    log.warn("paymentIntentsCreate, {}", cause);
                    return null;
                }

                @Override
                public WooshPayPaymentIntentCaptureDTO paymentIntentsCapture(String id, WooshPayPaymentIntentCaptureRequest request, Map header) {
                    log.warn("paymentIntentsCapture, {}", cause);
                    return null;
                }

                @Override
                public WooshPayPaymentIntentCancelDTO paymentIntentsCancel(String id, WooshPayPaymentIntentCancelRequest request, Map header) {
                    log.warn("paymentIntentsCancel, {}", cause);
                    return null;
                }

                @Override
                public WooshPayRefundResultDTO refund(WooshPayRefundRequest request, Map header) {
                    log.warn("refund, {}", cause);
                    return null;
                }

                @Override
                public WooshPayRefundResultDTO retrieveRefund(String id, Map header) {
                    log.warn("retrieveRefund, {}", cause);
                    return null;
                }

                @Override
                public WooshPayPaymentIntentRetrieveDTO paymentIntentsRetrieve(String id, Map header) {
                    log.warn("paymentIntentsRetrieve, {}", cause);
                    return null;
                }

                @Override
                public WooshPayWebhookNotifyDTO retrieveEvent(String id, Map header) {
                    log.warn("retrieveEvent, {}", cause);
                    return null;
                }
            };
        }
    }
}
