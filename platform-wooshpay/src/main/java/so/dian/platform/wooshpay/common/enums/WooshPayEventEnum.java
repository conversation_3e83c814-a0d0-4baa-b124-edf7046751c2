package so.dian.platform.wooshpay.common.enums;

import lombok.Getter;

@Getter
public enum WooshPayEventEnum {

    PAYMENT_INTENT_CANCELED("payment_intent.canceled"),
    PAYMENT_INTENT_CREATE("payment_intent.created"),
    PAYMENT_INTENT_PAYMENT_FAILED("payment_intent.payment_failed"),
    PAYMENT_INTENT_PROCESSING("payment_intent.processing"),
    PAYMENT_INTENT_REQUIRES_ACTION("payment_intent.requires_action"),
    PAYMENT_INTENT_SUCCEEDED("payment_intent.succeeded"),
    PAYMENT_INTENT_REQUIRES_CONFIRMATION("payment_intent.requires_confirmation"),
    PAYMENT_INTENT_UPDATED("payment_intent.updated"),
    PAYMENT_INTENT_AMOUNT_CAPTURABLE_UPDATED("payment_intent.amount_capturable_updated"),
    CHARGE_REFUND_UPDATED("charge.refund.updated"),
    CHARGE_CAPTURED("charge.captured"),

    ;
    private String value;

    WooshPayEventEnum(String value) {
        this.value = value;
    }
}
