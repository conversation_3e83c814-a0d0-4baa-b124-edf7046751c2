package so.dian.platform.wooshpay.common.enums;

import lombok.Getter;

@Getter
public enum WooshPayStatusEnum {
    /**
     * 处理中
     * 预授权，支付场景
     */
    PROCESSING("processing"),
    /**
     * 成功
     * 预授权、支付、退款场景
     */
    SUCCEEDED("succeeded"),
    /**
     * 取消
     * 预授权、支付、退款场景
     */
    CANCELED("canceled"),
    /**
     * 处理中
     * 退款场景
     */
    PENDING("pending"),

    /**
     * 待支付
     * 预授权场景
     */
    REQUIRES_PAYMENT_METHOD("requires_payment_method"),
    /**
     * 待确认
     * 预授权场景
     */
    REQUIRES_CAPTURE("requires_capture"),
    ;
    private String value;

    WooshPayStatusEnum(String value) {
        this.value = value;
    }
}
