package so.dian.platform.wooshpay.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import so.dian.commons.eden.util.LogColorUtils;

import javax.annotation.PostConstruct;

@Configuration
@ComponentScan("so.dian.platform.wooshpay.*")
@ConditionalOnProperty(prefix = "channel.enable", name = "wooshpay", havingValue = "true")
@Slf4j
public class WooshPayAutoConfiguration {

    static {
        log.info(LogColorUtils.blueMessage("WooshPay enable"));
    }

    @PostConstruct
    public void init(){

    }
}
