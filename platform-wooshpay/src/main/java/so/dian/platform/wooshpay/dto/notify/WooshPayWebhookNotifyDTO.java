package so.dian.platform.wooshpay.dto.notify;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
public class WooshPayWebhookNotifyDTO {

    @JsonProperty("livemode")
    private boolean livemode;
    @JsonProperty("data")
    private DataBean data;
    @JsonProperty("created")
    private long created;
    @JsonProperty("id")
    private String id;
    @JsonProperty("type")
    private String type;
    @JsonProperty("object")
    private String object;

    @NoArgsConstructor
    @Data
    public static class DataBean {
        @JsonProperty("object")
        private ObjectBean object;

        @NoArgsConstructor
        @Data
        public static class ObjectBean {
            @JsonProperty("id")
            private String id;
            @JsonProperty("object")
            private String object;
            @JsonProperty("created")
            private long created;
            @JsonProperty("livemode")
            private boolean livemode;
            @JsonProperty("currency")
            private String currency;
            @JsonProperty("amount")
            private int amount;
            @JsonProperty("status")
            private String status;
            @JsonProperty("merchant_order_id")
            private String merchantOrderId;
            @JsonProperty("client_secret")
            private String clientSecret;
            @JsonProperty("payment_method_types")
            private List<String> paymentMethodTypes;
            @JsonProperty("confirmation_method")
            private String confirmationMethod;
            @JsonProperty("payment_method_options")
            private PaymentMethodOptionsBean paymentMethodOptions;
            @JsonProperty("amount_capturable")
            private int amountCapturable;
            @JsonProperty("return_url")
            private String returnUrl;
            @JsonProperty("payment_method")
            private String paymentMethod;
            @JsonProperty("capture_method")
            private String captureMethod;
            @JsonProperty("latest_charge")
            private String latestCharge;

            @NoArgsConstructor
            @Data
            public static class PaymentMethodOptionsBean {
                @JsonProperty("card")
                private CardBean card;

                @NoArgsConstructor
                @Data
                public static class CardBean {
                    @JsonProperty("request_three_d_secure")
                    private String requestThreeDSecure;
                    @JsonProperty("capture_method")
                    private String captureMethod;
                }
            }
        }
    }
}
