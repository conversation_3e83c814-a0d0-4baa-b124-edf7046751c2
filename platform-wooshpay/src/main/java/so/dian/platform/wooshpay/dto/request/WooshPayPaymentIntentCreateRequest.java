package so.dian.platform.wooshpay.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class WooshPayPaymentIntentCreateRequest {
    @JsonProperty("amount")
    private Integer amount;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("confirm")
    private Boolean confirm;
    @JsonProperty("payment_method_data")
    private PaymentMethodDataDTO paymentMethodData;
    @JsonProperty("description")
    private String description;
    @JsonProperty("quantity")
    private Integer quantity;
    @JsonProperty("metadata")
    private MetadataDTO metadata;
    @JsonProperty("merchant_user_id")
    private String merchantUserId;
    @JsonProperty("merchant_order_id")
    private String merchantOrderId;
    @JsonProperty("shipping")
    private ShippingDTO shipping;
    @JsonProperty("return_url")
    private String returnUrl;
    @JsonProperty("capture_method")
    private String captureMethod;

    @NoArgsConstructor
    @Data
    public static class PaymentMethodDataDTO {
        @JsonProperty("type")
        private String type;
        @JsonProperty("card")
        private CardDTO card;
        @JsonProperty("billing_details")
        private BillingDetailsDTO billingDetails;

        @NoArgsConstructor
        @Data
        public static class CardDTO {
            @JsonProperty("exp_month")
            private String expMonth;
            @JsonProperty("exp_year")
            private String expYear;
            @JsonProperty("number")
            private String number;
            @JsonProperty("cvc")
            private String cvc;
        }

        @NoArgsConstructor
        @Data
        public static class BillingDetailsDTO {
            @JsonProperty("address")
            private AddressDTO address;
            @JsonProperty("name")
            private String name;

            @NoArgsConstructor
            @Data
            public static class AddressDTO {
                @JsonProperty("line1")
                private String line1;
                @JsonProperty("line2")
                private String line2;
                @JsonProperty("city")
                private String city;
                @JsonProperty("state")
                private String state;
                @JsonProperty("country")
                private String country;
                @JsonProperty("postal_code")
                private String postalCode;
            }
        }
    }

    @NoArgsConstructor
    @Data
    public static class MetadataDTO {
        @JsonProperty("key1")
        private String key1;
    }

    @NoArgsConstructor
    @Data
    public static class ShippingDTO {
        @JsonProperty("address")
        private AddressDTO address;
        @JsonProperty("name")
        private String name;
        @JsonProperty("phone")
        private String phone;

        @NoArgsConstructor
        @Data
        public static class AddressDTO {
            @JsonProperty("line1")
            private String line1;
            @JsonProperty("line2")
            private String line2;
            @JsonProperty("city")
            private String city;
            @JsonProperty("state")
            private String state;
            @JsonProperty("country")
            private String country;
            @JsonProperty("postal_code")
            private String postalCode;
        }
    }
}
