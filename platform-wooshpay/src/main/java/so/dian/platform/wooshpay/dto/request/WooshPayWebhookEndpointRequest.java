package so.dian.platform.wooshpay.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class WooshPayWebhookEndpointRequest {
    @JsonProperty("description")
    private String description;
    @JsonProperty("enabled_events")
    private List<String> enabledEvents;
    @JsonProperty("url")
    private String url;
    @JsonProperty("api_version")
    private String apiVersion;
}
