package so.dian.platform.wooshpay.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * {
 *   "id": "pi_1701519119838347264",
 *   "object": "payment_intent",
 *   "created": 1694508746000,
 *   "livemode": false,
 *   "currency": "CNY",
 *   "amount": 100,
 *   "status": "succeeded",
 *   "merchant_order_id": "CNT232309121652233381359649",
 *   "client_secret": "pi_1701519119838347264_secret_Bl0Tod4YDgAdvec3kQrraqkk",
 *   "payment_method_types": [
 *     "alipay",
 *     "wechat_pay",
 *     "card"
 *   ],
 *   "confirmation_method": "automatic",
 *   "payment_method_options": {
 *     "card": {
 *       "request_three_d_secure": "auto",
 *       "capture_method": "automatic"
 *     }
 *   },
 *   "amount_capturable": 0,
 *   "return_url": "https://jstest.wooshpay.com/v1/payment/success?redirect=result",
 *   "payment_method": "pm_1701519441847648256",
 *   "amount_received": 100,
 *   "capture_method": "manual",
 *   "latest_charge": "ch_1701519442069946368"
 * }
 *
 * */
@NoArgsConstructor
@Data
public class WooshPayPaymentIntentCaptureDTO {

    @JsonProperty("id")
    private String id;
    @JsonProperty("object")
    private String object;
    @JsonProperty("created")
    private Long created;
    @JsonProperty("livemode")
    private Boolean livemode;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("amount")
    private Integer amount;
    @JsonProperty("status")
    private String status;
    @JsonProperty("merchant_order_id")
    private String merchantOrderId;
    @JsonProperty("client_secret")
    private String clientSecret;
    @JsonProperty("payment_method_types")
    private List<String> paymentMethodTypes;
    @JsonProperty("confirmation_method")
    private String confirmationMethod;
    @JsonProperty("payment_method_options")
    private PaymentMethodOptionsDTO paymentMethodOptions;
    @JsonProperty("amount_capturable")
    private Integer amountCapturable;
    @JsonProperty("return_url")
    private String returnUrl;
    @JsonProperty("payment_method")
    private String paymentMethod;
    @JsonProperty("amount_received")
    private Integer amountReceived;
    @JsonProperty("capture_method")
    private String captureMethod;
    @JsonProperty("latest_charge")
    private String latestCharge;

    @NoArgsConstructor
    @Data
    public static class PaymentMethodOptionsDTO {
        @JsonProperty("card")
        private CardDTO card;

        @NoArgsConstructor
        @Data
        public static class CardDTO {
            @JsonProperty("request_three_d_secure")
            private String requestThreeDSecure;
            @JsonProperty("capture_method")
            private String captureMethod;
        }
    }
}
