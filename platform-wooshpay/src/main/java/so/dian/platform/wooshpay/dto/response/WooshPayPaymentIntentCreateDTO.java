package so.dian.platform.wooshpay.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class WooshPayPaymentIntentCreateDTO {

    @JsonProperty("id")
    private String id;
    @JsonProperty("object")
    private String object;
    @JsonProperty("created")
    private Long created;
    @JsonProperty("livemode")
    private Boolean livemode;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("amount")
    private Integer amount;
    @JsonProperty("status")
    private String status;
    @JsonProperty("merchant_order_id")
    private String merchantOrderId;
    @JsonProperty("client_secret")
    private String clientSecret;
    @JsonProperty("next_action")
    private NextActionDTO nextAction;
    @JsonProperty("payment_method_types")
    private List<String> paymentMethodTypes;
    @JsonProperty("confirmation_method")
    private String confirmationMethod;
    @JsonProperty("payment_method_options")
    private PaymentMethodOptionsDTO paymentMethodOptions;
    @JsonProperty("return_url")
    private String returnUrl;
    @JsonProperty("payment_method")
    private String paymentMethod;
    @JsonProperty("capture_method")
    private String captureMethod;

    @NoArgsConstructor
    @Data
    public static class NextActionDTO {
        @JsonProperty("type")
        private String type;
        @JsonProperty("challenge_redirect")
        private ChallengeRedirectDTO challengeRedirect;

        @NoArgsConstructor
        @Data
        public static class ChallengeRedirectDTO {
            @JsonProperty("url")
            private String url;
            @JsonProperty("return_url")
            private String returnUrl;
        }
    }

    @NoArgsConstructor
    @Data
    public static class PaymentMethodOptionsDTO {
        @JsonProperty("card")
        private CardDTO card;

        @NoArgsConstructor
        @Data
        public static class CardDTO {
            @JsonProperty("request_three_d_secure")
            private String requestThreeDSecure;
        }
    }
}
