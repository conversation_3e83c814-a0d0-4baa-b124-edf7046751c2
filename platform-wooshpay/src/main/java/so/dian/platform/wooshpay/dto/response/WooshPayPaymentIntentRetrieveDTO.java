package so.dian.platform.wooshpay.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class WooshPayPaymentIntentRetrieveDTO {


    @JsonProperty("id")
    private String id;
    @JsonProperty("object")
    private String object;
    @JsonProperty("created")
    private Long created;
    @JsonProperty("livemode")
    private Boolean livemode;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("amount")
    private Integer amount;
    @JsonProperty("status")
    private String status;
    @JsonProperty("merchant_order_id")
    private String merchantOrderId;
    @JsonProperty("client_secret")
    private String clientSecret;
    @JsonProperty("last_payment_error")
    private LastPaymentErrorDTO lastPaymentError;
    @JsonProperty("payment_method_types")
    private List<String> paymentMethodTypes;
    @JsonProperty("confirmation_method")
    private String confirmationMethod;
    @JsonProperty("payment_method_options")
    private PaymentMethodOptionsDTO paymentMethodOptions;
    @JsonProperty("amount_capturable")
    private Integer amountCapturable;
    @JsonProperty("return_url")
    private String returnUrl;
    @JsonProperty("payment_method")
    private String paymentMethod;
    @JsonProperty("amount_received")
    private Integer amountReceived;
    @JsonProperty("capture_method")
    private String captureMethod;

    @NoArgsConstructor
    @Data
    public static class LastPaymentErrorDTO {
        @JsonProperty("code")
        private String code;
        @JsonProperty("type")
        private String type;
        @JsonProperty("message")
        private String message;
        @JsonProperty("payment_method")
        private PaymentMethodDTO paymentMethod;
        @JsonProperty("decline_code")
        private String declineCode;

        @NoArgsConstructor
        @Data
        public static class PaymentMethodDTO {
            @JsonProperty("id")
            private String id;
            @JsonProperty("object")
            private String object;
            @JsonProperty("created")
            private Long created;
            @JsonProperty("livemode")
            private Boolean livemode;
            @JsonProperty("type")
            private String type;
            @JsonProperty("card")
            private CardDTO card;
            @JsonProperty("billing_details")
            private BillingDetailsDTO billingDetails;

            @NoArgsConstructor
            @Data
            public static class CardDTO {
                @JsonProperty("id")
                private String id;
                @JsonProperty("object")
                private String object;
                @JsonProperty("created")
                private Long created;
                @JsonProperty("livemode")
                private Boolean livemode;
                @JsonProperty("brand")
                private String brand;
                @JsonProperty("country")
                private String country;
                @JsonProperty("fingerprint")
                private String fingerprint;
                @JsonProperty("funding")
                private String funding;
                @JsonProperty("last4")
                private String last4;
                @JsonProperty("name")
                private String name;
                @JsonProperty("address_country")
                private String addressCountry;
                @JsonProperty("exp_month")
                private Integer expMonth;
                @JsonProperty("exp_year")
                private Integer expYear;
            }

            @NoArgsConstructor
            @Data
            public static class BillingDetailsDTO {
                @JsonProperty("address")
                private AddressDTO address;
                @JsonProperty("name")
                private String name;

                @NoArgsConstructor
                @Data
                public static class AddressDTO {
                    @JsonProperty("country")
                    private String country;
                }
            }
        }
    }

    @NoArgsConstructor
    @Data
    public static class PaymentMethodOptionsDTO {
        @JsonProperty("card")
        private CardDTO card;

        @NoArgsConstructor
        @Data
        public static class CardDTO {
            @JsonProperty("request_three_d_secure")
            private String requestThreeDSecure;
        }
    }
}
