package so.dian.platform.wooshpay.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class WooshPayRefundResultDTO {
    @JsonProperty("id")
    private String id;
    @JsonProperty("object")
    private String object;
    @JsonProperty("amount")
    private Integer amount;
    @JsonProperty("created")
    private Long created;
    @JsonProperty("currency")
    private String currency;
    @JsonProperty("metadata")
    private MetadataDTO metadata;
    @JsonProperty("payment_intent")
    private String paymentIntent;
    @JsonProperty("reason")
    private Object reason;
    @JsonProperty("receipt_number")
    private Object receiptNumber;
    @JsonProperty("status")
    private String status;

    @NoArgsConstructor
    @Data
    public static class MetadataDTO {
    }
}
