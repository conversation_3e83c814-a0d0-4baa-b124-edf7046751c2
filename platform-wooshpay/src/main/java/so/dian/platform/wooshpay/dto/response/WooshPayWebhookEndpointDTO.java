package so.dian.platform.wooshpay.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class WooshPayWebhookEndpointDTO {
    @JsonProperty("id")
    private String id;
    @JsonProperty("object")
    private String object;
    @JsonProperty("api_version")
    private Object apiVersion;
    @JsonProperty("created")
    private Long created;
    @JsonProperty("description")
    private String description;
    @JsonProperty("enabled_events")
    private List<String> enabledEvents;
    @JsonProperty("livemode")
    private Boolean livemode;
    @JsonProperty("metadata")
    private MetadataDTO metadata;
    @JsonProperty("status")
    private String status;
    @JsonProperty("url")
    private String url;
    @JsonProperty("secret")
    private String secret;

    @NoArgsConstructor
    @Data
    public static class MetadataDTO {
    }
}
