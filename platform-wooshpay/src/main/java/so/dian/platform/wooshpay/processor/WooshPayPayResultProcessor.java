/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.wooshpay.processor;

import cn.hutool.core.codec.Base64Encoder;
import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.QueryPayProcessor;
import so.dian.hera.interceptor.dto.req.PayQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;
import so.dian.platform.wooshpay.client.WooshPayClient;
import so.dian.platform.wooshpay.common.enums.WooshPayStatusEnum;
import so.dian.platform.wooshpay.dto.response.WooshPayPaymentIntentRetrieveDTO;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: WooshPayPayResultProcessor.java, v 1.0 2024-04-17 3:09 PM Exp $
 */
@Slf4j
@Component
public class WooshPayPayResultProcessor implements QueryPayProcessor {
    private final WooshPayClient wooshPayClient;
    @Value("${channel.wooshpay.skKey}")
    private String skKey;
    public WooshPayPayResultProcessor(ObjectProvider<WooshPayClient> wooshPayClientProvider){
        this.wooshPayClient= wooshPayClientProvider.getIfUnique();
    }
    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.WOOSHPAY_CARD);
    }

    @Override
    public PayQueryResultDTO orderPayQuery(final PayQueryResultRequest req) {
        Map header = Maps.newHashMap();
        header.put("Authorization", "Basic " + Base64Encoder.encode(skKey + ":"));
        log.info("wooshPay 支付订单查询,请求参数:{} header:{}", req.getTradeNo(), header.get("Authorization"));
        WooshPayPaymentIntentRetrieveDTO response= wooshPayClient.paymentIntentsRetrieve(req.getPayNo(), header);
        log.info("wooshPay 支付订单查询,返回结果:{}", JSON.toJSONString(response));
        // Status of this PaymentIntent, one of requires_payment_method, requires_confirmation,
        // requires_action, processing, requires_capture, canceled, or succeeded
        PayQueryResultDTO resp= new PayQueryResultDTO();
        resp.setTradeNo(response.getMerchantOrderId());
        resp.setPayNo(response.getId());
        if(StringUtils.equalsIgnoreCase(response.getStatus(), WooshPayStatusEnum.SUCCEEDED.getValue())){
            resp.setStatus(PayStatus.PAID.getCode());
            // FIXME api返回没有交易成功时间，以当前时间返回
            resp.setPayTime(new Date());
        }else if(StringUtils.equalsIgnoreCase(response.getStatus(), WooshPayStatusEnum.CANCELED.getValue())){
            resp.setStatus(PayStatus.CANCEL.getCode());
        }else if(StringUtils.equalsIgnoreCase(response.getStatus(), WooshPayStatusEnum.PROCESSING.getValue())){
            resp.setStatus(PayStatus.INIT.getCode());
        } else if(StringUtils.equalsIgnoreCase(response.getStatus(), WooshPayStatusEnum.REQUIRES_PAYMENT_METHOD.getValue())){
            resp.setStatus(PayStatus.CARD_AUTHORIZED.getCode());
        }else{
            resp.setStatus(PayStatus.INIT.getCode());
        }
        return resp;
    }
}