package so.dian.platform.wooshpay.processor;

import cn.hutool.core.codec.Base64Encoder;
import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.checkout.req.*;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.*;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundResultDTO;
import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.google.common.base.Function;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.exception.BizException;
import so.dian.commons.eden.exception.ErrorCodeEnum;
import so.dian.hera.interceptor.CheckoutPayProcessor;
import so.dian.hera.interceptor.RefundProcessor;
import so.dian.hera.interceptor.dto.req.PaymentQueryRequest;
import so.dian.hera.interceptor.dto.req.ProcessorRefundRequest;
import so.dian.hera.interceptor.dto.rsp.PaymentQueryResultDTO;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.common.configuration.redis.RedisClient;
import so.dian.platform.common.enums.CacheEnum;
import so.dian.platform.wooshpay.client.WooshPayClient;
import so.dian.platform.wooshpay.common.enums.WooshPayEventEnum;
import so.dian.platform.wooshpay.dto.request.*;
import so.dian.platform.wooshpay.dto.response.*;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WooshPayProcessor implements CheckoutPayProcessor, RefundProcessor {

    @Resource
    private WooshPayClient wooshPayClient;
    @Resource
    private RedisClient redisClient;
    @Value("${channel.wooshpay.skKey}")
    private String skKey;
    @Value("${channel.wooshpay.notifyUrl}")
    private String notifyUrl;
    @Value("${channel.wooshpay.returnUrl}")
    private String returnUrl;

    private Map header = Maps.newHashMap();

    @PostConstruct
    public void init() {
        String authorization = "Basic " + Base64Encoder.encode(skKey + ":");
        log.info("wooshpay authorization: {}", authorization);
        header.put("Authorization", authorization);
    }

    public void createWebhook() {
        WooshPayWebhookEndpointRequest request = new WooshPayWebhookEndpointRequest();
        request.setDescription("Chargebolt webhook");
        request.setUrl(notifyUrl);
        request.setEnabledEvents(Arrays.stream(WooshPayEventEnum.values()).map(wooshPayEventEnum -> wooshPayEventEnum.getValue()).collect(Collectors.toList()));
        request.setApiVersion("1.0.1");
        WooshPayWebhookEndpointDTO response = wooshPayClient.webhook_endpoints(request, header);
        log.info(JSON.toJSONString(response));
    }

    @Override
    public PrepayCreateResultDTO doPrePay(PrepayCreateRequest prePayReq, PaymentDO paymentDO) {
        WooshPayPaymentIntentCreateRequest request = new WooshPayPaymentIntentCreateRequest();
        request.setAmount(prePayReq.getPayAmount());
        request.setCurrency(prePayReq.getCurrency());
        request.setConfirm(false);
//        request.setShipping(new WooshPayPaymentIntentCreateRequest.ShippingDTO());
//        request.getShipping().setName("Test");
        request.setMerchantOrderId(paymentDO.getTradeNo());
        request.setCaptureMethod("manual");
        request.setReturnUrl(returnUrl);

        WooshPayPaymentIntentCreateDTO createDTO = wooshPayClient.paymentIntentsCreate(request, header);
        log.info("wooshpay 预授权请求参数：{} 返回结果：{}", JsonUtil.beanToJson(request), JsonUtil.beanToJson(createDTO));
        if (createDTO == null || createDTO.getReturnUrl() == null) {
            throw BizException.create(ErrorCodeEnum.FALLBACK, "调用WooshPay接口失败");
        }
        PrepayCreateResultDTO prepayCreateResultDTO = new PrepayCreateResultDTO();
        prepayCreateResultDTO.setTradeNo(paymentDO.getTradeNo());
        prepayCreateResultDTO.setPrepayResult(Maps.newHashMap());
        prepayCreateResultDTO.getPrepayResult().put("returnUrl", createDTO.getReturnUrl());
        prepayCreateResultDTO.getPrepayResult().put("clientSecret", createDTO.getClientSecret());
        prepayCreateResultDTO.getPrepayResult().put("currency", prePayReq.getCurrency());
        prepayCreateResultDTO.getPrepayResult().put("amount", prePayReq.getPayAmount());
        prepayCreateResultDTO.getPrepayResult().put("nextAction", createDTO.getNextAction());
        prepayCreateResultDTO.setPayNo(createDTO.getId());
        prepayCreateResultDTO.setStatus(TransStatusEnum.SUCCESS.getKey());
        redisClient.set(CacheEnum.WOOSHPAY_NOTIFY.ns, createDTO.getId(), paymentDO.getTradeNo(), CacheEnum.WOOSHPAY_NOTIFY.expiredTime);
        return prepayCreateResultDTO;
    }

    @Override
    public PreAuthCancelResultDTO doCancel(PaymentDO paymentDO) {
        WooshPayPaymentIntentCancelRequest request = new WooshPayPaymentIntentCancelRequest();
        request.setAmount(paymentDO.getPayAmount());
        request.setCurrency(paymentDO.getCurrency());
        WooshPayPaymentIntentCancelDTO cancelDTO = wooshPayClient.paymentIntentsCancel(paymentDO.getPayNo(), request, header);
        PreAuthCancelResultDTO resultDTO = new PreAuthCancelResultDTO();
        resultDTO.setStatus(cancelDTO.getStatus());
        return resultDTO;
    }

    @Override
    public PreAuthCaptureResultDTO doCapture(PreAuthCaptureRequest preauthCaptureRequest, String captureId, PaymentDO capturePaymentDO) {
        WooshPayPaymentIntentCaptureRequest request = new WooshPayPaymentIntentCaptureRequest();
        request.setCurrency(preauthCaptureRequest.getCurrency());
        request.setAmountToCapture(preauthCaptureRequest.getOrderAmount());
        WooshPayPaymentIntentCaptureDTO wooshPayPaymentIntentCaptureDTO = wooshPayClient.paymentIntentsCapture(captureId, request, header);
        log.info("wooshPayPaymentIntentCapture {}", JSON.toJSONString(wooshPayPaymentIntentCaptureDTO));
        PreAuthCaptureResultDTO preAuthCaptureResultDTO = new PreAuthCaptureResultDTO();
        preAuthCaptureResultDTO.setSuccess(true);
        return preAuthCaptureResultDTO;
    }

    @Override
    public RefundResultDTO refund(ProcessorRefundRequest checkoutProcessorRefundRequest) {
        WooshPayRefundRequest request = new WooshPayRefundRequest();
        request.setPaymentIntent(checkoutProcessorRefundRequest.getPayNo());
        // wooshpay的退款原因只能是固定的几个值之一
        // String indicating the reason for the refund. If set, possible values are duplicate , fraudulent , and requested_by_customer.
        request.setReason("requested_by_customer");
        request.setAmount(checkoutProcessorRefundRequest.getRefundAmount());
        WooshPayRefundResultDTO resultDTO = wooshPayClient.refund(request, header);
        log.info("wooshPayRefund {}", JSON.toJSONString(resultDTO));
        RefundResultDTO checkoutRefundResultDTO = new RefundResultDTO();
        checkoutRefundResultDTO.setRefundPayNo(resultDTO.getId());
        return checkoutRefundResultDTO;
    }

    @Override
    public ChannelEnum getRefundChannel() {
        return ChannelEnum.WOOSH_PAY;
    }

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.WOOSHPAY_CARD);
    }

}
