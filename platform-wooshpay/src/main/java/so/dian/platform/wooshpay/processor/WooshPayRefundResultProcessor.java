/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.wooshpay.processor;

import cn.hutool.core.codec.Base64Encoder;
import com.alibaba.fastjson.JSON;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.QueryPayProcessor;
import so.dian.hera.interceptor.QueryRefundProcessor;
import so.dian.hera.interceptor.dto.req.RefundQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;
import so.dian.hera.interceptor.dto.rsp.RefundQueryResultDTO;
import so.dian.platform.wooshpay.client.WooshPayClient;
import so.dian.platform.wooshpay.dto.response.WooshPayPaymentIntentRetrieveDTO;
import so.dian.platform.wooshpay.dto.response.WooshPayRefundResultDTO;

import java.util.List;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: WooshPayPayResultProcessor.java, v 1.0 2024-04-17 3:09 PM Exp $
 */
@Slf4j
@Component
public class WooshPayRefundResultProcessor implements QueryRefundProcessor {
    private final WooshPayClient wooshPayClient;
    @Value("${channel.wooshpay.skKey}")
    private String skKey;
    public WooshPayRefundResultProcessor(ObjectProvider<WooshPayClient> wooshPayClientProvider){
        this.wooshPayClient= wooshPayClientProvider.getIfUnique();
    }
    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.WOOSHPAY_CARD);
    }

    @Override
    public RefundQueryResultDTO orderRefundQuery(RefundQueryResultRequest queryResultRequest) {
//        wooshPayClient.retrieveRefund()
        Map header = Maps.newHashMap();
        header.put("Authorization", "Basic " + Base64Encoder.encode(skKey + ":"));
        log.info("wooshPay 退款订单查询,请求参数:{} header:{}", queryResultRequest.getRefundNo(), header.get("Authorization"));
        WooshPayRefundResultDTO response= wooshPayClient.retrieveRefund(queryResultRequest.getRefundNo(), header);
        log.info("wooshPay 退款订单查询,返回结果:{}", JSON.toJSONString(response));
        // Status of the refund. For credit card refunds, this can be pending, succeeded, or failed.
        // For other types of refunds, it can be pending, succeeded, failed, or canceled.
        RefundQueryResultDTO resp= new RefundQueryResultDTO();
        resp.setRefundNo(queryResultRequest.getRefundNo());
        if(Boolean.TRUE){


        }
        return resp;
    }
}