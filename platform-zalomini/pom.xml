<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.chargebolt.hera</groupId>
    <artifactId>chargebolt-hera</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>
  <groupId>com.chargebolt.hera</groupId>
  <artifactId>platform-zalomini</artifactId>
  <version>${platform.zalomini.version}</version>
  <packaging>jar</packaging>
  <name>platform-zalomini</name>
  <url>http://maven.apache.org</url>
  <dependencies>
    <dependency>
      <groupId>com.chargebolt.hera</groupId>
      <artifactId>platform-common</artifactId>
    </dependency>
    <dependency>
      <groupId>so.dian.mofa3</groupId>
      <artifactId>common-lang</artifactId>
    </dependency>
    <dependency>
      <groupId>so.dian.mofa3</groupId>
      <artifactId>common-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.chargebolt.hera</groupId>
      <artifactId>hera-interceptor</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-autoconfigure</artifactId>
      <version>${springboot.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <version>${springboot.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-aop</artifactId>
      <version>${springboot.version}</version>
    </dependency>
  </dependencies>
</project>
