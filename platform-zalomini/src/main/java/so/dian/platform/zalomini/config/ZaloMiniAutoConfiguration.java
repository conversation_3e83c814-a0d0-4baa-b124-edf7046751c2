package so.dian.platform.zalomini.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import so.dian.commons.eden.util.LogColorUtils;

import javax.annotation.PostConstruct;

@Configuration
@ComponentScan("so.dian.platform.zalomini.*")
@ConditionalOnProperty(prefix = "channel.enable", name = "zalomini", havingValue = "true")
@Slf4j
public class ZaloMiniAutoConfiguration {

    static {
        log.info(LogColorUtils.blueMessage("ZaloMini enable"));
    }

    @PostConstruct
    public void init(){

    }
}
