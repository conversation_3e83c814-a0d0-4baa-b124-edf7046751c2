/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalomini.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloPayProperties.java, v 1.0 2024-04-09 3:37 PM Exp $
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "channel.zalo-mini")
public class ZaloMiniProperties {
    private String url;
    private String appId;
    private String privateKey;
    private String method;
    private String notifyUrl;

}