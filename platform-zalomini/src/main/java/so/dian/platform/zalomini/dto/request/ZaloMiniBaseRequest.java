/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalomini.dto.request;

import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloBaseRequest.java, v 1.0 2024-04-09 9:51 AM Exp $
 */
@Data
public class ZaloMiniBaseRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202404199736549L;
    /**
     * Identifier of the mini app
     */
    private String appId;

    /**
     * mac = HMAC(hmac_algorihtm, key1, hmac_input)
     */
    private String mac;
}