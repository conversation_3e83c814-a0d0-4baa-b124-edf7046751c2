/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalomini.dto.request;

import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloMiniCallbackDataRequest.java, v 1.0 2024-05-31 下午2:30 Exp $
 */
@Data
public class ZaloMiniCallbackDataRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202405152143027L;
    /**
     * Identifier of the mini app
     */
    private String appId;
    /**
     * Id of the payment request created in the createOrder API
     */
    private String orderId;
    /**
     * Id of the payment request from the payment partner system
     */
    private String transId;
    /**
     * Payment methods. Refer to the code here
     */
    private String method;
    /**
     * Payment partner transaction time
     */
    private Long transTime;
    /**
     * Payment partner transaction code
     */
    private String merchantTransId;
    /**
     * Payment amount
     */
    private Long amount;
    /**
     * Information line
     */
    private String description;
    /**
     * Order transaction status:
     * 1: Success
     * -1 : Failure
     */
    private Integer resultCode;
    /**
     * Code description resultCode
     */
    private String message;
    /**
     * More information
     * Note: This data has been encodedURIComponent
     */
    private String extradata;
}