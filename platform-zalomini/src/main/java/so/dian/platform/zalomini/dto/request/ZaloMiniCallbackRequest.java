/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalomini.dto.request;

import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloBaseRequest.java, v 1.0 2024-04-09 9:51 AM Exp $
 */
@Data
public class ZaloMiniCallbackRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202404185336329L;

    /**
     * Authentication information of transaction data, using the provided PrivateKey to authenticate transaction data. The following data is used to create hash codes for information:
     * appId, orderId, transId, amount, description, resultCode, message
     */
    private String mac;
    /**
     * Authentication information of all data, using the provided PrivateKey to authenticate all data. The data is arranged in ascending lexicographic order to create a hash code for the information
     */
    private String overallMac;
    private ZaloMiniCallbackDataRequest data;
}