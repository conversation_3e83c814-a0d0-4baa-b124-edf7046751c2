/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalomini.dto.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloMiniOrderQueryRequest.java, v 1.0 2024-04-09 11:18 AM Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ZaloMiniRefundRequest extends ZaloMiniBaseRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202404652453673L;
    /**
     * Transaction code for refund (code returned upon payment)
     */
    private String transId;
    /**
     * The amount you want to refund to the customer
     */
    private Long amount;
    /**
     * Reason for refund
     */
    private String description;
}