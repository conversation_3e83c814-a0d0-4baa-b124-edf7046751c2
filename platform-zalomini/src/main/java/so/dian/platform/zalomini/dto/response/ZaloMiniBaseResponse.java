/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalomini.dto.response;

import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloBaseResponse.java, v 1.0 2024-04-09 10:22 AM Exp $
 */
@Data
public class ZaloMiniBaseResponse implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202404776436536L;
    /**
     * 1: Successful transaction refund
     * < 1 : Refund failed, transaction needs to be repeated
     * > 1 : Refund in progress, call getRefundStatus api to get final status
     */
    private Integer returnCode;

    /**
     * Description of returnCode information
     */
    private String returnMessage;

}