/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalomini.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: OrderQueryResponse.java, v 1.0 2024-04-09 11:21 AM Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ZaloMiniOrderQueryResponse extends ZaloMiniBaseResponse implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202404100112132L;
    private String transId;
    private String method;

    /**
     * Process's status
     */
    @JsonProperty("isProcessing")
    private Boolean processing;

    private Long amount;
    private Long transTime;
    private String merchantTransId;
    private String extradata;
}