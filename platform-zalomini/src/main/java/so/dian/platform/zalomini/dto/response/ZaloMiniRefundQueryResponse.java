/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalomini.dto.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: OrderQueryResponse.java, v 1.0 2024-04-09 11:21 AM Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ZaloMiniRefundQueryResponse extends ZaloMiniBaseResponse implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1712024043342653243L;
//  returnCode	int
//  1: Successful transaction refund
//  != 1 : Refund failed
}