/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalomini.dto.response;

import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloMiniResult.java, v 1.0 2024-06-05 下午1:41 Exp $
 */
@Data
@SuppressWarnings("unused")
public class ZaloMiniResult<T> implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202406157134314L;

    private Integer err;
    private String msg;
    private T data;
}