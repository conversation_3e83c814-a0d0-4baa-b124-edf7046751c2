/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalomini.processor;

import so.dian.platform.zalomini.dto.request.ZaloMiniRefundQueryRequest;
import so.dian.platform.zalomini.dto.request.ZaloMiniOrderQueryRequest;
import so.dian.platform.zalomini.dto.request.ZaloMiniRefundRequest;
import so.dian.platform.zalomini.dto.response.ZaloMiniBaseResponse;
import so.dian.platform.zalomini.dto.response.ZaloMiniOrderQueryResponse;
import so.dian.platform.zalomini.dto.response.ZaloMiniRefundResponse;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloPayApiService.java, v 1.0 2024-04-09 3:17 PM Exp $
 */
public interface ZaloMiniApiService {

    /**
     * 支付订单查询
     *
     * @param request
     * @return
     */
    ZaloMiniOrderQueryResponse queryOrder(ZaloMiniOrderQueryRequest request);

    /**
     * 退款
     *
     * @param request
     * @return
     */
    ZaloMiniRefundResponse refundOrder(ZaloMiniRefundRequest request);

    /**
     * 退款结果查询
     *
     * @param request
     * @return
     */
    ZaloMiniBaseResponse refundQuery(ZaloMiniRefundQueryRequest request);
}