/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalomini.processor;

import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import okhttp3.RequestBody;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.mofa3.client.okhttp3.OkHttpHeader;
import so.dian.mofa3.client.okhttp3.OkHttpRequest;
import so.dian.mofa3.lang.common.constant.HttpConstants;
import so.dian.mofa3.lang.exception.BizProcessException;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.common.utils.MACUtil;
import so.dian.platform.zalomini.config.ZaloMiniProperties;
import so.dian.platform.zalomini.dto.request.ZaloMiniOrderQueryRequest;
import so.dian.platform.zalomini.dto.request.ZaloMiniRefundQueryRequest;
import so.dian.platform.zalomini.dto.request.ZaloMiniRefundRequest;
import so.dian.platform.zalomini.dto.response.ZaloMiniBaseResponse;
import so.dian.platform.zalomini.dto.response.ZaloMiniOrderQueryResponse;
import so.dian.platform.zalomini.dto.response.ZaloMiniRefundResponse;
import so.dian.platform.zalomini.dto.response.ZaloMiniResult;

import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloMiniApiServiceImpl.java, v 1.0 2024-05-11 下午5:59 Exp $
 */
@Slf4j
@Service
public class ZaloMiniApiServiceImpl implements ZaloMiniApiService {
    /**
     * 支付订单查询Path
     */
    private static final String QUERY_PAY= "/api/transaction/get-status";
    /**
     * 退款Path
     */
    private static final String REFUND_ORDER= "/api/refund/create";
    /**
     * 退款结果查询Path
     */
    private static final String QUERY_REFUND= "/api/refund";
    /**
     * 超时时间
     */
    private static final Integer ALL_TIME_OUT= 10000;
    OkHttpHeader headers = OkHttpHeader.newBuilder().contentType(HttpConstants.APP_FORM_JSON);
    private ZaloMiniProperties zaloMiniProperties;
    public ZaloMiniApiServiceImpl (ObjectProvider<ZaloMiniProperties> zaloMiniPropertiesProvider) {
        this.zaloMiniProperties= zaloMiniPropertiesProvider.getIfUnique();
    }
    @Override
    public ZaloMiniOrderQueryResponse queryOrder(ZaloMiniOrderQueryRequest request) {
        request.setAppId(zaloMiniProperties.getAppId());
        String mac = getQueryOrderMac(request);
        request.setMac(mac);
        log.info("ZaloMini 查询订单请求参数:{}", JsonUtil.beanToJson(request));
        String result = OkHttpRequest.get(zaloMiniProperties.getUrl() + QUERY_PAY)
                .header(headers)
                .connectTimeout(ALL_TIME_OUT)
                .readTimeout(ALL_TIME_OUT)
                .writeTimeout(ALL_TIME_OUT)
                .addQueryParameter("appId", zaloMiniProperties.getAppId())
                .addQueryParameter("orderId", request.getOrderId())
                .addQueryParameter("mac", mac)
                .exec()
                .toStr();
        log.info("ZaloMini 查询订单返回结果:{}", result);
        ZaloMiniResult<ZaloMiniOrderQueryResponse> response= JsonUtil.jsonToBean(result, new TypeReference<ZaloMiniResult<ZaloMiniOrderQueryResponse>>() {});
        if(Objects.isNull(response.getData())|| Objects.equals(response.getErr(), -1) ){
            log.error("ZaloMini 查询订单失败请求参数:{} 返回结果：{}", JsonUtil.beanToJson(request), result);
            throw new BizProcessException(response.getMsg());
        }
        // returnCode
        // 0:交易正在进行中
        // 1:交易成功
        // -1:交易失败
        if(Objects.equals(response.getData().getReturnCode(), 1)
                ||Objects.equals(response.getData().getReturnCode(), -1)
                ||Objects.equals(response.getData().getReturnCode(), 0)){
            return response.getData();
        }
        log.error("ZaloMini 查询订单失败:{}", result);
        throw new BizProcessException(response.getData().getReturnMessage());
    }

    @Override
    public ZaloMiniRefundResponse refundOrder(ZaloMiniRefundRequest request) {
        request.setAppId(zaloMiniProperties.getAppId());
        String mac =getRefundOrderMac(request);
        request.setMac(mac);
        log.info("ZaloMini 退款请求参数：{}", JsonUtil.beanToJson(request));
        String result = OkHttpRequest.post(zaloMiniProperties.getUrl() + REFUND_ORDER)
                .header(headers)
                .connectTimeout(ALL_TIME_OUT)
                .readTimeout(ALL_TIME_OUT)
                .writeTimeout(ALL_TIME_OUT)
                .body(RequestBody.create(JsonUtil.beanToJson(request).getBytes()))
                .exec()
                .toStr();
        log.info("ZaloMini 退款返回结果：{}", JsonUtil.beanToJson(result));
        ZaloMiniResult<ZaloMiniRefundResponse> response= JsonUtil.jsonToBean(result, new TypeReference<ZaloMiniResult<ZaloMiniRefundResponse>>() {});
        if(Objects.isNull(response.getData())|| Objects.equals(response.getErr(), -1)){
            log.error("ZaloMini 订单退款失败:{}", result);
            throw new BizProcessException(response.getMsg());
        }
        // 1: Successful transaction refund
        // < 1 : Refund failed, transaction needs to be repeated
        // > 1 : Refund in progress, call getRefundStatus api to get final status
        return response.getData();
    }

    @Override
    public ZaloMiniBaseResponse refundQuery(ZaloMiniRefundQueryRequest request) {
        request.setAppId(zaloMiniProperties.getAppId());
//        TreeMap<String, Object> sortedData = new TreeMap<>(JsonUtil.beanToMap(request));
//        String concatenatedString = sortedData.entrySet().stream()
//                .map(entry -> entry.getKey() + "=" + entry.getValue())
//                .collect(Collectors.joining("&"));
//        log.info("mac String：{}", concatenatedString);
        String mac = getQueryRefundMac(request);
        request.setMac(mac);
        log.info("ZaloMini 退款查询请求参数:{}", JsonUtil.beanToJson(request));
        String result = OkHttpRequest.get(zaloMiniProperties.getUrl() + QUERY_REFUND)
                .header(headers)
                .connectTimeout(ALL_TIME_OUT)
                .readTimeout(ALL_TIME_OUT)
                .writeTimeout(ALL_TIME_OUT)
                .addQueryParameter("appId", zaloMiniProperties.getAppId())
                .addQueryParameter("refundId", request.getRefundId())
                .addQueryParameter("mac", mac)
                .log()
                .exec()
                .toStr();
        log.info("ZaloMini 退款查询返回结果:{}", result);
        ZaloMiniBaseResponse response= JsonUtil.jsonToBean(result, ZaloMiniBaseResponse.class);
        // returnCode
        // 1: Successful transaction refund
        // != 1 : Refund failed
        return response;
    }

    private String getRefundOrderMac(ZaloMiniRefundRequest request){
//        data =
//                "appId={appId}&transId={transId}&amount={amount}&description={description}&privateKey={privateKey}";
        StringBuilder data = new StringBuilder();
        data.append("appId=").append(request.getAppId())
                .append("&transId=").append(request.getTransId())
                .append("&amount=").append(request.getAmount())
                .append("&description=").append(request.getDescription())
                .append("&privateKey=").append(zaloMiniProperties.getPrivateKey());
        log.info("getRefundOrderMac conStr：{}", data.toString());
        return MACUtil.sha256Mac(data.toString(), zaloMiniProperties.getPrivateKey());
    }

    private String getQueryOrderMac(ZaloMiniOrderQueryRequest request){
        StringBuilder data = new StringBuilder();
        data.append("appId=").append(request.getAppId())
                .append("&orderId=").append(request.getOrderId())
                .append("&privateKey=").append(zaloMiniProperties.getPrivateKey());
        log.info("getQueryOrderMac conStr：{}", data.toString());
        return MACUtil.sha256Mac(data.toString(), zaloMiniProperties.getPrivateKey());
    }

    private String getQueryRefundMac(ZaloMiniRefundQueryRequest request){
        StringBuilder data = new StringBuilder();
        data.append("appId=").append(request.getAppId())
                .append("&refundId=").append(request.getRefundId())
                .append("&privateKey=").append(zaloMiniProperties.getPrivateKey());
        log.info("getQueryRefundMac conStr：{}", data.toString());
        return MACUtil.sha256Mac(data.toString(), zaloMiniProperties.getPrivateKey());
    }

}