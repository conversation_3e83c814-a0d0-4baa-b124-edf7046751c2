/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalomini.processor;

import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.checkout.req.PreAuthCaptureRequest;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PreAuthCancelResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PreAuthCaptureResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PrepayCreateResultDTO;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundResultDTO;
import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;
import com.chargebolt.hera.domain.notify.DelayQueryResultBody;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.constant.MqDelayLevelConst;
import so.dian.hera.interceptor.CheckoutPayProcessor;
import so.dian.hera.interceptor.RefundProcessor;
import so.dian.hera.interceptor.dto.req.ProcessorRefundRequest;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.common.constants.BusinessConstants;
import so.dian.platform.common.mq.producer.SelfMqProducer;
import so.dian.platform.common.utils.MACUtil;
import so.dian.platform.zalomini.config.ZaloMiniProperties;
import so.dian.platform.zalomini.dto.request.ZaloMiniRefundRequest;
import so.dian.platform.zalomini.dto.response.ZaloMiniRefundResponse;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloPayProcessor.java, v 1.0 2024-04-09 3:06 PM Exp $
 */
@Slf4j
@Component
public class ZaloMiniPayProcessor implements CheckoutPayProcessor, RefundProcessor {

    private final ZaloMiniApiService zaloMiniApiService;
    private final ZaloMiniProperties zaloMiniProperties;
    private final SelfMqProducer selfMqProducer;
    public ZaloMiniPayProcessor(ObjectProvider<ZaloMiniApiService> zaloPayApiServiceProvider,
                                ObjectProvider<ZaloMiniProperties> zaloMiniPropertiesProvider,
                                ObjectProvider<SelfMqProducer> selfMqProducerProvider){
        this.zaloMiniApiService= zaloPayApiServiceProvider.getIfUnique();
        this.zaloMiniProperties= zaloMiniPropertiesProvider.getIfUnique();
        this.selfMqProducer= selfMqProducerProvider.getIfUnique();
    }

    @Override
    public PrepayCreateResultDTO doPrePay(final PrepayCreateRequest prePayReq, final PaymentDO paymentDO) {
        PrepayCreateResultDTO prepayCreateResultDTO = new PrepayCreateResultDTO();
        prepayCreateResultDTO.setTradeNo(paymentDO.getTradeNo());
        Map<String, Object> data= new HashMap<>();
        data.put("amount", prePayReq.getPayAmount().longValue());
        Map<String, Object> itemMap= new HashMap<>();
        itemMap.put("orderNo", paymentDO.getOrderNo());
        itemMap.put("payAmount", paymentDO.getPayAmount());
        itemMap.put("tradeNo", paymentDO.getTradeNo());
        itemMap.put("userId", paymentDO.getUserId());
        data.put("item",Arrays.asList(itemMap));
        data.put("desc", "chargebolt - Payment for the order");
        data.put("method", zaloMiniProperties.getMethod());
        // zaloPay embedData
        data.put("extradata", "{\"tradeNo\": \""+paymentDO.getTradeNo()+"\"}");
//        data.put("extradata", "{tradeNo: \""+paymentDO.getTradeNo()+"\"}");

//        data.put("extradata", Collections.singletonMap("tradeNo",paymentDO.getTradeNo()));
        TreeMap<String, Object> sortedData = new TreeMap<>(data);
        String concatenatedString = sortedData.entrySet().stream()
                .map(entry -> {
                    String key = entry.getKey();
                    Object value = entry.getValue();

                    // 类型检查，如果value是对象，则转换为JSON字符串
                    String stringValue = (value instanceof Number || value instanceof Boolean || value instanceof CharSequence || value.getClass().isArray())
                            ? value.toString() // 基础类型、Boolean、CharSequence（包括String）和数组直接转换
                            : JsonUtil.beanToJson(value); // 对象转换为JSON字符串
                    return key + "=" + stringValue;
                })
                .collect(Collectors.joining("&"));
        log.info("mac前字符串：{}", concatenatedString);
        // Tạo overall mac từ dữ liệu
        // mac = HMAC("HmacSHA256", privateKey, dataMac);
        String mac= MACUtil.sha256Mac(concatenatedString, zaloMiniProperties.getPrivateKey());
        log.info("zalo mac：{}", mac);
        data.put("mac", mac);
        prepayCreateResultDTO.setPrepayResult(data);
        DelayQueryResultBody delayQueryResultBody=new DelayQueryResultBody();
        delayQueryResultBody.setTransNo(paymentDO.getTradeNo());
        delayQueryResultBody.setType(BusinessConstants.TRANSACTION_PAY);
        selfMqProducer.sendSelfQueryDelayQuery(delayQueryResultBody, MqDelayLevelConst.DELAY_2M);
        return prepayCreateResultDTO;
    }

    @Override
    public PreAuthCancelResultDTO doCancel(final PaymentDO paymentDO) {
        log.error("No API support");
        return null;
    }

    @Override
    public PreAuthCaptureResultDTO doCapture(final PreAuthCaptureRequest preauthCaptureRequest, final String captureId, final PaymentDO capturePaymentDO) {
        log.error("No API support");
        return null;
    }

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.ZALOPAY_MINI);
    }

    @Override
    public RefundResultDTO refund(final ProcessorRefundRequest request) {
        ZaloMiniRefundRequest req=new ZaloMiniRefundRequest();
        req.setTransId(request.getPayNo());
        req.setAmount(request.getRefundAmount().longValue());
        req.setDescription(request.getReason());
        ZaloMiniRefundResponse response= zaloMiniApiService.refundOrder(req);

        RefundResultDTO resp= new RefundResultDTO();
        if(response.getReturnCode()>0){
            resp.setStatus(TransStatusEnum.SUCCESS.getKey());
            resp.setRefundPayNo(response.getRefundId().toString());
        }else{
            resp.setStatus(TransStatusEnum.FAIL.getKey());
        }
        return resp;
    }

    @Override
    public ChannelEnum getRefundChannel() {
        return ChannelEnum.ZALOMINI_PAY;
    }

}