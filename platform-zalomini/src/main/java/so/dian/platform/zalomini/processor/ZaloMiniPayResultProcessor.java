/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalomini.processor;

import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.QueryPayProcessor;
import so.dian.hera.interceptor.dto.req.PayQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.platform.zalomini.dto.request.ZaloMiniOrderQueryRequest;
import so.dian.platform.zalomini.dto.response.ZaloMiniOrderQueryResponse;

import java.util.List;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloPayPayResultProcessor.java, v 1.0 2024-04-09 3:06 PM Exp $
 */
@Slf4j
@Component
public class ZaloMiniPayResultProcessor implements QueryPayProcessor {

    private final ZaloMiniApiService zaloMiniApiService;

    public ZaloMiniPayResultProcessor(ObjectProvider<ZaloMiniApiService> zaloMiniApiServiceProvider){
        this.zaloMiniApiService= zaloMiniApiServiceProvider.getIfUnique();
    }

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.ZALOPAY_MINI);
    }

    @Override
    public PayQueryResultDTO orderPayQuery(final PayQueryResultRequest request) {
        ZaloMiniOrderQueryRequest req = new ZaloMiniOrderQueryRequest();
        req.setOrderId(request.getPayNo());
        ZaloMiniOrderQueryResponse response= zaloMiniApiService.queryOrder(req);
        PayQueryResultDTO resp= new PayQueryResultDTO();
        resp.setTradeNo(request.getTradeNo());
        resp.setPayNo(request.getPayNo());
        if(Objects.equals(1, response.getReturnCode())){
            // sub_return_code
            resp.setStatus(PayStatus.PAID.getCode());
            resp.setThirdPayTradeNo(response.getTransId());
            // 查询接口没有返回支付成功时间，如果订单状态没有更新过，则以当前查询时间为支付成功时间返回
            resp.setPayTime(new DateBuild(response.getTransTime()).toDate());
        }else if(Objects.equals(-1, response.getReturnCode())){
            resp.setStatus(PayStatus.FAIL.getCode());
        }else{
            // 处理中，初始化未支付状态
            resp.setStatus(PayStatus.INIT.getCode());
        }
        return resp;
    }
}