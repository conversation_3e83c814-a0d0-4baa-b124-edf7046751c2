/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalomini.processor;

import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.QueryRefundProcessor;
import so.dian.hera.interceptor.dto.req.RefundQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.RefundQueryResultDTO;
import so.dian.platform.zalomini.dto.request.ZaloMiniRefundQueryRequest;
import so.dian.platform.zalomini.dto.response.ZaloMiniBaseResponse;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloPayPayResultProcessor.java, v 1.0 2024-04-09 3:06 PM Exp $
 */
@Slf4j
@Component
public class ZaloMiniRefundResultProcessor implements QueryRefundProcessor {

    private final ZaloMiniApiService zaloMiniApiService;

    public ZaloMiniRefundResultProcessor(ObjectProvider<ZaloMiniApiService> zaloMiniApiServiceProvider){
        this.zaloMiniApiService= zaloMiniApiServiceProvider.getIfUnique();
    }

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.ZALOPAY_MINI);
    }


    @Override
    public RefundQueryResultDTO orderRefundQuery(RefundQueryResultRequest queryResultRequest) {
        ZaloMiniRefundQueryRequest request= new ZaloMiniRefundQueryRequest();
        request.setRefundId(queryResultRequest.getRefundNo());
        ZaloMiniBaseResponse response= zaloMiniApiService.refundQuery(request);
        RefundQueryResultDTO resp= new RefundQueryResultDTO();
        resp.setRefundNo(queryResultRequest.getRefundNo());
        if(Objects.equals(response.getReturnCode(), 1)){
            // sub_return_code
            resp.setStatus(PayStatus.REFUNDED.getCode());
            // 查询接口没有返回退款成功时间，如果订单状态没有更新过，则以当前查询时间为退款成功时间返回
            resp.setRefundTime(new Date());
        }else{
            resp.setStatus(PayStatus.FAIL.getCode());
        }
        return resp;
    }
}