/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalopay.common.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloPayReturnCodeEnum.java, v 1.0 2024-04-09 4:16 PM Exp $
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum ZaloPayReturnCodeEnum {
    SUCCESS(1,"Success"),
    FAILURE(2,"Failure"),
    PROCESSING(3,"Processing"),

    REFUND_EXPIRE_TIME(-13,"Refund expire time"),
    ;
    /**
     * 枚举编码
     */
    @Getter
    private final Integer code;

    /**
     * value
     */
    @Getter
    private final String value;
}