package so.dian.platform.zalopay.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import so.dian.commons.eden.util.LogColorUtils;

import javax.annotation.PostConstruct;

@Configuration
@ComponentScan("so.dian.platform.zalopay.*")
@ConditionalOnProperty(prefix = "channel.enable", name = "zalopay", havingValue = "true")
@Slf4j
public class ZaloPayAutoConfiguration {

    static {
        log.info(LogColorUtils.blueMessage("ZaloPay enable"));
    }

    @PostConstruct
    public void init(){

    }
}
