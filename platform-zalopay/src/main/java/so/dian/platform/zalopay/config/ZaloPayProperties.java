/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalopay.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloPayProperties.java, v 1.0 2024-04-09 3:37 PM Exp $
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "channel.zalopay")
public class ZaloPayProperties {
    private String url;
    private Integer appId;
    private String appKey;
    private String appKey2;
    private String notifyUrl;

}