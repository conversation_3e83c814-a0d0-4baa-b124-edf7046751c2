/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalopay.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: CallbackData.java, v 1.0 2024-04-11 10:46 AM Exp $
 */
@Data
public class CallbackData implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202404100110539L;

    /**
     *
     */
    @JsonProperty("app_id")
    private Integer appId;
    /**
     * Order's app_trans_id
     */
    @JsonProperty("app_trans_id")
    private String appTransId;

    /**
     *
     */
    @JsonProperty("app_time")
    private Long appTime;
    /**
     * ZaloPay's Transaction code
     */
    @JsonProperty("zp_trans_id")
    private String zpTransId;
    /**
     *
     */
    @JsonProperty("app_user")
    private String appUser;

    /**
     *
     */
    @JsonProperty("amount")
    private Long amount;

    @JsonProperty("embed_data")
    private String embedData;

    /**
     * ZaloPay's Transaction trading time (unix timestamp in milliseconds)'
     */
    @JsonProperty("server_time")
    private Long serverTime;

    /**
     * Fee (VND)
     */
    @JsonProperty("user_fee_amount")
    private Long userFeeAmount;

    /**
     * Discount (VND)
     */
    @JsonProperty("discount_amount")
    private Long discountAmount;
    @JsonProperty("merchant_user_id")
    private String merchantUserId;
    @JsonProperty("zp_user_id")
    private String zpUserId;
    private Integer channel;

}