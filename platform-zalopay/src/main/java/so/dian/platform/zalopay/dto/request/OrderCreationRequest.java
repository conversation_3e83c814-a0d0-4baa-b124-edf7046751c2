/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalopay.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 创建订单请求对象
 *
 * <AUTHOR>
 * @version: OrderCreationRequest.java, v 1.0 2024-04-09 10:00 AM Exp $
 */
@Data
public class OrderCreationRequest extends ZaloBaseRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202404100100038L;
    /**
     * Identification information of the application user: Id / Username / Name / Phone Num / Email.
     * If you cannot identify the user, you can use the default information, such as the application name
     */
    @JsonProperty("app_user")
    private String appUser;

    /**
     * 	Order's transaction code. Must be preceded by yymmdd of the current date. The transaction-code's format should be yymmddOrder identifier
     * Note: yymmdd is correct TimeZone Vietnam (GMT+7) (Because of cross check transactions is in Vietnam time and date)
     */
    @JsonProperty("app_trans_id")
    private String appTransId;
    /**
     * 	The time of order creation, which calculated in milliseconds, and couldn't over 15 minutes from the time of payment
     */
    @JsonProperty("app_time")
    private Long appTime;
    /**
     * Order expiration time. Time in seconds (minimum value: 300, maximum value: 2592000)
     */
    @JsonProperty("expire_duration_seconds")
    private Long expireDurationSeconds;
    /**
     * The amount of the order (VND)
     */
    private Long amount;
    /**
     * Additional data, defined by Merchant. Use "{}" when empty.
     * JSON Array String
     */
    private String item="[]";
    /**
     * The description of the order, used to display to users on the ZaloPay app
     */
    private String description;
    /**
     * Merchant's item data. Use "[]" when empty.
     * JSON String
     */
    @JsonProperty("embed_data")
    private String embedData="{}";
    /**
     * ZaloPay will notify to this URL only when the payment is success;
     * If not provided, the default app Callback URL will be used.
     */
    @JsonProperty("callback_url")
    private String callbackUrl;

    private String title;
    /**
     * The currency of order. Default is VND.
     */
    private String currency;
}