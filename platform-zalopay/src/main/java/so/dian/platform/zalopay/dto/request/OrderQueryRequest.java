/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalopay.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: OrderQueryRequest.java, v 1.0 2024-04-09 11:18 AM Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderQueryRequest extends ZaloBaseRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202404100111847L;
    /**
     * Order's app_trans_id
     */
    @JsonProperty("app_trans_id")
    private String appTransId;
}