/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalopay.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: RefundQueryRequest.java, v 1.0 2024-04-09 2:12 PM Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RefundQueryRequest extends ZaloBaseRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202404100141314L;
    /**
     * • The transaction code, which was generated by merchant when querying refund transaction.
     * • Format: yymmdd_appid_xxxxxxxxxx
     */
    @JsonProperty("m_refund_id")
    private String mRefundId;

    /**
     * The time call API (timestamp in millisecond)
     */
    private Long timestamp;
}