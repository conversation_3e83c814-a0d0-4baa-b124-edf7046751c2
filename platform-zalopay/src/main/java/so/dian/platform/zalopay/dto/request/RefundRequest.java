/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalopay.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: RefundRequest.java, v 1.0 2024-04-09 11:43 AM Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RefundRequest extends ZaloBaseRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202404100114315L;

    /**
     * Merchant must be generate merchant own transaction code when submit refund requirement.
     * • Format: yymmdd_appid_xxxxxxxxxx
     */
    @JsonProperty("m_refund_id")
    private String refundId;
    /**
     * • Transaction code, which want to be refund.
     * • Transaction code of ZaloPay, retrieved from callback data
     */
    @JsonProperty("zp_trans_id")
    private String zpTransId;

    private Long amount;
    private Long timestamp;
    /**
     * Refund reason
     */
    private String description;
}