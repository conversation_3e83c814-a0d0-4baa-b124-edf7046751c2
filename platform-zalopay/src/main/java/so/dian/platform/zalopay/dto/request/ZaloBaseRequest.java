/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalopay.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloBaseRequest.java, v 1.0 2024-04-09 9:51 AM Exp $
 */
@Data
public class ZaloBaseRequest implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202404100095249L;
    /**
     * Identification of the application that was issued when registering the application with ZaloPay.
     */
    @JsonProperty("app_id")
    private Integer appId;

    /**
     * mac = HMAC(hmac_algorihtm, key1, hmac_input)
     */
    private String mac;
}