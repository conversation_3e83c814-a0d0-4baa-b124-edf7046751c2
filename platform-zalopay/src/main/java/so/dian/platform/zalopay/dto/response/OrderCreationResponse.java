/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalopay.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: OrderCreationResponse.java, v 1.0 2024-04-09 10:24 AM Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderCreationResponse extends ZaloBaseResponse implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202404100102507L;

    @JsonProperty("order_url")
    private String orderUrl;

    @JsonProperty("zp_trans_token")
    private String zpTransToken;

    @JsonProperty("order_token")
    private String orderToken;
    /**
     * Used to create NAPAS VietQR on Merchant system.
     * NAPAS VietQR is one of our brand new payment solution,
     * which accepts payments made by both ZaloPay & +40 banks belonged to NAPAS system.
     * User can using bank app scan NAPAS VietQR for payment
     */
    @JsonProperty("qr_code")
    private String qrCode;
}