/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalopay.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: OrderQueryResponse.java, v 1.0 2024-04-09 11:21 AM Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderQueryResponse extends ZaloBaseResponse implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202404100112132L;

    /**
     * Process's status
     */
    @JsonProperty("is_processing")
    private Boolean processing;

    private Long amount;

    @JsonProperty("discount_amount")
    private Boolean discountAmount;
    @JsonProperty("zp_trans_id")
    private Long zpTransId;
}