/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalopay.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: RefundResponse.java, v 1.0 2024-04-09 2:08 PM Exp $
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RefundResponse extends ZaloBaseResponse implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202404100140903L;
    /**
     * ZaloPay refund's transaction code, need to save for cross-check then
     */
    @JsonProperty("refund_id")
    private Long refundId;
}