/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalopay.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloBaseResponse.java, v 1.0 2024-04-09 10:22 AM Exp $
 */
@Data
public class ZaloBaseResponse implements Serializable {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 171202404100102336L;
    /**
     * 1 : SUCCESS
     * 2 : FAIL
     * 3 : PROCESSING
     */
    @JsonProperty("return_code")
    private Integer returnCode;

    /**
     * Description of status code
     */
    @JsonProperty("return_message")
    private String returnMessage;

    @JsonProperty("sub_return_code")
    private String subReturnCode;

    @JsonProperty("sub_return_message")
    private String subReturnMessage;
}