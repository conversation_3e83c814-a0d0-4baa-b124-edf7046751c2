/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalopay.processor;

import so.dian.platform.zalopay.dto.request.OrderCreationRequest;
import so.dian.platform.zalopay.dto.request.OrderQueryRequest;
import so.dian.platform.zalopay.dto.request.RefundQueryRequest;
import so.dian.platform.zalopay.dto.request.RefundRequest;
import so.dian.platform.zalopay.dto.response.OrderCreationResponse;
import so.dian.platform.zalopay.dto.response.OrderQueryResponse;
import so.dian.platform.zalopay.dto.response.RefundResponse;
import so.dian.platform.zalopay.dto.response.ZaloBaseResponse;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloPayApiService.java, v 1.0 2024-04-09 3:17 PM Exp $
 */
public interface ZaloPayApiService {
    /**
     * 创建订单
     *
     * @param request
     * @return
     */
    OrderCreationResponse createOrder(OrderCreationRequest request);

    /**
     * 支付订单查询
     *
     * @param request
     * @return
     */
    OrderQueryResponse queryOrder(OrderQueryRequest request);

    /**
     * 退款
     *
     * @param request
     * @return
     */
    RefundResponse refundOrder(RefundRequest request);

    /**
     * 退款结果查询
     *
     * @param request
     * @return
     */
    ZaloBaseResponse refundQuery(RefundQueryRequest request);
}