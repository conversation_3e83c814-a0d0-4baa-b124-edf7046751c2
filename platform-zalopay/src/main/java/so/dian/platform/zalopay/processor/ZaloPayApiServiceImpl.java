/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalopay.processor;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Service;
import so.dian.mofa3.client.okhttp3.OkHttpHeader;
import so.dian.mofa3.client.okhttp3.OkHttpRequest;
import so.dian.mofa3.lang.common.constant.HttpConstants;
import so.dian.mofa3.lang.exception.BizProcessException;
import so.dian.mofa3.lang.util.JsonUtil;
import so.dian.platform.common.utils.MACUtil;
import so.dian.platform.zalopay.common.enums.ZaloPayReturnCodeEnum;
import so.dian.platform.zalopay.config.ZaloPayProperties;
import so.dian.platform.zalopay.dto.request.OrderCreationRequest;
import so.dian.platform.zalopay.dto.request.OrderQueryRequest;
import so.dian.platform.zalopay.dto.request.RefundQueryRequest;
import so.dian.platform.zalopay.dto.request.RefundRequest;
import so.dian.platform.zalopay.dto.response.OrderCreationResponse;
import so.dian.platform.zalopay.dto.response.OrderQueryResponse;
import so.dian.platform.zalopay.dto.response.RefundResponse;
import so.dian.platform.zalopay.dto.response.ZaloBaseResponse;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloPayApiServiceImpl.java, v 1.0 2024-04-09 3:20 PM Exp $
 */
@Slf4j
@Service
public class ZaloPayApiServiceImpl implements ZaloPayApiService {
    /**
     * 创建订单Path
     */
    private static final String CREATE_ORDER= "/v2/create";
    /**
     * 支付订单查询Path
     */
    private static final String QUERY_PAY= "/v2/query";
    /**
     * 退款Path
     */
    private static final String REFUND_ORDER= "/v2/refund";
    /**
     * 退款结果查询Path
     */
    private static final String QUERY_REFUND= "/v2/query_refund";

    /**
     * 超时时间
     */
    private static final Integer ALL_TIME_OUT= 20000;
    private final ZaloPayProperties zaloPayProperties;
    public ZaloPayApiServiceImpl(ObjectProvider<ZaloPayProperties> zaloPayPropertiesProvider){
        this.zaloPayProperties= zaloPayPropertiesProvider.getIfUnique();
    }
    OkHttpHeader headers = OkHttpHeader.newBuilder().contentType(HttpConstants.APP_FORM_JSON);
    @Override
    public OrderCreationResponse createOrder(OrderCreationRequest request) {
        request.setAppId(zaloPayProperties.getAppId());
        request.setExpireDurationSeconds(900L);
        request.setCallbackUrl(zaloPayProperties.getNotifyUrl());
        // hmac_input: app_id +”|”+ app_trans_id +”|”+ app_user +”|”+ amount +"|"+ app_time +”|”+ embed_data +"|"+ item
        String hmacInput = buildHmacInput(Arrays.asList(
                zaloPayProperties.getAppId().toString(),
                request.getAppTransId(),
                request.getAppUser(),
                request.getAmount().toString(),
                request.getAppTime().toString(),
                request.getEmbedData(),
                request.getItem()));
        String mac = MACUtil.sha256Mac(hmacInput, zaloPayProperties.getAppKey());
        request.setMac(mac);

        log.info("Zalopay创建订单请求参数:{}", JsonUtil.beanToJson(request));
        String result = sendPost(JsonUtil.beanToJson(request), CREATE_ORDER);
        log.info("Zalopay创建订单返回结果:{}", result);
        OrderCreationResponse response= JsonUtil.jsonToBean(result, OrderCreationResponse.class);
        if(Objects.equals(response.getReturnCode(), ZaloPayReturnCodeEnum.SUCCESS.getCode())){
            return response;
        }
        log.error("Zalopay创建订单失败:{}", result);
        throw new BizProcessException(response.getReturnMessage());
    }

    @Override
    public OrderQueryResponse queryOrder(OrderQueryRequest request) {
        request.setAppId(zaloPayProperties.getAppId());
        // app_id+"|"+app_trans_id+"|"+key1
        String hmacInput = buildHmacInput(Arrays.asList(
                zaloPayProperties.getAppId().toString(),
                request.getAppTransId(),
                zaloPayProperties.getAppKey()));
        String mac = MACUtil.sha256Mac(hmacInput, zaloPayProperties.getAppKey());
        request.setMac(mac);
        log.info("Zalopay查询订单请求参数:{}", JsonUtil.beanToJson(request));
        String result = sendPost(JsonUtil.beanToJson(request), QUERY_PAY);
        log.info("Zalopay查询订单返回结果:{}", result);
        OrderQueryResponse response= JsonUtil.jsonToBean(result, OrderQueryResponse.class);
        if(Objects.equals(response.getReturnCode(), ZaloPayReturnCodeEnum.SUCCESS.getCode())
                ||Objects.equals(response.getReturnCode(), ZaloPayReturnCodeEnum.FAILURE.getCode())
                ||Objects.equals(response.getReturnCode(), ZaloPayReturnCodeEnum.PROCESSING.getCode())){
            return response;
        }
        log.error("Zalopay查询订单失败:{}", result);
        throw new BizProcessException(response.getReturnMessage());
    }

    @Override
    public RefundResponse refundOrder(RefundRequest request) {
        request.setAppId(zaloPayProperties.getAppId());
        // hmac_input: app_id +"|"+ zp_trans_id +"|"+ amount +"|"+ description +"|"+ timestamp
        String hmacInput = buildHmacInput(Arrays.asList(
                zaloPayProperties.getAppId().toString(),
                request.getZpTransId(),
                request.getAmount().toString(),
                request.getDescription(),
                request.getTimestamp().toString()));
        String mac = MACUtil.sha256Mac(hmacInput, zaloPayProperties.getAppKey());
        request.setMac(mac);
        log.info("Zalopay退款订单请求参数:{}", JsonUtil.beanToJson(request));
        String result = sendPost(JsonUtil.beanToJson(request), REFUND_ORDER);
        log.info("Zalopay退款订单返回结果:{}", result);
        RefundResponse response= JsonUtil.jsonToBean(result, RefundResponse.class);
        if(Objects.equals(response.getReturnCode(), ZaloPayReturnCodeEnum.SUCCESS.getCode())
                ||Objects.equals(response.getReturnCode(), ZaloPayReturnCodeEnum.REFUND_EXPIRE_TIME.getCode())
                ||Objects.equals(response.getReturnCode(), ZaloPayReturnCodeEnum.PROCESSING.getCode())){
            return response;
        }
        log.error("Zalopay退款订单失败:{}", result);
        throw new BizProcessException(response.getReturnMessage());
    }

    @Override
    public ZaloBaseResponse refundQuery(RefundQueryRequest request) {
        request.setAppId(zaloPayProperties.getAppId());
        // hmac_input: app_id+"|"+m_refund_id+"|"+timestamp
        String hmacInput = buildHmacInput(Arrays.asList(
                zaloPayProperties.getAppId().toString(),
                request.getMRefundId(),
                request.getTimestamp().toString()));
        String mac = MACUtil.sha256Mac(hmacInput, zaloPayProperties.getAppKey());
        request.setMac(mac);
        log.info("Zalopay退款查询请求参数:{}", JsonUtil.beanToJson(request));
        String result = sendPost(JsonUtil.beanToJson(request), QUERY_REFUND);
        log.info("Zalopay退款查询返回结果:{}", result);
        RefundResponse response= JsonUtil.jsonToBean(result, RefundResponse.class);
        if(Objects.equals(response.getReturnCode(), ZaloPayReturnCodeEnum.SUCCESS.getCode())
                ||Objects.equals(response.getReturnCode(), ZaloPayReturnCodeEnum.REFUND_EXPIRE_TIME.getCode())
                ||Objects.equals(response.getReturnCode(), ZaloPayReturnCodeEnum.PROCESSING.getCode())){
            return response;
        }
        log.error("Zalopay退款查询失败:{}", result);
        throw new BizProcessException(response.getReturnMessage());
    }

    private String sendPost(String param, String url){
        return OkHttpRequest.post(zaloPayProperties.getUrl() + url)
                .header(headers)
                .connectTimeout(ALL_TIME_OUT)
                .readTimeout(ALL_TIME_OUT)
                .writeTimeout(ALL_TIME_OUT)
                .json(param)
                .log()
                .exec()
                .toStr();
    }
    private static String buildHmacInput(List<String> params){
        if (CollectionUtils.isEmpty(params)) {
            throw new IllegalArgumentException("Input parameter list cannot be null or empty");
        }
        String mac= String.join("|", params);
        log.info("mac:{}", mac);
        return mac;
    }

}