/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalopay.processor;

import com.chargebolt.hera.client.dto.pay.PrepayCreateRequest;
import com.chargebolt.hera.client.dto.pay.checkout.req.PreAuthCaptureRequest;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PreAuthCancelResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PreAuthCaptureResultDTO;
import com.chargebolt.hera.client.dto.pay.checkout.rsp.PrepayCreateResultDTO;
import com.chargebolt.hera.client.dto.pay.refund.rsp.RefundResultDTO;
import com.chargebolt.hera.client.enums.ChannelEnum;
import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.TransStatusEnum;
import com.chargebolt.hera.domain.notify.DelayQueryResultBody;
import com.chargebolt.hera.domain.sharding.PaymentDO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import so.dian.commons.eden.constant.MqDelayLevelConst;
import so.dian.hera.interceptor.CheckoutPayProcessor;
import so.dian.hera.interceptor.RefundProcessor;
import so.dian.hera.interceptor.dto.req.ProcessorRefundRequest;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.platform.common.constants.BusinessConstants;
import so.dian.platform.common.mq.producer.SelfMqProducer;
import so.dian.platform.zalopay.config.ZaloPayProperties;
import so.dian.platform.zalopay.dto.request.OrderCreationRequest;
import so.dian.platform.zalopay.dto.request.RefundRequest;
import so.dian.platform.zalopay.dto.response.OrderCreationResponse;
import so.dian.platform.zalopay.dto.response.RefundResponse;

import java.text.MessageFormat;
import java.time.ZoneId;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloPayProcessor.java, v 1.0 2024-04-09 3:06 PM Exp $
 */
@Slf4j
@Component
public class ZaloPayProcessor implements CheckoutPayProcessor, RefundProcessor {
    @Value("${zalopay-payment-description:}")
    private String paymentDescr;

    private final ZaloPayApiService zaloPayApiService;
    private final ZaloPayProperties zaloPayProperties;
    private final SelfMqProducer selfMqProducer;

    public ZaloPayProcessor(ObjectProvider<ZaloPayApiService> zaloPayApiServiceProvider,
                            ObjectProvider<SelfMqProducer> selfMqProducerProvider,
                            ObjectProvider<ZaloPayProperties> zaloPayPropertiesProvider) {
        this.zaloPayApiService = zaloPayApiServiceProvider.getIfUnique();
        this.selfMqProducer = selfMqProducerProvider.getIfUnique();
        this.zaloPayProperties = zaloPayPropertiesProvider.getIfUnique();
    }

    @Override
    public PrepayCreateResultDTO doPrePay(final PrepayCreateRequest prePayReq, final PaymentDO paymentDO) {
        PrepayCreateResultDTO prepayCreateResultDTO = new PrepayCreateResultDTO();
        prepayCreateResultDTO.setTradeNo(paymentDO.getTradeNo());

        OrderCreationRequest request = new OrderCreationRequest();
        request.setAppUser(paymentDO.getUserId().toString());
        request.setAppTransId(paymentDO.getTradeNo());
        // fixme 时区从配置获取
        request.setAppTime(new DateBuild(ZoneId.of("Asia/Ho_Chi_Minh")).toDate().getTime());
        request.setAmount(paymentDO.getPayAmount().longValue());
//        request.setItem("");
        request.setDescription(paymentDescr+ paymentDO.getOrderNo());
        request.setDescription(prePayReq.getRemark());
        String redirectUrl = prePayReq.getExtInfo().getRedirectUrl();
        String deviceNo = prePayReq.getExtInfo().getDeviceNo();
        MessageFormat urlFormatter = new MessageFormat(redirectUrl);
        // "?deviceNo={0}&cbPrepayTradeNo={1}&merchantTransactionId={2}"
        String urlFormatted = urlFormatter.format(new Object[]{deviceNo, paymentDO.getTradeNo(), paymentDO.getTradeNo()});
        request.setEmbedData("{\"redirecturl\": \"" + urlFormatted + "\"}");
        OrderCreationResponse response = zaloPayApiService.createOrder(request);

        prepayCreateResultDTO.setSuccess(true);
//        prepayCreateResultDTO.setPayNo(response.getBizContentObject().getTransactionId());
        prepayCreateResultDTO.getPrepayResult().put("innerJsUrl", response.getOrderUrl());
        prepayCreateResultDTO.getPrepayResult().put("paymentUrl", response.getOrderUrl());

        DelayQueryResultBody delayQueryResultBody = new DelayQueryResultBody();
        delayQueryResultBody.setTransNo(paymentDO.getTradeNo());
        delayQueryResultBody.setType(BusinessConstants.TRANSACTION_PAY);
        selfMqProducer.sendSelfQueryDelayQuery(delayQueryResultBody, MqDelayLevelConst.DELAY_2M);
        return prepayCreateResultDTO;
    }

    @Override
    public PreAuthCancelResultDTO doCancel(final PaymentDO paymentDO) {
        log.error("No API support");
        return null;
    }

    @Override
    public PreAuthCaptureResultDTO doCapture(final PreAuthCaptureRequest preauthCaptureRequest, final String captureId, final PaymentDO capturePaymentDO) {
        log.error("No API support");
        return null;
    }

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.ZALOPAY);
    }

    @Override
    public RefundResultDTO refund(final ProcessorRefundRequest request) {
        RefundRequest req = new RefundRequest();
        req.setRefundId(request.getRefundNo());
        req.setZpTransId(request.getPayNo());
        req.setAmount(request.getRefundAmount().longValue());
        // fixme 时区从配置获取
//        req.setTimestamp(new DateBuild(ZoneId.of("Asia/Ho_Chi_Minh")).toDate().getTime());
        req.setTimestamp(System.currentTimeMillis());
        req.setDescription(request.getReason());
        RefundResponse response = zaloPayApiService.refundOrder(req);

        RefundResultDTO resp = new RefundResultDTO();
        if (response.getReturnCode() > 0) {
            resp.setStatus(TransStatusEnum.SUCCESS.getKey());
            resp.setRefundPayNo(response.getRefundId().toString());
        } else {
            resp.setStatus(TransStatusEnum.FAIL.getKey());
        }
        return resp;
    }

    @Override
    public ChannelEnum getRefundChannel() {
        return ChannelEnum.ZALO_PAY;
    }

}