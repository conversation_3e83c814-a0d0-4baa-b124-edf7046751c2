/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalopay.processor;

import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.QueryRefundProcessor;
import so.dian.hera.interceptor.dto.req.RefundQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.RefundQueryResultDTO;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.mofa3.lang.util.DateUtil;
import so.dian.platform.zalopay.common.enums.ZaloPayReturnCodeEnum;
import so.dian.platform.zalopay.dto.request.RefundQueryRequest;
import so.dian.platform.zalopay.dto.response.ZaloBaseResponse;

import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloPayPayResultProcessor.java, v 1.0 2024-04-09 3:06 PM Exp $
 */
@Slf4j
@Component
public class ZaloPayRefundResultProcessor implements QueryRefundProcessor {

    private final ZaloPayApiService zaloPayApiService;

    public ZaloPayRefundResultProcessor(ObjectProvider<ZaloPayApiService> zaloPayApiServiceProvider){
        this.zaloPayApiService = zaloPayApiServiceProvider.getIfUnique();
    }

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.ZALOPAY);
    }


    @Override
    public RefundQueryResultDTO orderRefundQuery(RefundQueryResultRequest queryResultRequest) {
        RefundQueryRequest request= new RefundQueryRequest();
        request.setMRefundId(queryResultRequest.getRefundNo());
        // fixme 时区从配置获取
        request.setTimestamp(new DateBuild(ZoneId.of("Asia/Ho_Chi_Minh")).toDate().getTime());
        ZaloBaseResponse response= zaloPayApiService.refundQuery(request);
        RefundQueryResultDTO resp= new RefundQueryResultDTO();
        resp.setRefundNo(queryResultRequest.getRefundNo());
        if(Objects.equals(response.getReturnCode(), ZaloPayReturnCodeEnum.SUCCESS.getCode())){
            // sub_return_code
            resp.setStatus(PayStatus.REFUNDED.getCode());
            // 查询接口没有返回退款成功时间，如果订单状态没有更新过，则以当前查询时间为退款成功时间返回
            resp.setRefundTime(new Date());
        }else if(Objects.equals(response.getReturnCode(), ZaloPayReturnCodeEnum.PROCESSING.getCode())){
            resp.setStatus(PayStatus.REFUNDING.getCode());
        }else{
            resp.setStatus(PayStatus.FAIL.getCode());
        }
        return resp;
    }
}