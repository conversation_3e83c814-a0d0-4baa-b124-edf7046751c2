/*
 * Dian.so Inc.
 * Copyright (c) 2016-2024 All Rights Reserved.
 */
package so.dian.platform.zalopay.processor;

import com.chargebolt.hera.client.enums.PaywayEnum;
import com.chargebolt.hera.client.enums.status.PayStatus;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.stereotype.Component;
import so.dian.hera.interceptor.QueryPayProcessor;
import so.dian.hera.interceptor.dto.req.PayQueryResultRequest;
import so.dian.hera.interceptor.dto.rsp.PayQueryResultDTO;
import so.dian.mofa3.lang.util.DateBuild;
import so.dian.platform.zalopay.common.enums.ZaloPayReturnCodeEnum;
import so.dian.platform.zalopay.dto.request.OrderQueryRequest;
import so.dian.platform.zalopay.dto.response.OrderQueryResponse;

import java.util.List;
import java.util.Objects;

/**
 * TODO
 *
 * <AUTHOR>
 * @version: ZaloPayPayResultProcessor.java, v 1.0 2024-04-09 3:06 PM Exp $
 */
@Slf4j
@Component
public class ZaloPayResultProcessor implements QueryPayProcessor {

    private final ZaloPayApiService zaloPayApiService;

    public ZaloPayResultProcessor(ObjectProvider<ZaloPayApiService> zaloPayApiServiceProvider){
        this.zaloPayApiService= zaloPayApiServiceProvider.getIfUnique();
    }

    @Override
    public List<PaywayEnum> getPayway() {
        return Lists.newArrayList(PaywayEnum.ZALOPAY);
    }

    @Override
    public PayQueryResultDTO orderPayQuery(final PayQueryResultRequest request) {
        PayQueryResultDTO resp= new PayQueryResultDTO();
        OrderQueryRequest req= new OrderQueryRequest();
        req.setAppTransId(request.getTradeNo());
        OrderQueryResponse result= zaloPayApiService.queryOrder(req);
        resp.setTradeNo(request.getTradeNo());
        resp.setPayNo(request.getPayNo());
        if(Objects.equals(ZaloPayReturnCodeEnum.SUCCESS.getCode(), result.getReturnCode())){
            // sub_return_code
            resp.setStatus(PayStatus.PAID.getCode());
            resp.setThirdPayTradeNo(result.getZpTransId().toString());
            // 查询接口没有返回支付成功时间，如果订单状态没有更新过，则以当前查询时间为支付成功时间返回
            resp.setPayTime(new DateBuild().toDate());
        }else if(Objects.equals(ZaloPayReturnCodeEnum.FAILURE.getCode(), result.getReturnCode())){
            resp.setStatus(PayStatus.FAIL.getCode());
        }else{
            // 处理中，初始化未支付状态
            resp.setStatus(PayStatus.INIT.getCode());
        }
        return resp;
    }
}